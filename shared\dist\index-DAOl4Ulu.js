"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});/*! @azure/msal-common v14.16.1 2025-08-05 */const u={LIBRARY_NAME:"MSAL.JS",SKU:"msal.js.common",CACHE_PREFIX:"msal",DEFAULT_AUTHORITY:"https://login.microsoftonline.com/common/",DEFAULT_AUTHORITY_HOST:"login.microsoftonline.com",DEFAULT_COMMON_TENANT:"common",ADFS:"adfs",DSTS:"dstsv2",AAD_INSTANCE_DISCOVERY_ENDPT:"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",CIAM_AUTH_URL:".ciamlogin.com",AAD_TENANT_DOMAIN_SUFFIX:".onmicrosoft.com",RESOURCE_DELIM:"|",NO_ACCOUNT:"NO_ACCOUNT",CLAIMS:"claims",CONSUMER_UTID:"9188040d-6c67-4c5b-b112-36a304b66dad",OPENID_SCOPE:"openid",PROFILE_SCOPE:"profile",OFFLINE_ACCESS_SCOPE:"offline_access",EMAIL_SCOPE:"email",CODE_RESPONSE_TYPE:"code",CODE_GRANT_TYPE:"authorization_code",RT_GRANT_TYPE:"refresh_token",FRAGMENT_RESPONSE_MODE:"fragment",S256_CODE_CHALLENGE_METHOD:"S256",URL_FORM_CONTENT_TYPE:"application/x-www-form-urlencoded;charset=utf-8",AUTHORIZATION_PENDING:"authorization_pending",NOT_DEFINED:"not_defined",EMPTY_STRING:"",NOT_APPLICABLE:"N/A",NOT_AVAILABLE:"Not Available",FORWARD_SLASH:"/",IMDS_ENDPOINT:"http://***************/metadata/instance/compute/location",IMDS_VERSION:"2020-06-01",IMDS_TIMEOUT:2e3,AZURE_REGION_AUTO_DISCOVER_FLAG:"TryAutoDetect",REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX:"login.microsoft.com",KNOWN_PUBLIC_CLOUDS:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"],TOKEN_RESPONSE_TYPE:"token",ID_TOKEN_RESPONSE_TYPE:"id_token",SHR_NONCE_VALIDITY:240,INVALID_INSTANCE:"invalid_instance"},We={CLIENT_ERROR_RANGE_START:400,CLIENT_ERROR_RANGE_END:499,SERVER_ERROR_RANGE_START:500,SERVER_ERROR_RANGE_END:599},Ie=[u.OPENID_SCOPE,u.PROFILE_SCOPE,u.OFFLINE_ACCESS_SCOPE],Hn=[...Ie,u.EMAIL_SCOPE],x={CONTENT_TYPE:"Content-Type",CONTENT_LENGTH:"Content-Length",RETRY_AFTER:"Retry-After",CCS_HEADER:"X-AnchorMailbox",WWWAuthenticate:"WWW-Authenticate",AuthenticationInfo:"Authentication-Info",X_MS_REQUEST_ID:"x-ms-request-id",X_MS_HTTP_VERSION:"x-ms-httpver"},U={ID_TOKEN:"idtoken",CLIENT_INFO:"client.info",ADAL_ID_TOKEN:"adal.idtoken",ERROR:"error",ERROR_DESC:"error.description",ACTIVE_ACCOUNT:"active-account",ACTIVE_ACCOUNT_FILTERS:"active-account-filters"},ge={COMMON:"common",ORGANIZATIONS:"organizations",CONSUMERS:"consumers"},je={ACCESS_TOKEN:"access_token",XMS_CC:"xms_cc"},M={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none",CREATE:"create",NO_SESSION:"no_session"},Dn={PLAIN:"plain",S256:"S256"},He={QUERY:"query",FRAGMENT:"fragment"},ci={...He},mo={AUTHORIZATION_CODE_GRANT:"authorization_code",REFRESH_TOKEN_GRANT:"refresh_token"},Je={MSSTS_ACCOUNT_TYPE:"MSSTS",ADFS_ACCOUNT_TYPE:"ADFS",GENERIC_ACCOUNT_TYPE:"Generic"},K={CACHE_KEY_SEPARATOR:"-",CLIENT_INFO_SEPARATOR:"."},I={ID_TOKEN:"IdToken",ACCESS_TOKEN:"AccessToken",ACCESS_TOKEN_WITH_AUTH_SCHEME:"AccessToken_With_AuthScheme",REFRESH_TOKEN:"RefreshToken"},Qt="appmetadata",hi="client_info",Be="1",ot={CACHE_KEY:"authority-metadata",REFRESH_TIME_SECONDS:3600*24},q={CONFIG:"config",CACHE:"cache",NETWORK:"network",HARDCODED_VALUES:"hardcoded_values"},H={SCHEMA_VERSION:5,MAX_LAST_HEADER_BYTES:330,MAX_CACHED_ERRORS:50,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"},_={BEARER:"Bearer",POP:"pop",SSH:"ssh-cert"},Fe={DEFAULT_THROTTLE_TIME_SECONDS:60,DEFAULT_MAX_THROTTLE_TIME_SECONDS:3600,THROTTLING_PREFIX:"throttling",X_MS_LIB_CAPABILITY_VALUE:"retry-after, h429"},Kn={INVALID_GRANT_ERROR:"invalid_grant",CLIENT_MISMATCH_ERROR:"client_mismatch"},xn={username:"username",password:"password"},Xe={httpSuccess:200,httpBadRequest:400},ve={FAILED_AUTO_DETECTION:"1",INTERNAL_CACHE:"2",ENVIRONMENT_VARIABLE:"3",IMDS:"4"},Rt={CONFIGURED_NO_AUTO_DETECTION:"2",AUTO_DETECTION_REQUESTED_SUCCESSFUL:"4",AUTO_DETECTION_REQUESTED_FAILED:"5"},ue={NOT_APPLICABLE:"0",FORCE_REFRESH_OR_CLAIMS:"1",NO_CACHED_ACCESS_TOKEN:"2",CACHED_ACCESS_TOKEN_EXPIRED:"3",PROACTIVELY_REFRESHED:"4"},fo={Pop:"pop"},li=300;/*! @azure/msal-common v14.16.1 2025-08-05 */const Yt="unexpected_error",di="post_request_failed";/*! @azure/msal-common v14.16.1 2025-08-05 */const Bn={[Yt]:"Unexpected error in authentication.",[di]:"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."};class b extends Error{constructor(e,t,n){const o=t?`${e}: ${t}`:e;super(o),Object.setPrototypeOf(this,b.prototype),this.errorCode=e||u.EMPTY_STRING,this.errorMessage=t||u.EMPTY_STRING,this.subError=n||u.EMPTY_STRING,this.name="AuthError"}setCorrelationId(e){this.correlationId=e}}function Co(a,e){return new b(a,e?`${Bn[a]} ${e}`:Bn[a])}/*! @azure/msal-common v14.16.1 2025-08-05 */const Wt="client_info_decoding_error",yo="client_info_empty_error",jt="token_parsing_error",To="null_or_empty_token",ae="endpoints_resolution_error",Io="network_error",Ao="openid_config_error",Eo="hash_not_deserialized",Pe="invalid_state",vo="state_mismatch",rt="state_not_found",So="nonce_mismatch",Jt="auth_time_not_found",wo="max_age_transpired",ui="multiple_matching_tokens",gi="multiple_matching_accounts",_o="multiple_matching_appMetadata",ko="request_cannot_be_made",Ro="cannot_remove_empty_scope",bo="cannot_append_scopeset",Mt="empty_input_scopeset",pi="device_code_polling_cancelled",mi="device_code_expired",fi="device_code_unknown_error",Xt="no_account_in_silent_request",Oo="invalid_cache_record",Zt="invalid_cache_environment",Lt="no_account_found",Ut="no_crypto_object",Ht="unexpected_credential_type",Ci="invalid_assertion",yi="invalid_client_credential",se="token_refresh_required",Ti="user_timeout_reached",No="token_claims_cnf_required_for_signedjwt",Po="authorization_code_missing_from_server_response",Ii="binding_key_not_removed",Mo="end_session_endpoint_not_supported",en="key_id_missing",Ai="no_network_connectivity",Ei="user_canceled",vi="missing_tenant_id_error",E="method_not_implemented",Si="nested_app_auth_bridge_disabled";/*! @azure/msal-common v14.16.1 2025-08-05 */const Fn={[Wt]:"The client info could not be parsed/decoded correctly",[yo]:"The client info was empty",[jt]:"Token cannot be parsed",[To]:"The token is null or empty",[ae]:"Endpoints cannot be resolved",[Io]:"Network request failed",[Ao]:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.",[Eo]:"The hash parameters could not be deserialized",[Pe]:"State was not the expected format",[vo]:"State mismatch error",[rt]:"State not found",[So]:"Nonce mismatch error",[Jt]:"Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information.",[wo]:"Max Age is set to 0, or too much time has elapsed since the last end-user authentication.",[ui]:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account.",[gi]:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account",[_o]:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata",[ko]:"Token request cannot be made without authorization code or refresh token.",[Ro]:"Cannot remove null or empty scope from ScopeSet",[bo]:"Cannot append ScopeSet",[Mt]:"Empty input ScopeSet cannot be processed",[pi]:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.",[mi]:"Device code is expired.",[fi]:"Device code stopped polling for unknown reasons.",[Xt]:"Please pass an account object, silent flow is not supported without account information",[Oo]:"Cache record object was null or undefined.",[Zt]:"Invalid environment when attempting to create cache entry",[Lt]:"No account found in cache for given key.",[Ut]:"No crypto object detected.",[Ht]:"Unexpected credential type.",[Ci]:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515",[yi]:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential",[se]:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.",[Ti]:"User defined timeout for device code polling reached",[No]:"Cannot generate a POP jwt if the token_claims are not populated",[Po]:"Server response does not contain an authorization code to proceed",[Ii]:"Could not remove the credential's binding key from storage.",[Mo]:"The provided authority does not support logout",[en]:"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.",[Ai]:"No network connectivity. Check your internet connection.",[Ei]:"User cancelled the flow.",[vi]:"A tenant id - not common, organizations, or consumers - must be specified when using the client_credentials flow.",[E]:"This method has not been implemented",[Si]:"The nested app auth bridge is disabled"};class ze extends b{constructor(e,t){super(e,t?`${Fn[e]}: ${t}`:Fn[e]),this.name="ClientAuthError",Object.setPrototypeOf(this,ze.prototype)}}function p(a,e){return new ze(a,e)}/*! @azure/msal-common v14.16.1 2025-08-05 */const it={createNewGuid:()=>{throw p(E)},base64Decode:()=>{throw p(E)},base64Encode:()=>{throw p(E)},base64UrlEncode:()=>{throw p(E)},encodeKid:()=>{throw p(E)},async getPublicKeyThumbprint(){throw p(E)},async removeTokenBindingKey(){throw p(E)},async clearKeystore(){throw p(E)},async signJwt(){throw p(E)},async hashString(){throw p(E)}};/*! @azure/msal-common v14.16.1 2025-08-05 */exports.LogLevel=void 0;(function(a){a[a.Error=0]="Error",a[a.Warning=1]="Warning",a[a.Info=2]="Info",a[a.Verbose=3]="Verbose",a[a.Trace=4]="Trace"})(exports.LogLevel||(exports.LogLevel={}));class he{constructor(e,t,n){this.level=exports.LogLevel.Info;const o=()=>{},r=e||he.createDefaultLoggerOptions();this.localCallback=r.loggerCallback||o,this.piiLoggingEnabled=r.piiLoggingEnabled||!1,this.level=typeof r.logLevel=="number"?r.logLevel:exports.LogLevel.Info,this.correlationId=r.correlationId||u.EMPTY_STRING,this.packageName=t||u.EMPTY_STRING,this.packageVersion=n||u.EMPTY_STRING}static createDefaultLoggerOptions(){return{loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:exports.LogLevel.Info}}clone(e,t,n){return new he({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level,correlationId:n||this.correlationId},e,t)}logMessage(e,t){if(t.logLevel>this.level||!this.piiLoggingEnabled&&t.containsPii)return;const r=`${`[${new Date().toUTCString()}] : [${t.correlationId||this.correlationId||""}]`} : ${this.packageName}@${this.packageVersion} : ${exports.LogLevel[t.logLevel]} - ${e}`;this.executeCallback(t.logLevel,r,t.containsPii||!1)}executeCallback(e,t,n){this.localCallback&&this.localCallback(e,t,n)}error(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Error,containsPii:!1,correlationId:t||u.EMPTY_STRING})}errorPii(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Error,containsPii:!0,correlationId:t||u.EMPTY_STRING})}warning(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Warning,containsPii:!1,correlationId:t||u.EMPTY_STRING})}warningPii(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Warning,containsPii:!0,correlationId:t||u.EMPTY_STRING})}info(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Info,containsPii:!1,correlationId:t||u.EMPTY_STRING})}infoPii(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Info,containsPii:!0,correlationId:t||u.EMPTY_STRING})}verbose(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Verbose,containsPii:!1,correlationId:t||u.EMPTY_STRING})}verbosePii(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Verbose,containsPii:!0,correlationId:t||u.EMPTY_STRING})}trace(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Trace,containsPii:!1,correlationId:t||u.EMPTY_STRING})}tracePii(e,t){this.logMessage(e,{logLevel:exports.LogLevel.Trace,containsPii:!0,correlationId:t||u.EMPTY_STRING})}isPiiLoggingEnabled(){return this.piiLoggingEnabled||!1}}/*! @azure/msal-common v14.16.1 2025-08-05 */const Lo="@azure/msal-common",tn="14.16.1";/*! @azure/msal-common v14.16.1 2025-08-05 */const pt={None:"none"};/*! @azure/msal-common v14.16.1 2025-08-05 */function Ae(a,e){const t=wi(a);try{const n=e(t);return JSON.parse(n)}catch{throw p(jt)}}function wi(a){if(!a)throw p(To);const t=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(a);if(!t||t.length<4)throw p(jt);return t[2]}function Uo(a,e){if(e===0||Date.now()-3e5>a+e)throw p(wo)}/*! @azure/msal-common v14.16.1 2025-08-05 */function oe(){return Math.round(new Date().getTime()/1e3)}function Dt(a,e){const t=Number(a)||0;return oe()+e>t}function _i(a){return Number(a)>oe()}/*! @azure/msal-common v14.16.1 2025-08-05 */function ke(a){return[ki(a),Ri(a),bi(a),Oi(a),Ni(a)].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}function mt(a,e,t,n,o){return{credentialType:I.ID_TOKEN,homeAccountId:a,environment:e,clientId:n,secret:t,realm:o}}function ft(a,e,t,n,o,r,i,s,c,h,d,g,f,y,A){var N,F;const v={homeAccountId:a,credentialType:I.ACCESS_TOKEN,secret:t,cachedAt:oe().toString(),expiresOn:i.toString(),extendedExpiresOn:s.toString(),environment:e,clientId:n,realm:o,target:r,tokenType:d||_.BEARER};if(g&&(v.userAssertionHash=g),h&&(v.refreshOn=h.toString()),y&&(v.requestedClaims=y,v.requestedClaimsHash=A),((N=v.tokenType)==null?void 0:N.toLowerCase())!==_.BEARER.toLowerCase())switch(v.credentialType=I.ACCESS_TOKEN_WITH_AUTH_SCHEME,v.tokenType){case _.POP:const L=Ae(t,c);if(!((F=L==null?void 0:L.cnf)!=null&&F.kid))throw p(No);v.keyId=L.cnf.kid;break;case _.SSH:v.keyId=f}return v}function Ho(a,e,t,n,o,r,i){const s={credentialType:I.REFRESH_TOKEN,homeAccountId:a,environment:e,clientId:n,secret:t};return r&&(s.userAssertionHash=r),o&&(s.familyId=o),i&&(s.expiresOn=i.toString()),s}function nn(a){return a.hasOwnProperty("homeAccountId")&&a.hasOwnProperty("environment")&&a.hasOwnProperty("credentialType")&&a.hasOwnProperty("clientId")&&a.hasOwnProperty("secret")}function Gn(a){return a?nn(a)&&a.hasOwnProperty("realm")&&a.hasOwnProperty("target")&&(a.credentialType===I.ACCESS_TOKEN||a.credentialType===I.ACCESS_TOKEN_WITH_AUTH_SCHEME):!1}function qn(a){return a?nn(a)&&a.hasOwnProperty("realm")&&a.credentialType===I.ID_TOKEN:!1}function $n(a){return a?nn(a)&&a.credentialType===I.REFRESH_TOKEN:!1}function ki(a){return[a.homeAccountId,a.environment].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}function Ri(a){const e=a.credentialType===I.REFRESH_TOKEN&&a.familyId||a.clientId;return[a.credentialType,e,a.realm||""].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}function bi(a){return(a.target||"").toLowerCase()}function Oi(a){return(a.requestedClaimsHash||"").toLowerCase()}function Ni(a){return a.tokenType&&a.tokenType.toLowerCase()!==_.BEARER.toLowerCase()?a.tokenType.toLowerCase():""}function Pi(a,e){const t=a.indexOf(H.CACHE_KEY)===0;let n=!0;return e&&(n=e.hasOwnProperty("failedRequests")&&e.hasOwnProperty("errors")&&e.hasOwnProperty("cacheHits")),t&&n}function Mi(a,e){let t=!1;a&&(t=a.indexOf(Fe.THROTTLING_PREFIX)===0);let n=!0;return e&&(n=e.hasOwnProperty("throttleTime")),t&&n}function Li({environment:a,clientId:e}){return[Qt,a,e].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}function Ui(a,e){return e?a.indexOf(Qt)===0&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("environment"):!1}function Hi(a,e){return e?a.indexOf(ot.CACHE_KEY)===0&&e.hasOwnProperty("aliases")&&e.hasOwnProperty("preferred_cache")&&e.hasOwnProperty("preferred_network")&&e.hasOwnProperty("canonical_authority")&&e.hasOwnProperty("authorization_endpoint")&&e.hasOwnProperty("token_endpoint")&&e.hasOwnProperty("issuer")&&e.hasOwnProperty("aliasesFromNetwork")&&e.hasOwnProperty("endpointsFromNetwork")&&e.hasOwnProperty("expiresAt")&&e.hasOwnProperty("jwks_uri"):!1}function zn(){return oe()+ot.REFRESH_TIME_SECONDS}function Ze(a,e,t){a.authorization_endpoint=e.authorization_endpoint,a.token_endpoint=e.token_endpoint,a.end_session_endpoint=e.end_session_endpoint,a.issuer=e.issuer,a.endpointsFromNetwork=t,a.jwks_uri=e.jwks_uri}function bt(a,e,t){a.aliases=e.aliases,a.preferred_cache=e.preferred_cache,a.preferred_network=e.preferred_network,a.aliasesFromNetwork=t}function Vn(a){return a.expiresAt<=oe()}/*! @azure/msal-common v14.16.1 2025-08-05 */const Do="redirect_uri_empty",Di="claims_request_parsing_error",Ko="authority_uri_insecure",xe="url_parse_error",xo="empty_url_error",Bo="empty_input_scopes_error",Fo="invalid_prompt_value",on="invalid_claims",Go="token_request_empty",qo="logout_request_empty",$o="invalid_code_challenge_method",rn="pkce_params_missing",an="invalid_cloud_discovery_metadata",zo="invalid_authority_metadata",Vo="untrusted_authority",Ct="missing_ssh_jwk",Qo="missing_ssh_kid",Ki="missing_nonce_authentication_header",xi="invalid_authentication_header",Yo="cannot_set_OIDCOptions",Wo="cannot_allow_native_broker",jo="authority_mismatch";/*! @azure/msal-common v14.16.1 2025-08-05 */const Bi={[Do]:"A redirect URI is required for all calls, and none has been set.",[Di]:"Could not parse the given claims request object.",[Ko]:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options",[xe]:"URL could not be parsed into appropriate segments.",[xo]:"URL was empty or null.",[Bo]:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.",[Fo]:"Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest",[on]:"Given claims parameter must be a stringified JSON object.",[Go]:"Token request was empty and not found in cache.",[qo]:"The logout request was null or undefined.",[$o]:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".',[rn]:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request",[an]:"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields",[zo]:"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.",[Vo]:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.",[Ct]:"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.",[Qo]:"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.",[Ki]:"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.",[xi]:"Invalid authentication header provided",[Yo]:"Cannot set OIDCOptions parameter. Please change the protocol mode to OIDC or use a non-Microsoft authority.",[Wo]:"Cannot set allowNativeBroker parameter to true when not in AAD protocol mode.",[jo]:"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."};class yt extends b{constructor(e){super(e,Bi[e]),this.name="ClientConfigurationError",Object.setPrototypeOf(this,yt.prototype)}}function k(a){return new yt(a)}/*! @azure/msal-common v14.16.1 2025-08-05 */class Q{static isEmptyObj(e){if(e)try{const t=JSON.parse(e);return Object.keys(t).length===0}catch{}return!0}static startsWith(e,t){return e.indexOf(t)===0}static endsWith(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length}static queryStringToObject(e){const t={},n=e.split("&"),o=r=>decodeURIComponent(r.replace(/\+/g," "));return n.forEach(r=>{if(r.trim()){const[i,s]=r.split(/=(.+)/g,2);i&&s&&(t[o(i)]=o(s))}}),t}static trimArrayEntries(e){return e.map(t=>t.trim())}static removeEmptyStringsFromArray(e){return e.filter(t=>!!t)}static jsonParseHelper(e){try{return JSON.parse(e)}catch{return null}}static matchPattern(e,t){return new RegExp(e.replace(/\\/g,"\\\\").replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class O{constructor(e){const t=e?Q.trimArrayEntries([...e]):[],n=t?Q.removeEmptyStringsFromArray(t):[];this.validateInputScopes(n),this.scopes=new Set,n.forEach(o=>this.scopes.add(o))}static fromString(e){const n=(e||u.EMPTY_STRING).split(" ");return new O(n)}static createSearchScopes(e){const t=new O(e);return t.containsOnlyOIDCScopes()?t.removeScope(u.OFFLINE_ACCESS_SCOPE):t.removeOIDCScopes(),t}validateInputScopes(e){if(!e||e.length<1)throw k(Bo)}containsScope(e){const t=this.printScopesLowerCase().split(" "),n=new O(t);return e?n.scopes.has(e.toLowerCase()):!1}containsScopeSet(e){return!e||e.scopes.size<=0?!1:this.scopes.size>=e.scopes.size&&e.asArray().every(t=>this.containsScope(t))}containsOnlyOIDCScopes(){let e=0;return Hn.forEach(t=>{this.containsScope(t)&&(e+=1)}),this.scopes.size===e}appendScope(e){e&&this.scopes.add(e.trim())}appendScopes(e){try{e.forEach(t=>this.appendScope(t))}catch{throw p(bo)}}removeScope(e){if(!e)throw p(Ro);this.scopes.delete(e.trim())}removeOIDCScopes(){Hn.forEach(e=>{this.scopes.delete(e)})}unionScopeSets(e){if(!e)throw p(Mt);const t=new Set;return e.scopes.forEach(n=>t.add(n.toLowerCase())),this.scopes.forEach(n=>t.add(n.toLowerCase())),t}intersectingScopeSets(e){if(!e)throw p(Mt);e.containsOnlyOIDCScopes()||e.removeOIDCScopes();const t=this.unionScopeSets(e),n=e.getScopeCount(),o=this.getScopeCount();return t.size<o+n}getScopeCount(){return this.scopes.size}asArray(){const e=[];return this.scopes.forEach(t=>e.push(t)),e}printScopes(){return this.scopes?this.asArray().join(" "):u.EMPTY_STRING}printScopesLowerCase(){return this.printScopes().toLowerCase()}}/*! @azure/msal-common v14.16.1 2025-08-05 */function at(a,e){if(!a)throw p(yo);try{const t=e(a);return JSON.parse(t)}catch{throw p(Wt)}}function Re(a){if(!a)throw p(Wt);const e=a.split(K.CLIENT_INFO_SEPARATOR,2);return{uid:e[0],utid:e.length<2?u.EMPTY_STRING:e[1]}}/*! @azure/msal-common v14.16.1 2025-08-05 */function st(a,e){return!!a&&!!e&&a===e.split(".")[1]}function sn(a,e,t,n){if(n){const{oid:o,sub:r,tid:i,name:s,tfp:c,acr:h}=n,d=i||c||h||"";return{tenantId:d,localAccountId:o||r||"",name:s,isHomeTenant:st(d,a)}}else return{tenantId:t,localAccountId:e,isHomeTenant:st(t,a)}}function cn(a,e,t,n){let o=a;if(e){const{isHomeTenant:r,...i}=e;o={...a,...i}}if(t){const{isHomeTenant:r,...i}=sn(a.homeAccountId,a.localAccountId,a.tenantId,t);return o={...o,...i,idTokenClaims:t,idToken:n},o}return o}/*! @azure/msal-common v14.16.1 2025-08-05 */const W={Default:0,Adfs:1,Dsts:2,Ciam:3};/*! @azure/msal-common v14.16.1 2025-08-05 */function Jo(a){return a&&(a.tid||a.tfp||a.acr)||null}/*! @azure/msal-common v14.16.1 2025-08-05 */const te={AAD:"AAD",OIDC:"OIDC"};/*! @azure/msal-common v14.16.1 2025-08-05 */class P{generateAccountId(){return[this.homeAccountId,this.environment].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}generateAccountKey(){return P.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})}getAccountInfo(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,nativeAccountId:this.nativeAccountId,authorityType:this.authorityType,tenantProfiles:new Map((this.tenantProfiles||[]).map(e=>[e.tenantId,e]))}}isSingleTenant(){return!this.tenantProfiles}static generateAccountCacheKey(e){const t=e.homeAccountId.split(".")[1];return[e.homeAccountId,e.environment||"",t||e.tenantId||""].join(K.CACHE_KEY_SEPARATOR).toLowerCase()}static createAccount(e,t,n){var h,d,g,f,y,A;const o=new P;t.authorityType===W.Adfs?o.authorityType=Je.ADFS_ACCOUNT_TYPE:t.protocolMode===te.AAD?o.authorityType=Je.MSSTS_ACCOUNT_TYPE:o.authorityType=Je.GENERIC_ACCOUNT_TYPE;let r;e.clientInfo&&n&&(r=at(e.clientInfo,n)),o.clientInfo=e.clientInfo,o.homeAccountId=e.homeAccountId,o.nativeAccountId=e.nativeAccountId;const i=e.environment||t&&t.getPreferredCache();if(!i)throw p(Zt);o.environment=i,o.realm=(r==null?void 0:r.utid)||Jo(e.idTokenClaims)||"",o.localAccountId=(r==null?void 0:r.uid)||((h=e.idTokenClaims)==null?void 0:h.oid)||((d=e.idTokenClaims)==null?void 0:d.sub)||"";const s=((g=e.idTokenClaims)==null?void 0:g.preferred_username)||((f=e.idTokenClaims)==null?void 0:f.upn),c=(y=e.idTokenClaims)!=null&&y.emails?e.idTokenClaims.emails[0]:null;if(o.username=s||c||"",o.name=((A=e.idTokenClaims)==null?void 0:A.name)||"",o.cloudGraphHostName=e.cloudGraphHostName,o.msGraphHost=e.msGraphHost,e.tenantProfiles)o.tenantProfiles=e.tenantProfiles;else{const v=sn(e.homeAccountId,o.localAccountId,o.realm,e.idTokenClaims);o.tenantProfiles=[v]}return o}static createFromAccountInfo(e,t,n){var r;const o=new P;return o.authorityType=e.authorityType||Je.GENERIC_ACCOUNT_TYPE,o.homeAccountId=e.homeAccountId,o.localAccountId=e.localAccountId,o.nativeAccountId=e.nativeAccountId,o.realm=e.tenantId,o.environment=e.environment,o.username=e.username,o.name=e.name,o.cloudGraphHostName=t,o.msGraphHost=n,o.tenantProfiles=Array.from(((r=e.tenantProfiles)==null?void 0:r.values())||[]),o}static generateHomeAccountId(e,t,n,o,r){if(!(t===W.Adfs||t===W.Dsts)){if(e)try{const i=at(e,o.base64Decode);if(i.uid&&i.utid)return`${i.uid}.${i.utid}`}catch{}n.warning("No client info in response")}return(r==null?void 0:r.sub)||""}static isAccountEntity(e){return e?e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType"):!1}static accountInfoIsEqual(e,t,n){if(!e||!t)return!1;let o=!0;if(n){const r=e.idTokenClaims||{},i=t.idTokenClaims||{};o=r.iat===i.iat&&r.nonce===i.nonce}return e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment&&e.nativeAccountId===t.nativeAccountId&&o}}/*! @azure/msal-common v14.16.1 2025-08-05 */function Xo(a){return a.startsWith("#/")?a.substring(2):a.startsWith("#")||a.startsWith("?")?a.substring(1):a}function ct(a){if(!a||a.indexOf("=")<0)return null;try{const e=Xo(a),t=Object.fromEntries(new URLSearchParams(e));if(t.code||t.error||t.error_description||t.state)return t}catch{throw p(Eo)}return null}/*! @azure/msal-common v14.16.1 2025-08-05 */class S{get urlString(){return this._urlString}constructor(e){if(this._urlString=e,!this._urlString)throw k(xo);e.includes("#")||(this._urlString=S.canonicalizeUri(e))}static canonicalizeUri(e){if(e){let t=e.toLowerCase();return Q.endsWith(t,"?")?t=t.slice(0,-1):Q.endsWith(t,"?/")&&(t=t.slice(0,-2)),Q.endsWith(t,"/")||(t+="/"),t}return e}validateAsUri(){let e;try{e=this.getUrlComponents()}catch{throw k(xe)}if(!e.HostNameAndPort||!e.PathSegments)throw k(xe);if(!e.Protocol||e.Protocol.toLowerCase()!=="https:")throw k(Ko)}static appendQueryString(e,t){return t?e.indexOf("?")<0?`${e}?${t}`:`${e}&${t}`:e}static removeHashFromUrl(e){return S.canonicalizeUri(e.split("#")[0])}replaceTenantPath(e){const t=this.getUrlComponents(),n=t.PathSegments;return e&&n.length!==0&&(n[0]===ge.COMMON||n[0]===ge.ORGANIZATIONS)&&(n[0]=e),S.constructAuthorityUriFromObject(t)}getUrlComponents(){const e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw k(xe);const n={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]};let o=n.AbsolutePath.split("/");return o=o.filter(r=>r&&r.length>0),n.PathSegments=o,n.QueryString&&n.QueryString.endsWith("/")&&(n.QueryString=n.QueryString.substring(0,n.QueryString.length-1)),n}static getDomainFromUrl(e){const t=RegExp("^([^:/?#]+://)?([^/?#]*)"),n=e.match(t);if(!n)throw k(xe);return n[2]}static getAbsoluteUrl(e,t){if(e[0]===u.FORWARD_SLASH){const o=new S(t).getUrlComponents();return o.Protocol+"//"+o.HostNameAndPort+e}return e}static constructAuthorityUriFromObject(e){return new S(e.Protocol+"//"+e.HostNameAndPort+"/"+e.PathSegments.join("/"))}static hashContainsKnownProperties(e){return!!ct(e)}}/*! @azure/msal-common v14.16.1 2025-08-05 */const Zo={endpointMetadata:{"login.microsoftonline.com":{token_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.com/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/logout"},"login.chinacloudapi.cn":{token_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.chinacloudapi.cn/{tenantid}/discovery/v2.0/keys",issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",authorization_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/logout"},"login.microsoftonline.us":{token_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.us/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/logout"}},instanceDiscoveryMetadata:{metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]}},Qn=Zo.endpointMetadata,hn=Zo.instanceDiscoveryMetadata,er=new Set;hn.metadata.forEach(a=>{a.aliases.forEach(e=>{er.add(e)})});function Fi(a,e){var o;let t;const n=a.canonicalAuthority;if(n){const r=new S(n).getUrlComponents().HostNameAndPort;t=Yn(r,(o=a.cloudDiscoveryMetadata)==null?void 0:o.metadata,q.CONFIG,e)||Yn(r,hn.metadata,q.HARDCODED_VALUES,e)||a.knownAuthorities}return t||[]}function Yn(a,e,t,n){if(n==null||n.trace(`getAliasesFromMetadata called with source: ${t}`),a&&e){const o=ht(e,a);if(o)return n==null||n.trace(`getAliasesFromMetadata: found cloud discovery metadata in ${t}, returning aliases`),o.aliases;n==null||n.trace(`getAliasesFromMetadata: did not find cloud discovery metadata in ${t}`)}return null}function Gi(a){return ht(hn.metadata,a)}function ht(a,e){for(let t=0;t<a.length;t++){const n=a[t];if(n.aliases.includes(e))return n}return null}/*! @azure/msal-common v14.16.1 2025-08-05 */const ln="cache_quota_exceeded",dn="cache_error_unknown";/*! @azure/msal-common v14.16.1 2025-08-05 */const Ot={[ln]:"Exceeded cache storage capacity.",[dn]:"Unexpected error occurred when using cache storage."};class Oe extends Error{constructor(e,t){const n=t||(Ot[e]?Ot[e]:Ot[dn]);super(`${e}: ${n}`),Object.setPrototypeOf(this,Oe.prototype),this.name="CacheError",this.errorCode=e,this.errorMessage=n}}function tr(a){return a instanceof Error?a.name==="QuotaExceededError"||a.name==="NS_ERROR_DOM_QUOTA_REACHED"||a.message.includes("exceeded the quota")?new Oe(ln):new Oe(a.name,a.message):new Oe(dn)}/*! @azure/msal-common v14.16.1 2025-08-05 */class Me{constructor(e,t,n,o){this.clientId=e,this.cryptoImpl=t,this.commonLogger=n.clone(Lo,tn),this.staticAuthorityOptions=o}getAllAccounts(e,t){return this.buildTenantProfiles(this.getAccountsFilteredBy(t||{},e),e,t)}getAccountInfoFilteredBy(e,t){const n=this.getAllAccounts(t,e);return n.length>1?n.sort(r=>r.idTokenClaims?-1:1)[0]:n.length===1?n[0]:null}getBaseAccountInfo(e,t){const n=this.getAccountsFilteredBy(e,t);return n.length>0?n[0].getAccountInfo():null}buildTenantProfiles(e,t,n){return e.flatMap(o=>this.getTenantProfilesFromAccountEntity(o,t,n==null?void 0:n.tenantId,n))}getTenantedAccountInfoByFilter(e,t,n,o,r){let i=null,s;if(r&&!this.tenantProfileMatchesFilter(n,r))return null;const c=this.getIdToken(e,o,t,n.tenantId);return c&&(s=Ae(c.secret,this.cryptoImpl.base64Decode),!this.idTokenClaimsMatchTenantProfileFilter(s,r))?null:(i=cn(e,n,s,c==null?void 0:c.secret),i)}getTenantProfilesFromAccountEntity(e,t,n,o){const r=e.getAccountInfo();let i=r.tenantProfiles||new Map;const s=this.getTokenKeys();if(n){const h=i.get(n);if(h)i=new Map([[n,h]]);else return[]}const c=[];return i.forEach(h=>{const d=this.getTenantedAccountInfoByFilter(r,s,h,t,o);d&&c.push(d)}),c}tenantProfileMatchesFilter(e,t){return!(t.localAccountId&&!this.matchLocalAccountIdFromTenantProfile(e,t.localAccountId)||t.name&&e.name!==t.name||t.isHomeTenant!==void 0&&e.isHomeTenant!==t.isHomeTenant)}idTokenClaimsMatchTenantProfileFilter(e,t){return!(t&&(t.localAccountId&&!this.matchLocalAccountIdFromTokenClaims(e,t.localAccountId)||t.loginHint&&!this.matchLoginHintFromTokenClaims(e,t.loginHint)||t.username&&!this.matchUsername(e.preferred_username,t.username)||t.name&&!this.matchName(e,t.name)||t.sid&&!this.matchSid(e,t.sid)))}async saveCacheRecord(e,t,n){var o;if(!e)throw p(Oo);try{e.account&&this.setAccount(e.account,t),e.idToken&&(n==null?void 0:n.idToken)!==!1&&this.setIdTokenCredential(e.idToken,t),e.accessToken&&(n==null?void 0:n.accessToken)!==!1&&await this.saveAccessToken(e.accessToken,t),e.refreshToken&&(n==null?void 0:n.refreshToken)!==!1&&this.setRefreshTokenCredential(e.refreshToken,t),e.appMetadata&&this.setAppMetadata(e.appMetadata,t)}catch(r){throw(o=this.commonLogger)==null||o.error("CacheManager.saveCacheRecord: failed"),r instanceof b?r:tr(r)}}async saveAccessToken(e,t){const n={clientId:e.clientId,credentialType:e.credentialType,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm,tokenType:e.tokenType,requestedClaimsHash:e.requestedClaimsHash},o=this.getTokenKeys(),r=O.fromString(e.target);o.accessToken.forEach(i=>{if(!this.accessTokenKeyMatchesFilter(i,n,!1))return;const s=this.getAccessTokenCredential(i,t);s&&this.credentialMatchesFilter(s,n)&&O.fromString(s.target).intersectingScopeSets(r)&&this.removeAccessToken(i,t)}),this.setAccessTokenCredential(e,t)}getAccountsFilteredBy(e,t){const n=this.getAccountKeys(),o=[];return n.forEach(r=>{var h;if(!this.isAccountKey(r,e.homeAccountId))return;const i=this.getAccount(r,t,this.commonLogger);if(!i||e.homeAccountId&&!this.matchHomeAccountId(i,e.homeAccountId)||e.username&&!this.matchUsername(i.username,e.username)||e.environment&&!this.matchEnvironment(i,e.environment)||e.realm&&!this.matchRealm(i,e.realm)||e.nativeAccountId&&!this.matchNativeAccountId(i,e.nativeAccountId)||e.authorityType&&!this.matchAuthorityType(i,e.authorityType))return;const s={localAccountId:e==null?void 0:e.localAccountId,name:e==null?void 0:e.name},c=(h=i.tenantProfiles)==null?void 0:h.filter(d=>this.tenantProfileMatchesFilter(d,s));c&&c.length===0||o.push(i)}),o}isAccountKey(e,t,n){return!(e.split(K.CACHE_KEY_SEPARATOR).length<3||t&&!e.toLowerCase().includes(t.toLowerCase())||n&&!e.toLowerCase().includes(n.toLowerCase()))}isCredentialKey(e){if(e.split(K.CACHE_KEY_SEPARATOR).length<6)return!1;const t=e.toLowerCase();if(t.indexOf(I.ID_TOKEN.toLowerCase())===-1&&t.indexOf(I.ACCESS_TOKEN.toLowerCase())===-1&&t.indexOf(I.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())===-1&&t.indexOf(I.REFRESH_TOKEN.toLowerCase())===-1)return!1;if(t.indexOf(I.REFRESH_TOKEN.toLowerCase())>-1){const n=`${I.REFRESH_TOKEN}${K.CACHE_KEY_SEPARATOR}${this.clientId}${K.CACHE_KEY_SEPARATOR}`,o=`${I.REFRESH_TOKEN}${K.CACHE_KEY_SEPARATOR}${Be}${K.CACHE_KEY_SEPARATOR}`;if(t.indexOf(n.toLowerCase())===-1&&t.indexOf(o.toLowerCase())===-1)return!1}else if(t.indexOf(this.clientId.toLowerCase())===-1)return!1;return!0}credentialMatchesFilter(e,t){return!(t.clientId&&!this.matchClientId(e,t.clientId)||t.userAssertionHash&&!this.matchUserAssertionHash(e,t.userAssertionHash)||typeof t.homeAccountId=="string"&&!this.matchHomeAccountId(e,t.homeAccountId)||t.environment&&!this.matchEnvironment(e,t.environment)||t.realm&&!this.matchRealm(e,t.realm)||t.credentialType&&!this.matchCredentialType(e,t.credentialType)||t.familyId&&!this.matchFamilyId(e,t.familyId)||t.target&&!this.matchTarget(e,t.target)||(t.requestedClaimsHash||e.requestedClaimsHash)&&e.requestedClaimsHash!==t.requestedClaimsHash||e.credentialType===I.ACCESS_TOKEN_WITH_AUTH_SCHEME&&(t.tokenType&&!this.matchTokenType(e,t.tokenType)||t.tokenType===_.SSH&&t.keyId&&!this.matchKeyId(e,t.keyId)))}getAppMetadataFilteredBy(e){const t=this.getKeys(),n={};return t.forEach(o=>{if(!this.isAppMetadata(o))return;const r=this.getAppMetadata(o);r&&(e.environment&&!this.matchEnvironment(r,e.environment)||e.clientId&&!this.matchClientId(r,e.clientId)||(n[o]=r))}),n}getAuthorityMetadataByAlias(e){const t=this.getAuthorityMetadataKeys();let n=null;return t.forEach(o=>{if(!this.isAuthorityMetadata(o)||o.indexOf(this.clientId)===-1)return;const r=this.getAuthorityMetadata(o);r&&r.aliases.indexOf(e)!==-1&&(n=r)}),n}async removeAllAccounts(e){const t=this.getAccountKeys(),n=[];t.forEach(o=>{n.push(this.removeAccount(o,e))}),await Promise.all(n)}async removeAccount(e,t){const n=this.getAccount(e,t,this.commonLogger);n&&(await this.removeAccountContext(n,t),this.removeItem(e,t))}async removeAccountContext(e,t){const n=this.getTokenKeys(),o=e.generateAccountId();n.idToken.forEach(r=>{r.indexOf(o)===0&&this.removeIdToken(r,t)}),n.accessToken.forEach(r=>{r.indexOf(o)===0&&this.removeAccessToken(r,t)}),n.refreshToken.forEach(r=>{r.indexOf(o)===0&&this.removeRefreshToken(r,t)}),this.getKeys().forEach(r=>{r.includes(o)&&this.removeItem(r,t)})}updateOutdatedCachedAccount(e,t,n,o){var r;if(t&&t.isSingleTenant()){(r=this.commonLogger)==null||r.verbose("updateOutdatedCachedAccount: Found a single-tenant (outdated) account entity in the cache, migrating to multi-tenant account entity");const i=this.getAccountKeys().filter(g=>g.startsWith(t.homeAccountId)),s=[];i.forEach(g=>{const f=this.getCachedAccountEntity(g,n);f&&s.push(f)});const c=s.find(g=>st(g.realm,g.homeAccountId))||s[0];c.tenantProfiles=s.map(g=>({tenantId:g.realm,localAccountId:g.localAccountId,name:g.name,isHomeTenant:st(g.realm,g.homeAccountId)}));const h=Me.toObject(new P,{...c}),d=h.generateAccountKey();return i.forEach(g=>{g!==d&&this.removeOutdatedAccount(e,n)}),this.setAccount(h,n),o==null||o.verbose("Updated an outdated account entity in the cache"),h}return t}removeAccessToken(e,t){const n=this.getAccessTokenCredential(e,t);if(this.removeItem(e,t),!n||n.credentialType.toLowerCase()!==I.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()||n.tokenType!==_.POP)return;const o=n.keyId;o&&this.cryptoImpl.removeTokenBindingKey(o).catch(()=>{this.commonLogger.error("Binding key could not be removed")})}removeAppMetadata(e){return this.getKeys().forEach(n=>{this.isAppMetadata(n)&&this.removeItem(n,e)}),!0}readAccountFromCache(e,t){const n=P.generateAccountCacheKey(e);return this.getAccount(n,t,this.commonLogger)}getIdToken(e,t,n,o,r){this.commonLogger.trace("CacheManager - getIdToken called");const i={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:I.ID_TOKEN,clientId:this.clientId,realm:o},s=this.getIdTokensByFilter(i,t,n),c=s.size;if(c<1)return this.commonLogger.info("CacheManager:getIdToken - No token found"),null;if(c>1){let h=s;if(!o){const d=new Map;s.forEach((f,y)=>{f.realm===e.tenantId&&d.set(y,f)});const g=d.size;if(g<1)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account but none match account entity tenant id, returning first result"),s.values().next().value;if(g===1)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account, defaulting to home tenant profile"),d.values().next().value;h=d}return this.commonLogger.info("CacheManager:getIdToken - Multiple matching ID tokens found, clearing them"),h.forEach((d,g)=>{this.removeIdToken(g,t)}),r&&t&&r.addFields({multiMatchedID:s.size},t),null}return this.commonLogger.info("CacheManager:getIdToken - Returning ID token"),s.values().next().value}getIdTokensByFilter(e,t,n){const o=n&&n.idToken||this.getTokenKeys().idToken,r=new Map;return o.forEach(i=>{if(!this.idTokenKeyMatchesFilter(i,{clientId:this.clientId,...e}))return;const s=this.getIdTokenCredential(i,t);s&&this.credentialMatchesFilter(s,e)&&r.set(i,s)}),r}idTokenKeyMatchesFilter(e,t){const n=e.toLowerCase();return!(t.clientId&&n.indexOf(t.clientId.toLowerCase())===-1||t.homeAccountId&&n.indexOf(t.homeAccountId.toLowerCase())===-1)}removeIdToken(e,t){this.removeItem(e,t)}removeRefreshToken(e,t){this.removeItem(e,t)}getAccessToken(e,t,n,o,r){this.commonLogger.trace("CacheManager - getAccessToken called");const i=O.createSearchScopes(t.scopes),s=t.authenticationScheme||_.BEARER,c=s.toLowerCase()!==_.BEARER.toLowerCase()?I.ACCESS_TOKEN_WITH_AUTH_SCHEME:I.ACCESS_TOKEN,h={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:c,clientId:this.clientId,realm:o||e.tenantId,target:i,tokenType:s,keyId:t.sshKid,requestedClaimsHash:t.requestedClaimsHash},d=n&&n.accessToken||this.getTokenKeys().accessToken,g=[];d.forEach(y=>{if(this.accessTokenKeyMatchesFilter(y,h,!0)){const A=this.getAccessTokenCredential(y,t.correlationId);A&&this.credentialMatchesFilter(A,h)&&g.push(A)}});const f=g.length;return f<1?(this.commonLogger.info("CacheManager:getAccessToken - No token found"),null):f>1?(this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"),g.forEach(y=>{this.removeAccessToken(ke(y),t.correlationId)}),r&&t.correlationId&&r.addFields({multiMatchedAT:g.length},t.correlationId),null):(this.commonLogger.info("CacheManager:getAccessToken - Returning access token"),g[0])}accessTokenKeyMatchesFilter(e,t,n){const o=e.toLowerCase();if(t.clientId&&o.indexOf(t.clientId.toLowerCase())===-1||t.homeAccountId&&o.indexOf(t.homeAccountId.toLowerCase())===-1||t.realm&&o.indexOf(t.realm.toLowerCase())===-1||t.requestedClaimsHash&&o.indexOf(t.requestedClaimsHash.toLowerCase())===-1)return!1;if(t.target){const r=t.target.asArray();for(let i=0;i<r.length;i++){if(n&&!o.includes(r[i].toLowerCase()))return!1;if(!n&&o.includes(r[i].toLowerCase()))return!0}}return!0}getAccessTokensByFilter(e,t){const n=this.getTokenKeys(),o=[];return n.accessToken.forEach(r=>{if(!this.accessTokenKeyMatchesFilter(r,e,!0))return;const i=this.getAccessTokenCredential(r,t);i&&this.credentialMatchesFilter(i,e)&&o.push(i)}),o}getRefreshToken(e,t,n,o,r){this.commonLogger.trace("CacheManager - getRefreshToken called");const i=t?Be:void 0,s={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:I.REFRESH_TOKEN,clientId:this.clientId,familyId:i},c=o&&o.refreshToken||this.getTokenKeys().refreshToken,h=[];c.forEach(g=>{if(this.refreshTokenKeyMatchesFilter(g,s)){const f=this.getRefreshTokenCredential(g,n);f&&this.credentialMatchesFilter(f,s)&&h.push(f)}});const d=h.length;return d<1?(this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."),null):(d>1&&r&&n&&r.addFields({multiMatchedRT:d},n),this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"),h[0])}refreshTokenKeyMatchesFilter(e,t){const n=e.toLowerCase();return!(t.familyId&&n.indexOf(t.familyId.toLowerCase())===-1||!t.familyId&&t.clientId&&n.indexOf(t.clientId.toLowerCase())===-1||t.homeAccountId&&n.indexOf(t.homeAccountId.toLowerCase())===-1)}readAppMetadataFromCache(e){const t={environment:e,clientId:this.clientId},n=this.getAppMetadataFilteredBy(t),o=Object.keys(n).map(i=>n[i]),r=o.length;if(r<1)return null;if(r>1)throw p(_o);return o[0]}isAppMetadataFOCI(e){const t=this.readAppMetadataFromCache(e);return!!(t&&t.familyId===Be)}matchHomeAccountId(e,t){return typeof e.homeAccountId=="string"&&t===e.homeAccountId}matchLocalAccountIdFromTokenClaims(e,t){const n=e.oid||e.sub;return t===n}matchLocalAccountIdFromTenantProfile(e,t){return e.localAccountId===t}matchName(e,t){var n;return t.toLowerCase()===((n=e.name)==null?void 0:n.toLowerCase())}matchUsername(e,t){return!!(e&&typeof e=="string"&&(t==null?void 0:t.toLowerCase())===e.toLowerCase())}matchUserAssertionHash(e,t){return!!(e.userAssertionHash&&t===e.userAssertionHash)}matchEnvironment(e,t){if(this.staticAuthorityOptions){const o=Fi(this.staticAuthorityOptions,this.commonLogger);if(o.includes(t)&&o.includes(e.environment))return!0}const n=this.getAuthorityMetadataByAlias(t);return!!(n&&n.aliases.indexOf(e.environment)>-1)}matchCredentialType(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()}matchClientId(e,t){return!!(e.clientId&&t===e.clientId)}matchFamilyId(e,t){return!!(e.familyId&&t===e.familyId)}matchRealm(e,t){var n;return((n=e.realm)==null?void 0:n.toLowerCase())===t.toLowerCase()}matchNativeAccountId(e,t){return!!(e.nativeAccountId&&t===e.nativeAccountId)}matchLoginHintFromTokenClaims(e,t){return e.login_hint===t||e.preferred_username===t||e.upn===t}matchSid(e,t){return e.sid===t}matchAuthorityType(e,t){return!!(e.authorityType&&t.toLowerCase()===e.authorityType.toLowerCase())}matchTarget(e,t){return e.credentialType!==I.ACCESS_TOKEN&&e.credentialType!==I.ACCESS_TOKEN_WITH_AUTH_SCHEME||!e.target?!1:O.fromString(e.target).containsScopeSet(t)}matchTokenType(e,t){return!!(e.tokenType&&e.tokenType===t)}matchKeyId(e,t){return!!(e.keyId&&e.keyId===t)}isAppMetadata(e){return e.indexOf(Qt)!==-1}isAuthorityMetadata(e){return e.indexOf(ot.CACHE_KEY)!==-1}generateAuthorityMetadataCacheKey(e){return`${ot.CACHE_KEY}-${this.clientId}-${e}`}static toObject(e,t){for(const n in t)e[n]=t[n];return e}}class qi extends Me{setAccount(){throw p(E)}getAccount(){throw p(E)}getCachedAccountEntity(){throw p(E)}setIdTokenCredential(){throw p(E)}getIdTokenCredential(){throw p(E)}setAccessTokenCredential(){throw p(E)}getAccessTokenCredential(){throw p(E)}setRefreshTokenCredential(){throw p(E)}getRefreshTokenCredential(){throw p(E)}setAppMetadata(){throw p(E)}getAppMetadata(){throw p(E)}setServerTelemetry(){throw p(E)}getServerTelemetry(){throw p(E)}setAuthorityMetadata(){throw p(E)}getAuthorityMetadata(){throw p(E)}getAuthorityMetadataKeys(){throw p(E)}setThrottlingCache(){throw p(E)}getThrottlingCache(){throw p(E)}removeItem(){throw p(E)}getKeys(){throw p(E)}getAccountKeys(){throw p(E)}getTokenKeys(){throw p(E)}updateCredentialCacheKey(){throw p(E)}removeOutdatedAccount(){throw p(E)}}/*! @azure/msal-common v14.16.1 2025-08-05 */const nr={tokenRenewalOffsetSeconds:li,preventCorsPreflight:!1},$i={loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:exports.LogLevel.Info,correlationId:u.EMPTY_STRING},zi={claimsBasedCachingEnabled:!1},Vi={async sendGetRequestAsync(){throw p(E)},async sendPostRequestAsync(){throw p(E)}},Qi={sku:u.SKU,version:tn,cpu:u.EMPTY_STRING,os:u.EMPTY_STRING},Yi={clientSecret:u.EMPTY_STRING,clientAssertion:void 0},Wi={azureCloudInstance:pt.None,tenant:`${u.DEFAULT_COMMON_TENANT}`},ji={application:{appName:"",appVersion:""}};function Ji({authOptions:a,systemOptions:e,loggerOptions:t,cacheOptions:n,storageInterface:o,networkInterface:r,cryptoInterface:i,clientCredentials:s,libraryInfo:c,telemetry:h,serverTelemetryManager:d,persistencePlugin:g,serializableCache:f}){const y={...$i,...t};return{authOptions:Xi(a),systemOptions:{...nr,...e},loggerOptions:y,cacheOptions:{...zi,...n},storageInterface:o||new qi(a.clientId,it,new he(y)),networkInterface:r||Vi,cryptoInterface:i||it,clientCredentials:s||Yi,libraryInfo:{...Qi,...c},telemetry:{...ji,...h},serverTelemetryManager:d||null,persistencePlugin:g||null,serializableCache:f||null}}function Xi(a){return{clientCapabilities:[],azureCloudOptions:Wi,skipAuthorityMetadataCache:!1,instanceAware:!1,...a}}function Kt(a){return a.authOptions.authority.options.protocolMode===te.OIDC}/*! @azure/msal-common v14.16.1 2025-08-05 */const z={HOME_ACCOUNT_ID:"home_account_id",UPN:"UPN"};/*! @azure/msal-common v14.16.1 2025-08-05 */const me="client_id",or="redirect_uri",Wn="response_type",Zi="response_mode",ea="grant_type",ta="claims",na="scope",oa="refresh_token",ra="state",ia="nonce",aa="prompt",sa="code",ca="code_challenge",ha="code_challenge_method",la="code_verifier",da="client-request-id",ua="x-client-SKU",ga="x-client-VER",pa="x-client-OS",ma="x-client-CPU",fa="x-client-current-telemetry",Ca="x-client-last-telemetry",ya="x-ms-lib-capability",Ta="x-app-name",Ia="x-app-ver",Aa="post_logout_redirect_uri",Ea="id_token_hint",va="device_code",Sa="client_secret",wa="client_assertion",_a="client_assertion_type",jn="token_type",Jn="req_cnf",ka="assertion",Ra="requested_token_use",Xn="return_spa_code",ba="nativebroker",Oa="logout_hint",Na="sid",Pa="login_hint",Ma="domain_hint",La="x-client-xtra-sku",un="brk_client_id",xt="brk_redirect_uri";/*! @azure/msal-common v14.16.1 2025-08-05 */class Se{static validateRedirectUri(e){if(!e)throw k(Do)}static validatePrompt(e){const t=[];for(const n in M)t.push(M[n]);if(t.indexOf(e)<0)throw k(Fo)}static validateClaims(e){try{JSON.parse(e)}catch{throw k(on)}}static validateCodeChallengeParams(e,t){if(!e||!t)throw k(rn);this.validateCodeChallengeMethod(t)}static validateCodeChallengeMethod(e){if([Dn.PLAIN,Dn.S256].indexOf(e)<0)throw k($o)}}/*! @azure/msal-common v14.16.1 2025-08-05 */function Ua(a,e,t){if(!e)return;const n=a.get(me);n&&a.has(un)&&(t==null||t.addFields({embeddedClientId:n,embeddedRedirectUri:a.get(or)},e))}class Ge{constructor(e,t){this.parameters=new Map,this.performanceClient=t,this.correlationId=e}addResponseTypeCode(){this.parameters.set(Wn,encodeURIComponent(u.CODE_RESPONSE_TYPE))}addResponseTypeForTokenAndIdToken(){this.parameters.set(Wn,encodeURIComponent(`${u.TOKEN_RESPONSE_TYPE} ${u.ID_TOKEN_RESPONSE_TYPE}`))}addResponseMode(e){this.parameters.set(Zi,encodeURIComponent(e||ci.QUERY))}addNativeBroker(){this.parameters.set(ba,encodeURIComponent("1"))}addScopes(e,t=!0,n=Ie){t&&!n.includes("openid")&&!e.includes("openid")&&n.push("openid");const o=t?[...e||[],...n]:e||[],r=new O(o);this.parameters.set(na,encodeURIComponent(r.printScopes()))}addClientId(e){this.parameters.set(me,encodeURIComponent(e))}addRedirectUri(e){Se.validateRedirectUri(e),this.parameters.set(or,encodeURIComponent(e))}addPostLogoutRedirectUri(e){Se.validateRedirectUri(e),this.parameters.set(Aa,encodeURIComponent(e))}addIdTokenHint(e){this.parameters.set(Ea,encodeURIComponent(e))}addDomainHint(e){this.parameters.set(Ma,encodeURIComponent(e))}addLoginHint(e){this.parameters.set(Pa,encodeURIComponent(e))}addCcsUpn(e){this.parameters.set(x.CCS_HEADER,encodeURIComponent(`UPN:${e}`))}addCcsOid(e){this.parameters.set(x.CCS_HEADER,encodeURIComponent(`Oid:${e.uid}@${e.utid}`))}addSid(e){this.parameters.set(Na,encodeURIComponent(e))}addClaims(e,t){const n=this.addClientCapabilitiesToClaims(e,t);Se.validateClaims(n),this.parameters.set(ta,encodeURIComponent(n))}addCorrelationId(e){this.parameters.set(da,encodeURIComponent(e))}addLibraryInfo(e){this.parameters.set(ua,e.sku),this.parameters.set(ga,e.version),e.os&&this.parameters.set(pa,e.os),e.cpu&&this.parameters.set(ma,e.cpu)}addApplicationTelemetry(e){e!=null&&e.appName&&this.parameters.set(Ta,e.appName),e!=null&&e.appVersion&&this.parameters.set(Ia,e.appVersion)}addPrompt(e){Se.validatePrompt(e),this.parameters.set(`${aa}`,encodeURIComponent(e))}addState(e){e&&this.parameters.set(ra,encodeURIComponent(e))}addNonce(e){this.parameters.set(ia,encodeURIComponent(e))}addCodeChallengeParams(e,t){if(Se.validateCodeChallengeParams(e,t),e&&t)this.parameters.set(ca,encodeURIComponent(e)),this.parameters.set(ha,encodeURIComponent(t));else throw k(rn)}addAuthorizationCode(e){this.parameters.set(sa,encodeURIComponent(e))}addDeviceCode(e){this.parameters.set(va,encodeURIComponent(e))}addRefreshToken(e){this.parameters.set(oa,encodeURIComponent(e))}addCodeVerifier(e){this.parameters.set(la,encodeURIComponent(e))}addClientSecret(e){this.parameters.set(Sa,encodeURIComponent(e))}addClientAssertion(e){e&&this.parameters.set(wa,encodeURIComponent(e))}addClientAssertionType(e){e&&this.parameters.set(_a,encodeURIComponent(e))}addOboAssertion(e){this.parameters.set(ka,encodeURIComponent(e))}addRequestTokenUse(e){this.parameters.set(Ra,encodeURIComponent(e))}addGrantType(e){this.parameters.set(ea,encodeURIComponent(e))}addClientInfo(){this.parameters.set(hi,"1")}addExtraQueryParameters(e){Object.entries(e).forEach(([t,n])=>{!this.parameters.has(t)&&n&&this.parameters.set(t,n)})}addClientCapabilitiesToClaims(e,t){let n;if(!e)n={};else try{n=JSON.parse(e)}catch{throw k(on)}return t&&t.length>0&&(n.hasOwnProperty(je.ACCESS_TOKEN)||(n[je.ACCESS_TOKEN]={}),n[je.ACCESS_TOKEN][je.XMS_CC]={values:t}),JSON.stringify(n)}addUsername(e){this.parameters.set(xn.username,encodeURIComponent(e))}addPassword(e){this.parameters.set(xn.password,encodeURIComponent(e))}addPopToken(e){e&&(this.parameters.set(jn,_.POP),this.parameters.set(Jn,encodeURIComponent(e)))}addSshJwk(e){e&&(this.parameters.set(jn,_.SSH),this.parameters.set(Jn,encodeURIComponent(e)))}addServerTelemetry(e){this.parameters.set(fa,e.generateCurrentRequestHeaderValue()),this.parameters.set(Ca,e.generateLastRequestHeaderValue())}addThrottling(){this.parameters.set(ya,Fe.X_MS_LIB_CAPABILITY_VALUE)}addLogoutHint(e){this.parameters.set(Oa,encodeURIComponent(e))}addBrokerParameters(e){const t={};t[un]=e.brokerClientId,t[xt]=e.brokerRedirectUri,this.addExtraQueryParameters(t)}createQueryString(){const e=new Array;return this.parameters.forEach((t,n)=>{e.push(`${n}=${t}`)}),Ua(this.parameters,this.correlationId,this.performanceClient),e.join("&")}}/*! @azure/msal-common v14.16.1 2025-08-05 */function Ha(a){return a.hasOwnProperty("authorization_endpoint")&&a.hasOwnProperty("token_endpoint")&&a.hasOwnProperty("issuer")&&a.hasOwnProperty("jwks_uri")}/*! @azure/msal-common v14.16.1 2025-08-05 */function Da(a){return a.hasOwnProperty("tenant_discovery_endpoint")&&a.hasOwnProperty("metadata")}/*! @azure/msal-common v14.16.1 2025-08-05 */function Ka(a){return a.hasOwnProperty("error")&&a.hasOwnProperty("error_description")}/*! @azure/msal-common v14.16.1 2025-08-05 */const l={AcquireTokenByCode:"acquireTokenByCode",AcquireTokenByRefreshToken:"acquireTokenByRefreshToken",AcquireTokenSilent:"acquireTokenSilent",AcquireTokenSilentAsync:"acquireTokenSilentAsync",AcquireTokenPopup:"acquireTokenPopup",AcquireTokenPreRedirect:"acquireTokenPreRedirect",AcquireTokenRedirect:"acquireTokenRedirect",CryptoOptsGetPublicKeyThumbprint:"cryptoOptsGetPublicKeyThumbprint",CryptoOptsSignJwt:"cryptoOptsSignJwt",SilentCacheClientAcquireToken:"silentCacheClientAcquireToken",SilentIframeClientAcquireToken:"silentIframeClientAcquireToken",AwaitConcurrentIframe:"awaitConcurrentIframe",SilentRefreshClientAcquireToken:"silentRefreshClientAcquireToken",SsoSilent:"ssoSilent",StandardInteractionClientGetDiscoveredAuthority:"standardInteractionClientGetDiscoveredAuthority",FetchAccountIdWithNativeBroker:"fetchAccountIdWithNativeBroker",NativeInteractionClientAcquireToken:"nativeInteractionClientAcquireToken",BaseClientCreateTokenRequestHeaders:"baseClientCreateTokenRequestHeaders",NetworkClientSendPostRequestAsync:"networkClientSendPostRequestAsync",RefreshTokenClientExecutePostToTokenEndpoint:"refreshTokenClientExecutePostToTokenEndpoint",AuthorizationCodeClientExecutePostToTokenEndpoint:"authorizationCodeClientExecutePostToTokenEndpoint",BrokerHandhshake:"brokerHandshake",AcquireTokenByRefreshTokenInBroker:"acquireTokenByRefreshTokenInBroker",AcquireTokenByBroker:"acquireTokenByBroker",RefreshTokenClientExecuteTokenRequest:"refreshTokenClientExecuteTokenRequest",RefreshTokenClientAcquireToken:"refreshTokenClientAcquireToken",RefreshTokenClientAcquireTokenWithCachedRefreshToken:"refreshTokenClientAcquireTokenWithCachedRefreshToken",RefreshTokenClientAcquireTokenByRefreshToken:"refreshTokenClientAcquireTokenByRefreshToken",RefreshTokenClientCreateTokenRequestBody:"refreshTokenClientCreateTokenRequestBody",AcquireTokenFromCache:"acquireTokenFromCache",SilentFlowClientAcquireCachedToken:"silentFlowClientAcquireCachedToken",SilentFlowClientGenerateResultFromCacheRecord:"silentFlowClientGenerateResultFromCacheRecord",AcquireTokenBySilentIframe:"acquireTokenBySilentIframe",InitializeBaseRequest:"initializeBaseRequest",InitializeSilentRequest:"initializeSilentRequest",InitializeClientApplication:"initializeClientApplication",SilentIframeClientTokenHelper:"silentIframeClientTokenHelper",SilentHandlerInitiateAuthRequest:"silentHandlerInitiateAuthRequest",SilentHandlerMonitorIframeForHash:"silentHandlerMonitorIframeForHash",SilentHandlerLoadFrame:"silentHandlerLoadFrame",SilentHandlerLoadFrameSync:"silentHandlerLoadFrameSync",StandardInteractionClientCreateAuthCodeClient:"standardInteractionClientCreateAuthCodeClient",StandardInteractionClientGetClientConfiguration:"standardInteractionClientGetClientConfiguration",StandardInteractionClientInitializeAuthorizationRequest:"standardInteractionClientInitializeAuthorizationRequest",StandardInteractionClientInitializeAuthorizationCodeRequest:"standardInteractionClientInitializeAuthorizationCodeRequest",GetAuthCodeUrl:"getAuthCodeUrl",HandleCodeResponseFromServer:"handleCodeResponseFromServer",HandleCodeResponse:"handleCodeResponse",UpdateTokenEndpointAuthority:"updateTokenEndpointAuthority",AuthClientAcquireToken:"authClientAcquireToken",AuthClientExecuteTokenRequest:"authClientExecuteTokenRequest",AuthClientCreateTokenRequestBody:"authClientCreateTokenRequestBody",AuthClientCreateQueryString:"authClientCreateQueryString",PopTokenGenerateCnf:"popTokenGenerateCnf",PopTokenGenerateKid:"popTokenGenerateKid",HandleServerTokenResponse:"handleServerTokenResponse",DeserializeResponse:"deserializeResponse",AuthorityFactoryCreateDiscoveredInstance:"authorityFactoryCreateDiscoveredInstance",AuthorityResolveEndpointsAsync:"authorityResolveEndpointsAsync",AuthorityResolveEndpointsFromLocalSources:"authorityResolveEndpointsFromLocalSources",AuthorityGetCloudDiscoveryMetadataFromNetwork:"authorityGetCloudDiscoveryMetadataFromNetwork",AuthorityUpdateCloudDiscoveryMetadata:"authorityUpdateCloudDiscoveryMetadata",AuthorityGetEndpointMetadataFromNetwork:"authorityGetEndpointMetadataFromNetwork",AuthorityUpdateEndpointMetadata:"authorityUpdateEndpointMetadata",AuthorityUpdateMetadataWithRegionalInformation:"authorityUpdateMetadataWithRegionalInformation",RegionDiscoveryDetectRegion:"regionDiscoveryDetectRegion",RegionDiscoveryGetRegionFromIMDS:"regionDiscoveryGetRegionFromIMDS",RegionDiscoveryGetCurrentVersion:"regionDiscoveryGetCurrentVersion",AcquireTokenByCodeAsync:"acquireTokenByCodeAsync",GetEndpointMetadataFromNetwork:"getEndpointMetadataFromNetwork",GetCloudDiscoveryMetadataFromNetworkMeasurement:"getCloudDiscoveryMetadataFromNetworkMeasurement",HandleRedirectPromiseMeasurement:"handleRedirectPromise",HandleNativeRedirectPromiseMeasurement:"handleNativeRedirectPromise",UpdateCloudDiscoveryMetadataMeasurement:"updateCloudDiscoveryMetadataMeasurement",UsernamePasswordClientAcquireToken:"usernamePasswordClientAcquireToken",NativeMessageHandlerHandshake:"nativeMessageHandlerHandshake",NativeGenerateAuthResult:"nativeGenerateAuthResult",RemoveHiddenIframe:"removeHiddenIframe",ClearTokensAndKeysWithClaims:"clearTokensAndKeysWithClaims",CacheManagerGetRefreshToken:"cacheManagerGetRefreshToken",GeneratePkceCodes:"generatePkceCodes",GenerateCodeVerifier:"generateCodeVerifier",GenerateCodeChallengeFromVerifier:"generateCodeChallengeFromVerifier",Sha256Digest:"sha256Digest",GetRandomValues:"getRandomValues"},xa={InProgress:1};/*! @azure/msal-common v14.16.1 2025-08-05 */const Ee=(a,e,t,n,o)=>(...r)=>{t.trace(`Executing function ${e}`);const i=n==null?void 0:n.startMeasurement(e,o);if(o){const s=e+"CallCount";n==null||n.incrementFields({[s]:1},o)}try{const s=a(...r);return i==null||i.end({success:!0}),t.trace(`Returning result from ${e}`),s}catch(s){t.trace(`Error occurred in ${e}`);try{t.trace(JSON.stringify(s))}catch{t.trace("Unable to print error message.")}throw i==null||i.end({success:!1},s),s}},m=(a,e,t,n,o)=>(...r)=>{t.trace(`Executing function ${e}`);const i=n==null?void 0:n.startMeasurement(e,o);if(o){const s=e+"CallCount";n==null||n.incrementFields({[s]:1},o)}return n==null||n.setPreQueueTime(e,o),a(...r).then(s=>(t.trace(`Returning result from ${e}`),i==null||i.end({success:!0}),s)).catch(s=>{t.trace(`Error occurred in ${e}`);try{t.trace(JSON.stringify(s))}catch{t.trace("Unable to print error message.")}throw i==null||i.end({success:!1},s),s})};/*! @azure/msal-common v14.16.1 2025-08-05 */class Tt{constructor(e,t,n,o){this.networkInterface=e,this.logger=t,this.performanceClient=n,this.correlationId=o}async detectRegion(e,t){var o;(o=this.performanceClient)==null||o.addQueueMeasurement(l.RegionDiscoveryDetectRegion,this.correlationId);let n=e;if(n)t.region_source=ve.ENVIRONMENT_VARIABLE;else{const r=Tt.IMDS_OPTIONS;try{const i=await m(this.getRegionFromIMDS.bind(this),l.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(u.IMDS_VERSION,r);if(i.status===Xe.httpSuccess&&(n=i.body,t.region_source=ve.IMDS),i.status===Xe.httpBadRequest){const s=await m(this.getCurrentVersion.bind(this),l.RegionDiscoveryGetCurrentVersion,this.logger,this.performanceClient,this.correlationId)(r);if(!s)return t.region_source=ve.FAILED_AUTO_DETECTION,null;const c=await m(this.getRegionFromIMDS.bind(this),l.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(s,r);c.status===Xe.httpSuccess&&(n=c.body,t.region_source=ve.IMDS)}}catch{return t.region_source=ve.FAILED_AUTO_DETECTION,null}}return n||(t.region_source=ve.FAILED_AUTO_DETECTION),n||null}async getRegionFromIMDS(e,t){var n;return(n=this.performanceClient)==null||n.addQueueMeasurement(l.RegionDiscoveryGetRegionFromIMDS,this.correlationId),this.networkInterface.sendGetRequestAsync(`${u.IMDS_ENDPOINT}?api-version=${e}&format=text`,t,u.IMDS_TIMEOUT)}async getCurrentVersion(e){var t;(t=this.performanceClient)==null||t.addQueueMeasurement(l.RegionDiscoveryGetCurrentVersion,this.correlationId);try{const n=await this.networkInterface.sendGetRequestAsync(`${u.IMDS_ENDPOINT}?format=json`,e);return n.status===Xe.httpBadRequest&&n.body&&n.body["newest-versions"]&&n.body["newest-versions"].length>0?n.body["newest-versions"][0]:null}catch{return null}}}Tt.IMDS_OPTIONS={headers:{Metadata:"true"}};/*! @azure/msal-common v14.16.1 2025-08-05 */class D{constructor(e,t,n,o,r,i,s,c){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=n,this.authorityOptions=o,this.regionDiscoveryMetadata={region_used:void 0,region_source:void 0,region_outcome:void 0},this.logger=r,this.performanceClient=s,this.correlationId=i,this.managedIdentity=c||!1,this.regionDiscovery=new Tt(t,this.logger,this.performanceClient,this.correlationId)}getAuthorityType(e){if(e.HostNameAndPort.endsWith(u.CIAM_AUTH_URL))return W.Ciam;const t=e.PathSegments;if(t.length)switch(t[0].toLowerCase()){case u.ADFS:return W.Adfs;case u.DSTS:return W.Dsts}return W.Default}get authorityType(){return this.getAuthorityType(this.canonicalAuthorityUrlComponents)}get protocolMode(){return this.authorityOptions.protocolMode}get options(){return this.authorityOptions}get canonicalAuthority(){return this._canonicalAuthority.urlString}set canonicalAuthority(e){this._canonicalAuthority=new S(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null}get canonicalAuthorityUrlComponents(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents}get hostnameAndPort(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()}get tenant(){return this.canonicalAuthorityUrlComponents.PathSegments[0]}get authorizationEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.authorization_endpoint);throw p(ae)}get tokenEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint);throw p(ae)}get deviceCodeEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));throw p(ae)}get endSessionEndpoint(){if(this.discoveryComplete()){if(!this.metadata.end_session_endpoint)throw p(Mo);return this.replacePath(this.metadata.end_session_endpoint)}else throw p(ae)}get selfSignedJwtAudience(){if(this.discoveryComplete())return this.replacePath(this.metadata.issuer);throw p(ae)}get jwksUri(){if(this.discoveryComplete())return this.replacePath(this.metadata.jwks_uri);throw p(ae)}canReplaceTenant(e){return e.PathSegments.length===1&&!D.reservedTenantDomains.has(e.PathSegments[0])&&this.getAuthorityType(e)===W.Default&&this.protocolMode===te.AAD}replaceTenant(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)}replacePath(e){let t=e;const o=new S(this.metadata.canonical_authority).getUrlComponents(),r=o.PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach((s,c)=>{let h=r[c];if(c===0&&this.canReplaceTenant(o)){const d=new S(this.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];h!==d&&(this.logger.verbose(`Replacing tenant domain name ${h} with id ${d}`),h=d)}s!==h&&(t=t.replace(`/${h}/`,`/${s}/`))}),this.replaceTenant(t)}get defaultOpenIdConfigurationEndpoint(){const e=this.hostnameAndPort;return this.canonicalAuthority.endsWith("v2.0/")||this.authorityType===W.Adfs||this.protocolMode!==te.AAD&&!this.isAliasOfKnownMicrosoftAuthority(e)?`${this.canonicalAuthority}.well-known/openid-configuration`:`${this.canonicalAuthority}v2.0/.well-known/openid-configuration`}discoveryComplete(){return!!this.metadata}async resolveEndpointsAsync(){var o,r;(o=this.performanceClient)==null||o.addQueueMeasurement(l.AuthorityResolveEndpointsAsync,this.correlationId);const e=this.getCurrentMetadataEntity(),t=await m(this.updateCloudDiscoveryMetadata.bind(this),l.AuthorityUpdateCloudDiscoveryMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,e.preferred_network);const n=await m(this.updateEndpointMetadata.bind(this),l.AuthorityUpdateEndpointMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.updateCachedMetadata(e,t,{source:n}),(r=this.performanceClient)==null||r.addFields({cloudDiscoverySource:t,authorityEndpointSource:n},this.correlationId)}getCurrentMetadataEntity(){let e=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);return e||(e={aliases:[],preferred_cache:this.hostnameAndPort,preferred_network:this.hostnameAndPort,canonical_authority:this.canonicalAuthority,authorization_endpoint:"",token_endpoint:"",end_session_endpoint:"",issuer:"",aliasesFromNetwork:!1,endpointsFromNetwork:!1,expiresAt:zn(),jwks_uri:""}),e}updateCachedMetadata(e,t,n){t!==q.CACHE&&(n==null?void 0:n.source)!==q.CACHE&&(e.expiresAt=zn(),e.canonical_authority=this.canonicalAuthority);const o=this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache);this.cacheManager.setAuthorityMetadata(o,e),this.metadata=e}async updateEndpointMetadata(e){var o,r,i;(o=this.performanceClient)==null||o.addQueueMeasurement(l.AuthorityUpdateEndpointMetadata,this.correlationId);const t=this.updateEndpointMetadataFromLocalSources(e);if(t){if(t.source===q.HARDCODED_VALUES&&(r=this.authorityOptions.azureRegionConfiguration)!=null&&r.azureRegion&&t.metadata){const s=await m(this.updateMetadataWithRegionalInformation.bind(this),l.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(t.metadata);Ze(e,s,!1),e.canonical_authority=this.canonicalAuthority}return t.source}let n=await m(this.getEndpointMetadataFromNetwork.bind(this),l.AuthorityGetEndpointMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(n)return(i=this.authorityOptions.azureRegionConfiguration)!=null&&i.azureRegion&&(n=await m(this.updateMetadataWithRegionalInformation.bind(this),l.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(n)),Ze(e,n,!0),q.NETWORK;throw p(Ao,this.defaultOpenIdConfigurationEndpoint)}updateEndpointMetadataFromLocalSources(e){this.logger.verbose("Attempting to get endpoint metadata from authority configuration");const t=this.getEndpointMetadataFromConfig();if(t)return this.logger.verbose("Found endpoint metadata in authority configuration"),Ze(e,t,!1),{source:q.CONFIG};if(this.logger.verbose("Did not find endpoint metadata in the config... Attempting to get endpoint metadata from the hardcoded values."),this.authorityOptions.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get endpoint metadata from the network metadata cache.");else{const o=this.getEndpointMetadataFromHardcodedValues();if(o)return Ze(e,o,!1),{source:q.HARDCODED_VALUES,metadata:o};this.logger.verbose("Did not find endpoint metadata in hardcoded values... Attempting to get endpoint metadata from the network metadata cache.")}const n=Vn(e);return this.isAuthoritySameType(e)&&e.endpointsFromNetwork&&!n?(this.logger.verbose("Found endpoint metadata in the cache."),{source:q.CACHE}):(n&&this.logger.verbose("The metadata entity is expired."),null)}isAuthoritySameType(e){return new S(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length}getEndpointMetadataFromConfig(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch{throw k(zo)}return null}async getEndpointMetadataFromNetwork(){var n;(n=this.performanceClient)==null||n.addQueueMeasurement(l.AuthorityGetEndpointMetadataFromNetwork,this.correlationId);const e={},t=this.defaultOpenIdConfigurationEndpoint;this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: attempting to retrieve OAuth endpoints from ${t}`);try{const o=await this.networkInterface.sendGetRequestAsync(t,e);return Ha(o.body)?o.body:(this.logger.verbose("Authority.getEndpointMetadataFromNetwork: could not parse response as OpenID configuration"),null)}catch(o){return this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: ${o}`),null}}getEndpointMetadataFromHardcodedValues(){return this.hostnameAndPort in Qn?Qn[this.hostnameAndPort]:null}async updateMetadataWithRegionalInformation(e){var n,o,r;(n=this.performanceClient)==null||n.addQueueMeasurement(l.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId);const t=(o=this.authorityOptions.azureRegionConfiguration)==null?void 0:o.azureRegion;if(t){if(t!==u.AZURE_REGION_AUTO_DISCOVER_FLAG)return this.regionDiscoveryMetadata.region_outcome=Rt.CONFIGURED_NO_AUTO_DETECTION,this.regionDiscoveryMetadata.region_used=t,D.replaceWithRegionalInformation(e,t);const i=await m(this.regionDiscovery.detectRegion.bind(this.regionDiscovery),l.RegionDiscoveryDetectRegion,this.logger,this.performanceClient,this.correlationId)((r=this.authorityOptions.azureRegionConfiguration)==null?void 0:r.environmentRegion,this.regionDiscoveryMetadata);if(i)return this.regionDiscoveryMetadata.region_outcome=Rt.AUTO_DETECTION_REQUESTED_SUCCESSFUL,this.regionDiscoveryMetadata.region_used=i,D.replaceWithRegionalInformation(e,i);this.regionDiscoveryMetadata.region_outcome=Rt.AUTO_DETECTION_REQUESTED_FAILED}return e}async updateCloudDiscoveryMetadata(e){var o;(o=this.performanceClient)==null||o.addQueueMeasurement(l.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId);const t=this.updateCloudDiscoveryMetadataFromLocalSources(e);if(t)return t;const n=await m(this.getCloudDiscoveryMetadataFromNetwork.bind(this),l.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(n)return bt(e,n,!0),q.NETWORK;throw k(Vo)}updateCloudDiscoveryMetadataFromLocalSources(e){this.logger.verbose("Attempting to get cloud discovery metadata  from authority configuration"),this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities||u.NOT_APPLICABLE}`),this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata||u.NOT_APPLICABLE}`),this.logger.verbosePii(`Canonical Authority: ${e.canonical_authority||u.NOT_APPLICABLE}`);const t=this.getCloudDiscoveryMetadataFromConfig();if(t)return this.logger.verbose("Found cloud discovery metadata in authority configuration"),bt(e,t,!1),q.CONFIG;if(this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the hardcoded values."),this.options.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded cloud discovery metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get cloud discovery metadata from the network metadata cache.");else{const o=Gi(this.hostnameAndPort);if(o)return this.logger.verbose("Found cloud discovery metadata from hardcoded values."),bt(e,o,!1),q.HARDCODED_VALUES;this.logger.verbose("Did not find cloud discovery metadata in hardcoded values... Attempting to get cloud discovery metadata from the network metadata cache.")}const n=Vn(e);return this.isAuthoritySameType(e)&&e.aliasesFromNetwork&&!n?(this.logger.verbose("Found cloud discovery metadata in the cache."),q.CACHE):(n&&this.logger.verbose("The metadata entity is expired."),null)}getCloudDiscoveryMetadataFromConfig(){if(this.authorityType===W.Ciam)return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."),D.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);if(this.authorityOptions.cloudDiscoveryMetadata){this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");try{this.logger.verbose("Attempting to parse the cloud discovery metadata.");const e=JSON.parse(this.authorityOptions.cloudDiscoveryMetadata),t=ht(e.metadata,this.hostnameAndPort);if(this.logger.verbose("Parsed the cloud discovery metadata."),t)return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."),t;this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.")}catch{throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."),k(an)}}return this.isInKnownAuthorities()?(this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."),D.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)):null}async getCloudDiscoveryMetadataFromNetwork(){var o;(o=this.performanceClient)==null||o.addQueueMeasurement(l.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId);const e=`${u.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`,t={};let n=null;try{const r=await this.networkInterface.sendGetRequestAsync(e,t);let i,s;if(Da(r.body))i=r.body,s=i.metadata,this.logger.verbosePii(`tenant_discovery_endpoint is: ${i.tenant_discovery_endpoint}`);else if(Ka(r.body)){if(this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${r.status}`),i=r.body,i.error===u.INVALID_INSTANCE)return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."),null;this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${i.error}`),this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${i.error_description}`),this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"),s=[]}else return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"),null;this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."),n=ht(s,this.hostnameAndPort)}catch(r){if(r instanceof b)this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.
Error: ${r.errorCode}
Error Description: ${r.errorMessage}`);else{const i=r;this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.
Error: ${i.name}
Error Description: ${i.message}`)}return null}return n||(this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."),this.logger.verbose("Creating custom Authority for custom domain scenario."),n=D.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),n}isInKnownAuthorities(){return this.authorityOptions.knownAuthorities.filter(t=>t&&S.getDomainFromUrl(t).toLowerCase()===this.hostnameAndPort).length>0}static generateAuthority(e,t){let n;if(t&&t.azureCloudInstance!==pt.None){const o=t.tenant?t.tenant:u.DEFAULT_COMMON_TENANT;n=`${t.azureCloudInstance}/${o}/`}return n||e}static createCloudDiscoveryMetadataFromHost(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}}getPreferredCache(){if(this.managedIdentity)return u.DEFAULT_AUTHORITY_HOST;if(this.discoveryComplete())return this.metadata.preferred_cache;throw p(ae)}isAlias(e){return this.metadata.aliases.indexOf(e)>-1}isAliasOfKnownMicrosoftAuthority(e){return er.has(e)}static isPublicCloudAuthority(e){return u.KNOWN_PUBLIC_CLOUDS.indexOf(e)>=0}static buildRegionalAuthorityString(e,t,n){const o=new S(e);o.validateAsUri();const r=o.getUrlComponents();let i=`${t}.${r.HostNameAndPort}`;this.isPublicCloudAuthority(r.HostNameAndPort)&&(i=`${t}.${u.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`);const s=S.constructAuthorityUriFromObject({...o.getUrlComponents(),HostNameAndPort:i}).urlString;return n?`${s}?${n}`:s}static replaceWithRegionalInformation(e,t){const n={...e};return n.authorization_endpoint=D.buildRegionalAuthorityString(n.authorization_endpoint,t),n.token_endpoint=D.buildRegionalAuthorityString(n.token_endpoint,t),n.end_session_endpoint&&(n.end_session_endpoint=D.buildRegionalAuthorityString(n.end_session_endpoint,t)),n}static transformCIAMAuthority(e){let t=e;const o=new S(e).getUrlComponents();if(o.PathSegments.length===0&&o.HostNameAndPort.endsWith(u.CIAM_AUTH_URL)){const r=o.HostNameAndPort.split(".")[0];t=`${t}${r}${u.AAD_TENANT_DOMAIN_SUFFIX}`}return t}}D.reservedTenantDomains=new Set(["{tenant}","{tenantid}",ge.COMMON,ge.CONSUMERS,ge.ORGANIZATIONS]);function Ba(a){var o;const n=(o=new S(a).getUrlComponents().PathSegments.slice(-1)[0])==null?void 0:o.toLowerCase();switch(n){case ge.COMMON:case ge.ORGANIZATIONS:case ge.CONSUMERS:return;default:return n}}function rr(a){return a.endsWith(u.FORWARD_SLASH)?a:`${a}${u.FORWARD_SLASH}`}function Fa(a){const e=a.cloudDiscoveryMetadata;let t;if(e)try{t=JSON.parse(e)}catch{throw k(an)}return{canonicalAuthority:a.authority?rr(a.authority):void 0,knownAuthorities:a.knownAuthorities,cloudDiscoveryMetadata:t}}/*! @azure/msal-common v14.16.1 2025-08-05 */async function ir(a,e,t,n,o,r,i){i==null||i.addQueueMeasurement(l.AuthorityFactoryCreateDiscoveredInstance,r);const s=D.transformCIAMAuthority(rr(a)),c=new D(s,e,t,n,o,r,i);try{return await m(c.resolveEndpointsAsync.bind(c),l.AuthorityResolveEndpointsAsync,o,i,r)(),c}catch{throw p(ae)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class le extends b{constructor(e,t,n,o,r){super(e,t,n),this.name="ServerError",this.errorNo=o,this.status=r,Object.setPrototypeOf(this,le.prototype)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class X{static generateThrottlingStorageKey(e){return`${Fe.THROTTLING_PREFIX}.${JSON.stringify(e)}`}static preProcess(e,t,n){var i;const o=X.generateThrottlingStorageKey(t),r=e.getThrottlingCache(o);if(r){if(r.throttleTime<Date.now()){e.removeItem(o,n);return}throw new le(((i=r.errorCodes)==null?void 0:i.join(" "))||u.EMPTY_STRING,r.errorMessage,r.subError)}}static postProcess(e,t,n,o){if(X.checkResponseStatus(n)||X.checkResponseForRetryAfter(n)){const r={throttleTime:X.calculateThrottleTime(parseInt(n.headers[x.RETRY_AFTER])),error:n.body.error,errorCodes:n.body.error_codes,errorMessage:n.body.error_description,subError:n.body.suberror};e.setThrottlingCache(X.generateThrottlingStorageKey(t),r,o)}}static checkResponseStatus(e){return e.status===429||e.status>=500&&e.status<600}static checkResponseForRetryAfter(e){return e.headers?e.headers.hasOwnProperty(x.RETRY_AFTER)&&(e.status<200||e.status>=300):!1}static calculateThrottleTime(e){const t=e<=0?0:e,n=Date.now()/1e3;return Math.floor(Math.min(n+(t||Fe.DEFAULT_THROTTLE_TIME_SECONDS),n+Fe.DEFAULT_MAX_THROTTLE_TIME_SECONDS)*1e3)}static removeThrottle(e,t,n,o){const r={clientId:t,authority:n.authority,scopes:n.scopes,homeAccountIdentifier:o,claims:n.claims,authenticationScheme:n.authenticationScheme,resourceRequestMethod:n.resourceRequestMethod,resourceRequestUri:n.resourceRequestUri,shrClaims:n.shrClaims,sshKid:n.sshKid},i=this.generateThrottlingStorageKey(r);e.removeItem(i,n.correlationId)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class It extends b{constructor(e,t,n){super(e.errorCode,e.errorMessage,e.subError),Object.setPrototypeOf(this,It.prototype),this.name="NetworkError",this.error=e,this.httpStatus=t,this.responseHeaders=n}}function Zn(a,e,t){return new It(a,e,t)}/*! @azure/msal-common v14.16.1 2025-08-05 */class gn{constructor(e,t){this.config=Ji(e),this.logger=new he(this.config.loggerOptions,Lo,tn),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority,this.performanceClient=t}createTokenRequestHeaders(e){const t={};if(t[x.CONTENT_TYPE]=u.URL_FORM_CONTENT_TYPE,!this.config.systemOptions.preventCorsPreflight&&e)switch(e.type){case z.HOME_ACCOUNT_ID:try{const n=Re(e.credential);t[x.CCS_HEADER]=`Oid:${n.uid}@${n.utid}`}catch(n){this.logger.verbose("Could not parse home account ID for CCS Header: "+n)}break;case z.UPN:t[x.CCS_HEADER]=`UPN: ${e.credential}`;break}return t}async executePostToTokenEndpoint(e,t,n,o,r,i){var c;i&&((c=this.performanceClient)==null||c.addQueueMeasurement(i,r));const s=await this.sendPostRequest(o,e,{body:t,headers:n},r);return this.config.serverTelemetryManager&&s.status<500&&s.status!==429&&this.config.serverTelemetryManager.clearTelemetryCache(),s}async sendPostRequest(e,t,n,o){var i,s,c;X.preProcess(this.cacheManager,e,o);let r;try{r=await m(this.networkClient.sendPostRequestAsync.bind(this.networkClient),l.NetworkClientSendPostRequestAsync,this.logger,this.performanceClient,o)(t,n);const h=r.headers||{};(s=this.performanceClient)==null||s.addFields({refreshTokenSize:((i=r.body.refresh_token)==null?void 0:i.length)||0,httpVerToken:h[x.X_MS_HTTP_VERSION]||"",requestId:h[x.X_MS_REQUEST_ID]||""},o)}catch(h){if(h instanceof It){const d=h.responseHeaders;throw d&&((c=this.performanceClient)==null||c.addFields({httpVerToken:d[x.X_MS_HTTP_VERSION]||"",requestId:d[x.X_MS_REQUEST_ID]||"",contentTypeHeader:d[x.CONTENT_TYPE]||void 0,contentLengthHeader:d[x.CONTENT_LENGTH]||void 0,httpStatus:h.httpStatus},o)),h.error}throw h instanceof b?h:p(Io)}return X.postProcess(this.cacheManager,e,r,o),r}async updateAuthority(e,t){var r;(r=this.performanceClient)==null||r.addQueueMeasurement(l.UpdateTokenEndpointAuthority,t);const n=`https://${e}/${this.authority.tenant}/`,o=await ir(n,this.networkClient,this.cacheManager,this.authority.options,this.logger,t,this.performanceClient);this.authority=o}createTokenQueryParameters(e){const t=new Ge(e.correlationId,this.performanceClient);return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.addCorrelationId(e.correlationId),t.createQueryString()}}/*! @azure/msal-common v14.16.1 2025-08-05 */const lt="no_tokens_found",ar="native_account_unavailable",pn="refresh_token_expired",Ga="interaction_required",qa="consent_required",$a="login_required",At="bad_token";/*! @azure/msal-common v14.16.1 2025-08-05 */const eo=[Ga,qa,$a,At],za=["message_only","additional_action","basic_action","user_password_expired","consent_required","bad_token"],Va={[lt]:"No refresh token found in the cache. Please sign-in.",[ar]:"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.",[pn]:"Refresh token has expired.",[At]:"Identity provider returned bad_token due to an expired or invalid refresh token. Please invoke an interactive API to resolve."};class Y extends b{constructor(e,t,n,o,r,i,s,c){super(e,t,n),Object.setPrototypeOf(this,Y.prototype),this.timestamp=o||u.EMPTY_STRING,this.traceId=r||u.EMPTY_STRING,this.correlationId=i||u.EMPTY_STRING,this.claims=s||u.EMPTY_STRING,this.name="InteractionRequiredAuthError",this.errorNo=c}}function to(a,e,t){const n=!!a&&eo.indexOf(a)>-1,o=!!t&&za.indexOf(t)>-1,r=!!e&&eo.some(i=>e.indexOf(i)>-1);return n||r||o}function Bt(a){return new Y(a,Va[a])}/*! @azure/msal-common v14.16.1 2025-08-05 */class j{static setRequestState(e,t,n){const o=j.generateLibraryState(e,n);return t?`${o}${u.RESOURCE_DELIM}${t}`:o}static generateLibraryState(e,t){if(!e)throw p(Ut);const n={id:e.createNewGuid()};t&&(n.meta=t);const o=JSON.stringify(n);return e.base64Encode(o)}static parseRequestState(e,t){if(!e)throw p(Ut);if(!t)throw p(Pe);try{const n=t.split(u.RESOURCE_DELIM),o=n[0],r=n.length>1?n.slice(1).join(u.RESOURCE_DELIM):u.EMPTY_STRING,i=e.base64Decode(o),s=JSON.parse(i);return{userRequestState:r||u.EMPTY_STRING,libraryState:s}}catch{throw p(Pe)}}}/*! @azure/msal-common v14.16.1 2025-08-05 */const Qa={SW:"sw"};class Le{constructor(e,t){this.cryptoUtils=e,this.performanceClient=t}async generateCnf(e,t){var r;(r=this.performanceClient)==null||r.addQueueMeasurement(l.PopTokenGenerateCnf,e.correlationId);const n=await m(this.generateKid.bind(this),l.PopTokenGenerateCnf,t,this.performanceClient,e.correlationId)(e),o=this.cryptoUtils.base64UrlEncode(JSON.stringify(n));return{kid:n.kid,reqCnfString:o}}async generateKid(e){var n;return(n=this.performanceClient)==null||n.addQueueMeasurement(l.PopTokenGenerateKid,e.correlationId),{kid:await this.cryptoUtils.getPublicKeyThumbprint(e),xms_ksl:Qa.SW}}async signPopToken(e,t,n){return this.signPayload(e,t,n)}async signPayload(e,t,n,o){const{resourceRequestMethod:r,resourceRequestUri:i,shrClaims:s,shrNonce:c,shrOptions:h}=n,d=i?new S(i):void 0,g=d==null?void 0:d.getUrlComponents();return this.cryptoUtils.signJwt({at:e,ts:oe(),m:r==null?void 0:r.toUpperCase(),u:g==null?void 0:g.HostNameAndPort,nonce:c||this.cryptoUtils.createNewGuid(),p:g==null?void 0:g.AbsolutePath,q:g!=null&&g.QueryString?[[],g.QueryString]:void 0,client_claims:s||void 0,...o},t,h,n.correlationId)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class Ya{constructor(e,t){this.cache=e,this.hasChanged=t}get cacheHasChanged(){return this.hasChanged}get tokenCache(){return this.cache}}/*! @azure/msal-common v14.16.1 2025-08-05 */function Wa(a){var n,o;const e="code=",t=(n=a.error_uri)==null?void 0:n.lastIndexOf(e);return t&&t>=0?(o=a.error_uri)==null?void 0:o.substring(t+e.length):void 0}class fe{constructor(e,t,n,o,r,i,s){this.clientId=e,this.cacheStorage=t,this.cryptoObj=n,this.logger=o,this.serializableCache=r,this.persistencePlugin=i,this.performanceClient=s}validateServerAuthorizationCodeResponse(e,t){if(!e.state||!t)throw e.state?p(rt,"Cached State"):p(rt,"Server State");let n,o;try{n=decodeURIComponent(e.state)}catch{throw p(Pe,e.state)}try{o=decodeURIComponent(t)}catch{throw p(Pe,e.state)}if(n!==o)throw p(vo);if(e.error||e.error_description||e.suberror){const r=Wa(e);throw to(e.error,e.error_description,e.suberror)?new Y(e.error||"",e.error_description,e.suberror,e.timestamp||"",e.trace_id||"",e.correlation_id||"",e.claims||"",r):new le(e.error||"",e.error_description,e.suberror,r)}}validateTokenResponse(e,t){var n;if(e.error||e.error_description||e.suberror){const o=`Error(s): ${e.error_codes||u.NOT_AVAILABLE} - Timestamp: ${e.timestamp||u.NOT_AVAILABLE} - Description: ${e.error_description||u.NOT_AVAILABLE} - Correlation ID: ${e.correlation_id||u.NOT_AVAILABLE} - Trace ID: ${e.trace_id||u.NOT_AVAILABLE}`,r=(n=e.error_codes)!=null&&n.length?e.error_codes[0]:void 0,i=new le(e.error,o,e.suberror,r,e.status);if(t&&e.status&&e.status>=We.SERVER_ERROR_RANGE_START&&e.status<=We.SERVER_ERROR_RANGE_END){this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently unavailable and the access token is unable to be refreshed.
${i}`);return}else if(t&&e.status&&e.status>=We.CLIENT_ERROR_RANGE_START&&e.status<=We.CLIENT_ERROR_RANGE_END){this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently available but is unable to refresh the access token.
${i}`);return}throw to(e.error,e.error_description,e.suberror)?new Y(e.error,e.error_description,e.suberror,e.timestamp||u.EMPTY_STRING,e.trace_id||u.EMPTY_STRING,e.correlation_id||u.EMPTY_STRING,e.claims||u.EMPTY_STRING,r):i}}async handleServerTokenResponse(e,t,n,o,r,i,s,c,h){var A;(A=this.performanceClient)==null||A.addQueueMeasurement(l.HandleServerTokenResponse,e.correlation_id);let d;if(e.id_token){if(d=Ae(e.id_token||u.EMPTY_STRING,this.cryptoObj.base64Decode),r&&r.nonce&&d.nonce!==r.nonce)throw p(So);if(o.maxAge||o.maxAge===0){const v=d.auth_time;if(!v)throw p(Jt);Uo(v,o.maxAge)}}this.homeAccountIdentifier=P.generateHomeAccountId(e.client_info||u.EMPTY_STRING,t.authorityType,this.logger,this.cryptoObj,d);let g;r&&r.state&&(g=j.parseRequestState(this.cryptoObj,r.state)),e.key_id=e.key_id||o.sshKid||void 0;const f=this.generateCacheRecord(e,t,n,o,d,i,r);let y;try{if(this.persistencePlugin&&this.serializableCache&&(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),y=new Ya(this.serializableCache,!0),await this.persistencePlugin.beforeCacheAccess(y)),s&&!c&&f.account){const v=f.account.generateAccountKey();if(!this.cacheStorage.getAccount(v,o.correlationId,this.logger))return this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),await fe.generateAuthenticationResult(this.cryptoObj,t,f,!1,o,d,g,void 0,h)}await this.cacheStorage.saveCacheRecord(f,o.correlationId,o.storeInCache)}finally{this.persistencePlugin&&this.serializableCache&&y&&(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),await this.persistencePlugin.afterCacheAccess(y))}return fe.generateAuthenticationResult(this.cryptoObj,t,f,!1,o,d,g,e,h)}generateCacheRecord(e,t,n,o,r,i,s){const c=t.getPreferredCache();if(!c)throw p(Zt);const h=Jo(r);let d,g;e.id_token&&r&&(d=mt(this.homeAccountIdentifier,c,e.id_token,this.clientId,h||""),g=mn(this.cacheStorage,t,this.homeAccountIdentifier,this.cryptoObj.base64Decode,o.correlationId,r,e.client_info,c,h,s,void 0,this.logger));let f=null;if(e.access_token){const v=e.scope?O.fromString(e.scope):new O(o.scopes||[]),N=(typeof e.expires_in=="string"?parseInt(e.expires_in,10):e.expires_in)||0,F=(typeof e.ext_expires_in=="string"?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0,L=(typeof e.refresh_in=="string"?parseInt(e.refresh_in,10):e.refresh_in)||void 0,de=n+N,Qe=de+F,Ye=L&&L>0?n+L:void 0;f=ft(this.homeAccountIdentifier,c,e.access_token,this.clientId,h||t.tenant||"",v.printScopes(),de,Qe,this.cryptoObj.base64Decode,Ye,e.token_type,i,e.key_id,o.claims,o.requestedClaimsHash)}let y=null;if(e.refresh_token){let v;if(e.refresh_token_expires_in){const N=typeof e.refresh_token_expires_in=="string"?parseInt(e.refresh_token_expires_in,10):e.refresh_token_expires_in;v=n+N}y=Ho(this.homeAccountIdentifier,c,e.refresh_token,this.clientId,e.foci,i,v)}let A=null;return e.foci&&(A={clientId:this.clientId,environment:c,familyId:e.foci}),{account:g,idToken:d,accessToken:f,refreshToken:y,appMetadata:A}}static async generateAuthenticationResult(e,t,n,o,r,i,s,c,h){var de,Qe,Ye,Mn,Ln;let d=u.EMPTY_STRING,g=[],f=null,y,A,v=u.EMPTY_STRING;if(n.accessToken){if(n.accessToken.tokenType===_.POP&&!r.popKid){const ai=new Le(e),{secret:si,keyId:Un}=n.accessToken;if(!Un)throw p(en);d=await ai.signPopToken(si,Un,r)}else d=n.accessToken.secret;g=O.fromString(n.accessToken.target).asArray(),f=new Date(Number(n.accessToken.expiresOn)*1e3),y=new Date(Number(n.accessToken.extendedExpiresOn)*1e3),n.accessToken.refreshOn&&(A=new Date(Number(n.accessToken.refreshOn)*1e3))}n.appMetadata&&(v=n.appMetadata.familyId===Be?Be:"");const N=(i==null?void 0:i.oid)||(i==null?void 0:i.sub)||"",F=(i==null?void 0:i.tid)||"";c!=null&&c.spa_accountid&&n.account&&(n.account.nativeAccountId=c==null?void 0:c.spa_accountid);const L=n.account?cn(n.account.getAccountInfo(),void 0,i,(de=n.idToken)==null?void 0:de.secret):null;return{authority:t.canonicalAuthority,uniqueId:N,tenantId:F,scopes:g,account:L,idToken:((Qe=n==null?void 0:n.idToken)==null?void 0:Qe.secret)||"",idTokenClaims:i||{},accessToken:d,fromCache:o,expiresOn:f,extExpiresOn:y,refreshOn:A,correlationId:r.correlationId,requestId:h||u.EMPTY_STRING,familyId:v,tokenType:((Ye=n.accessToken)==null?void 0:Ye.tokenType)||u.EMPTY_STRING,state:s?s.userRequestState:u.EMPTY_STRING,cloudGraphHostName:((Mn=n.account)==null?void 0:Mn.cloudGraphHostName)||u.EMPTY_STRING,msGraphHost:((Ln=n.account)==null?void 0:Ln.msGraphHost)||u.EMPTY_STRING,code:c==null?void 0:c.spa_code,fromNativeBroker:!1}}}function mn(a,e,t,n,o,r,i,s,c,h,d,g){g==null||g.verbose("setCachedAccount called");const y=a.getAccountKeys().find(L=>L.startsWith(t));let A=null;y&&(A=a.getAccount(y,o,g));const v=A||P.createAccount({homeAccountId:t,idTokenClaims:r,clientInfo:i,environment:s,cloudGraphHostName:h==null?void 0:h.cloud_graph_host_name,msGraphHost:h==null?void 0:h.msgraph_host,nativeAccountId:d},e,n),N=v.tenantProfiles||[],F=c||v.realm;if(F&&!N.find(L=>L.tenantId===F)){const L=sn(t,v.localAccountId,F,r);N.push(L)}return v.tenantProfiles=N,v}/*! @azure/msal-common v14.16.1 2025-08-05 */async function sr(a,e,t){return typeof a=="string"?a:a({clientId:e,tokenEndpoint:t})}/*! @azure/msal-common v14.16.1 2025-08-05 */class cr extends gn{constructor(e,t){var n;super(e,t),this.includeRedirectUri=!0,this.oidcDefaultScopes=(n=this.config.authOptions.authority.options.OIDCOptions)==null?void 0:n.defaultScopes}async getAuthCodeUrl(e){var n;(n=this.performanceClient)==null||n.addQueueMeasurement(l.GetAuthCodeUrl,e.correlationId);const t=await m(this.createAuthCodeUrlQueryString.bind(this),l.AuthClientCreateQueryString,this.logger,this.performanceClient,e.correlationId)(e);return S.appendQueryString(this.authority.authorizationEndpoint,t)}async acquireToken(e,t){var s,c;if((s=this.performanceClient)==null||s.addQueueMeasurement(l.AuthClientAcquireToken,e.correlationId),!e.code)throw p(ko);const n=oe(),o=await m(this.executeTokenRequest.bind(this),l.AuthClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(this.authority,e),r=(c=o.headers)==null?void 0:c[x.X_MS_REQUEST_ID],i=new fe(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin,this.performanceClient);return i.validateTokenResponse(o.body),m(i.handleServerTokenResponse.bind(i),l.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(o.body,this.authority,n,e,t,void 0,void 0,void 0,r)}handleFragmentResponse(e,t){if(new fe(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,null,null).validateServerAuthorizationCodeResponse(e,t),!e.code)throw p(Po);return e}getLogoutUri(e){if(!e)throw k(qo);const t=this.createLogoutUrlQueryString(e);return S.appendQueryString(this.authority.endSessionEndpoint,t)}async executeTokenRequest(e,t){var h,d;(h=this.performanceClient)==null||h.addQueueMeasurement(l.AuthClientExecuteTokenRequest,t.correlationId);const n=this.createTokenQueryParameters(t),o=S.appendQueryString(e.tokenEndpoint,n),r=await m(this.createTokenRequestBody.bind(this),l.AuthClientCreateTokenRequestBody,this.logger,this.performanceClient,t.correlationId)(t);let i;if(t.clientInfo)try{const g=at(t.clientInfo,this.cryptoUtils.base64Decode);i={credential:`${g.uid}${K.CLIENT_INFO_SEPARATOR}${g.utid}`,type:z.HOME_ACCOUNT_ID}}catch(g){this.logger.verbose("Could not parse client info for CCS Header: "+g)}const s=this.createTokenRequestHeaders(i||t.ccsCredential),c={clientId:((d=t.tokenBodyParameters)==null?void 0:d.clientId)||this.config.authOptions.clientId,authority:e.canonicalAuthority,scopes:t.scopes,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid};return m(this.executePostToTokenEndpoint.bind(this),l.AuthorizationCodeClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,t.correlationId)(o,r,s,c,t.correlationId,l.AuthorizationCodeClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){var o,r;(o=this.performanceClient)==null||o.addQueueMeasurement(l.AuthClientCreateTokenRequestBody,e.correlationId);const t=new Ge(e.correlationId,this.performanceClient);if(t.addClientId(e.embeddedClientId||((r=e.tokenBodyParameters)==null?void 0:r[me])||this.config.authOptions.clientId),this.includeRedirectUri?t.addRedirectUri(e.redirectUri):Se.validateRedirectUri(e.redirectUri),t.addScopes(e.scopes,!0,this.oidcDefaultScopes),t.addAuthorizationCode(e.code),t.addLibraryInfo(this.config.libraryInfo),t.addApplicationTelemetry(this.config.telemetry.application),t.addThrottling(),this.serverTelemetryManager&&!Kt(this.config)&&t.addServerTelemetry(this.serverTelemetryManager),e.codeVerifier&&t.addCodeVerifier(e.codeVerifier),this.config.clientCredentials.clientSecret&&t.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const i=this.config.clientCredentials.clientAssertion;t.addClientAssertion(await sr(i.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),t.addClientAssertionType(i.assertionType)}if(t.addGrantType(mo.AUTHORIZATION_CODE_GRANT),t.addClientInfo(),e.authenticationScheme===_.POP){const i=new Le(this.cryptoUtils,this.performanceClient);let s;e.popKid?s=this.cryptoUtils.encodeKid(e.popKid):s=(await m(i.generateCnf.bind(i),l.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,t.addPopToken(s)}else if(e.authenticationScheme===_.SSH)if(e.sshJwk)t.addSshJwk(e.sshJwk);else throw k(Ct);(!Q.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities);let n;if(e.clientInfo)try{const i=at(e.clientInfo,this.cryptoUtils.base64Decode);n={credential:`${i.uid}${K.CLIENT_INFO_SEPARATOR}${i.utid}`,type:z.HOME_ACCOUNT_ID}}catch(i){this.logger.verbose("Could not parse client info for CCS Header: "+i)}else n=e.ccsCredential;if(this.config.systemOptions.preventCorsPreflight&&n)switch(n.type){case z.HOME_ACCOUNT_ID:try{const i=Re(n.credential);t.addCcsOid(i)}catch(i){this.logger.verbose("Could not parse home account ID for CCS Header: "+i)}break;case z.UPN:t.addCcsUpn(n.credential);break}return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&t.addExtraQueryParameters(e.tokenBodyParameters),e.enableSpaAuthorizationCode&&(!e.tokenBodyParameters||!e.tokenBodyParameters[Xn])&&t.addExtraQueryParameters({[Xn]:"1"}),t.createQueryString()}async createAuthCodeUrlQueryString(e){var r,i;const t=e.correlationId||this.config.cryptoInterface.createNewGuid();(r=this.performanceClient)==null||r.addQueueMeasurement(l.AuthClientCreateQueryString,t);const n=new Ge(t,this.performanceClient);n.addClientId(e.embeddedClientId||((i=e.extraQueryParameters)==null?void 0:i[me])||this.config.authOptions.clientId);const o=[...e.scopes||[],...e.extraScopesToConsent||[]];if(n.addScopes(o,!0,this.oidcDefaultScopes),n.addRedirectUri(e.redirectUri),n.addCorrelationId(t),n.addResponseMode(e.responseMode),n.addResponseTypeCode(),n.addLibraryInfo(this.config.libraryInfo),Kt(this.config)||n.addApplicationTelemetry(this.config.telemetry.application),n.addClientInfo(),e.codeChallenge&&e.codeChallengeMethod&&n.addCodeChallengeParams(e.codeChallenge,e.codeChallengeMethod),e.prompt&&n.addPrompt(e.prompt),e.domainHint&&n.addDomainHint(e.domainHint),e.prompt!==M.SELECT_ACCOUNT)if(e.sid&&e.prompt===M.NONE)this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),n.addSid(e.sid);else if(e.account){const s=this.extractAccountSid(e.account);let c=this.extractLoginHint(e.account);if(c&&e.domainHint&&(this.logger.warning('AuthorizationCodeClient.createAuthCodeUrlQueryString: "domainHint" param is set, skipping opaque "login_hint" claim. Please consider not passing domainHint'),c=null),c){this.logger.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"),n.addLoginHint(c);try{const h=Re(e.account.homeAccountId);n.addCcsOid(h)}catch{this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(s&&e.prompt===M.NONE){this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),n.addSid(s);try{const h=Re(e.account.homeAccountId);n.addCcsOid(h)}catch{this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(e.loginHint)this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),n.addLoginHint(e.loginHint),n.addCcsUpn(e.loginHint);else if(e.account.username){this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),n.addLoginHint(e.account.username);try{const h=Re(e.account.homeAccountId);n.addCcsOid(h)}catch{this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}}else e.loginHint&&(this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),n.addLoginHint(e.loginHint),n.addCcsUpn(e.loginHint));else this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");if(e.nonce&&n.addNonce(e.nonce),e.state&&n.addState(e.state),(e.claims||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&n.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.embeddedClientId&&n.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),this.addExtraQueryParams(e,n),e.nativeBroker&&(n.addNativeBroker(),e.authenticationScheme===_.POP)){const s=new Le(this.cryptoUtils);let c;e.popKid?c=this.cryptoUtils.encodeKid(e.popKid):c=(await m(s.generateCnf.bind(s),l.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,n.addPopToken(c)}return n.createQueryString()}createLogoutUrlQueryString(e){const t=new Ge(e.correlationId,this.performanceClient);return e.postLogoutRedirectUri&&t.addPostLogoutRedirectUri(e.postLogoutRedirectUri),e.correlationId&&t.addCorrelationId(e.correlationId),e.idTokenHint&&t.addIdTokenHint(e.idTokenHint),e.state&&t.addState(e.state),e.logoutHint&&t.addLogoutHint(e.logoutHint),this.addExtraQueryParams(e,t),t.createQueryString()}addExtraQueryParams(e,t){!(e.extraQueryParameters&&e.extraQueryParameters.hasOwnProperty("instance_aware"))&&this.config.authOptions.instanceAware&&(e.extraQueryParameters=e.extraQueryParameters||{},e.extraQueryParameters.instance_aware="true"),e.extraQueryParameters&&t.addExtraQueryParameters(e.extraQueryParameters)}extractAccountSid(e){var t;return((t=e.idTokenClaims)==null?void 0:t.sid)||null}extractLoginHint(e){var t;return((t=e.idTokenClaims)==null?void 0:t.login_hint)||null}}/*! @azure/msal-common v14.16.1 2025-08-05 */const ja=300;class Ft extends gn{constructor(e,t){super(e,t)}async acquireToken(e){var i,s;(i=this.performanceClient)==null||i.addQueueMeasurement(l.RefreshTokenClientAcquireToken,e.correlationId);const t=oe(),n=await m(this.executeTokenRequest.bind(this),l.RefreshTokenClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(e,this.authority),o=(s=n.headers)==null?void 0:s[x.X_MS_REQUEST_ID],r=new fe(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin);return r.validateTokenResponse(n.body),m(r.handleServerTokenResponse.bind(r),l.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(n.body,this.authority,t,e,void 0,void 0,!0,e.forceCache,o)}async acquireTokenByRefreshToken(e){var n;if(!e)throw k(Go);if((n=this.performanceClient)==null||n.addQueueMeasurement(l.RefreshTokenClientAcquireTokenByRefreshToken,e.correlationId),!e.account)throw p(Xt);if(this.cacheManager.isAppMetadataFOCI(e.account.environment))try{return await m(this.acquireTokenWithCachedRefreshToken.bind(this),l.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!0)}catch(o){const r=o instanceof Y&&o.errorCode===lt,i=o instanceof le&&o.errorCode===Kn.INVALID_GRANT_ERROR&&o.subError===Kn.CLIENT_MISMATCH_ERROR;if(r||i)return m(this.acquireTokenWithCachedRefreshToken.bind(this),l.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1);throw o}return m(this.acquireTokenWithCachedRefreshToken.bind(this),l.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1)}async acquireTokenWithCachedRefreshToken(e,t){var r;(r=this.performanceClient)==null||r.addQueueMeasurement(l.RefreshTokenClientAcquireTokenWithCachedRefreshToken,e.correlationId);const n=Ee(this.cacheManager.getRefreshToken.bind(this.cacheManager),l.CacheManagerGetRefreshToken,this.logger,this.performanceClient,e.correlationId)(e.account,t,e.correlationId,void 0,this.performanceClient);if(!n)throw Bt(lt);if(n.expiresOn&&Dt(n.expiresOn,e.refreshTokenExpirationOffsetSeconds||ja))throw Bt(pn);const o={...e,refreshToken:n.secret,authenticationScheme:e.authenticationScheme||_.BEARER,ccsCredential:{credential:e.account.homeAccountId,type:z.HOME_ACCOUNT_ID}};try{return await m(this.acquireToken.bind(this),l.RefreshTokenClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(o)}catch(i){if(i instanceof Y&&i.subError===At){this.logger.verbose("acquireTokenWithRefreshToken: bad refresh token, removing from cache");const s=ke(n);this.cacheManager.removeRefreshToken(s,e.correlationId)}throw i}}async executeTokenRequest(e,t){var c,h;(c=this.performanceClient)==null||c.addQueueMeasurement(l.RefreshTokenClientExecuteTokenRequest,e.correlationId);const n=this.createTokenQueryParameters(e),o=S.appendQueryString(t.tokenEndpoint,n),r=await m(this.createTokenRequestBody.bind(this),l.RefreshTokenClientCreateTokenRequestBody,this.logger,this.performanceClient,e.correlationId)(e),i=this.createTokenRequestHeaders(e.ccsCredential),s={clientId:((h=e.tokenBodyParameters)==null?void 0:h.clientId)||this.config.authOptions.clientId,authority:t.canonicalAuthority,scopes:e.scopes,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid};return m(this.executePostToTokenEndpoint.bind(this),l.RefreshTokenClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,e.correlationId)(o,r,i,s,e.correlationId,l.RefreshTokenClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){var o,r,i;(o=this.performanceClient)==null||o.addQueueMeasurement(l.RefreshTokenClientCreateTokenRequestBody,e.correlationId);const t=e.correlationId,n=new Ge(t,this.performanceClient);if(n.addClientId(e.embeddedClientId||((r=e.tokenBodyParameters)==null?void 0:r[me])||this.config.authOptions.clientId),e.redirectUri&&n.addRedirectUri(e.redirectUri),n.addScopes(e.scopes,!0,(i=this.config.authOptions.authority.options.OIDCOptions)==null?void 0:i.defaultScopes),n.addGrantType(mo.REFRESH_TOKEN_GRANT),n.addClientInfo(),n.addLibraryInfo(this.config.libraryInfo),n.addApplicationTelemetry(this.config.telemetry.application),n.addThrottling(),this.serverTelemetryManager&&!Kt(this.config)&&n.addServerTelemetry(this.serverTelemetryManager),n.addRefreshToken(e.refreshToken),this.config.clientCredentials.clientSecret&&n.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const s=this.config.clientCredentials.clientAssertion;n.addClientAssertion(await sr(s.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),n.addClientAssertionType(s.assertionType)}if(e.authenticationScheme===_.POP){const s=new Le(this.cryptoUtils,this.performanceClient);let c;e.popKid?c=this.cryptoUtils.encodeKid(e.popKid):c=(await m(s.generateCnf.bind(s),l.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString,n.addPopToken(c)}else if(e.authenticationScheme===_.SSH)if(e.sshJwk)n.addSshJwk(e.sshJwk);else throw k(Ct);if((!Q.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&n.addClaims(e.claims,this.config.authOptions.clientCapabilities),this.config.systemOptions.preventCorsPreflight&&e.ccsCredential)switch(e.ccsCredential.type){case z.HOME_ACCOUNT_ID:try{const s=Re(e.ccsCredential.credential);n.addCcsOid(s)}catch(s){this.logger.verbose("Could not parse home account ID for CCS Header: "+s)}break;case z.UPN:n.addCcsUpn(e.ccsCredential.credential);break}return e.embeddedClientId&&n.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&n.addExtraQueryParameters(e.tokenBodyParameters),n.createQueryString()}}/*! @azure/msal-common v14.16.1 2025-08-05 */class Ja extends gn{constructor(e,t){super(e,t)}async acquireToken(e){var t;try{const[n,o]=await this.acquireCachedToken({...e,scopes:(t=e.scopes)!=null&&t.length?e.scopes:[...Ie]});return o===ue.PROACTIVELY_REFRESHED&&(this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'. It's not expired, but must be refreshed."),new Ft(this.config,this.performanceClient).acquireTokenByRefreshToken(e).catch(()=>{})),n}catch(n){if(n instanceof ze&&n.errorCode===se)return new Ft(this.config,this.performanceClient).acquireTokenByRefreshToken(e);throw n}}async acquireCachedToken(e){var c;(c=this.performanceClient)==null||c.addQueueMeasurement(l.SilentFlowClientAcquireCachedToken,e.correlationId);let t=ue.NOT_APPLICABLE;if(e.forceRefresh||!this.config.cacheOptions.claimsBasedCachingEnabled&&!Q.isEmptyObj(e.claims))throw this.setCacheOutcome(ue.FORCE_REFRESH_OR_CLAIMS,e.correlationId),p(se);if(!e.account)throw p(Xt);const n=e.account.tenantId||Ba(e.authority),o=this.cacheManager.getTokenKeys(),r=this.cacheManager.getAccessToken(e.account,e,o,n,this.performanceClient);if(r){if(_i(r.cachedAt)||Dt(r.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))throw this.setCacheOutcome(ue.CACHED_ACCESS_TOKEN_EXPIRED,e.correlationId),p(se);r.refreshOn&&Dt(r.refreshOn,0)&&(t=ue.PROACTIVELY_REFRESHED)}else throw this.setCacheOutcome(ue.NO_CACHED_ACCESS_TOKEN,e.correlationId),p(se);const i=e.authority||this.authority.getPreferredCache(),s={account:this.cacheManager.readAccountFromCache(e.account,e.correlationId),accessToken:r,idToken:this.cacheManager.getIdToken(e.account,e.correlationId,o,n,this.performanceClient),refreshToken:null,appMetadata:this.cacheManager.readAppMetadataFromCache(i)};return this.setCacheOutcome(t,e.correlationId),this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[await m(this.generateResultFromCacheRecord.bind(this),l.SilentFlowClientGenerateResultFromCacheRecord,this.logger,this.performanceClient,e.correlationId)(s,e),t]}setCacheOutcome(e,t){var n,o;(n=this.serverTelemetryManager)==null||n.setCacheOutcome(e),(o=this.performanceClient)==null||o.addFields({cacheOutcome:e},t),e!==ue.NOT_APPLICABLE&&this.logger.info(`Token refresh is required due to cache outcome: ${e}`)}async generateResultFromCacheRecord(e,t){var o;(o=this.performanceClient)==null||o.addQueueMeasurement(l.SilentFlowClientGenerateResultFromCacheRecord,t.correlationId);let n;if(e.idToken&&(n=Ae(e.idToken.secret,this.config.cryptoInterface.base64Decode)),t.maxAge||t.maxAge===0){const r=n==null?void 0:n.auth_time;if(!r)throw p(Jt);Uo(r,t.maxAge)}return fe.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,n)}}/*! @azure/msal-common v14.16.1 2025-08-05 */const Xa={sendGetRequestAsync:()=>Promise.reject(p(E)),sendPostRequestAsync:()=>Promise.reject(p(E))};/*! @azure/msal-common v14.16.1 2025-08-05 */const no=",",hr="|";function Za(a){const{skus:e,libraryName:t,libraryVersion:n,extensionName:o,extensionVersion:r}=a,i=new Map([[0,[t,n]],[2,[o,r]]]);let s=[];if(e!=null&&e.length){if(s=e.split(no),s.length<4)return e}else s=Array.from({length:4},()=>hr);return i.forEach((c,h)=>{var d,g;c.length===2&&((d=c[0])!=null&&d.length)&&((g=c[1])!=null&&g.length)&&es({skuArr:s,index:h,skuName:c[0],skuVersion:c[1]})}),s.join(no)}function es(a){const{skuArr:e,index:t,skuName:n,skuVersion:o}=a;t>=e.length||(e[t]=[n,o].join(hr))}class qe{constructor(e,t){this.cacheOutcome=ue.NOT_APPLICABLE,this.cacheManager=t,this.apiId=e.apiId,this.correlationId=e.correlationId,this.wrapperSKU=e.wrapperSKU||u.EMPTY_STRING,this.wrapperVer=e.wrapperVer||u.EMPTY_STRING,this.telemetryCacheKey=H.CACHE_KEY+K.CACHE_KEY_SEPARATOR+e.clientId}generateCurrentRequestHeaderValue(){const e=`${this.apiId}${H.VALUE_SEPARATOR}${this.cacheOutcome}`,t=[this.wrapperSKU,this.wrapperVer],n=this.getNativeBrokerErrorCode();n!=null&&n.length&&t.push(`broker_error=${n}`);const o=t.join(H.VALUE_SEPARATOR),r=this.getRegionDiscoveryFields(),i=[e,r].join(H.VALUE_SEPARATOR);return[H.SCHEMA_VERSION,i,o].join(H.CATEGORY_SEPARATOR)}generateLastRequestHeaderValue(){const e=this.getLastRequests(),t=qe.maxErrorsToSend(e),n=e.failedRequests.slice(0,2*t).join(H.VALUE_SEPARATOR),o=e.errors.slice(0,t).join(H.VALUE_SEPARATOR),r=e.errors.length,i=t<r?H.OVERFLOW_TRUE:H.OVERFLOW_FALSE,s=[r,i].join(H.VALUE_SEPARATOR);return[H.SCHEMA_VERSION,e.cacheHits,n,o,s].join(H.CATEGORY_SEPARATOR)}cacheFailedRequest(e){const t=this.getLastRequests();t.errors.length>=H.MAX_CACHED_ERRORS&&(t.failedRequests.shift(),t.failedRequests.shift(),t.errors.shift()),t.failedRequests.push(this.apiId,this.correlationId),e instanceof Error&&e&&e.toString()?e instanceof b?e.subError?t.errors.push(e.subError):e.errorCode?t.errors.push(e.errorCode):t.errors.push(e.toString()):t.errors.push(e.toString()):t.errors.push(H.UNKNOWN_ERROR),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t,this.correlationId)}incrementCacheHits(){const e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e,this.correlationId),e.cacheHits}getLastRequests(){const e={failedRequests:[],errors:[],cacheHits:0};return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||e}clearTelemetryCache(){const e=this.getLastRequests(),t=qe.maxErrorsToSend(e),n=e.errors.length;if(t===n)this.cacheManager.removeItem(this.telemetryCacheKey,this.correlationId);else{const o={failedRequests:e.failedRequests.slice(t*2),errors:e.errors.slice(t),cacheHits:0};this.cacheManager.setServerTelemetry(this.telemetryCacheKey,o,this.correlationId)}}static maxErrorsToSend(e){let t,n=0,o=0;const r=e.errors.length;for(t=0;t<r;t++){const i=e.failedRequests[2*t]||u.EMPTY_STRING,s=e.failedRequests[2*t+1]||u.EMPTY_STRING,c=e.errors[t]||u.EMPTY_STRING;if(o+=i.toString().length+s.toString().length+c.length+3,o<H.MAX_LAST_HEADER_BYTES)n+=1;else break}return n}getRegionDiscoveryFields(){const e=[];return e.push(this.regionUsed||u.EMPTY_STRING),e.push(this.regionSource||u.EMPTY_STRING),e.push(this.regionOutcome||u.EMPTY_STRING),e.join(",")}updateRegionDiscoveryMetadata(e){this.regionUsed=e.region_used,this.regionSource=e.region_source,this.regionOutcome=e.region_outcome}setCacheOutcome(e){this.cacheOutcome=e}setNativeBrokerErrorCode(e){const t=this.getLastRequests();t.nativeBrokerErrorCode=e,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t,this.correlationId)}getNativeBrokerErrorCode(){return this.getLastRequests().nativeBrokerErrorCode}clearNativeBrokerErrorCode(){const e=this.getLastRequests();delete e.nativeBrokerErrorCode,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e,this.correlationId)}static makeExtraSkuString(e){return Za(e)}}/*! @azure/msal-common v14.16.1 2025-08-05 */const lr="missing_kid_error",dr="missing_alg_error";/*! @azure/msal-common v14.16.1 2025-08-05 */const ts={[lr]:"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.",[dr]:"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."};class fn extends b{constructor(e,t){super(e,t),this.name="JoseHeaderError",Object.setPrototypeOf(this,fn.prototype)}}function oo(a){return new fn(a,ts[a])}/*! @azure/msal-common v14.16.1 2025-08-05 */class Cn{constructor(e){this.typ=e.typ,this.alg=e.alg,this.kid=e.kid}static getShrHeaderString(e){if(!e.kid)throw oo(lr);if(!e.alg)throw oo(dr);const t=new Cn({typ:e.typ||fo.Pop,kid:e.kid,alg:e.alg});return JSON.stringify(t)}}/*! @azure/msal-common v14.16.1 2025-08-05 */class ro{startMeasurement(){}endMeasurement(){}flushMeasurement(){return null}}class ur{generateId(){return"callback-id"}startMeasurement(e,t){return{end:()=>null,discard:()=>{},add:()=>{},increment:()=>{},event:{eventId:this.generateId(),status:xa.InProgress,authority:"",libraryName:"",libraryVersion:"",clientId:"",name:e,startTimeMs:Date.now(),correlationId:t||""},measurement:new ro}}startPerformanceMeasurement(){return new ro}calculateQueuedTime(){return 0}addQueueMeasurement(){}setPreQueueTime(){}endMeasurement(){return null}discardMeasurements(){}removePerformanceCallback(){return!0}addPerformanceCallback(){return""}emitEvents(){}addFields(){}incrementFields(){}cacheEventByCorrelationId(){}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const yn="pkce_not_created",Gt="crypto_nonexistent",Et="empty_navigate_uri",gr="hash_empty_error",Tn="no_state_in_hash",pr="hash_does_not_contain_known_properties",mr="unable_to_parse_state",fr="state_interaction_type_mismatch",Cr="interaction_in_progress",yr="popup_window_error",Tr="empty_window_error",Ce="user_cancelled",ns="monitor_popup_timeout",Ir="monitor_window_timeout",Ar="redirect_in_iframe",Er="block_iframe_reload",vr="block_nested_popups",os="iframe_closed_prematurely",vt="silent_logout_unsupported",Sr="no_account_error",rs="silent_prompt_value_error",wr="no_token_request_cache_error",_r="unable_to_parse_token_request_cache_error",In="no_cached_authority_error",is="auth_request_not_set_error",as="invalid_cache_type",St="non_browser_environment",we="database_not_open",dt="no_network_connectivity",kr="post_request_failed",Rr="get_request_failed",qt="failed_to_parse_response",br="unable_to_load_token",An="crypto_key_not_found",Or="auth_code_required",Nr="auth_code_or_nativeAccountId_required",Pr="spa_code_and_nativeAccountId_present",En="database_unavailable",Mr="unable_to_acquire_token_from_native_platform",Lr="native_handshake_timeout",Ur="native_extension_not_installed",Ve="native_connection_not_established",Hr="uninitialized_public_client_application",Dr="native_prompt_not_supported",Kr="invalid_base64_string",xr="invalid_pop_token_request",Br="failed_to_build_headers",Fr="failed_to_parse_headers";/*! @azure/msal-browser v3.30.0 2025-08-05 */const ie="For more visit: aka.ms/msaljs/browser-errors",ss={[yn]:"The PKCE code challenge and verifier could not be generated.",[Gt]:"The crypto object or function is not available.",[Et]:"Navigation URI is empty. Please check stack trace for more info.",[gr]:`Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. ${ie}`,[Tn]:"Hash does not contain state. Please verify that the request originated from msal.",[pr]:`Hash does not contain known properites. Please verify that your redirectUri is not changing the hash.  ${ie}`,[mr]:"Unable to parse state. Please verify that the request originated from msal.",[fr]:"Hash contains state but the interaction type does not match the caller.",[Cr]:`Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.   ${ie}`,[yr]:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.",[Tr]:"window.open returned null or undefined window object.",[Ce]:"User cancelled the flow.",[ns]:`Token acquisition in popup failed due to timeout.  ${ie}`,[Ir]:`Token acquisition in iframe failed due to timeout.  ${ie}`,[Ar]:"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.",[Er]:`Request was blocked inside an iframe because MSAL detected an authentication response.  ${ie}`,[vr]:"Request was blocked inside a popup because MSAL detected it was running in a popup.",[os]:"The iframe being monitored was closed prematurely.",[vt]:"Silent logout not supported. Please call logoutRedirect or logoutPopup instead.",[Sr]:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.",[rs]:"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.",[wr]:"No token request found in cache.",[_r]:"The cached token request could not be parsed.",[In]:"No cached authority found.",[is]:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler",[as]:"Invalid cache type",[St]:"Login and token requests are not supported in non-browser environments.",[we]:"Database is not open!",[dt]:"No network connectivity. Check your internet connection.",[kr]:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'",[Rr]:"Network request failed. Please check the network trace to determine root cause.",[qt]:"Failed to parse network response. Check network trace.",[br]:"Error loading token to cache.",[An]:"Cryptographic Key or Keypair not found in browser storage.",[Or]:"An authorization code must be provided (as the `code` property on the request) to this flow.",[Nr]:"An authorization code or nativeAccountId must be provided to this flow.",[Pr]:"Request cannot contain both spa code and native account id.",[En]:"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.",[Mr]:`Unable to acquire token from native platform.  ${ie}`,[Lr]:"Timed out while attempting to establish connection to browser extension",[Ur]:"Native extension is not installed. If you think this is a mistake call the initialize function.",[Ve]:`Connection to native platform has not been established. Please install a compatible browser extension and run initialize().  ${ie}`,[Hr]:`You must call and await the initialize function before attempting to call any other MSAL API.  ${ie}`,[Dr]:"The provided prompt is not supported by the native platform. This request should be routed to the web based flow.",[Kr]:"Invalid base64 encoded string.",[xr]:"Invalid PoP token request. The request should not have both a popKid value and signPopToken set to true.",[Br]:"Failed to build request headers object.",[Fr]:"Failed to parse response headers"};class De extends b{constructor(e,t){super(e,ss[e],t),Object.setPrototypeOf(this,De.prototype),this.name="BrowserAuthError"}}function C(a,e){return new De(a,e)}/*! @azure/msal-browser v3.30.0 2025-08-05 */const $={INVALID_GRANT_ERROR:"invalid_grant",POPUP_WIDTH:483,POPUP_HEIGHT:600,POPUP_NAME_PREFIX:"msal",DEFAULT_POLL_INTERVAL_MS:30,MSAL_SKU:"msal.js.browser"},be={CHANNEL_ID:"53ee284d-920a-4b59-9d30-a60315b26836",PREFERRED_EXTENSION_ID:"ppnbnpeolgkicgegkbkbjmhlideopiji",MATS_TELEMETRY:"MATS"},pe={HandshakeRequest:"Handshake",HandshakeResponse:"HandshakeResponse",GetToken:"GetToken",Response:"Response"},B={LocalStorage:"localStorage",SessionStorage:"sessionStorage",MemoryStorage:"memoryStorage"},io={GET:"GET",POST:"POST"},w={AUTHORITY:"authority",ACQUIRE_TOKEN_ACCOUNT:"acquireToken.account",SESSION_STATE:"session.state",REQUEST_STATE:"request.state",NONCE_IDTOKEN:"nonce.id_token",ORIGIN_URI:"request.origin",RENEW_STATUS:"token.renew.status",URL_HASH:"urlHash",REQUEST_PARAMS:"request.params",SCOPES:"scopes",INTERACTION_STATUS_KEY:"interaction.status",CCS_CREDENTIAL:"ccs.credential",CORRELATION_ID:"request.correlationId",NATIVE_REQUEST:"request.native",REDIRECT_CONTEXT:"request.redirect.context"},V={ACCOUNT_KEYS:"msal.account.keys",TOKEN_KEYS:"msal.token.keys",VERSION:"msal.version"},et={WRAPPER_SKU:"wrapper.sku",WRAPPER_VER:"wrapper.version"},R={acquireTokenRedirect:861,acquireTokenPopup:862,ssoSilent:863,acquireTokenSilent_authCode:864,handleRedirectPromise:865,acquireTokenByCode:866,acquireTokenSilent_silentFlow:61,logout:961,logoutPopup:962};exports.InteractionType=void 0;(function(a){a.Redirect="redirect",a.Popup="popup",a.Silent="silent",a.None="none"})(exports.InteractionType||(exports.InteractionType={}));const ao={scopes:Ie},Gr="jwk",$t="msal.db",cs=1,hs=`${$t}.keys`,G={Default:0,AccessToken:1,AccessTokenAndRefreshToken:2,RefreshToken:3,RefreshTokenAndNetwork:4,Skip:5},ls=[G.Default,G.Skip,G.RefreshTokenAndNetwork],ds="msal.browser.log.level",us="msal.browser.log.pii";/*! @azure/msal-browser v3.30.0 2025-08-05 */function tt(a){return encodeURIComponent(vn(a).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"))}function wt(a){return qr(a).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function vn(a){return qr(new TextEncoder().encode(a))}function qr(a){const e=Array.from(a,t=>String.fromCodePoint(t)).join("");return btoa(e)}/*! @azure/msal-browser v3.30.0 2025-08-05 */const gs="RSASSA-PKCS1-v1_5",$r="SHA-256",ps=2048,ms=new Uint8Array([1,0,1]),so="0123456789abcdef",co=new Uint32Array(1),fs="crypto_subtle_undefined",Sn={name:gs,hash:$r,modulusLength:ps,publicExponent:ms};function Cs(a){if(!window)throw C(St);if(!window.crypto)throw C(Gt);if(!a&&!window.crypto.subtle)throw C(Gt,fs)}async function zr(a,e,t){e==null||e.addQueueMeasurement(l.Sha256Digest,t);const o=new TextEncoder().encode(a);return window.crypto.subtle.digest($r,o)}function ys(a){return window.crypto.getRandomValues(a)}function Nt(){return window.crypto.getRandomValues(co),co[0]}function re(){const a=Date.now(),e=Nt()*1024+(Nt()&1023),t=new Uint8Array(16),n=Math.trunc(e/2**30),o=e&2**30-1,r=Nt();t[0]=a/2**40,t[1]=a/2**32,t[2]=a/2**24,t[3]=a/2**16,t[4]=a/2**8,t[5]=a,t[6]=112|n>>>8,t[7]=n,t[8]=128|o>>>24,t[9]=o>>>16,t[10]=o>>>8,t[11]=o,t[12]=r>>>24,t[13]=r>>>16,t[14]=r>>>8,t[15]=r;let i="";for(let s=0;s<t.length;s++)i+=so.charAt(t[s]>>>4),i+=so.charAt(t[s]&15),(s===3||s===5||s===7||s===9)&&(i+="-");return i}async function Ts(a,e){return window.crypto.subtle.generateKey(Sn,a,e)}async function Pt(a){return window.crypto.subtle.exportKey(Gr,a)}async function Is(a,e,t){return window.crypto.subtle.importKey(Gr,a,Sn,e,t)}async function As(a,e){return window.crypto.subtle.sign(Sn,a,e)}async function Vr(a){const e=await zr(a),t=new Uint8Array(e);return wt(t)}/*! @azure/msal-browser v3.30.0 2025-08-05 */const wn="storage_not_supported",Es="stubbed_public_client_application_called",Qr="in_mem_redirect_unavailable";/*! @azure/msal-browser v3.30.0 2025-08-05 */const vs={[wn]:"Given storage configuration option was not supported.",[Es]:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors",[Qr]:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."};class _t extends b{constructor(e,t){super(e,t),this.name="BrowserConfigurationAuthError",Object.setPrototypeOf(this,_t.prototype)}}function _n(a){return new _t(a,vs[a])}/*! @azure/msal-browser v3.30.0 2025-08-05 */function Ss(a){a.location.hash="",typeof a.history.replaceState=="function"&&a.history.replaceState(null,"",`${a.location.origin}${a.location.pathname}${a.location.search}`)}function ws(a){const e=a.split("#");e.shift(),window.location.hash=e.length>0?e.join("#"):""}function kn(){return window.parent!==window}function _s(){return typeof window<"u"&&!!window.opener&&window.opener!==window&&typeof window.name=="string"&&window.name.indexOf(`${$.POPUP_NAME_PREFIX}.`)===0}function ce(){return typeof window<"u"&&window.location?window.location.href.split("?")[0].split("#")[0]:""}function ks(){const e=new S(window.location.href).getUrlComponents();return`${e.Protocol}//${e.HostNameAndPort}/`}function Rs(){if(S.hashContainsKnownProperties(window.location.hash)&&kn())throw C(Er)}function bs(a){if(kn()&&!a)throw C(Ar)}function Os(){if(_s())throw C(vr)}function Yr(){if(typeof window>"u")throw C(St)}function Wr(a){if(!a)throw C(Hr)}function Rn(a){Yr(),Rs(),Os(),Wr(a)}function ho(a,e){if(Rn(a),bs(e.system.allowRedirectInIframe),e.cache.cacheLocation===B.MemoryStorage&&!e.cache.storeAuthStateInCookie)throw _n(Qr)}function jr(a){const e=document.createElement("link");e.rel="preconnect",e.href=new URL(a).origin,e.crossOrigin="anonymous",document.head.appendChild(e),window.setTimeout(()=>{try{document.head.removeChild(e)}catch{}},1e4)}function Ns(){return re()}/*! @azure/msal-browser v3.30.0 2025-08-05 */class $e{navigateInternal(e,t){return $e.defaultNavigateWindow(e,t)}navigateExternal(e,t){return $e.defaultNavigateWindow(e,t)}static defaultNavigateWindow(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise(n=>{setTimeout(()=>{n(!0)},t.timeout)})}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ps{async sendGetRequestAsync(e,t){let n,o={},r=0;const i=lo(t);try{n=await fetch(e,{method:io.GET,headers:i})}catch{throw C(window.navigator.onLine?Rr:dt)}o=uo(n.headers);try{return r=n.status,{headers:o,body:await n.json(),status:r}}catch{throw Zn(C(qt),r,o)}}async sendPostRequestAsync(e,t){const n=t&&t.body||"",o=lo(t);let r,i=0,s={};try{r=await fetch(e,{method:io.POST,headers:o,body:n})}catch{throw C(window.navigator.onLine?kr:dt)}s=uo(r.headers);try{return i=r.status,{headers:s,body:await r.json(),status:i}}catch{throw Zn(C(qt),i,s)}}}function lo(a){try{const e=new Headers;if(!(a&&a.headers))return e;const t=a.headers;return Object.entries(t).forEach(([n,o])=>{e.append(n,o)}),e}catch{throw C(Br)}}function uo(a){try{const e={};return a.forEach((t,n)=>{e[n]=t}),e}catch{throw C(Fr)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const Ms=6e4,ut=1e4,Ls=3e4,Us=2e3;function Hs({auth:a,cache:e,system:t,telemetry:n},o){const r={clientId:u.EMPTY_STRING,authority:`${u.DEFAULT_AUTHORITY}`,knownAuthorities:[],cloudDiscoveryMetadata:u.EMPTY_STRING,authorityMetadata:u.EMPTY_STRING,redirectUri:typeof window<"u"?ce():"",postLogoutRedirectUri:u.EMPTY_STRING,navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:te.AAD,OIDCOptions:{serverResponseType:He.FRAGMENT,defaultScopes:[u.OPENID_SCOPE,u.PROFILE_SCOPE,u.OFFLINE_ACCESS_SCOPE]},azureCloudOptions:{azureCloudInstance:pt.None,tenant:u.EMPTY_STRING},skipAuthorityMetadataCache:!1,supportsNestedAppAuth:!1,instanceAware:!1},i={cacheLocation:B.SessionStorage,temporaryCacheLocation:B.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!!(e&&e.cacheLocation===B.LocalStorage),claimsBasedCachingEnabled:!1},s={loggerCallback:()=>{},logLevel:exports.LogLevel.Info,piiLoggingEnabled:!1},h={...{...nr,loggerOptions:s,networkClient:o?new Ps:Xa,navigationClient:new $e,loadFrameTimeout:0,windowHashTimeout:(t==null?void 0:t.loadFrameTimeout)||Ms,iframeHashTimeout:(t==null?void 0:t.loadFrameTimeout)||ut,navigateFrameWait:0,redirectNavigationTimeout:Ls,asyncPopups:!1,allowRedirectInIframe:!1,allowNativeBroker:!1,nativeBrokerHandshakeTimeout:(t==null?void 0:t.nativeBrokerHandshakeTimeout)||Us,pollIntervalMilliseconds:$.DEFAULT_POLL_INTERVAL_MS},...t,loggerOptions:(t==null?void 0:t.loggerOptions)||s},d={application:{appName:u.EMPTY_STRING,appVersion:u.EMPTY_STRING},client:new ur};if((a==null?void 0:a.protocolMode)!==te.OIDC&&(a!=null&&a.OIDCOptions)&&new he(h.loggerOptions).warning(JSON.stringify(k(Yo))),a!=null&&a.protocolMode&&a.protocolMode!==te.AAD&&(h!=null&&h.allowNativeBroker))throw k(Wo);return{auth:{...r,...a,OIDCOptions:{...r.OIDCOptions,...a==null?void 0:a.OIDCOptions}},cache:{...i,...e},system:h,telemetry:{...d,...n}}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const Ds="@azure/msal-browser",ye="3.30.0";/*! @azure/msal-browser v3.30.0 2025-08-05 */class bn{static loggerCallback(e,t){switch(e){case exports.LogLevel.Error:console.error(t);return;case exports.LogLevel.Info:console.info(t);return;case exports.LogLevel.Verbose:console.debug(t);return;case exports.LogLevel.Warning:console.warn(t);return;default:console.log(t);return}}constructor(e){var c;this.browserEnvironment=typeof window<"u",this.config=Hs(e,this.browserEnvironment);let t;try{t=window[B.SessionStorage]}catch{}const n=t==null?void 0:t.getItem(ds),o=(c=t==null?void 0:t.getItem(us))==null?void 0:c.toLowerCase(),r=o==="true"?!0:o==="false"?!1:void 0,i={...this.config.system.loggerOptions},s=n&&Object.keys(exports.LogLevel).includes(n)?exports.LogLevel[n]:void 0;s&&(i.loggerCallback=bn.loggerCallback,i.logLevel=s),r!==void 0&&(i.piiLoggingEnabled=r),this.logger=new he(i,Ds,ye),this.available=!1}getConfig(){return this.config}getLogger(){return this.logger}isAvailable(){return this.available}isBrowserEnvironment(){return this.browserEnvironment}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Te extends bn{getModuleName(){return Te.MODULE_NAME}getId(){return Te.ID}async initialize(){return this.available=typeof window<"u",this.available}}Te.MODULE_NAME="";Te.ID="StandardOperatingContext";/*! @azure/msal-browser v3.30.0 2025-08-05 */function ne(a){return new TextDecoder().decode(Ks(a))}function Ks(a){let e=a.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:e+="==";break;case 3:e+="=";break;default:throw C(Kr)}const t=atob(e);return Uint8Array.from(t,n=>n.codePointAt(0)||0)}/*! @azure/msal-browser v3.30.0 2025-08-05 */class xs{constructor(){this.dbName=$t,this.version=cs,this.tableName=hs,this.dbOpen=!1}async open(){return new Promise((e,t)=>{const n=window.indexedDB.open(this.dbName,this.version);n.addEventListener("upgradeneeded",o=>{o.target.result.createObjectStore(this.tableName)}),n.addEventListener("success",o=>{const r=o;this.db=r.target.result,this.dbOpen=!0,e()}),n.addEventListener("error",()=>t(C(En)))})}closeConnection(){const e=this.db;e&&this.dbOpen&&(e.close(),this.dbOpen=!1)}async validateDbIsOpen(){if(!this.dbOpen)return this.open()}async getItem(e){return await this.validateDbIsOpen(),new Promise((t,n)=>{if(!this.db)return n(C(we));const i=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).get(e);i.addEventListener("success",s=>{const c=s;this.closeConnection(),t(c.target.result)}),i.addEventListener("error",s=>{this.closeConnection(),n(s)})})}async setItem(e,t){return await this.validateDbIsOpen(),new Promise((n,o)=>{if(!this.db)return o(C(we));const s=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).put(t,e);s.addEventListener("success",()=>{this.closeConnection(),n()}),s.addEventListener("error",c=>{this.closeConnection(),o(c)})})}async removeItem(e){return await this.validateDbIsOpen(),new Promise((t,n)=>{if(!this.db)return n(C(we));const i=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).delete(e);i.addEventListener("success",()=>{this.closeConnection(),t()}),i.addEventListener("error",s=>{this.closeConnection(),n(s)})})}async getKeys(){return await this.validateDbIsOpen(),new Promise((e,t)=>{if(!this.db)return t(C(we));const r=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).getAllKeys();r.addEventListener("success",i=>{const s=i;this.closeConnection(),e(s.target.result)}),r.addEventListener("error",i=>{this.closeConnection(),t(i)})})}async containsKey(e){return await this.validateDbIsOpen(),new Promise((t,n)=>{if(!this.db)return n(C(we));const i=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).count(e);i.addEventListener("success",s=>{const c=s;this.closeConnection(),t(c.target.result===1)}),i.addEventListener("error",s=>{this.closeConnection(),n(s)})})}async deleteDatabase(){return this.db&&this.dbOpen&&this.closeConnection(),new Promise((e,t)=>{const n=window.indexedDB.deleteDatabase($t),o=setTimeout(()=>t(!1),200);n.addEventListener("success",()=>(clearTimeout(o),e(!0))),n.addEventListener("blocked",()=>(clearTimeout(o),e(!0))),n.addEventListener("error",()=>(clearTimeout(o),t(!1)))})}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class gt{constructor(){this.cache=new Map}getItem(e){return this.cache.get(e)||null}setItem(e,t){this.cache.set(e,t)}removeItem(e){this.cache.delete(e)}getKeys(){const e=[];return this.cache.forEach((t,n)=>{e.push(n)}),e}containsKey(e){return this.cache.has(e)}clear(){this.cache.clear()}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Bs{constructor(e){this.inMemoryCache=new gt,this.indexedDBCache=new xs,this.logger=e}handleDatabaseAccessError(e){if(e instanceof De&&e.errorCode===En)this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.");else throw e}async getItem(e){const t=this.inMemoryCache.getItem(e);if(!t)try{return this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.getItem(e)}catch(n){this.handleDatabaseAccessError(n)}return t}async setItem(e,t){this.inMemoryCache.setItem(e,t);try{await this.indexedDBCache.setItem(e,t)}catch(n){this.handleDatabaseAccessError(n)}}async removeItem(e){this.inMemoryCache.removeItem(e);try{await this.indexedDBCache.removeItem(e)}catch(t){this.handleDatabaseAccessError(t)}}async getKeys(){const e=this.inMemoryCache.getKeys();if(e.length===0)try{return this.logger.verbose("In-memory cache is empty, now querying persistent storage."),await this.indexedDBCache.getKeys()}catch(t){this.handleDatabaseAccessError(t)}return e}async containsKey(e){const t=this.inMemoryCache.containsKey(e);if(!t)try{return this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.containsKey(e)}catch(n){this.handleDatabaseAccessError(n)}return t}clearInMemory(){this.logger.verbose("Deleting in-memory keystore"),this.inMemoryCache.clear(),this.logger.verbose("In-memory keystore deleted")}async clearPersistent(){try{this.logger.verbose("Deleting persistent keystore");const e=await this.indexedDBCache.deleteDatabase();return e&&this.logger.verbose("Persistent keystore deleted"),e}catch(e){return this.handleDatabaseAccessError(e),!1}}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ue{constructor(e,t,n){this.logger=e,Cs(n??!1),this.cache=new Bs(this.logger),this.performanceClient=t}createNewGuid(){return re()}base64Encode(e){return vn(e)}base64Decode(e){return ne(e)}base64UrlEncode(e){return tt(e)}encodeKid(e){return this.base64UrlEncode(JSON.stringify({kid:e}))}async getPublicKeyThumbprint(e){var d;const t=(d=this.performanceClient)==null?void 0:d.startMeasurement(l.CryptoOptsGetPublicKeyThumbprint,e.correlationId),n=await Ts(Ue.EXTRACTABLE,Ue.POP_KEY_USAGES),o=await Pt(n.publicKey),r={e:o.e,kty:o.kty,n:o.n},i=go(r),s=await this.hashString(i),c=await Pt(n.privateKey),h=await Is(c,!1,["sign"]);return await this.cache.setItem(s,{privateKey:h,publicKey:n.publicKey,requestMethod:e.resourceRequestMethod,requestUri:e.resourceRequestUri}),t&&t.end({success:!0}),s}async removeTokenBindingKey(e){return await this.cache.removeItem(e),!await this.cache.containsKey(e)}async clearKeystore(){this.cache.clearInMemory();try{return await this.cache.clearPersistent(),!0}catch(e){return e instanceof Error?this.logger.error(`Clearing keystore failed with error: ${e.message}`):this.logger.error("Clearing keystore failed with unknown error"),!1}}async signJwt(e,t,n,o){var de;const r=(de=this.performanceClient)==null?void 0:de.startMeasurement(l.CryptoOptsSignJwt,o),i=await this.cache.getItem(t);if(!i)throw C(An);const s=await Pt(i.publicKey),c=go(s),h=tt(JSON.stringify({kid:t})),d=Cn.getShrHeaderString({...n==null?void 0:n.header,alg:s.alg,kid:h}),g=tt(d);e.cnf={jwk:JSON.parse(c)};const f=tt(JSON.stringify(e)),y=`${g}.${f}`,v=new TextEncoder().encode(y),N=await As(i.privateKey,v),F=wt(new Uint8Array(N)),L=`${y}.${F}`;return r&&r.end({success:!0}),L}async hashString(e){return Vr(e)}}Ue.POP_KEY_USAGES=["sign","verify"];Ue.EXTRACTABLE=!0;function go(a){return JSON.stringify(a,Object.keys(a).sort())}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Jr{constructor(){if(!window.localStorage)throw _n(wn)}getItem(e){return window.localStorage.getItem(e)}setItem(e,t){window.localStorage.setItem(e,t)}removeItem(e){window.localStorage.removeItem(e)}getKeys(){return Object.keys(window.localStorage)}containsKey(e){return window.localStorage.hasOwnProperty(e)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Xr{constructor(){if(!window.sessionStorage)throw _n(wn)}getItem(e){return window.sessionStorage.getItem(e)}setItem(e,t){window.sessionStorage.setItem(e,t)}removeItem(e){window.sessionStorage.removeItem(e)}getKeys(){return Object.keys(window.sessionStorage)}containsKey(e){return window.sessionStorage.hasOwnProperty(e)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */function Zr(a,e){if(!e)return null;try{return j.parseRequestState(a,e).libraryState.meta}catch{throw p(Pe)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const Fs=24*60*60*1e3;class Gs{getItem(e){const t=`${encodeURIComponent(e)}`,n=document.cookie.split(";");for(let o=0;o<n.length;o++){const r=n[o],[i,...s]=decodeURIComponent(r).trim().split("="),c=s.join("=");if(i===t)return c}return""}setItem(e,t,n,o=!0){let r=`${encodeURIComponent(e)}=${encodeURIComponent(t)};path=/;SameSite=Lax;`;if(n){const i=qs(n);r+=`expires=${i};`}o&&(r+="Secure;"),document.cookie=r}removeItem(e){this.setItem(e,"",-1)}getKeys(){const e=document.cookie.split(";"),t=[];return e.forEach(n=>{const o=decodeURIComponent(n).trim().split("=");t.push(o[0])}),t}containsKey(e){return this.getKeys().includes(e)}}function qs(a){const e=new Date;return new Date(e.getTime()+a*Fs).toUTCString()}/*! @azure/msal-browser v3.30.0 2025-08-05 */class zt extends Me{constructor(e,t,n,o,r,i){super(e,n,o,r),this.cacheConfig=t,this.logger=o,this.internalStorage=new gt,this.browserStorage=this.setupBrowserStorage(this.cacheConfig.cacheLocation),this.temporaryCacheStorage=this.setupBrowserStorage(this.cacheConfig.temporaryCacheLocation),this.cookieStorage=new Gs,t.cacheMigrationEnabled&&(this.migrateCacheEntries(),this.createKeyMaps()),this.performanceClient=i}setupBrowserStorage(e){try{switch(e){case B.LocalStorage:return new Jr;case B.SessionStorage:return new Xr;case B.MemoryStorage:default:break}}catch(t){this.logger.error(t)}return this.cacheConfig.cacheLocation=B.MemoryStorage,new gt}migrateCacheEntries(){const e=this.browserStorage.getItem(V.VERSION);e&&this.logger.info(`MSAL.js was last initialized with version ${e}`),e!==ye&&this.browserStorage.setItem(V.VERSION,ye);const t=`${u.CACHE_PREFIX}.${U.ID_TOKEN}`,n=`${u.CACHE_PREFIX}.${U.CLIENT_INFO}`,o=`${u.CACHE_PREFIX}.${U.ERROR}`,r=`${u.CACHE_PREFIX}.${U.ERROR_DESC}`,i=this.browserStorage.getItem(t),s=this.browserStorage.getItem(n),c=this.browserStorage.getItem(o),h=this.browserStorage.getItem(r),d=[i,s,c,h];[U.ID_TOKEN,U.CLIENT_INFO,U.ERROR,U.ERROR_DESC].forEach((f,y)=>{const A=d[y];A&&this.setTemporaryCache(f,A,!0)})}createKeyMaps(){this.logger.trace("BrowserCacheManager - createKeyMaps called.");const e=this.cryptoImpl.createNewGuid(),t=this.getItem(V.ACCOUNT_KEYS),n=this.getItem(`${V.TOKEN_KEYS}.${this.clientId}`);if(t&&n){this.logger.verbose("BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration.");return}this.browserStorage.getKeys().forEach(r=>{if(this.isCredentialKey(r)){const i=this.getItem(r);if(i){const s=this.validateAndParseJson(i);if(s&&s.hasOwnProperty("credentialType"))switch(s.credentialType){case I.ID_TOKEN:if(qn(s)){this.logger.trace("BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - idToken with key: ${r} found, saving key to token key map`);const c=s,h=this.updateCredentialCacheKey(r,c,e);this.addTokenKey(h,I.ID_TOKEN,e);return}else this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed idToken validation on key: ${r}`);break;case I.ACCESS_TOKEN:case I.ACCESS_TOKEN_WITH_AUTH_SCHEME:if(Gn(s)){this.logger.trace("BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - accessToken with key: ${r} found, saving key to token key map`);const c=s,h=this.updateCredentialCacheKey(r,c,e);this.addTokenKey(h,I.ACCESS_TOKEN,e);return}else this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed accessToken validation on key: ${r}`);break;case I.REFRESH_TOKEN:if($n(s)){this.logger.trace("BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - refreshToken with key: ${r} found, saving key to token key map`);const c=s,h=this.updateCredentialCacheKey(r,c,e);this.addTokenKey(h,I.REFRESH_TOKEN,e);return}else this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: ${r}`);break}}}if(this.isAccountKey(r)){const i=this.getItem(r);if(i){const s=this.validateAndParseJson(i);s&&P.isAccountEntity(s)&&(this.logger.trace("BrowserCacheManager:createKeyMaps - account found, saving key to account key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - account with key: ${r} found, saving key to account key map`),this.addAccountKeyToMap(r,e))}}})}validateAndParseJson(e){try{const t=JSON.parse(e);return t&&typeof t=="object"?t:null}catch{return null}}getItem(e){return this.browserStorage.getItem(e)}setItem(e,t,n){let o=[];for(let i=0;i<=20;i++)try{this.browserStorage.setItem(e,t),i>0&&this.removeAccessTokenKeys(o.slice(0,i),n);break}catch(s){const c=tr(s);if(c.errorCode===ln&&i<20){if(o.length||(e===`${V.TOKEN_KEYS}.${this.clientId}`?o=JSON.parse(t).accessToken:o=this.getTokenKeys().accessToken),o.length<=i)throw c;this.removeAccessToken(o[i],n,!1)}else throw c}}getAccount(e,t,n){this.logger.trace("BrowserCacheManager.getAccount called");const o=this.getCachedAccountEntity(e,t);return this.updateOutdatedCachedAccount(e,o,t,n)}getCachedAccountEntity(e,t){const n=this.getItem(e);if(!n)return this.removeAccountKeyFromMap(e,t),null;const o=this.validateAndParseJson(n);return!o||!P.isAccountEntity(o)?null:Me.toObject(new P,o)}setAccount(e,t){this.logger.trace("BrowserCacheManager.setAccount called");const n=e.generateAccountKey();e.lastUpdatedAt=Date.now().toString(),this.setItem(n,JSON.stringify(e),t),this.addAccountKeyToMap(n,t)}getAccountKeys(){this.logger.trace("BrowserCacheManager.getAccountKeys called");const e=this.getItem(V.ACCOUNT_KEYS);return e?JSON.parse(e):(this.logger.verbose("BrowserCacheManager.getAccountKeys - No account keys found"),[])}addAccountKeyToMap(e,t){this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"),this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${e}`);const n=this.getAccountKeys();n.indexOf(e)===-1?(n.push(e),this.setItem(V.ACCOUNT_KEYS,JSON.stringify(n),t),this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added")):this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map")}removeAccountKeyFromMap(e,t){this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"),this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${e}`);const n=this.getAccountKeys(),o=n.indexOf(e);if(o>-1){if(n.splice(o,1),n.length===0){this.removeItem(V.ACCOUNT_KEYS);return}else this.setItem(V.ACCOUNT_KEYS,JSON.stringify(n),t);this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed")}else this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map")}async removeAccount(e,t){super.removeAccount(e,t),this.removeAccountKeyFromMap(e,t)}removeOutdatedAccount(e,t){this.removeItem(e),this.removeAccountKeyFromMap(e,t)}removeIdToken(e,t){super.removeIdToken(e,t),this.removeTokenKey(e,I.ID_TOKEN,t)}removeAccessToken(e,t,n=!0){var o;super.removeAccessToken(e,t),(o=this.performanceClient)==null||o.incrementFields({accessTokensRemoved:1},t),n&&this.removeTokenKey(e,I.ACCESS_TOKEN,t)}removeAccessTokenKeys(e,t){this.logger.trace("removeAccessTokenKey called");const n=this.getTokenKeys();let o=0;if(e.forEach(r=>{const i=n.accessToken.indexOf(r);i>-1&&(n.accessToken.splice(i,1),o++)}),o>0){this.logger.info(`removed ${o} accessToken keys from tokenKeys map`),this.setTokenKeys(n,t);return}}removeRefreshToken(e,t){super.removeRefreshToken(e,t),this.removeTokenKey(e,I.REFRESH_TOKEN,t)}getTokenKeys(){this.logger.trace("BrowserCacheManager.getTokenKeys called");const e=this.getItem(`${V.TOKEN_KEYS}.${this.clientId}`);if(e){const t=this.validateAndParseJson(e);if(t&&t.hasOwnProperty("idToken")&&t.hasOwnProperty("accessToken")&&t.hasOwnProperty("refreshToken"))return t;this.logger.error("BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.")}else this.logger.verbose("BrowserCacheManager.getTokenKeys - No token keys found");return{idToken:[],accessToken:[],refreshToken:[]}}setTokenKeys(e,t){if(e.idToken.length===0&&e.accessToken.length===0&&e.refreshToken.length===0){this.removeItem(`${V.TOKEN_KEYS}.${this.clientId}`);return}else this.setItem(`${V.TOKEN_KEYS}.${this.clientId}`,JSON.stringify(e),t)}addTokenKey(e,t,n){this.logger.trace("BrowserCacheManager addTokenKey called");const o=this.getTokenKeys();switch(t){case I.ID_TOKEN:o.idToken.indexOf(e)===-1&&(this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"),o.idToken.push(e));break;case I.ACCESS_TOKEN:const r=o.accessToken.indexOf(e);r!==-1&&o.accessToken.splice(r,1),this.logger.trace(`access token ${r===-1?"added to":"updated in"} map`),o.accessToken.push(e);break;case I.REFRESH_TOKEN:o.refreshToken.indexOf(e)===-1&&(this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"),o.refreshToken.push(e));break;default:throw this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${t}`),p(Ht)}this.setTokenKeys(o,n)}removeTokenKey(e,t,n,o=this.getTokenKeys()){switch(this.logger.trace("BrowserCacheManager removeTokenKey called"),t){case I.ID_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${e} from map`);const r=o.idToken.indexOf(e);r>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"),o.idToken.splice(r,1)):this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");break;case I.ACCESS_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${e} from map`);const i=o.accessToken.indexOf(e);i>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"),o.accessToken.splice(i,1)):this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");break;case I.REFRESH_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${e} from map`);const s=o.refreshToken.indexOf(e);s>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"),o.refreshToken.splice(s,1)):this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");break;default:throw this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${t}`),p(Ht)}this.setTokenKeys(o,n)}getIdTokenCredential(e,t){const n=this.getItem(e);if(!n)return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeIdToken(e,t),null;const o=this.validateAndParseJson(n);return!o||!qn(o)?(this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"),o)}setIdTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setIdTokenCredential called");const n=ke(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(n,JSON.stringify(e),t),this.addTokenKey(n,I.ID_TOKEN,t)}getAccessTokenCredential(e,t){const n=this.getItem(e);if(!n)return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,I.ACCESS_TOKEN,t),null;const o=this.validateAndParseJson(n);return!o||!Gn(o)?(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"),o)}setAccessTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");const n=ke(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(n,JSON.stringify(e),t),this.addTokenKey(n,I.ACCESS_TOKEN,t)}getRefreshTokenCredential(e,t){const n=this.getItem(e);if(!n)return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,I.REFRESH_TOKEN,t),null;const o=this.validateAndParseJson(n);return!o||!$n(o)?(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"),o)}setRefreshTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");const n=ke(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(n,JSON.stringify(e),t),this.addTokenKey(n,I.REFRESH_TOKEN,t)}getAppMetadata(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null;const n=this.validateAndParseJson(t);return!n||!Ui(e,n)?(this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"),n)}setAppMetadata(e,t){this.logger.trace("BrowserCacheManager.setAppMetadata called");const n=Li(e);this.setItem(n,JSON.stringify(e),t)}getServerTelemetry(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null;const n=this.validateAndParseJson(t);return!n||!Pi(e,n)?(this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"),n)}setServerTelemetry(e,t,n){this.logger.trace("BrowserCacheManager.setServerTelemetry called"),this.setItem(e,JSON.stringify(t),n)}getAuthorityMetadata(e){const t=this.internalStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"),null;const n=this.validateAndParseJson(t);return n&&Hi(e,n)?(this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"),n):null}getAuthorityMetadataKeys(){return this.internalStorage.getKeys().filter(t=>this.isAuthorityMetadata(t))}setWrapperMetadata(e,t){this.internalStorage.setItem(et.WRAPPER_SKU,e),this.internalStorage.setItem(et.WRAPPER_VER,t)}getWrapperMetadata(){const e=this.internalStorage.getItem(et.WRAPPER_SKU)||u.EMPTY_STRING,t=this.internalStorage.getItem(et.WRAPPER_VER)||u.EMPTY_STRING;return[e,t]}setAuthorityMetadata(e,t){this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))}getActiveAccount(e){const t=this.generateCacheKey(U.ACTIVE_ACCOUNT_FILTERS),n=this.getItem(t);if(!n){this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema");const r=this.generateCacheKey(U.ACTIVE_ACCOUNT),i=this.getItem(r);if(!i)return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null;const s=this.getAccountInfoFilteredBy({localAccountId:i},e);return s?(this.logger.trace("BrowserCacheManager.getActiveAccount: Legacy active account cache schema found"),this.logger.trace("BrowserCacheManager.getActiveAccount: Adding active account filters cache schema"),this.setActiveAccount(s,e),s):null}const o=this.validateAndParseJson(n);return o?(this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"),this.getAccountInfoFilteredBy({homeAccountId:o.homeAccountId,localAccountId:o.localAccountId,tenantId:o.tenantId},e)):(this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null)}setActiveAccount(e,t){const n=this.generateCacheKey(U.ACTIVE_ACCOUNT_FILTERS),o=this.generateCacheKey(U.ACTIVE_ACCOUNT);if(e){this.logger.verbose("setActiveAccount: Active account set");const r={homeAccountId:e.homeAccountId,localAccountId:e.localAccountId,tenantId:e.tenantId,lastUpdatedAt:Date.now().toString()};this.setItem(n,JSON.stringify(r),t),this.setItem(o,e.localAccountId,t)}else this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.browserStorage.removeItem(n),this.browserStorage.removeItem(o)}getThrottlingCache(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null;const n=this.validateAndParseJson(t);return!n||!Mi(e,n)?(this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null):(this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"),n)}setThrottlingCache(e,t,n){this.logger.trace("BrowserCacheManager.setThrottlingCache called"),this.setItem(e,JSON.stringify(t),n)}getTemporaryCache(e,t){const n=t?this.generateCacheKey(e):e;if(this.cacheConfig.storeAuthStateInCookie){const r=this.cookieStorage.getItem(n);if(r)return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"),r}const o=this.temporaryCacheStorage.getItem(n);if(!o){if(this.cacheConfig.cacheLocation===B.LocalStorage){const r=this.browserStorage.getItem(n);if(r)return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"),r}return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"),null}return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"),o}setTemporaryCache(e,t,n){const o=n?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(o,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.cookieStorage.setItem(o,t,void 0,this.cacheConfig.secureCookies))}removeItem(e){this.browserStorage.removeItem(e)}removeTemporaryItem(e){this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.cookieStorage.removeItem(e))}getKeys(){return this.browserStorage.getKeys()}async clear(e){await this.removeAllAccounts(e),this.removeAppMetadata(e),this.temporaryCacheStorage.getKeys().forEach(t=>{(t.indexOf(u.CACHE_PREFIX)!==-1||t.indexOf(this.clientId)!==-1)&&this.removeTemporaryItem(t)}),this.browserStorage.getKeys().forEach(t=>{(t.indexOf(u.CACHE_PREFIX)!==-1||t.indexOf(this.clientId)!==-1)&&this.browserStorage.removeItem(t)}),this.internalStorage.clear()}async clearTokensAndKeysWithClaims(e,t){e.addQueueMeasurement(l.ClearTokensAndKeysWithClaims,t);const n=this.getTokenKeys();let o=0;n.accessToken.forEach(r=>{const i=this.getAccessTokenCredential(r,t);i!=null&&i.requestedClaimsHash&&r.includes(i.requestedClaimsHash.toLowerCase())&&(this.removeAccessToken(r,t),o++)}),o>0&&this.logger.warning(`${o} access tokens with claims in the cache keys have been removed from the cache.`)}generateCacheKey(e){return this.validateAndParseJson(e)?JSON.stringify(e):Q.startsWith(e,u.CACHE_PREFIX)||Q.startsWith(e,U.ADAL_ID_TOKEN)?e:`${u.CACHE_PREFIX}.${this.clientId}.${e}`}generateAuthorityKey(e){const{libraryState:{id:t}}=j.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${w.AUTHORITY}.${t}`)}generateNonceKey(e){const{libraryState:{id:t}}=j.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${w.NONCE_IDTOKEN}.${t}`)}generateStateKey(e){const{libraryState:{id:t}}=j.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${w.REQUEST_STATE}.${t}`)}getCachedAuthority(e){const t=this.generateStateKey(e),n=this.getTemporaryCache(t);if(!n)return null;const o=this.generateAuthorityKey(n);return this.getTemporaryCache(o)}updateCacheEntries(e,t,n,o,r){this.logger.trace("BrowserCacheManager.updateCacheEntries called");const i=this.generateStateKey(e);this.setTemporaryCache(i,e,!1);const s=this.generateNonceKey(e);this.setTemporaryCache(s,t,!1);const c=this.generateAuthorityKey(e);if(this.setTemporaryCache(c,n,!1),r){const h={credential:r.homeAccountId,type:z.HOME_ACCOUNT_ID};this.setTemporaryCache(w.CCS_CREDENTIAL,JSON.stringify(h),!0)}else if(o){const h={credential:o,type:z.UPN};this.setTemporaryCache(w.CCS_CREDENTIAL,JSON.stringify(h),!0)}}resetRequestCache(e){this.logger.trace("BrowserCacheManager.resetRequestCache called"),e&&(this.temporaryCacheStorage.getKeys().forEach(t=>{t.indexOf(e)!==-1&&this.removeTemporaryItem(t)}),this.removeTemporaryItem(this.generateStateKey(e)),this.removeTemporaryItem(this.generateNonceKey(e)),this.removeTemporaryItem(this.generateAuthorityKey(e))),this.removeTemporaryItem(this.generateCacheKey(w.REQUEST_PARAMS)),this.removeTemporaryItem(this.generateCacheKey(w.ORIGIN_URI)),this.removeTemporaryItem(this.generateCacheKey(w.URL_HASH)),this.removeTemporaryItem(this.generateCacheKey(w.CORRELATION_ID)),this.removeTemporaryItem(this.generateCacheKey(w.CCS_CREDENTIAL)),this.removeTemporaryItem(this.generateCacheKey(w.NATIVE_REQUEST)),this.setInteractionInProgress(!1)}cleanRequestByState(e){if(this.logger.trace("BrowserCacheManager.cleanRequestByState called"),e){const t=this.generateStateKey(e),n=this.temporaryCacheStorage.getItem(t);this.logger.infoPii(`BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: ${n}`),this.resetRequestCache(n||u.EMPTY_STRING)}}cleanRequestByInteractionType(e){this.logger.trace("BrowserCacheManager.cleanRequestByInteractionType called"),this.temporaryCacheStorage.getKeys().forEach(t=>{if(t.indexOf(w.REQUEST_STATE)===-1)return;const n=this.temporaryCacheStorage.getItem(t);if(!n)return;const o=Zr(this.cryptoImpl,n);o&&o.interactionType===e&&(this.logger.infoPii(`BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: ${n}`),this.resetRequestCache(n))}),this.setInteractionInProgress(!1)}cacheCodeRequest(e){this.logger.trace("BrowserCacheManager.cacheCodeRequest called");const t=vn(JSON.stringify(e));this.setTemporaryCache(w.REQUEST_PARAMS,t,!0)}getCachedRequest(e){this.logger.trace("BrowserCacheManager.getCachedRequest called");const t=this.getTemporaryCache(w.REQUEST_PARAMS,!0);if(!t)throw C(wr);let n;try{n=JSON.parse(ne(t))}catch(o){throw this.logger.errorPii(`Attempted to parse: ${t}`),this.logger.error(`Parsing cached token request threw with error: ${o}`),C(_r)}if(this.removeTemporaryItem(this.generateCacheKey(w.REQUEST_PARAMS)),!n.authority){const o=this.generateAuthorityKey(e),r=this.getTemporaryCache(o);if(!r)throw C(In);n.authority=r}return n}getCachedNativeRequest(){this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");const e=this.getTemporaryCache(w.NATIVE_REQUEST,!0);if(!e)return this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"),null;const t=this.validateAndParseJson(e);return t||(this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"),null)}isInteractionInProgress(e){const t=this.getInteractionInProgress();return e?t===this.clientId:!!t}getInteractionInProgress(){const e=`${u.CACHE_PREFIX}.${w.INTERACTION_STATUS_KEY}`;return this.getTemporaryCache(e,!1)}setInteractionInProgress(e){const t=`${u.CACHE_PREFIX}.${w.INTERACTION_STATUS_KEY}`;if(e){if(this.getInteractionInProgress())throw C(Cr);this.setTemporaryCache(t,this.clientId,!1)}else!e&&this.getInteractionInProgress()===this.clientId&&this.removeTemporaryItem(t)}getLegacyLoginHint(){const e=this.getTemporaryCache(U.ADAL_ID_TOKEN);e&&(this.browserStorage.removeItem(U.ADAL_ID_TOKEN),this.logger.verbose("Cached ADAL id token retrieved."));const t=this.getTemporaryCache(U.ID_TOKEN,!0);t&&(this.browserStorage.removeItem(this.generateCacheKey(U.ID_TOKEN)),this.logger.verbose("Cached MSAL.js v1 id token retrieved"));const n=t||e;if(n){const o=Ae(n,ne);if(o.preferred_username)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint"),o.preferred_username;if(o.upn)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint"),o.upn;this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.")}return null}updateCredentialCacheKey(e,t,n){const o=ke(t);if(e!==o){const r=this.getItem(e);if(r)return this.browserStorage.removeItem(e),this.setItem(o,r,n),this.logger.verbose(`Updated an outdated ${t.credentialType} cache key`),o;this.logger.error(`Attempted to update an outdated ${t.credentialType} cache key but no item matching the outdated key was found in storage`)}return e}async hydrateCache(e,t){var s,c,h;const n=mt((s=e.account)==null?void 0:s.homeAccountId,(c=e.account)==null?void 0:c.environment,e.idToken,this.clientId,e.tenantId);let o;t.claims&&(o=await this.cryptoImpl.hashString(t.claims));const r=ft((h=e.account)==null?void 0:h.homeAccountId,e.account.environment,e.accessToken,this.clientId,e.tenantId,e.scopes.join(" "),e.expiresOn?e.expiresOn.getTime()/1e3:0,e.extExpiresOn?e.extExpiresOn.getTime()/1e3:0,ne,void 0,e.tokenType,void 0,t.sshKid,t.claims,o),i={idToken:n,accessToken:r};return this.saveCacheRecord(i,e.correlationId)}async saveCacheRecord(e,t,n){try{await super.saveCacheRecord(e,t,n)}catch(o){if(o instanceof Oe&&this.performanceClient&&t)try{const r=this.getTokenKeys();this.performanceClient.addFields({cacheRtCount:r.refreshToken.length,cacheIdCount:r.idToken.length,cacheAtCount:r.accessToken.length},t)}catch{}throw o}}}const $s=(a,e)=>{const t={cacheLocation:B.MemoryStorage,temporaryCacheLocation:B.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1};return new zt(a,t,it,e)};/*! @azure/msal-browser v3.30.0 2025-08-05 */function zs(a,e,t,n,o){return a.verbose("getAllAccounts called"),t?e.getAllAccounts(n,o):[]}function Vs(a,e,t,n){if(e.trace("getAccount called"),Object.keys(a).length===0)return e.warning("getAccount: No accountFilter provided"),null;const o=t.getAccountInfoFilteredBy(a,n);return o?(e.verbose("getAccount: Account matching provided filter found, returning"),o):(e.verbose("getAccount: No matching account found, returning null"),null)}function Qs(a,e,t,n){if(e.trace("getAccountByUsername called"),!a)return e.warning("getAccountByUsername: No username provided"),null;const o=t.getAccountInfoFilteredBy({username:a},n);return o?(e.verbose("getAccountByUsername: Account matching username found, returning"),e.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${a}`),o):(e.verbose("getAccountByUsername: No matching account found, returning null"),null)}function Ys(a,e,t,n){if(e.trace("getAccountByHomeId called"),!a)return e.warning("getAccountByHomeId: No homeAccountId provided"),null;const o=t.getAccountInfoFilteredBy({homeAccountId:a},n);return o?(e.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"),e.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${a}`),o):(e.verbose("getAccountByHomeId: No matching account found, returning null"),null)}function Ws(a,e,t,n){if(e.trace("getAccountByLocalId called"),!a)return e.warning("getAccountByLocalId: No localAccountId provided"),null;const o=t.getAccountInfoFilteredBy({localAccountId:a},n);return o?(e.verbose("getAccountByLocalId: Account matching localAccountId found, returning"),e.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${a}`),o):(e.verbose("getAccountByLocalId: No matching account found, returning null"),null)}function js(a,e,t){e.setActiveAccount(a,t)}function Js(a,e){return a.getActiveAccount(e)}/*! @azure/msal-browser v3.30.0 2025-08-05 */const T={INITIALIZE_START:"msal:initializeStart",INITIALIZE_END:"msal:initializeEnd",ACCOUNT_ADDED:"msal:accountAdded",ACCOUNT_REMOVED:"msal:accountRemoved",ACTIVE_ACCOUNT_CHANGED:"msal:activeAccountChanged",LOGIN_START:"msal:loginStart",LOGIN_SUCCESS:"msal:loginSuccess",LOGIN_FAILURE:"msal:loginFailure",ACQUIRE_TOKEN_START:"msal:acquireTokenStart",ACQUIRE_TOKEN_SUCCESS:"msal:acquireTokenSuccess",ACQUIRE_TOKEN_FAILURE:"msal:acquireTokenFailure",ACQUIRE_TOKEN_NETWORK_START:"msal:acquireTokenFromNetworkStart",SSO_SILENT_START:"msal:ssoSilentStart",SSO_SILENT_SUCCESS:"msal:ssoSilentSuccess",SSO_SILENT_FAILURE:"msal:ssoSilentFailure",ACQUIRE_TOKEN_BY_CODE_START:"msal:acquireTokenByCodeStart",ACQUIRE_TOKEN_BY_CODE_SUCCESS:"msal:acquireTokenByCodeSuccess",ACQUIRE_TOKEN_BY_CODE_FAILURE:"msal:acquireTokenByCodeFailure",HANDLE_REDIRECT_START:"msal:handleRedirectStart",HANDLE_REDIRECT_END:"msal:handleRedirectEnd",POPUP_OPENED:"msal:popupOpened",LOGOUT_START:"msal:logoutStart",LOGOUT_SUCCESS:"msal:logoutSuccess",LOGOUT_FAILURE:"msal:logoutFailure",LOGOUT_END:"msal:logoutEnd",RESTORE_FROM_BFCACHE:"msal:restoreFromBFCache"};/*! @azure/msal-browser v3.30.0 2025-08-05 */class ei{constructor(e){this.eventCallbacks=new Map,this.logger=e||new he({})}addEventCallback(e,t,n){if(typeof window<"u"){const o=n||Ns();return this.eventCallbacks.has(o)?(this.logger.error(`Event callback with id: ${o} is already registered. Please provide a unique id or remove the existing callback and try again.`),null):(this.eventCallbacks.set(o,[e,t||[]]),this.logger.verbose(`Event callback registered with id: ${o}`),o)}return null}removeEventCallback(e){this.eventCallbacks.delete(e),this.logger.verbose(`Event callback ${e} removed.`)}emitEvent(e,t,n,o){if(typeof window<"u"){const r={eventType:e,interactionType:t||null,payload:n||null,error:o||null,timestamp:Date.now()};this.eventCallbacks.forEach(([i,s],c)=>{(s.length===0||s.includes(e))&&(this.logger.verbose(`Emitting event to callback ${c}: ${e}`),i.apply(null,[r]))})}}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class ti{constructor(e,t,n,o,r,i,s,c,h){this.config=e,this.browserStorage=t,this.browserCrypto=n,this.networkClient=this.config.system.networkClient,this.eventHandler=r,this.navigationClient=i,this.nativeMessageHandler=c,this.correlationId=h||re(),this.logger=o.clone($.MSAL_SKU,ye,this.correlationId),this.performanceClient=s}async clearCacheOnLogout(e){if(e){P.accountInfoIsEqual(e,this.browserStorage.getActiveAccount(this.correlationId),!1)&&(this.logger.verbose("Setting active account to null"),this.browserStorage.setActiveAccount(null,this.correlationId));try{await this.browserStorage.removeAccount(P.generateAccountCacheKey(e),this.correlationId),this.logger.verbose("Cleared cache items belonging to the account provided in the logout request.")}catch{this.logger.error("Account provided in logout request was not found. Local cache unchanged.")}}else try{this.logger.verbose("No account provided in logout request, clearing all cache items.",this.correlationId),await this.browserStorage.clear(this.correlationId),await this.browserCrypto.clearKeystore()}catch{this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged.")}}getRedirectUri(e){this.logger.verbose("getRedirectUri called");const t=e||this.config.auth.redirectUri;return S.getAbsoluteUrl(t,ce())}initializeServerTelemetryManager(e,t){this.logger.verbose("initializeServerTelemetryManager called");const n={clientId:this.config.auth.clientId,correlationId:this.correlationId,apiId:e,forceRefresh:t||!1,wrapperSKU:this.browserStorage.getWrapperMetadata()[0],wrapperVer:this.browserStorage.getWrapperMetadata()[1]};return new qe(n,this.browserStorage)}async getDiscoveredAuthority(e){const{account:t}=e,n=e.requestExtraQueryParameters&&e.requestExtraQueryParameters.hasOwnProperty("instance_aware")?e.requestExtraQueryParameters.instance_aware:void 0;this.performanceClient.addQueueMeasurement(l.StandardInteractionClientGetDiscoveredAuthority,this.correlationId);const o={protocolMode:this.config.auth.protocolMode,OIDCOptions:this.config.auth.OIDCOptions,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},r=e.requestAuthority||this.config.auth.authority,i=n!=null&&n.length?n==="true":this.config.auth.instanceAware,s=t&&i?this.config.auth.authority.replace(S.getDomainFromUrl(r),t.environment):r,c=D.generateAuthority(s,e.requestAzureCloudOptions||this.config.auth.azureCloudOptions),h=await m(ir,l.AuthorityFactoryCreateDiscoveredInstance,this.logger,this.performanceClient,this.correlationId)(c,this.config.system.networkClient,this.browserStorage,o,this.logger,this.correlationId,this.performanceClient);if(t&&!h.isAlias(t.environment))throw k(jo);return h}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const Xs=32;async function Zs(a,e,t){a.addQueueMeasurement(l.GeneratePkceCodes,t);const n=Ee(ec,l.GenerateCodeVerifier,e,a,t)(a,e,t),o=await m(tc,l.GenerateCodeChallengeFromVerifier,e,a,t)(n,a,e,t);return{verifier:n,challenge:o}}function ec(a,e,t){try{const n=new Uint8Array(Xs);return Ee(ys,l.GetRandomValues,e,a,t)(n),wt(n)}catch{throw C(yn)}}async function tc(a,e,t,n){e.addQueueMeasurement(l.GenerateCodeChallengeFromVerifier,n);try{const o=await m(zr,l.Sha256Digest,t,e,n)(a,e,n);return wt(new Uint8Array(o))}catch{throw C(yn)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */async function On(a,e,t,n){t.addQueueMeasurement(l.InitializeBaseRequest,a.correlationId);const o=a.authority||e.auth.authority,r=[...a&&a.scopes||[]],i={...a,correlationId:a.correlationId,authority:o,scopes:r};if(!i.authenticationScheme)i.authenticationScheme=_.BEARER,n.verbose(`Authentication Scheme wasn't explicitly set in request, defaulting to "Bearer" request`);else{if(i.authenticationScheme===_.SSH){if(!a.sshJwk)throw k(Ct);if(!a.sshKid)throw k(Qo)}n.verbose(`Authentication Scheme set to "${i.authenticationScheme}" as configured in Auth request`)}return e.cache.claimsBasedCachingEnabled&&a.claims&&!Q.isEmptyObj(a.claims)&&(i.requestedClaimsHash=await Vr(a.claims)),i}async function nc(a,e,t,n,o){n.addQueueMeasurement(l.InitializeSilentRequest,a.correlationId);const r=await m(On,l.InitializeBaseRequest,o,n,a.correlationId)(a,t,n,o);return{...a,...r,account:e,forceRefresh:a.forceRefresh||!1}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ke extends ti{async initializeAuthorizationCodeRequest(e){this.performanceClient.addQueueMeasurement(l.StandardInteractionClientInitializeAuthorizationCodeRequest,this.correlationId);const t=await m(Zs,l.GeneratePkceCodes,this.logger,this.performanceClient,this.correlationId)(this.performanceClient,this.logger,this.correlationId),n={...e,redirectUri:e.redirectUri,code:u.EMPTY_STRING,codeVerifier:t.verifier};return e.codeChallenge=t.challenge,e.codeChallengeMethod=u.S256_CODE_CHALLENGE_METHOD,n}initializeLogoutRequest(e){this.logger.verbose("initializeLogoutRequest called",e==null?void 0:e.correlationId);const t={correlationId:this.correlationId||re(),...e};if(e)if(e.logoutHint)this.logger.verbose("logoutHint has already been set in logoutRequest");else if(e.account){const n=this.getLogoutHintFromIdTokenClaims(e.account);n&&(this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"),t.logoutHint=n)}else this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set");else this.logger.verbose("logoutHint will not be set since no logout request was configured");return!e||e.postLogoutRedirectUri!==null?e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request",t.correlationId),t.postLogoutRedirectUri=S.getAbsoluteUrl(e.postLogoutRedirectUri,ce())):this.config.auth.postLogoutRedirectUri===null?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect",t.correlationId):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri",t.correlationId),t.postLogoutRedirectUri=S.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,ce())):(this.logger.verbose("Setting postLogoutRedirectUri to current page",t.correlationId),t.postLogoutRedirectUri=S.getAbsoluteUrl(ce(),ce())):this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri",t.correlationId),t}getLogoutHintFromIdTokenClaims(e){const t=e.idTokenClaims;if(t){if(t.login_hint)return t.login_hint;this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request")}else this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");return null}async createAuthCodeClient(e){this.performanceClient.addQueueMeasurement(l.StandardInteractionClientCreateAuthCodeClient,this.correlationId);const t=await m(this.getClientConfiguration.bind(this),l.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)(e);return new cr(t,this.performanceClient)}async getClientConfiguration(e){const{serverTelemetryManager:t,requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:r,account:i}=e;this.performanceClient.addQueueMeasurement(l.StandardInteractionClientGetClientConfiguration,this.correlationId);const s=await m(this.getDiscoveredAuthority.bind(this),l.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,this.correlationId)({requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:r,account:i}),c=this.config.system.loggerOptions;return{authOptions:{clientId:this.config.auth.clientId,authority:s,clientCapabilities:this.config.auth.clientCapabilities,redirectUri:this.config.auth.redirectUri},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds,preventCorsPreflight:!0},loggerOptions:{loggerCallback:c.loggerCallback,piiLoggingEnabled:c.piiLoggingEnabled,logLevel:c.logLevel,correlationId:this.correlationId},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:t,libraryInfo:{sku:$.MSAL_SKU,version:ye,cpu:u.EMPTY_STRING,os:u.EMPTY_STRING},telemetry:this.config.telemetry}}async initializeAuthorizationRequest(e,t){this.performanceClient.addQueueMeasurement(l.StandardInteractionClientInitializeAuthorizationRequest,this.correlationId);const n=this.getRedirectUri(e.redirectUri),o={interactionType:t},r=j.setRequestState(this.browserCrypto,e&&e.state||u.EMPTY_STRING,o),s={...await m(On,l.InitializeBaseRequest,this.logger,this.performanceClient,this.correlationId)({...e,correlationId:this.correlationId},this.config,this.performanceClient,this.logger),redirectUri:n,state:r,nonce:e.nonce||re(),responseMode:this.config.auth.OIDCOptions.serverResponseType};if(e.loginHint||e.sid)return s;const c=e.account||this.browserStorage.getActiveAccount(this.correlationId);if(c&&(this.logger.verbose("Setting validated request account",this.correlationId),this.logger.verbosePii(`Setting validated request account: ${c.homeAccountId}`,this.correlationId),s.account=c),!s.loginHint&&!c){const h=this.browserStorage.getLegacyLoginHint();h&&(s.loginHint=h)}return s}}/*! @azure/msal-browser v3.30.0 2025-08-05 */const oc="ContentError",ni="user_switch";/*! @azure/msal-browser v3.30.0 2025-08-05 */const rc="USER_INTERACTION_REQUIRED",ic="USER_CANCEL",ac="NO_NETWORK",sc="PERSISTENT_ERROR",cc="DISABLED",hc="ACCOUNT_UNAVAILABLE";/*! @azure/msal-browser v3.30.0 2025-08-05 */const lc=-**********,dc={[ni]:"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."};class Z extends b{constructor(e,t,n){super(e,t),Object.setPrototypeOf(this,Z.prototype),this.name="NativeAuthError",this.ext=n}}function _e(a){if(a.ext&&a.ext.status&&(a.ext.status===sc||a.ext.status===cc)||a.ext&&a.ext.error&&a.ext.error===lc)return!0;switch(a.errorCode){case oc:return!0;default:return!1}}function Vt(a,e,t){if(t&&t.status)switch(t.status){case hc:return Bt(ar);case rc:return new Y(a,e);case ic:return C(Ce);case ac:return C(dt)}return new Z(a,dc[a]||e,t)}/*! @azure/msal-browser v3.30.0 2025-08-05 */class oi extends Ke{async acquireToken(e){this.performanceClient.addQueueMeasurement(l.SilentCacheClientAcquireToken,e.correlationId);const t=this.initializeServerTelemetryManager(R.acquireTokenSilent_silentFlow),n=await m(this.getClientConfiguration.bind(this),l.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,account:e.account}),o=new Ja(n,this.performanceClient);this.logger.verbose("Silent auth client created");try{const i=(await m(o.acquireCachedToken.bind(o),l.SilentFlowClientAcquireCachedToken,this.logger,this.performanceClient,e.correlationId)(e))[0];return this.performanceClient.addFields({fromCache:!0},e.correlationId),i}catch(r){throw r instanceof De&&r.errorCode===An&&this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."),r}}logout(e){this.logger.verbose("logoutRedirect called");const t=this.initializeLogoutRequest(e);return this.clearCacheOnLogout(t==null?void 0:t.account)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ne extends ti{constructor(e,t,n,o,r,i,s,c,h,d,g,f){var A;super(e,t,n,o,r,i,c,h,f),this.apiId=s,this.accountId=d,this.nativeMessageHandler=h,this.nativeStorageManager=g,this.silentCacheClient=new oi(e,this.nativeStorageManager,n,o,r,i,c,h,f),this.serverTelemetryManager=this.initializeServerTelemetryManager(this.apiId);const y=this.nativeMessageHandler.getExtensionId()===be.PREFERRED_EXTENSION_ID?"chrome":(A=this.nativeMessageHandler.getExtensionId())!=null&&A.length?"unknown":void 0;this.skus=qe.makeExtraSkuString({libraryName:$.MSAL_SKU,libraryVersion:ye,extensionName:y,extensionVersion:this.nativeMessageHandler.getExtensionVersion()})}addRequestSKUs(e){e.extraParameters={...e.extraParameters,[La]:this.skus}}async acquireToken(e){this.performanceClient.addQueueMeasurement(l.NativeInteractionClientAcquireToken,e.correlationId),this.logger.trace("NativeInteractionClient - acquireToken called.");const t=this.performanceClient.startMeasurement(l.NativeInteractionClientAcquireToken,e.correlationId),n=oe();try{const o=await this.initializeNativeRequest(e);try{const h=await this.acquireTokensFromCache(this.accountId,o);return t.end({success:!0,isNativeBroker:!1,fromCache:!0}),h}catch{this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call")}const{...r}=o,i={method:pe.GetToken,request:r},s=await this.nativeMessageHandler.sendMessage(i),c=this.validateNativeResponse(s);return await this.handleNativeResponse(c,o,n).then(h=>(t.end({success:!0,isNativeBroker:!0,requestId:h.requestId}),this.serverTelemetryManager.clearNativeBrokerErrorCode(),h)).catch(h=>{throw t.end({success:!1,errorCode:h.errorCode,subErrorCode:h.subError,isNativeBroker:!0}),h})}catch(o){throw o instanceof Z&&this.serverTelemetryManager.setNativeBrokerErrorCode(o.errorCode),o}}createSilentCacheRequest(e,t){return{authority:e.authority,correlationId:this.correlationId,scopes:O.fromString(e.scope).asArray(),account:t,forceRefresh:!1}}async acquireTokensFromCache(e,t){if(!e)throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"),p(Lt);const n=this.browserStorage.getBaseAccountInfo({nativeAccountId:e},t.correlationId);if(!n)throw p(Lt);try{const o=this.createSilentCacheRequest(t,n),r=await this.silentCacheClient.acquireToken(o),i={...n,idTokenClaims:r==null?void 0:r.idTokenClaims,idToken:r==null?void 0:r.idToken};return{...r,account:i}}catch(o){throw o}}async acquireTokenRedirect(e,t){this.logger.trace("NativeInteractionClient - acquireTokenRedirect called.");const{...n}=e;delete n.onRedirectNavigate;const o=await this.initializeNativeRequest(n),r={method:pe.GetToken,request:o};try{const c=await this.nativeMessageHandler.sendMessage(r);this.validateNativeResponse(c)}catch(c){if(c instanceof Z&&(this.serverTelemetryManager.setNativeBrokerErrorCode(c.errorCode),_e(c)))throw c}this.browserStorage.setTemporaryCache(w.NATIVE_REQUEST,JSON.stringify(o),!0);const i={apiId:R.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},s=this.config.auth.navigateToLoginRequestUrl?window.location.href:this.getRedirectUri(e.redirectUri);t.end({success:!0}),await this.navigationClient.navigateExternal(s,i)}async handleRedirectPromise(e,t){if(this.logger.trace("NativeInteractionClient - handleRedirectPromise called."),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const n=this.browserStorage.getCachedNativeRequest();if(!n)return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."),e&&t&&(e==null||e.addFields({errorCode:"no_cached_request"},t)),null;const{prompt:o,...r}=n;o&&this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(w.NATIVE_REQUEST));const i={method:pe.GetToken,request:r},s=oe();try{this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker.");const c=await this.nativeMessageHandler.sendMessage(i);this.validateNativeResponse(c);const h=this.handleNativeResponse(c,r,s);this.browserStorage.setInteractionInProgress(!1);const d=await h;return this.serverTelemetryManager.clearNativeBrokerErrorCode(),d}catch(c){throw this.browserStorage.setInteractionInProgress(!1),c}}logout(){return this.logger.trace("NativeInteractionClient - logout called."),Promise.reject("Logout not implemented yet")}async handleNativeResponse(e,t,n){var d;this.logger.trace("NativeInteractionClient - handleNativeResponse called.");const o=Ae(e.id_token,ne),r=this.createHomeAccountIdentifier(e,o),i=(d=this.browserStorage.getAccountInfoFilteredBy({nativeAccountId:t.accountId},this.correlationId))==null?void 0:d.homeAccountId;if(r!==i&&e.account.id!==t.accountId)throw Vt(ni);const s=await this.getDiscoveredAuthority({requestAuthority:t.authority}),c=mn(this.browserStorage,s,r,ne,this.correlationId,o,e.client_info,void 0,o.tid,void 0,e.account.id,this.logger),h=await this.generateAuthenticationResult(e,t,o,c,s.canonicalAuthority,n);return this.cacheAccount(c),this.cacheNativeTokens(e,t,r,o,e.access_token,h.tenantId,n),h}createHomeAccountIdentifier(e,t){return P.generateHomeAccountId(e.client_info||u.EMPTY_STRING,W.Default,this.logger,this.browserCrypto,t)}generateScopes(e,t){return e.scope?O.fromString(e.scope):O.fromString(t.scope)}async generatePopAccessToken(e,t){if(t.tokenType===_.POP&&t.signPopToken){if(e.shr)return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"),e.shr;const n=new Le(this.browserCrypto),o={resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,shrNonce:t.shrNonce};if(!t.keyId)throw p(en);return n.signPopToken(e.access_token,t.keyId,o)}else return e.access_token}async generateAuthenticationResult(e,t,n,o,r,i){const s=this.addTelemetryFromNativeResponse(e),c=e.scope?O.fromString(e.scope):O.fromString(t.scope),h=e.account.properties||{},d=h.UID||n.oid||n.sub||u.EMPTY_STRING,g=h.TenantId||n.tid||u.EMPTY_STRING,f=cn(o.getAccountInfo(),void 0,n,e.id_token);f.nativeAccountId!==e.account.id&&(f.nativeAccountId=e.account.id);const y=await this.generatePopAccessToken(e,t),A=t.tokenType===_.POP?_.POP:_.BEARER;return{authority:r,uniqueId:d,tenantId:g,scopes:c.asArray(),account:f,idToken:e.id_token,idTokenClaims:n,accessToken:y,fromCache:s?this.isResponseFromCache(s):!1,expiresOn:new Date(Number(i+e.expires_in)*1e3),tokenType:A,correlationId:this.correlationId,state:e.state,fromNativeBroker:!0}}cacheAccount(e){this.browserStorage.setAccount(e,this.correlationId),this.browserStorage.removeAccountContext(e,this.correlationId).catch(t=>{this.logger.error(`Error occurred while removing account context from browser storage. ${t}`)})}cacheNativeTokens(e,t,n,o,r,i,s){const c=mt(n,t.authority,e.id_token||"",t.clientId,o.tid||""),h=t.tokenType===_.POP?u.SHR_NONCE_VALIDITY:(typeof e.expires_in=="string"?parseInt(e.expires_in,10):e.expires_in)||0,d=s+h,g=this.generateScopes(e,t),f=ft(n,t.authority,r,t.clientId,o.tid||i,g.printScopes(),d,0,ne,void 0,t.tokenType,void 0,t.keyId),y={idToken:c,accessToken:f};this.nativeStorageManager.saveCacheRecord(y,t.correlationId,t.storeInCache)}addTelemetryFromNativeResponse(e){const t=this.getMATSFromResponse(e);return t?(this.performanceClient.addFields({extensionId:this.nativeMessageHandler.getExtensionId(),extensionVersion:this.nativeMessageHandler.getExtensionVersion(),matsBrokerVersion:t.broker_version,matsAccountJoinOnStart:t.account_join_on_start,matsAccountJoinOnEnd:t.account_join_on_end,matsDeviceJoin:t.device_join,matsPromptBehavior:t.prompt_behavior,matsApiErrorCode:t.api_error_code,matsUiVisible:t.ui_visible,matsSilentCode:t.silent_code,matsSilentBiSubCode:t.silent_bi_sub_code,matsSilentMessage:t.silent_message,matsSilentStatus:t.silent_status,matsHttpStatus:t.http_status,matsHttpEventCount:t.http_event_count},this.correlationId),t):null}validateNativeResponse(e){if(e.hasOwnProperty("access_token")&&e.hasOwnProperty("id_token")&&e.hasOwnProperty("client_info")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scope")&&e.hasOwnProperty("expires_in"))return e;throw Co(Yt,"Response missing expected properties.")}getMATSFromResponse(e){if(e.properties.MATS)try{return JSON.parse(e.properties.MATS)}catch{this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead")}return null}isResponseFromCache(e){return typeof e.is_cached>"u"?(this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."),!1):!!e.is_cached}async initializeNativeRequest(e){this.logger.trace("NativeInteractionClient - initializeNativeRequest called");const t=e.authority||this.config.auth.authority;e.account&&await this.getDiscoveredAuthority({requestAuthority:t,requestAzureCloudOptions:e.azureCloudOptions,account:e.account});const n=new S(t);n.validateAsUri();const{scopes:o,...r}=e,i=new O(o||[]);i.appendScopes(Ie);const s=()=>{switch(this.apiId){case R.ssoSilent:case R.acquireTokenSilent_silentFlow:return this.logger.trace("initializeNativeRequest: silent request sets prompt to none"),M.NONE}if(!e.prompt){this.logger.trace("initializeNativeRequest: prompt was not provided");return}switch(e.prompt){case M.NONE:case M.CONSENT:case M.LOGIN:return this.logger.trace("initializeNativeRequest: prompt is compatible with native flow"),e.prompt;default:throw this.logger.trace(`initializeNativeRequest: prompt = ${e.prompt} is not compatible with native flow`),C(Dr)}},c={...r,accountId:this.accountId,clientId:this.config.auth.clientId,authority:n.urlString,scope:i.printScopes(),redirectUri:this.getRedirectUri(e.redirectUri),prompt:s(),correlationId:this.correlationId,tokenType:e.authenticationScheme,windowTitleSubstring:document.title,extraParameters:{...e.extraQueryParameters,...e.tokenQueryParameters},extendedExpiryToken:!1,keyId:e.popKid};if(c.signPopToken&&e.popKid)throw C(xr);if(this.handleExtraBrokerParams(c),c.extraParameters=c.extraParameters||{},c.extraParameters.telemetry=be.MATS_TELEMETRY,e.authenticationScheme===_.POP){const h={resourceRequestUri:e.resourceRequestUri,resourceRequestMethod:e.resourceRequestMethod,shrClaims:e.shrClaims,shrNonce:e.shrNonce},d=new Le(this.browserCrypto);let g;if(c.keyId)g=this.browserCrypto.base64UrlEncode(JSON.stringify({kid:c.keyId})),c.signPopToken=!1;else{const f=await m(d.generateCnf.bind(d),l.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(h,this.logger);g=f.reqCnfString,c.keyId=f.kid,c.signPopToken=!0}c.reqCnf=g}return this.addRequestSKUs(c),c}handleExtraBrokerParams(e){var r;const t=e.extraParameters&&e.extraParameters.hasOwnProperty(un)&&e.extraParameters.hasOwnProperty(xt)&&e.extraParameters.hasOwnProperty(me);if(!e.embeddedClientId&&!t)return;let n="";const o=e.redirectUri;e.embeddedClientId?(e.redirectUri=this.config.auth.redirectUri,n=e.embeddedClientId):e.extraParameters&&(e.redirectUri=e.extraParameters[xt],n=e.extraParameters[me]),e.extraParameters={child_client_id:n,child_redirect_uri:o},(r=this.performanceClient)==null||r.addFields({embeddedClientId:n,embeddedRedirectUri:o},e.correlationId)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class ee{constructor(e,t,n,o){this.logger=e,this.handshakeTimeoutMs=t,this.extensionId=o,this.resolvers=new Map,this.handshakeResolvers=new Map,this.messageChannel=new MessageChannel,this.windowListener=this.onWindowMessage.bind(this),this.performanceClient=n,this.handshakeEvent=n.startMeasurement(l.NativeMessageHandlerHandshake)}async sendMessage(e){this.logger.trace("NativeMessageHandler - sendMessage called.");const t={channel:be.CHANNEL_ID,extensionId:this.extensionId,responseId:re(),body:e};return this.logger.trace("NativeMessageHandler - Sending request to browser extension"),this.logger.tracePii(`NativeMessageHandler - Sending request to browser extension: ${JSON.stringify(t)}`),this.messageChannel.port1.postMessage(t),new Promise((n,o)=>{this.resolvers.set(t.responseId,{resolve:n,reject:o})})}static async createProvider(e,t,n){e.trace("NativeMessageHandler - createProvider called.");try{const o=new ee(e,t,n,be.PREFERRED_EXTENSION_ID);return await o.sendHandshakeRequest(),o}catch{const r=new ee(e,t,n);return await r.sendHandshakeRequest(),r}}async sendHandshakeRequest(){this.logger.trace("NativeMessageHandler - sendHandshakeRequest called."),window.addEventListener("message",this.windowListener,!1);const e={channel:be.CHANNEL_ID,extensionId:this.extensionId,responseId:re(),body:{method:pe.HandshakeRequest}};return this.handshakeEvent.add({extensionId:this.extensionId,extensionHandshakeTimeoutMs:this.handshakeTimeoutMs}),this.messageChannel.port1.onmessage=t=>{this.onChannelMessage(t)},window.postMessage(e,window.origin,[this.messageChannel.port2]),new Promise((t,n)=>{this.handshakeResolvers.set(e.responseId,{resolve:t,reject:n}),this.timeoutId=window.setTimeout(()=>{window.removeEventListener("message",this.windowListener,!1),this.messageChannel.port1.close(),this.messageChannel.port2.close(),this.handshakeEvent.end({extensionHandshakeTimedOut:!0,success:!1}),n(C(Lr)),this.handshakeResolvers.delete(e.responseId)},this.handshakeTimeoutMs)})}onWindowMessage(e){if(this.logger.trace("NativeMessageHandler - onWindowMessage called"),e.source!==window)return;const t=e.data;if(!(!t.channel||t.channel!==be.CHANNEL_ID)&&!(t.extensionId&&t.extensionId!==this.extensionId)&&t.body.method===pe.HandshakeRequest){const n=this.handshakeResolvers.get(t.responseId);if(!n){this.logger.trace(`NativeMessageHandler.onWindowMessage - resolver can't be found for request ${t.responseId}`);return}this.logger.verbose(t.extensionId?`Extension with id: ${t.extensionId} not installed`:"No extension installed"),clearTimeout(this.timeoutId),this.messageChannel.port1.close(),this.messageChannel.port2.close(),window.removeEventListener("message",this.windowListener,!1),this.handshakeEvent.end({success:!1,extensionInstalled:!1}),n.reject(C(Ur))}}onChannelMessage(e){this.logger.trace("NativeMessageHandler - onChannelMessage called.");const t=e.data,n=this.resolvers.get(t.responseId),o=this.handshakeResolvers.get(t.responseId);try{const r=t.body.method;if(r===pe.Response){if(!n)return;const i=t.body.response;if(this.logger.trace("NativeMessageHandler - Received response from browser extension"),this.logger.tracePii(`NativeMessageHandler - Received response from browser extension: ${JSON.stringify(i)}`),i.status!=="Success")n.reject(Vt(i.code,i.description,i.ext));else if(i.result)i.result.code&&i.result.description?n.reject(Vt(i.result.code,i.result.description,i.result.ext)):n.resolve(i.result);else throw Co(Yt,"Event does not contain result.");this.resolvers.delete(t.responseId)}else if(r===pe.HandshakeResponse){if(!o){this.logger.trace(`NativeMessageHandler.onChannelMessage - resolver can't be found for request ${t.responseId}`);return}clearTimeout(this.timeoutId),window.removeEventListener("message",this.windowListener,!1),this.extensionId=t.extensionId,this.extensionVersion=t.body.version,this.logger.verbose(`NativeMessageHandler - Received HandshakeResponse from extension: ${this.extensionId}`),this.handshakeEvent.end({extensionInstalled:!0,success:!0}),o.resolve(),this.handshakeResolvers.delete(t.responseId)}}catch(r){this.logger.error("Error parsing response from WAM Extension"),this.logger.errorPii(`Error parsing response from WAM Extension: ${r}`),this.logger.errorPii(`Unable to parse ${e}`),n?n.reject(r):o&&o.reject(r)}}getExtensionId(){return this.extensionId}getExtensionVersion(){return this.extensionVersion}static isNativeAvailable(e,t,n,o){if(t.trace("isNativeAvailable called"),!e.system.allowNativeBroker)return t.trace("isNativeAvailable: allowNativeBroker is not enabled, returning false"),!1;if(!n)return t.trace("isNativeAvailable: WAM extension provider is not initialized, returning false"),!1;if(o)switch(o){case _.BEARER:case _.POP:return t.trace("isNativeAvailable: authenticationScheme is supported, returning true"),!0;default:return t.trace("isNativeAvailable: authenticationScheme is not supported, returning false"),!1}return!0}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Nn{constructor(e,t,n,o,r){this.authModule=e,this.browserStorage=t,this.authCodeRequest=n,this.logger=o,this.performanceClient=r}async handleCodeResponse(e,t){this.performanceClient.addQueueMeasurement(l.HandleCodeResponse,t.correlationId);let n;try{n=this.authModule.handleFragmentResponse(e,t.state)}catch(o){throw o instanceof le&&o.subError===Ce?C(Ce):o}return m(this.handleCodeResponseFromServer.bind(this),l.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)(n,t)}async handleCodeResponseFromServer(e,t,n=!0){if(this.performanceClient.addQueueMeasurement(l.HandleCodeResponseFromServer,t.correlationId),this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"),this.authCodeRequest.code=e.code,e.cloud_instance_host_name&&await m(this.authModule.updateAuthority.bind(this.authModule),l.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,t.correlationId)(e.cloud_instance_host_name,t.correlationId),n&&(e.nonce=t.nonce||void 0),e.state=t.state,e.client_info)this.authCodeRequest.clientInfo=e.client_info;else{const r=this.createCcsCredentials(t);r&&(this.authCodeRequest.ccsCredential=r)}return await m(this.authModule.acquireToken.bind(this.authModule),l.AuthClientAcquireToken,this.logger,this.performanceClient,t.correlationId)(this.authCodeRequest,e)}createCcsCredentials(e){return e.account?{credential:e.account.homeAccountId,type:z.HOME_ACCOUNT_ID}:e.loginHint?{credential:e.loginHint,type:z.UPN}:null}}/*! @azure/msal-browser v3.30.0 2025-08-05 */function ri(a,e,t){const n=ct(a);if(!n)throw Xo(a)?(t.error(`A ${e} is present in the iframe but it does not contain known properties. It's likely that the ${e} has been replaced by code running on the redirectUri page.`),t.errorPii(`The ${e} detected is: ${a}`),C(pr)):(t.error(`The request has returned to the redirectUri but a ${e} is not present. It's likely that the ${e} has been removed or the page has been redirected by code running on the redirectUri page.`),C(gr));return n}function uc(a,e,t){if(!a.state)throw C(Tn);const n=Zr(e,a.state);if(!n)throw C(mr);if(n.interactionType!==t)throw C(fr)}/*! @azure/msal-browser v3.30.0 2025-08-05 */class gc extends Ke{constructor(e,t,n,o,r,i,s,c,h,d){super(e,t,n,o,r,i,s,h,d),this.unloadWindow=this.unloadWindow.bind(this),this.nativeStorage=c}acquireToken(e){try{const n={popupName:this.generatePopupName(e.scopes||Ie,e.authority||this.config.auth.authority),popupWindowAttributes:e.popupWindowAttributes||{},popupWindowParent:e.popupWindowParent??window};return this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(e,n)):(this.logger.verbose("asyncPopup set to false, opening popup before acquiring token"),n.popup=this.openSizedPopup("about:blank",n),this.acquireTokenPopupAsync(e,n))}catch(t){return Promise.reject(t)}}logout(e){try{this.logger.verbose("logoutPopup called");const t=this.initializeLogoutRequest(e),n={popupName:this.generateLogoutPopupName(t),popupWindowAttributes:(e==null?void 0:e.popupWindowAttributes)||{},popupWindowParent:(e==null?void 0:e.popupWindowParent)??window},o=e&&e.authority,r=e&&e.mainWindowRedirectUri;return this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true"),this.logoutPopupAsync(t,n,o,r)):(this.logger.verbose("asyncPopup set to false, opening popup"),n.popup=this.openSizedPopup("about:blank",n),this.logoutPopupAsync(t,n,o,r))}catch(t){return Promise.reject(t)}}async acquireTokenPopupAsync(e,t){var r;this.logger.verbose("acquireTokenPopupAsync called");const n=this.initializeServerTelemetryManager(R.acquireTokenPopup),o=await m(this.initializeAuthorizationRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(e,exports.InteractionType.Popup);jr(o.authority);try{const i=await m(this.initializeAuthorizationCodeRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(o),s=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:o.authority,requestAzureCloudOptions:o.azureCloudOptions,requestExtraQueryParameters:o.extraQueryParameters,account:o.account}),c=ee.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,e.authenticationScheme);let h;c&&(h=this.performanceClient.startMeasurement(l.FetchAccountIdWithNativeBroker,e.correlationId));const d=await s.getAuthCodeUrl({...o,nativeBroker:c}),g=new Nn(s,this.browserStorage,i,this.logger,this.performanceClient),f=this.initiateAuthRequest(d,t);this.eventHandler.emitEvent(T.POPUP_OPENED,exports.InteractionType.Popup,{popupWindow:f},null);const y=await this.monitorPopupForHash(f,t.popupWindowParent),A=Ee(ri,l.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(y,this.config.auth.OIDCOptions.serverResponseType,this.logger);if(X.removeThrottle(this.browserStorage,this.config.auth.clientId,i),A.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),h&&h.end({success:!0,isNativeBroker:!0}),!this.nativeMessageHandler)throw C(Ve);const N=new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,A.accountId,this.nativeStorage,o.correlationId),{userRequestState:F}=j.parseRequestState(this.browserCrypto,o.state);return await N.acquireToken({...o,state:F,prompt:void 0})}return await g.handleCodeResponse(A,o)}catch(i){throw(r=t.popup)==null||r.close(),i instanceof b&&(i.setCorrelationId(this.correlationId),n.cacheFailedRequest(i)),i}}async logoutPopupAsync(e,t,n,o){var i,s,c,h;this.logger.verbose("logoutPopupAsync called"),this.eventHandler.emitEvent(T.LOGOUT_START,exports.InteractionType.Popup,e);const r=this.initializeServerTelemetryManager(R.logoutPopup);try{await this.clearCacheOnLogout(e.account);const d=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:r,requestAuthority:n,account:e.account||void 0});try{d.authority.endSessionEndpoint}catch{if((i=e.account)!=null&&i.homeAccountId&&e.postLogoutRedirectUri&&d.authority.protocolMode===te.OIDC){if(this.browserStorage.removeAccount((s=e.account)==null?void 0:s.homeAccountId,this.correlationId),this.eventHandler.emitEvent(T.LOGOUT_SUCCESS,exports.InteractionType.Popup,e),o){const y={apiId:R.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},A=S.getAbsoluteUrl(o,ce());await this.navigationClient.navigateInternal(A,y)}(c=t.popup)==null||c.close();return}}const g=d.getLogoutUri(e);this.eventHandler.emitEvent(T.LOGOUT_SUCCESS,exports.InteractionType.Popup,e);const f=this.openPopup(g,t);if(this.eventHandler.emitEvent(T.POPUP_OPENED,exports.InteractionType.Popup,{popupWindow:f},null),await this.monitorPopupForHash(f,t.popupWindowParent).catch(()=>{}),o){const y={apiId:R.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},A=S.getAbsoluteUrl(o,ce());this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii(`Redirecting main window to: ${A}`),await this.navigationClient.navigateInternal(A,y)}else this.logger.verbose("No main window navigation requested")}catch(d){throw(h=t.popup)==null||h.close(),d instanceof b&&(d.setCorrelationId(this.correlationId),r.cacheFailedRequest(d)),this.browserStorage.setInteractionInProgress(!1),this.eventHandler.emitEvent(T.LOGOUT_FAILURE,exports.InteractionType.Popup,null,d),this.eventHandler.emitEvent(T.LOGOUT_END,exports.InteractionType.Popup),d}this.eventHandler.emitEvent(T.LOGOUT_END,exports.InteractionType.Popup)}initiateAuthRequest(e,t){if(e)return this.logger.infoPii(`Navigate to: ${e}`),this.openPopup(e,t);throw this.logger.error("Navigate url is empty"),C(Et)}monitorPopupForHash(e,t){return new Promise((n,o)=>{this.logger.verbose("PopupHandler.monitorPopupForHash - polling started");const r=setInterval(()=>{if(e.closed){this.logger.error("PopupHandler.monitorPopupForHash - window closed"),clearInterval(r),o(C(Ce));return}let i="";try{i=e.location.href}catch{}if(!i||i==="about:blank")return;clearInterval(r);let s="";const c=this.config.auth.OIDCOptions.serverResponseType;e&&(c===He.QUERY?s=e.location.search:s=e.location.hash),this.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"),n(s)},this.config.system.pollIntervalMilliseconds)}).finally(()=>{this.cleanPopup(e,t)})}openPopup(e,t){try{let n;if(t.popup?(n=t.popup,this.logger.verbosePii(`Navigating popup window to: ${e}`),n.location.assign(e)):typeof t.popup>"u"&&(this.logger.verbosePii(`Opening popup window to: ${e}`),n=this.openSizedPopup(e,t)),!n)throw C(Tr);return n.focus&&n.focus(),this.currentWindow=n,t.popupWindowParent.addEventListener("beforeunload",this.unloadWindow),n}catch(n){throw this.logger.error("error opening popup "+n.message),this.browserStorage.setInteractionInProgress(!1),C(yr)}}openSizedPopup(e,{popupName:t,popupWindowAttributes:n,popupWindowParent:o}){var y,A,v,N;const r=o.screenLeft?o.screenLeft:o.screenX,i=o.screenTop?o.screenTop:o.screenY,s=o.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,c=o.innerHeight||document.documentElement.clientHeight||document.body.clientHeight;let h=(y=n.popupSize)==null?void 0:y.width,d=(A=n.popupSize)==null?void 0:A.height,g=(v=n.popupPosition)==null?void 0:v.top,f=(N=n.popupPosition)==null?void 0:N.left;return(!h||h<0||h>s)&&(this.logger.verbose("Default popup window width used. Window width not configured or invalid."),h=$.POPUP_WIDTH),(!d||d<0||d>c)&&(this.logger.verbose("Default popup window height used. Window height not configured or invalid."),d=$.POPUP_HEIGHT),(!g||g<0||g>c)&&(this.logger.verbose("Default popup window top position used. Window top not configured or invalid."),g=Math.max(0,c/2-$.POPUP_HEIGHT/2+i)),(!f||f<0||f>s)&&(this.logger.verbose("Default popup window left position used. Window left not configured or invalid."),f=Math.max(0,s/2-$.POPUP_WIDTH/2+r)),o.open(e,t,`width=${h}, height=${d}, top=${g}, left=${f}, scrollbars=yes`)}unloadWindow(e){this.browserStorage.cleanRequestByInteractionType(exports.InteractionType.Popup),this.currentWindow&&this.currentWindow.close(),e.preventDefault()}cleanPopup(e,t){e.close(),t.removeEventListener("beforeunload",this.unloadWindow),this.browserStorage.setInteractionInProgress(!1)}generatePopupName(e,t){return`${$.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${e.join("-")}.${t}.${this.correlationId}`}generateLogoutPopupName(e){const t=e.account&&e.account.homeAccountId;return`${$.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${t}.${this.correlationId}`}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class po{constructor(e,t,n,o,r){this.authModule=e,this.browserStorage=t,this.authCodeRequest=n,this.logger=o,this.performanceClient=r}async initiateAuthRequest(e,t){if(this.logger.verbose("RedirectHandler.initiateAuthRequest called"),e){t.redirectStartPage&&(this.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page"),this.browserStorage.setTemporaryCache(w.ORIGIN_URI,t.redirectStartPage,!0)),this.browserStorage.setTemporaryCache(w.CORRELATION_ID,this.authCodeRequest.correlationId,!0),this.browserStorage.cacheCodeRequest(this.authCodeRequest),this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${e}`);const n={apiId:R.acquireTokenRedirect,timeout:t.redirectTimeout,noHistory:!1};if(typeof t.onRedirectNavigate=="function")if(this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback"),t.onRedirectNavigate(e)!==!1){this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),await t.navigationClient.navigateExternal(e,n);return}else{this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation");return}else{this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),await t.navigationClient.navigateExternal(e,n);return}}else throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),C(Et)}async handleCodeResponse(e,t){this.logger.verbose("RedirectHandler.handleCodeResponse called"),this.browserStorage.setInteractionInProgress(!1);const n=this.browserStorage.generateStateKey(t),o=this.browserStorage.getTemporaryCache(n);if(!o)throw p(rt,"Cached State");let r;try{r=this.authModule.handleFragmentResponse(e,o)}catch(h){throw h instanceof le&&h.subError===Ce?C(Ce):h}const i=this.browserStorage.generateNonceKey(o),s=this.browserStorage.getTemporaryCache(i);if(this.authCodeRequest.code=r.code,r.cloud_instance_host_name&&await m(this.authModule.updateAuthority.bind(this.authModule),l.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,this.authCodeRequest.correlationId)(r.cloud_instance_host_name,this.authCodeRequest.correlationId),r.nonce=s||void 0,r.state=o,r.client_info)this.authCodeRequest.clientInfo=r.client_info;else{const h=this.checkCcsCredentials();h&&(this.authCodeRequest.ccsCredential=h)}const c=await this.authModule.acquireToken(this.authCodeRequest,r);return this.browserStorage.cleanRequestByState(t),c}checkCcsCredentials(){const e=this.browserStorage.getTemporaryCache(w.CCS_CREDENTIAL,!0);if(e)try{return JSON.parse(e)}catch{this.authModule.logger.error("Cache credential could not be parsed"),this.authModule.logger.errorPii(`Cache credential could not be parsed: ${e}`)}return null}}/*! @azure/msal-browser v3.30.0 2025-08-05 */function pc(){if(typeof window>"u"||typeof window.performance>"u"||typeof window.performance.getEntriesByType!="function")return;const a=window.performance.getEntriesByType("navigation"),e=a.length?a[0]:void 0;return e==null?void 0:e.type}class mc extends Ke{constructor(e,t,n,o,r,i,s,c,h,d){super(e,t,n,o,r,i,s,h,d),this.nativeStorage=c}async acquireToken(e){const t=await m(this.initializeAuthorizationRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(e,exports.InteractionType.Redirect);this.browserStorage.updateCacheEntries(t.state,t.nonce,t.authority,t.loginHint||"",t.account||null);const n=this.initializeServerTelemetryManager(R.acquireTokenRedirect),o=r=>{r.persisted&&(this.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."),this.browserStorage.cleanRequestByState(t.state),this.eventHandler.emitEvent(T.RESTORE_FROM_BFCACHE,exports.InteractionType.Redirect))};try{const r=await m(this.initializeAuthorizationCodeRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(t),i=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:t.authority,requestAzureCloudOptions:t.azureCloudOptions,requestExtraQueryParameters:t.extraQueryParameters,account:t.account}),s=new po(i,this.browserStorage,r,this.logger,this.performanceClient),c=await i.getAuthCodeUrl({...t,nativeBroker:ee.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,e.authenticationScheme)}),h=this.getRedirectStartPage(e.redirectStartPage);return this.logger.verbosePii(`Redirect start page: ${h}`),window.addEventListener("pageshow",o),await s.initiateAuthRequest(c,{navigationClient:this.navigationClient,redirectTimeout:this.config.system.redirectNavigationTimeout,redirectStartPage:h,onRedirectNavigate:e.onRedirectNavigate||this.config.auth.onRedirectNavigate})}catch(r){throw r instanceof b&&(r.setCorrelationId(this.correlationId),n.cacheFailedRequest(r)),window.removeEventListener("pageshow",o),this.browserStorage.cleanRequestByState(t.state),r}}async handleRedirectPromise(e="",t){const n=this.initializeServerTelemetryManager(R.handleRedirectPromise);try{if(!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const[o,r]=this.getRedirectResponse(e||"");if(!o)return this.logger.info("handleRedirectPromise did not detect a response as a result of a redirect. Cleaning temporary cache."),this.browserStorage.cleanRequestByInteractionType(exports.InteractionType.Redirect),pc()!=="back_forward"?t.event.errorCode="no_server_response":this.logger.verbose("Back navigation event detected. Muting no_server_response error"),null;const i=this.browserStorage.getTemporaryCache(w.ORIGIN_URI,!0)||u.EMPTY_STRING,s=S.removeHashFromUrl(i),c=S.removeHashFromUrl(window.location.href);if(s===c&&this.config.auth.navigateToLoginRequestUrl)return this.logger.verbose("Current page is loginRequestUrl, handling response"),i.indexOf("#")>-1&&ws(i),await this.handleResponse(o,n);if(this.config.auth.navigateToLoginRequestUrl){if(!kn()||this.config.system.allowRedirectInIframe){this.browserStorage.setTemporaryCache(w.URL_HASH,r,!0);const h={apiId:R.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0};let d=!0;if(!i||i==="null"){const g=ks();this.browserStorage.setTemporaryCache(w.ORIGIN_URI,g,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),d=await this.navigationClient.navigateInternal(g,h)}else this.logger.verbose(`Navigating to loginRequestUrl: ${i}`),d=await this.navigationClient.navigateInternal(i,h);if(!d)return await this.handleResponse(o,n)}}else return this.logger.verbose("NavigateToLoginRequestUrl set to false, handling response"),await this.handleResponse(o,n);return null}catch(o){throw o instanceof b&&(o.setCorrelationId(this.correlationId),n.cacheFailedRequest(o)),this.browserStorage.cleanRequestByInteractionType(exports.InteractionType.Redirect),o}}getRedirectResponse(e){this.logger.verbose("getRedirectResponseHash called");let t=e;t||(this.config.auth.OIDCOptions.serverResponseType===He.QUERY?t=window.location.search:t=window.location.hash);let n=ct(t);if(n){try{uc(n,this.browserCrypto,exports.InteractionType.Redirect)}catch(r){return r instanceof b&&this.logger.error(`Interaction type validation failed due to ${r.errorCode}: ${r.errorMessage}`),[null,""]}return Ss(window),this.logger.verbose("Hash contains known properties, returning response hash"),[n,t]}const o=this.browserStorage.getTemporaryCache(w.URL_HASH,!0);return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(w.URL_HASH)),o&&(n=ct(o),n)?(this.logger.verbose("Hash does not contain known properties, returning cached hash"),[n,o]):[null,""]}async handleResponse(e,t){const n=e.state;if(!n)throw C(Tn);const o=this.browserStorage.getCachedRequest(n);if(this.logger.verbose("handleResponse called, retrieved cached request"),e.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw C(Ve);const c=new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,e.accountId,this.nativeStorage,o.correlationId),{userRequestState:h}=j.parseRequestState(this.browserCrypto,n);return c.acquireToken({...o,state:h,prompt:void 0}).finally(()=>{this.browserStorage.cleanRequestByState(n)})}const r=this.browserStorage.getCachedAuthority(n);if(!r)throw C(In);const i=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:r});return X.removeThrottle(this.browserStorage,this.config.auth.clientId,o),new po(i,this.browserStorage,o,this.logger,this.performanceClient).handleCodeResponse(e,n)}async logout(e){var o,r;this.logger.verbose("logoutRedirect called");const t=this.initializeLogoutRequest(e),n=this.initializeServerTelemetryManager(R.logout);try{this.eventHandler.emitEvent(T.LOGOUT_START,exports.InteractionType.Redirect,e),await this.clearCacheOnLogout(t.account);const i={apiId:R.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},s=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:e&&e.authority,requestExtraQueryParameters:e==null?void 0:e.extraQueryParameters,account:e&&e.account||void 0});if(s.authority.protocolMode===te.OIDC)try{s.authority.endSessionEndpoint}catch{if((o=t.account)!=null&&o.homeAccountId){this.browserStorage.removeAccount((r=t.account)==null?void 0:r.homeAccountId,this.correlationId),this.eventHandler.emitEvent(T.LOGOUT_SUCCESS,exports.InteractionType.Redirect,t);return}}const c=s.getLogoutUri(t);if(this.eventHandler.emitEvent(T.LOGOUT_SUCCESS,exports.InteractionType.Redirect,t),e&&typeof e.onRedirectNavigate=="function")if(e.onRedirectNavigate(c)!==!1){this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),await this.navigationClient.navigateExternal(c,i);return}else this.browserStorage.setInteractionInProgress(!1),this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation");else{this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),await this.navigationClient.navigateExternal(c,i);return}}catch(i){throw i instanceof b&&(i.setCorrelationId(this.correlationId),n.cacheFailedRequest(i)),this.eventHandler.emitEvent(T.LOGOUT_FAILURE,exports.InteractionType.Redirect,null,i),this.eventHandler.emitEvent(T.LOGOUT_END,exports.InteractionType.Redirect),i}this.eventHandler.emitEvent(T.LOGOUT_END,exports.InteractionType.Redirect)}getRedirectStartPage(e){const t=e||window.location.href;return S.getAbsoluteUrl(t,ce())}}/*! @azure/msal-browser v3.30.0 2025-08-05 */async function fc(a,e,t,n,o){if(e.addQueueMeasurement(l.SilentHandlerInitiateAuthRequest,n),!a)throw t.info("Navigate url is empty"),C(Et);return o?m(yc,l.SilentHandlerLoadFrame,t,e,n)(a,o,e,n):Ee(Tc,l.SilentHandlerLoadFrameSync,t,e,n)(a)}async function Cc(a,e,t,n,o,r,i){return n.addQueueMeasurement(l.SilentHandlerMonitorIframeForHash,r),new Promise((s,c)=>{e<ut&&o.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${e}ms) than the default (${ut}ms). This may result in timeouts.`);const h=window.setTimeout(()=>{window.clearInterval(d),c(C(Ir))},e),d=window.setInterval(()=>{let g="";const f=a.contentWindow;try{g=f?f.location.href:""}catch{}if(!g||g==="about:blank")return;let y="";f&&(i===He.QUERY?y=f.location.search:y=f.location.hash),window.clearTimeout(h),window.clearInterval(d),s(y)},t)}).finally(()=>{Ee(Ic,l.RemoveHiddenIframe,o,n,r)(a)})}function yc(a,e,t,n){return t.addQueueMeasurement(l.SilentHandlerLoadFrame,n),new Promise((o,r)=>{const i=ii();window.setTimeout(()=>{if(!i){r("Unable to load iframe");return}i.src=a,o(i)},e)})}function Tc(a){const e=ii();return e.src=a,e}function ii(){const a=document.createElement("iframe");return a.className="msalSilentIframe",a.style.visibility="hidden",a.style.position="absolute",a.style.width=a.style.height="0",a.style.border="0",a.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.body.appendChild(a),a}function Ic(a){document.body===a.parentNode&&document.body.removeChild(a)}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ac extends Ke{constructor(e,t,n,o,r,i,s,c,h,d,g){super(e,t,n,o,r,i,c,d,g),this.apiId=s,this.nativeStorage=h}async acquireToken(e){this.performanceClient.addQueueMeasurement(l.SilentIframeClientAcquireToken,e.correlationId),!e.loginHint&&!e.sid&&(!e.account||!e.account.username)&&this.logger.warning("No user hint provided. The authorization server may need more information to complete this request.");const t={...e};t.prompt?t.prompt!==M.NONE&&t.prompt!==M.NO_SESSION&&(this.logger.warning(`SilentIframeClient. Replacing invalid prompt ${t.prompt} with ${M.NONE}`),t.prompt=M.NONE):t.prompt=M.NONE;const n=await m(this.initializeAuthorizationRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(t,exports.InteractionType.Silent);jr(n.authority);const o=this.initializeServerTelemetryManager(this.apiId);let r;try{return r=await m(this.createAuthCodeClient.bind(this),l.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,e.correlationId)({serverTelemetryManager:o,requestAuthority:n.authority,requestAzureCloudOptions:n.azureCloudOptions,requestExtraQueryParameters:n.extraQueryParameters,account:n.account}),await m(this.silentTokenHelper.bind(this),l.SilentIframeClientTokenHelper,this.logger,this.performanceClient,e.correlationId)(r,n)}catch(i){if(i instanceof b&&(i.setCorrelationId(this.correlationId),o.cacheFailedRequest(i)),!r||!(i instanceof b)||i.errorCode!==$.INVALID_GRANT_ERROR)throw i;this.performanceClient.addFields({retryError:i.errorCode},this.correlationId);const s=await m(this.initializeAuthorizationRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(t,exports.InteractionType.Silent);return await m(this.silentTokenHelper.bind(this),l.SilentIframeClientTokenHelper,this.logger,this.performanceClient,this.correlationId)(r,s)}}logout(){return Promise.reject(C(vt))}async silentTokenHelper(e,t){const n=t.correlationId;this.performanceClient.addQueueMeasurement(l.SilentIframeClientTokenHelper,n);const o=await m(this.initializeAuthorizationCodeRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,n)(t),r=await m(e.getAuthCodeUrl.bind(e),l.GetAuthCodeUrl,this.logger,this.performanceClient,n)({...t,nativeBroker:ee.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme)}),i=new Nn(e,this.browserStorage,o,this.logger,this.performanceClient),s=await m(fc,l.SilentHandlerInitiateAuthRequest,this.logger,this.performanceClient,n)(r,this.performanceClient,this.logger,n,this.config.system.navigateFrameWait),c=this.config.auth.OIDCOptions.serverResponseType,h=await m(Cc,l.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,n)(s,this.config.system.iframeHashTimeout,this.config.system.pollIntervalMilliseconds,this.performanceClient,this.logger,n,c),d=Ee(ri,l.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(h,c,this.logger);if(d.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw C(Ve);const g=new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.apiId,this.performanceClient,this.nativeMessageHandler,d.accountId,this.browserStorage,n),{userRequestState:f}=j.parseRequestState(this.browserCrypto,t.state);return m(g.acquireToken.bind(g),l.NativeInteractionClientAcquireToken,this.logger,this.performanceClient,n)({...t,state:f,prompt:t.prompt||M.NONE})}return m(i.handleCodeResponse.bind(i),l.HandleCodeResponse,this.logger,this.performanceClient,n)(d,t)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Ec extends Ke{async acquireToken(e){this.performanceClient.addQueueMeasurement(l.SilentRefreshClientAcquireToken,e.correlationId);const t=await m(On,l.InitializeBaseRequest,this.logger,this.performanceClient,e.correlationId)(e,this.config,this.performanceClient,this.logger),n={...e,...t};e.redirectUri&&(n.redirectUri=this.getRedirectUri(e.redirectUri));const o=this.initializeServerTelemetryManager(R.acquireTokenSilent_silentFlow),r=await this.createRefreshTokenClient({serverTelemetryManager:o,authorityUrl:n.authority,azureCloudOptions:n.azureCloudOptions,account:n.account});return m(r.acquireTokenByRefreshToken.bind(r),l.RefreshTokenClientAcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(n).catch(i=>{throw i.setCorrelationId(this.correlationId),o.cacheFailedRequest(i),i})}logout(){return Promise.reject(C(vt))}async createRefreshTokenClient(e){const t=await m(this.getClientConfiguration.bind(this),l.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:e.serverTelemetryManager,requestAuthority:e.authorityUrl,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account});return new Ft(t,this.performanceClient)}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class vc{constructor(e,t,n,o){this.isBrowserEnvironment=typeof window<"u",this.config=e,this.storage=t,this.logger=n,this.cryptoObj=o}loadExternalTokens(e,t,n){if(!this.isBrowserEnvironment)throw C(St);const o=e.correlationId||re(),r=t.id_token?Ae(t.id_token,ne):void 0,i={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},s=e.authority?new D(D.generateAuthority(e.authority,e.azureCloudOptions),this.config.system.networkClient,this.storage,i,this.logger,e.correlationId||re()):void 0,c=this.loadAccount(e,n.clientInfo||t.client_info||"",o,r,s),h=this.loadIdToken(t,c.homeAccountId,c.environment,c.realm,o),d=this.loadAccessToken(e,t,c.homeAccountId,c.environment,c.realm,n,o),g=this.loadRefreshToken(t,c.homeAccountId,c.environment,o);return this.generateAuthenticationResult(e,{account:c,idToken:h,accessToken:d,refreshToken:g},r,s)}loadAccount(e,t,n,o,r){if(this.logger.verbose("TokenCache - loading account"),e.account){const h=P.createFromAccountInfo(e.account);return this.storage.setAccount(h,n),h}else if(!r||!t&&!o)throw this.logger.error("TokenCache - if an account is not provided on the request, authority and either clientInfo or idToken must be provided instead."),C(br);const i=P.generateHomeAccountId(t,r.authorityType,this.logger,this.cryptoObj,o),s=o==null?void 0:o.tid,c=mn(this.storage,r,i,ne,n,o,t,r.hostnameAndPort,s,void 0,void 0,this.logger);return this.storage.setAccount(c,n),c}loadIdToken(e,t,n,o,r){if(!e.id_token)return this.logger.verbose("TokenCache - no id token found in response"),null;this.logger.verbose("TokenCache - loading id token");const i=mt(t,n,e.id_token,this.config.auth.clientId,o);return this.storage.setIdTokenCredential(i,r),i}loadAccessToken(e,t,n,o,r,i,s){if(t.access_token)if(t.expires_in){if(!t.scope&&(!e.scopes||!e.scopes.length))return this.logger.error("TokenCache - scopes not specified in the request or response. Cannot add token to the cache."),null}else return this.logger.error("TokenCache - no expiration set on the access token. Cannot add it to the cache."),null;else return this.logger.verbose("TokenCache - no access token found in response"),null;this.logger.verbose("TokenCache - loading access token");const c=t.scope?O.fromString(t.scope):new O(e.scopes),h=i.expiresOn||t.expires_in+new Date().getTime()/1e3,d=i.extendedExpiresOn||(t.ext_expires_in||t.expires_in)+new Date().getTime()/1e3,g=ft(n,o,t.access_token,this.config.auth.clientId,r,c.printScopes(),h,d,ne);return this.storage.setAccessTokenCredential(g,s),g}loadRefreshToken(e,t,n,o){if(!e.refresh_token)return this.logger.verbose("TokenCache - no refresh token found in response"),null;this.logger.verbose("TokenCache - loading refresh token");const r=Ho(t,n,e.refresh_token,this.config.auth.clientId,e.foci,void 0,e.refresh_token_expires_in);return this.storage.setRefreshTokenCredential(r,o),r}generateAuthenticationResult(e,t,n,o){var d,g,f;let r="",i=[],s=null,c;t!=null&&t.accessToken&&(r=t.accessToken.secret,i=O.fromString(t.accessToken.target).asArray(),s=new Date(Number(t.accessToken.expiresOn)*1e3),c=new Date(Number(t.accessToken.extendedExpiresOn)*1e3));const h=t.account;return{authority:o?o.canonicalAuthority:"",uniqueId:t.account.localAccountId,tenantId:t.account.realm,scopes:i,account:h.getAccountInfo(),idToken:((d=t.idToken)==null?void 0:d.secret)||"",idTokenClaims:n||{},accessToken:r,fromCache:!0,expiresOn:s,correlationId:e.correlationId||"",requestId:"",extExpiresOn:c,familyId:((g=t.refreshToken)==null?void 0:g.familyId)||"",tokenType:((f=t==null?void 0:t.accessToken)==null?void 0:f.tokenType)||"",state:e.state||"",cloudGraphHostName:h.cloudGraphHostName||"",msGraphHost:h.msGraphHost||"",fromNativeBroker:!1}}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Sc extends cr{constructor(e){super(e),this.includeRedirectUri=!1}}/*! @azure/msal-browser v3.30.0 2025-08-05 */class wc extends Ke{constructor(e,t,n,o,r,i,s,c,h,d){super(e,t,n,o,r,i,c,h,d),this.apiId=s}async acquireToken(e){if(!e.code)throw C(Or);const t=await m(this.initializeAuthorizationRequest.bind(this),l.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,e.correlationId)(e,exports.InteractionType.Silent),n=this.initializeServerTelemetryManager(this.apiId);try{const o={...t,code:e.code},r=await m(this.getClientConfiguration.bind(this),l.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,e.correlationId)({serverTelemetryManager:n,requestAuthority:t.authority,requestAzureCloudOptions:t.azureCloudOptions,requestExtraQueryParameters:t.extraQueryParameters,account:t.account}),i=new Sc(r);this.logger.verbose("Auth code client created");const s=new Nn(i,this.browserStorage,o,this.logger,this.performanceClient);return await m(s.handleCodeResponseFromServer.bind(s),l.HandleCodeResponseFromServer,this.logger,this.performanceClient,e.correlationId)({code:e.code,msgraph_host:e.msGraphHost,cloud_graph_host_name:e.cloudGraphHostName,cloud_instance_host_name:e.cloudInstanceHostName},t,!1)}catch(o){throw o instanceof b&&(o.setCorrelationId(this.correlationId),n.cacheFailedRequest(o)),o}}logout(){return Promise.reject(C(vt))}}/*! @azure/msal-browser v3.30.0 2025-08-05 */function J(a){const e=a==null?void 0:a.idTokenClaims;if(e!=null&&e.tfp||e!=null&&e.acr)return"B2C";if(e!=null&&e.tid){if((e==null?void 0:e.tid)==="9188040d-6c67-4c5b-b112-36a304b66dad")return"MSA"}else return;return"AAD"}function nt(a,e){try{Rn(a)}catch(t){throw e.end({success:!1},t),t}}class kt{constructor(e){this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.initialized=!1,this.logger=this.operatingContext.getLogger(),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,this.hybridAuthCodeResponses=new Map,this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new Ue(this.logger,this.performanceClient):it,this.eventHandler=new ei(this.logger),this.browserStorage=this.isBrowserEnvironment?new zt(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,Fa(this.config.auth),this.performanceClient):$s(this.config.auth.clientId,this.logger);const t={cacheLocation:B.MemoryStorage,temporaryCacheLocation:B.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1};this.nativeInternalStorage=new zt(this.config.auth.clientId,t,this.browserCrypto,this.logger,void 0,this.performanceClient),this.tokenCache=new vc(this.config,this.browserStorage,this.logger,this.browserCrypto),this.activeSilentTokenRequests=new Map,this.trackPageVisibility=this.trackPageVisibility.bind(this),this.trackPageVisibilityWithMeasurement=this.trackPageVisibilityWithMeasurement.bind(this),this.listeningToStorageEvents=!1,this.handleAccountCacheChange=this.handleAccountCacheChange.bind(this)}static async createController(e,t){const n=new kt(e);return await n.initialize(t),n}trackPageVisibility(e){e&&(this.logger.info("Perf: Visibility change detected"),this.performanceClient.incrementFields({visibilityChangeCount:1},e))}async initialize(e){if(this.logger.trace("initialize called"),this.initialized){this.logger.info("initialize has already been called, exiting early.");return}if(!this.isBrowserEnvironment){this.logger.info("in non-browser environment, exiting early."),this.initialized=!0,this.eventHandler.emitEvent(T.INITIALIZE_END);return}const t=(e==null?void 0:e.correlationId)||this.getRequestCorrelationId(),n=this.config.system.allowNativeBroker,o=this.performanceClient.startMeasurement(l.InitializeClientApplication,t);if(this.eventHandler.emitEvent(T.INITIALIZE_START),n)try{this.nativeExtensionProvider=await ee.createProvider(this.logger,this.config.system.nativeBrokerHandshakeTimeout,this.performanceClient)}catch(r){this.logger.verbose(r)}this.config.cache.claimsBasedCachingEnabled||(this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"),await m(this.browserStorage.clearTokensAndKeysWithClaims.bind(this.browserStorage),l.ClearTokensAndKeysWithClaims,this.logger,this.performanceClient,t)(this.performanceClient,t)),this.initialized=!0,this.eventHandler.emitEvent(T.INITIALIZE_END),o.end({allowNativeBroker:n,success:!0})}async handleRedirectPromise(e){if(this.logger.verbose("handleRedirectPromise called"),Wr(this.initialized),this.isBrowserEnvironment){const t=e||"";let n=this.redirectResponse.get(t);return typeof n>"u"?(n=this.handleRedirectPromiseInternal(e),this.redirectResponse.set(t,n),this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise")):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),n}return this.logger.verbose("handleRedirectPromise returns null, not browser environment"),null}async handleRedirectPromiseInternal(e){const t=this.getAllAccounts(),n=this.browserStorage.getCachedNativeRequest(),o=n&&ee.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider)&&this.nativeExtensionProvider&&!e,r=o?n==null?void 0:n.correlationId:this.browserStorage.getTemporaryCache(w.CORRELATION_ID,!0)||"",i=this.performanceClient.startMeasurement(l.AcquireTokenRedirect,r);this.eventHandler.emitEvent(T.HANDLE_REDIRECT_START,exports.InteractionType.Redirect);let s;if(o&&this.nativeExtensionProvider){this.logger.trace("handleRedirectPromise - acquiring token from native platform");const c=new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.handleRedirectPromise,this.performanceClient,this.nativeExtensionProvider,n.accountId,this.nativeInternalStorage,n.correlationId);s=m(c.handleRedirectPromise.bind(c),l.HandleNativeRedirectPromiseMeasurement,this.logger,this.performanceClient,i.event.correlationId)(this.performanceClient,i.event.correlationId)}else{this.logger.trace("handleRedirectPromise - acquiring token from web flow");const c=this.createRedirectClient(r);s=m(c.handleRedirectPromise.bind(c),l.HandleRedirectPromiseMeasurement,this.logger,this.performanceClient,i.event.correlationId)(e,i)}return s.then(c=>(c?(t.length<this.getAllAccounts().length?(this.eventHandler.emitEvent(T.LOGIN_SUCCESS,exports.InteractionType.Redirect,c),this.logger.verbose("handleRedirectResponse returned result, login success")):(this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_SUCCESS,exports.InteractionType.Redirect,c),this.logger.verbose("handleRedirectResponse returned result, acquire token success")),i.end({success:!0,accountType:J(c.account)})):i.event.errorCode?i.end({success:!1}):i.discard(),this.eventHandler.emitEvent(T.HANDLE_REDIRECT_END,exports.InteractionType.Redirect),c)).catch(c=>{const h=c;throw t.length>0?this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_FAILURE,exports.InteractionType.Redirect,null,h):this.eventHandler.emitEvent(T.LOGIN_FAILURE,exports.InteractionType.Redirect,null,h),this.eventHandler.emitEvent(T.HANDLE_REDIRECT_END,exports.InteractionType.Redirect),i.end({success:!1},h),c})}async acquireTokenRedirect(e){const t=this.getRequestCorrelationId(e);this.logger.verbose("acquireTokenRedirect called",t);const n=this.performanceClient.startMeasurement(l.AcquireTokenPreRedirect,t);n.add({accountType:J(e.account),scenarioId:e.scenarioId});const o=e.onRedirectNavigate;if(o)e.onRedirectNavigate=i=>{const s=typeof o=="function"?o(i):void 0;return s!==!1?n.end({success:!0}):n.discard(),s};else{const i=this.config.auth.onRedirectNavigate;this.config.auth.onRedirectNavigate=s=>{const c=typeof i=="function"?i(s):void 0;return c!==!1?n.end({success:!0}):n.discard(),c}}const r=this.getAllAccounts().length>0;try{ho(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0),r?this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_START,exports.InteractionType.Redirect,e):this.eventHandler.emitEvent(T.LOGIN_START,exports.InteractionType.Redirect,e);let i;return this.nativeExtensionProvider&&this.canUseNative(e)?i=new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.acquireTokenRedirect,this.performanceClient,this.nativeExtensionProvider,this.getNativeAccountId(e),this.nativeInternalStorage,t).acquireTokenRedirect(e,n).catch(c=>{if(c instanceof Z&&_e(c))return this.nativeExtensionProvider=void 0,this.createRedirectClient(t).acquireToken(e);if(c instanceof Y)return this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow"),this.createRedirectClient(t).acquireToken(e);throw this.browserStorage.setInteractionInProgress(!1),c}):i=this.createRedirectClient(t).acquireToken(e),await i}catch(i){throw n.end({success:!1},i),r?this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_FAILURE,exports.InteractionType.Redirect,null,i):this.eventHandler.emitEvent(T.LOGIN_FAILURE,exports.InteractionType.Redirect,null,i),i}}acquireTokenPopup(e){const t=this.getRequestCorrelationId(e),n=this.performanceClient.startMeasurement(l.AcquireTokenPopup,t);n.add({scenarioId:e.scenarioId,accountType:J(e.account)});try{this.logger.verbose("acquireTokenPopup called",t),nt(this.initialized,n),this.browserStorage.setInteractionInProgress(!0)}catch(i){return Promise.reject(i)}const o=this.getAllAccounts();o.length>0?this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_START,exports.InteractionType.Popup,e):this.eventHandler.emitEvent(T.LOGIN_START,exports.InteractionType.Popup,e);let r;return this.canUseNative(e)?r=this.acquireTokenNative({...e,correlationId:t},R.acquireTokenPopup).then(i=>(this.browserStorage.setInteractionInProgress(!1),n.end({success:!0,isNativeBroker:!0,accountType:J(i.account)}),i)).catch(i=>{if(i instanceof Z&&_e(i))return this.nativeExtensionProvider=void 0,this.createPopupClient(t).acquireToken(e);if(i instanceof Y)return this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow"),this.createPopupClient(t).acquireToken(e);throw this.browserStorage.setInteractionInProgress(!1),i}):r=this.createPopupClient(t).acquireToken(e),r.then(i=>(o.length<this.getAllAccounts().length?this.eventHandler.emitEvent(T.LOGIN_SUCCESS,exports.InteractionType.Popup,i):this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_SUCCESS,exports.InteractionType.Popup,i),n.end({success:!0,accessTokenSize:i.accessToken.length,idTokenSize:i.idToken.length,accountType:J(i.account)}),i)).catch(i=>(o.length>0?this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_FAILURE,exports.InteractionType.Popup,null,i):this.eventHandler.emitEvent(T.LOGIN_FAILURE,exports.InteractionType.Popup,null,i),n.end({success:!1},i),Promise.reject(i)))}trackPageVisibilityWithMeasurement(){const e=this.ssoSilentMeasurement||this.acquireTokenByCodeAsyncMeasurement;e&&(this.logger.info("Perf: Visibility change detected in ",e.event.name),e.increment({visibilityChangeCount:1}))}async ssoSilent(e){var r,i;const t=this.getRequestCorrelationId(e),n={...e,prompt:e.prompt,correlationId:t};this.ssoSilentMeasurement=this.performanceClient.startMeasurement(l.SsoSilent,t),(r=this.ssoSilentMeasurement)==null||r.add({scenarioId:e.scenarioId,accountType:J(e.account)}),nt(this.initialized,this.ssoSilentMeasurement),(i=this.ssoSilentMeasurement)==null||i.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),this.logger.verbose("ssoSilent called",t),this.eventHandler.emitEvent(T.SSO_SILENT_START,exports.InteractionType.Silent,n);let o;return this.canUseNative(n)?o=this.acquireTokenNative(n,R.ssoSilent).catch(s=>{if(s instanceof Z&&_e(s))return this.nativeExtensionProvider=void 0,this.createSilentIframeClient(n.correlationId).acquireToken(n);throw s}):o=this.createSilentIframeClient(n.correlationId).acquireToken(n),o.then(s=>{var c;return this.eventHandler.emitEvent(T.SSO_SILENT_SUCCESS,exports.InteractionType.Silent,s),(c=this.ssoSilentMeasurement)==null||c.end({success:!0,isNativeBroker:s.fromNativeBroker,accessTokenSize:s.accessToken.length,idTokenSize:s.idToken.length,accountType:J(s.account)}),s}).catch(s=>{var c;throw this.eventHandler.emitEvent(T.SSO_SILENT_FAILURE,exports.InteractionType.Silent,null,s),(c=this.ssoSilentMeasurement)==null||c.end({success:!1},s),s}).finally(()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)})}async acquireTokenByCode(e){const t=this.getRequestCorrelationId(e);this.logger.trace("acquireTokenByCode called",t);const n=this.performanceClient.startMeasurement(l.AcquireTokenByCode,t);nt(this.initialized,n),this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_BY_CODE_START,exports.InteractionType.Silent,e),n.add({scenarioId:e.scenarioId});try{if(e.code&&e.nativeAccountId)throw C(Pr);if(e.code){const o=e.code;let r=this.hybridAuthCodeResponses.get(o);return r?(this.logger.verbose("Existing acquireTokenByCode request found",t),n.discard()):(this.logger.verbose("Initiating new acquireTokenByCode request",t),r=this.acquireTokenByCodeAsync({...e,correlationId:t}).then(i=>(this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_BY_CODE_SUCCESS,exports.InteractionType.Silent,i),this.hybridAuthCodeResponses.delete(o),n.end({success:!0,isNativeBroker:i.fromNativeBroker,accessTokenSize:i.accessToken.length,idTokenSize:i.idToken.length,accountType:J(i.account)}),i)).catch(i=>{throw this.hybridAuthCodeResponses.delete(o),this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_BY_CODE_FAILURE,exports.InteractionType.Silent,null,i),n.end({success:!1},i),i}),this.hybridAuthCodeResponses.set(o,r)),await r}else if(e.nativeAccountId)if(this.canUseNative(e,e.nativeAccountId)){const o=await this.acquireTokenNative({...e,correlationId:t},R.acquireTokenByCode,e.nativeAccountId).catch(r=>{throw r instanceof Z&&_e(r)&&(this.nativeExtensionProvider=void 0),r});return n.end({accountType:J(o.account),success:!0}),o}else throw C(Mr);else throw C(Nr)}catch(o){throw this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_BY_CODE_FAILURE,exports.InteractionType.Silent,null,o),n.end({success:!1},o),o}}async acquireTokenByCodeAsync(e){var o;return this.logger.trace("acquireTokenByCodeAsync called",e.correlationId),this.acquireTokenByCodeAsyncMeasurement=this.performanceClient.startMeasurement(l.AcquireTokenByCodeAsync,e.correlationId),(o=this.acquireTokenByCodeAsyncMeasurement)==null||o.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),await this.createSilentAuthCodeClient(e.correlationId).acquireToken(e).then(r=>{var i;return(i=this.acquireTokenByCodeAsyncMeasurement)==null||i.end({success:!0,fromCache:r.fromCache,isNativeBroker:r.fromNativeBroker}),r}).catch(r=>{var i;throw(i=this.acquireTokenByCodeAsyncMeasurement)==null||i.end({success:!1},r),r}).finally(()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)})}async acquireTokenFromCache(e,t){switch(this.performanceClient.addQueueMeasurement(l.AcquireTokenFromCache,e.correlationId),t){case G.Default:case G.AccessToken:case G.AccessTokenAndRefreshToken:const n=this.createSilentCacheClient(e.correlationId);return m(n.acquireToken.bind(n),l.SilentCacheClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw p(se)}}async acquireTokenByRefreshToken(e,t){switch(this.performanceClient.addQueueMeasurement(l.AcquireTokenByRefreshToken,e.correlationId),t){case G.Default:case G.AccessTokenAndRefreshToken:case G.RefreshToken:case G.RefreshTokenAndNetwork:const n=this.createSilentRefreshClient(e.correlationId);return m(n.acquireToken.bind(n),l.SilentRefreshClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw p(se)}}async acquireTokenBySilentIframe(e){this.performanceClient.addQueueMeasurement(l.AcquireTokenBySilentIframe,e.correlationId);const t=this.createSilentIframeClient(e.correlationId);return m(t.acquireToken.bind(t),l.SilentIframeClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e)}async logout(e){const t=this.getRequestCorrelationId(e);return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.",t),this.logoutRedirect({correlationId:t,...e})}async logoutRedirect(e){const t=this.getRequestCorrelationId(e);return ho(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0),this.createRedirectClient(t).logout(e)}logoutPopup(e){try{const t=this.getRequestCorrelationId(e);return Rn(this.initialized),this.browserStorage.setInteractionInProgress(!0),this.createPopupClient(t).logout(e)}catch(t){return Promise.reject(t)}}async clearCache(e){if(!this.isBrowserEnvironment){this.logger.info("in non-browser environment, returning early.");return}const t=this.getRequestCorrelationId(e);return this.createSilentCacheClient(t).logout(e)}getAllAccounts(e){const t=this.getRequestCorrelationId();return zs(this.logger,this.browserStorage,this.isBrowserEnvironment,t,e)}getAccount(e){const t=this.getRequestCorrelationId();return Vs(e,this.logger,this.browserStorage,t)}getAccountByUsername(e){const t=this.getRequestCorrelationId();return Qs(e,this.logger,this.browserStorage,t)}getAccountByHomeId(e){const t=this.getRequestCorrelationId();return Ys(e,this.logger,this.browserStorage,t)}getAccountByLocalId(e){const t=this.getRequestCorrelationId();return Ws(e,this.logger,this.browserStorage,t)}setActiveAccount(e){const t=this.getRequestCorrelationId();js(e,this.browserStorage,t)}getActiveAccount(){const e=this.getRequestCorrelationId();return Js(this.browserStorage,e)}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");const n=P.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return this.browserStorage.setAccount(n,e.correlationId),e.fromNativeBroker?(this.logger.verbose("Response was from native broker, storing in-memory"),this.nativeInternalStorage.hydrateCache(e,t)):this.browserStorage.hydrateCache(e,t)}async acquireTokenNative(e,t,n){if(this.logger.trace("acquireTokenNative called"),!this.nativeExtensionProvider)throw C(Ve);return new Ne(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,t,this.performanceClient,this.nativeExtensionProvider,n||this.getNativeAccountId(e),this.nativeInternalStorage,e.correlationId).acquireToken(e)}canUseNative(e,t){if(this.logger.trace("canUseNative called"),!ee.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme))return this.logger.trace("canUseNative: isNativeAvailable returned false, returning false"),!1;if(e.prompt)switch(e.prompt){case M.NONE:case M.CONSENT:case M.LOGIN:this.logger.trace("canUseNative: prompt is compatible with native flow");break;default:return this.logger.trace(`canUseNative: prompt = ${e.prompt} is not compatible with native flow, returning false`),!1}return!t&&!this.getNativeAccountId(e)?(this.logger.trace("canUseNative: nativeAccountId is not available, returning false"),!1):!0}getNativeAccountId(e){const t=e.account||this.getAccount({loginHint:e.loginHint,sid:e.sid})||this.getActiveAccount();return t&&t.nativeAccountId||""}createPopupClient(e){return new gc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createRedirectClient(e){return new mc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentIframeClient(e){return new Ac(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.ssoSilent,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentCacheClient(e){return new oi(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentRefreshClient(e){return new Ec(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentAuthCodeClient(e){return new wc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,R.acquireTokenByCode,this.performanceClient,this.nativeExtensionProvider,e)}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){return Yr(),this.performanceClient.addPerformanceCallback(e)}removePerformanceCallback(e){return this.performanceClient.removePerformanceCallback(e)}enableAccountStorageEvents(){typeof window>"u"||(this.listeningToStorageEvents?this.logger.verbose("Account storage listener already registered."):(this.logger.verbose("Adding account storage listener."),this.listeningToStorageEvents=!0,window.addEventListener("storage",this.handleAccountCacheChange)))}disableAccountStorageEvents(){typeof window>"u"||(this.listeningToStorageEvents?(this.logger.verbose("Removing account storage listener."),window.removeEventListener("storage",this.handleAccountCacheChange),this.listeningToStorageEvents=!1):this.logger.verbose("No account storage listener registered."))}handleAccountCacheChange(e){var t;try{(t=e.key)!=null&&t.includes(U.ACTIVE_ACCOUNT_FILTERS)&&this.eventHandler.emitEvent(T.ACTIVE_ACCOUNT_CHANGED);const n=e.newValue||e.oldValue;if(!n)return;const o=JSON.parse(n);if(typeof o!="object"||!P.isAccountEntity(o))return;const i=Me.toObject(new P,o).getAccountInfo();!e.oldValue&&e.newValue?(this.logger.info("Account was added to cache in a different window"),this.eventHandler.emitEvent(T.ACCOUNT_ADDED,void 0,i)):!e.newValue&&e.oldValue&&(this.logger.info("Account was removed from cache in a different window"),this.eventHandler.emitEvent(T.ACCOUNT_REMOVED,void 0,i))}catch{return}}getTokenCache(){return this.tokenCache}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){this.navigationClient=e}getConfiguration(){return this.config}getPerformanceClient(){return this.performanceClient}isBrowserEnv(){return this.isBrowserEnvironment}getRequestCorrelationId(e){return e!=null&&e.correlationId?e.correlationId:this.isBrowserEnvironment?re():u.EMPTY_STRING}async loginRedirect(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginRedirect called",t),this.acquireTokenRedirect({correlationId:t,...e||ao})}loginPopup(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginPopup called",t),this.acquireTokenPopup({correlationId:t,...e||ao})}async acquireTokenSilent(e){const t=this.getRequestCorrelationId(e),n=this.performanceClient.startMeasurement(l.AcquireTokenSilent,t);n.add({cacheLookupPolicy:e.cacheLookupPolicy,scenarioId:e.scenarioId}),nt(this.initialized,n),this.logger.verbose("acquireTokenSilent called",t);const o=e.account||this.getActiveAccount();if(!o)throw C(Sr);n.add({accountType:J(o)});const r={clientId:this.config.auth.clientId,authority:e.authority||u.EMPTY_STRING,scopes:e.scopes,homeAccountIdentifier:o.homeAccountId,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid,shrOptions:e.shrOptions},i=JSON.stringify(r),s=this.activeSilentTokenRequests.get(i);if(typeof s>"u"){this.logger.verbose("acquireTokenSilent called for the first time, storing active request",t);const c=m(this.acquireTokenSilentAsync.bind(this),l.AcquireTokenSilentAsync,this.logger,this.performanceClient,t)({...e,correlationId:t},o).then(h=>(this.activeSilentTokenRequests.delete(i),n.end({success:!0,fromCache:h.fromCache,isNativeBroker:h.fromNativeBroker,cacheLookupPolicy:e.cacheLookupPolicy,accessTokenSize:h.accessToken.length,idTokenSize:h.idToken.length}),h)).catch(h=>{throw this.activeSilentTokenRequests.delete(i),n.end({success:!1},h),h});return this.activeSilentTokenRequests.set(i,c),{...await c,state:e.state}}else return this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call",t),n.discard(),{...await s,state:e.state}}async acquireTokenSilentAsync(e,t){const n=()=>this.trackPageVisibility(e.correlationId);this.performanceClient.addQueueMeasurement(l.AcquireTokenSilentAsync,e.correlationId),this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_START,exports.InteractionType.Silent,e),e.correlationId&&this.performanceClient.incrementFields({visibilityChangeCount:0},e.correlationId),document.addEventListener("visibilitychange",n);const o=await m(nc,l.InitializeSilentRequest,this.logger,this.performanceClient,e.correlationId)(e,t,this.config,this.performanceClient,this.logger),r=e.cacheLookupPolicy||G.Default;return this.acquireTokenSilentNoIframe(o,r).catch(async s=>{if(_c(s,r))if(this.activeIframeRequest)if(r!==G.Skip){const[h,d]=this.activeIframeRequest;this.logger.verbose(`Iframe request is already in progress, awaiting resolution for request with correlationId: ${d}`,o.correlationId);const g=this.performanceClient.startMeasurement(l.AwaitConcurrentIframe,o.correlationId);g.add({awaitIframeCorrelationId:d});const f=await h;if(g.end({success:f}),f)return this.logger.verbose(`Parallel iframe request with correlationId: ${d} succeeded. Retrying cache and/or RT redemption`,o.correlationId),this.acquireTokenSilentNoIframe(o,r);throw this.logger.info(`Iframe request with correlationId: ${d} failed. Interaction is required.`),s}else return this.logger.warning("Another iframe request is currently in progress and CacheLookupPolicy is set to Skip. This may result in degraded performance and/or reliability for both calls. Please consider changing the CacheLookupPolicy to take advantage of request queuing and token cache.",o.correlationId),m(this.acquireTokenBySilentIframe.bind(this),l.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o);else{let h;return this.activeIframeRequest=[new Promise(d=>{h=d}),o.correlationId],this.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.",o.correlationId),m(this.acquireTokenBySilentIframe.bind(this),l.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o).then(d=>(h(!0),d)).catch(d=>{throw h(!1),d}).finally(()=>{this.activeIframeRequest=void 0})}else throw s}).then(s=>(this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_SUCCESS,exports.InteractionType.Silent,s),e.correlationId&&this.performanceClient.addFields({fromCache:s.fromCache,isNativeBroker:s.fromNativeBroker},e.correlationId),s)).catch(s=>{throw this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_FAILURE,exports.InteractionType.Silent,null,s),s}).finally(()=>{document.removeEventListener("visibilitychange",n)})}async acquireTokenSilentNoIframe(e,t){return ee.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme)&&e.account.nativeAccountId?(this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"),this.acquireTokenNative(e,R.acquireTokenSilent_silentFlow).catch(async n=>{throw n instanceof Z&&_e(n)?(this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"),this.nativeExtensionProvider=void 0,p(se)):n})):(this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"),m(this.acquireTokenFromCache.bind(this),l.AcquireTokenFromCache,this.logger,this.performanceClient,e.correlationId)(e,t).catch(n=>{if(t===G.AccessToken)throw n;return this.eventHandler.emitEvent(T.ACQUIRE_TOKEN_NETWORK_START,exports.InteractionType.Silent,e),m(this.acquireTokenByRefreshToken.bind(this),l.AcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,t)}))}}function _c(a,e){const t=!(a instanceof Y&&a.subError!==At),n=a.errorCode===$.INVALID_GRANT_ERROR||a.errorCode===se,o=t&&n||a.errorCode===lt||a.errorCode===pn,r=ls.includes(e);return o&&r}/*! @azure/msal-browser v3.30.0 2025-08-05 */async function kc(a,e){const t=new Te(a);return await t.initialize(),kt.createController(t,e)}/*! @azure/msal-browser v3.30.0 2025-08-05 */class Pn{static async createPublicClientApplication(e){const t=await kc(e);return new Pn(e,t)}constructor(e,t){this.controller=t||new kt(new Te(e))}async initialize(e){return this.controller.initialize(e)}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}exports.AccountEntity=P;exports.ApiId=R;exports.AuthError=b;exports.AuthenticationScheme=_;exports.AzureCloudInstance=pt;exports.BrowserAuthError=De;exports.BrowserCacheLocation=B;exports.BrowserConfigurationAuthError=_t;exports.CacheLookupPolicy=G;exports.ClientAuthError=ze;exports.ClientConfigurationError=yt;exports.DEFAULT_IFRAME_TIMEOUT_MS=ut;exports.EventHandler=ei;exports.EventType=T;exports.InteractionRequiredAuthError=Y;exports.JsonWebTokenTypes=fo;exports.LocalStorage=Jr;exports.Logger=he;exports.MemoryStorage=gt;exports.NavigationClient=$e;exports.OIDC_DEFAULT_SCOPES=Ie;exports.PerformanceEvents=l;exports.PromptValue=M;exports.ProtocolMode=te;exports.PublicClientApplication=Pn;exports.ServerError=le;exports.ServerResponseType=He;exports.SessionStorage=Xr;exports.StringUtils=Q;exports.StubPerformanceClient=ur;exports.UrlString=S;exports.version=ye;
