import React from 'react';
import { useThemeStyles } from '../../services/theme/themeContext';

export interface ButtonProps {
  /**
   * Button contents
   */
  children: React.ReactNode;
  /**
   * Optional click handler
   */
  onClick?: () => void;
  /**
   * Button variant
   */
  variant?: 'primary' | 'secondary' | 'danger';
  /**
   * Button size
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Is the button disabled?
   */
  disabled?: boolean;
  /**
   * Button type
   */
  type?: 'button' | 'submit' | 'reset';
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

/**
 * Primary UI component for user interaction
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  type = 'button',
  className = '',
  style,
  ...props
}) => {
  const { getThemeClass } = useThemeStyles();

  // Use enhanced ZB theme-aware classes
  const baseClasses = 'zb-button theme-button';

  const variantClasses = {
    primary: 'zb-button-primary theme-button-primary',
    secondary: 'zb-button-secondary theme-button-secondary',
    danger: 'zb-button-danger theme-button-danger',
  };

  const sizeClasses = {
    small: 'zb-button-sm theme-button-sm',
    medium: 'zb-button-base theme-button-base',
    large: 'zb-button-lg theme-button-lg',
  };

  const disabledClasses = disabled
    ? 'disabled'
    : '';

  const classes = [
    getThemeClass(baseClasses),
    getThemeClass(variantClasses[variant]),
    getThemeClass(sizeClasses[size]),
    disabledClasses,
    className,
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={classes}
      style={style}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};
