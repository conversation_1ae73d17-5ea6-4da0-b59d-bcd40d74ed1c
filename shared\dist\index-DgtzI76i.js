"use strict";var u=Object.defineProperty;var g=(r,e,t)=>e in r?u(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var i=(r,e,t)=>g(r,typeof e!="symbol"?e+"":e,t);const m={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1};var f=(r=>(r.WEB_RESOURCE="web_resource",r.EMBEDDED_SPA="embedded_spa",r.STANDALONE_MFE="standalone_mfe",r))(f||{}),s=(r=>(r.CRM="crm",r.MFE="mfe",r))(s||{});function l(r){switch(r){case"standalone":return"standalone_mfe";case"web_resource":return"web_resource";case"embedded_spa":return"embedded_spa";case"standalone_mfe":return"standalone_mfe";default:return"web_resource"}}const o=class o{constructor(){i(this,"_detectedMode",null);i(this,"_config",null)}static getInstance(){return o._instance||(o._instance=new o),o._instance}detectDeploymentMode(){if(this._detectedMode)return this._detectedMode;const e=this.getConfigurationOverride();if(e)return this._detectedMode=e,this._detectedMode;const t=this.isDynamics365Environment(),n=this.isEmbeddedSPAEnvironment();return t?this._detectedMode="web_resource":n?this._detectedMode="embedded_spa":this._detectedMode="standalone_mfe",this._detectedMode}detectThemeMode(){const e=this.detectDeploymentMode(),t=this.getThemeOverride();if(t)return t;switch(e){case"web_resource":case"embedded_spa":return"crm";case"standalone_mfe":return"mfe";default:return"crm"}}isDynamics365Environment(){try{if(typeof window<"u"){if(window.Xrm&&window.Xrm.WebApi)return!0;if(window.parent&&window.parent!==window)try{if(window.parent.Xrm)return!0}catch{}const e=window.location.href;if(e.includes("/WebResources/")||e.includes("/_static/"))return!0}return!1}catch(e){return console.warn("Error detecting Dynamics 365 environment:",e),!1}}isEmbeddedSPAEnvironment(){try{if(typeof window<"u"){const e=window.location.href;if((e.includes(".dynamics.com")||e.includes(".crm.dynamics.com"))&&!e.includes("/WebResources/")&&!e.includes("/_static/"))return!0;const t=new URLSearchParams(window.location.search);if(t.has("embedded")||t.has("spa_mode")||document.referrer&&(document.referrer.includes(".dynamics.com")||document.referrer.includes(".crm.dynamics.com")))return!0}return!1}catch(e){return console.warn("Error detecting embedded SPA environment:",e),!1}}getConfigurationOverride(){try{const e=this.getEnvironmentVariable("VITE_DEPLOYMENT_MODE");if(e)return l(e);if(typeof window<"u"){const n=new URLSearchParams(window.location.search).get("deploymentMode");if(n)return l(n)}return null}catch(e){return console.warn("Error getting configuration override:",e),null}}getThemeOverride(){try{const e=this.getEnvironmentVariable("VITE_THEME_MODE");if(e&&Object.values(s).includes(e))return e;if(typeof window<"u"){const n=new URLSearchParams(window.location.search).get("themeMode");if(n&&Object.values(s).includes(n))return n}return null}catch(e){return console.warn("Error getting theme override:",e),null}}getEnvironmentVariable(e){try{return(m==null?void 0:m[e])||null}catch{return null}}getDeploymentConfig(){if(this._config)return this._config;const e=this.detectDeploymentMode();return this._config=this.createConfigForMode(e),this._config}createConfigForMode(e){const t=this.detectThemeMode(),n=this.createThemeConfig(t),d={mode:e,theme:n,features:{enableLogging:this.getEnvironmentVariable("VITE_ENABLE_LOGGING")==="true"||e!=="web_resource",enableOfflineMode:this.getEnvironmentVariable("VITE_ENABLE_OFFLINE")==="true"||!1,enableTelemetry:this.getEnvironmentVariable("VITE_ENABLE_TELEMETRY")==="true"||!1,enableThemeSwitching:this.getEnvironmentVariable("VITE_ENABLE_THEME_SWITCHING")==="true"||!1}};switch(e){case"web_resource":return{...d,apiBaseUrl:"",authMethod:"dynamics365",dynamicsConfig:{serverUrl:this.getEnvironmentVariable("VITE_DYNAMICS_SERVER_URL")||"",version:this.getEnvironmentVariable("VITE_DYNAMICS_API_VERSION")||"9.2"}};case"embedded_spa":return{...d,apiBaseUrl:this.getEnvironmentVariable("VITE_API_BASE_URL")||"https://your-org.api.crm.dynamics.com/api/data/v9.2",authMethod:"msal",msalConfig:{clientId:this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID")||"",authority:this.getEnvironmentVariable("VITE_MSAL_AUTHORITY")||"https://login.microsoftonline.com/common",redirectUri:this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI")||window.location.origin,scopes:(this.getEnvironmentVariable("VITE_MSAL_SCOPES")||"https://your-org.crm.dynamics.com/.default").split(",")}};case"standalone_mfe":return{...d,apiBaseUrl:this.getEnvironmentVariable("VITE_API_BASE_URL")||"https://your-org.api.crm.dynamics.com/api/data/v9.2",authMethod:"msal",msalConfig:{clientId:this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID")||"",authority:this.getEnvironmentVariable("VITE_MSAL_AUTHORITY")||"https://login.microsoftonline.com/common",redirectUri:this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI")||window.location.origin,scopes:(this.getEnvironmentVariable("VITE_MSAL_SCOPES")||"https://your-org.crm.dynamics.com/.default").split(",")}};default:throw new Error(`Unsupported deployment mode: ${e}`)}}createThemeConfig(e){switch(e){case"crm":return{mode:"crm",primaryColor:"#0078d4",secondaryColor:"#106ebe",backgroundColor:"#ffffff",textColor:"#323130",borderColor:"#8a8886",fontFamily:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',customProperties:{"--crm-header-bg":"#0078d4","--crm-sidebar-bg":"#f3f2f1","--crm-card-bg":"#ffffff","--crm-border-radius":"2px","--crm-shadow":"0 2px 4px rgba(0, 0, 0, 0.1)","--crm-spacing-xs":"4px","--crm-spacing-sm":"8px","--crm-spacing-md":"16px","--crm-spacing-lg":"24px","--crm-spacing-xl":"32px"}};case"mfe":return{mode:"mfe",primaryColor:"#6366f1",secondaryColor:"#4f46e5",backgroundColor:"#f8fafc",textColor:"#1e293b",borderColor:"#e2e8f0",fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',customProperties:{"--mfe-header-bg":"#1e293b","--mfe-sidebar-bg":"#f1f5f9","--mfe-card-bg":"#ffffff","--mfe-border-radius":"8px","--mfe-shadow":"0 4px 6px -1px rgba(0, 0, 0, 0.1)","--mfe-spacing-xs":"4px","--mfe-spacing-sm":"8px","--mfe-spacing-md":"16px","--mfe-spacing-lg":"24px","--mfe-spacing-xl":"32px"}};default:throw new Error(`Unsupported theme mode: ${e}`)}}forceDeploymentMode(e){this._detectedMode=e,this._config=null}reset(){this._detectedMode=null,this._config=null}};i(o,"_instance");let a=o;function c(){return a.getInstance().getDeploymentConfig()}function h(){return c().mode==="web_resource"}function E(){const r=c().mode;return r==="embedded_spa"||r==="standalone_mfe"}function _(){return c().theme}exports.DeploymentContextDetector=a;exports.DeploymentMode=f;exports.ThemeMode=s;exports.getDeploymentConfig=c;exports.getThemeConfig=_;exports.isStandaloneMode=E;exports.isWebResourceMode=h;
