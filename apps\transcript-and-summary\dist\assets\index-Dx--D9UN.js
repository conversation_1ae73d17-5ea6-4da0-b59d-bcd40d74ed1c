var xd=Object.defineProperty;var Cd=(e,t,n)=>t in e?xd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ee=(e,t,n)=>Cd(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const l of o.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Td(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pu={exports:{}},Yi={},Ou={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _r=Symbol.for("react.element"),_d=Symbol.for("react.portal"),Pd=Symbol.for("react.fragment"),Od=Symbol.for("react.strict_mode"),Rd=Symbol.for("react.profiler"),Nd=Symbol.for("react.provider"),Dd=Symbol.for("react.context"),Ad=Symbol.for("react.forward_ref"),Ld=Symbol.for("react.suspense"),Md=Symbol.for("react.memo"),Id=Symbol.for("react.lazy"),Js=Symbol.iterator;function Ud(e){return e===null||typeof e!="object"?null:(e=Js&&e[Js]||e["@@iterator"],typeof e=="function"?e:null)}var Ru={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Nu=Object.assign,Du={};function An(e,t,n){this.props=e,this.context=t,this.refs=Du,this.updater=n||Ru}An.prototype.isReactComponent={};An.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};An.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Au(){}Au.prototype=An.prototype;function Zl(e,t,n){this.props=e,this.context=t,this.refs=Du,this.updater=n||Ru}var es=Zl.prototype=new Au;es.constructor=Zl;Nu(es,An.prototype);es.isPureReactComponent=!0;var Zs=Array.isArray,Lu=Object.prototype.hasOwnProperty,ts={current:null},Mu={key:!0,ref:!0,__self:!0,__source:!0};function Iu(e,t,n){var r,i={},o=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(o=""+t.key),t)Lu.call(t,r)&&!Mu.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:_r,type:e,key:o,ref:l,props:i,_owner:ts.current}}function zd(e,t){return{$$typeof:_r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ns(e){return typeof e=="object"&&e!==null&&e.$$typeof===_r}function jd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ea=/\/+/g;function wo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?jd(""+e.key):t.toString(36)}function ti(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(o){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case _r:case _d:l=!0}}if(l)return l=e,i=i(l),e=r===""?"."+wo(l,0):r,Zs(i)?(n="",e!=null&&(n=e.replace(ea,"$&/")+"/"),ti(i,t,n,"",function(u){return u})):i!=null&&(ns(i)&&(i=zd(i,n+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(ea,"$&/")+"/")+e)),t.push(i)),1;if(l=0,r=r===""?".":r+":",Zs(e))for(var s=0;s<e.length;s++){o=e[s];var a=r+wo(o,s);l+=ti(o,t,n,a,i)}else if(a=Ud(e),typeof a=="function")for(e=a.call(e),s=0;!(o=e.next()).done;)o=o.value,a=r+wo(o,s++),l+=ti(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function zr(e,t,n){if(e==null)return e;var r=[],i=0;return ti(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Fd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},ni={transition:null},Bd={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:ni,ReactCurrentOwner:ts};function Uu(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:zr,forEach:function(e,t,n){zr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return zr(e,function(){t++}),t},toArray:function(e){return zr(e,function(t){return t})||[]},only:function(e){if(!ns(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=An;I.Fragment=Pd;I.Profiler=Rd;I.PureComponent=Zl;I.StrictMode=Od;I.Suspense=Ld;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bd;I.act=Uu;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Nu({},e.props),i=e.key,o=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,l=ts.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)Lu.call(t,a)&&!Mu.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:_r,type:e.type,key:i,ref:o,props:r,_owner:l}};I.createContext=function(e){return e={$$typeof:Dd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Nd,_context:e},e.Consumer=e};I.createElement=Iu;I.createFactory=function(e){var t=Iu.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:Ad,render:e}};I.isValidElement=ns;I.lazy=function(e){return{$$typeof:Id,_payload:{_status:-1,_result:e},_init:Fd}};I.memo=function(e,t){return{$$typeof:Md,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=ni.transition;ni.transition={};try{e()}finally{ni.transition=t}};I.unstable_act=Uu;I.useCallback=function(e,t){return ye.current.useCallback(e,t)};I.useContext=function(e){return ye.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};I.useEffect=function(e,t){return ye.current.useEffect(e,t)};I.useId=function(){return ye.current.useId()};I.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return ye.current.useMemo(e,t)};I.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};I.useRef=function(e){return ye.current.useRef(e)};I.useState=function(e){return ye.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return ye.current.useTransition()};I.version="18.3.1";Ou.exports=I;var K=Ou.exports;const $d=Td(K);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wd=K,Vd=Symbol.for("react.element"),Hd=Symbol.for("react.fragment"),bd=Object.prototype.hasOwnProperty,Yd=Wd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Qd={key:!0,ref:!0,__self:!0,__source:!0};function zu(e,t,n){var r,i={},o=null,l=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)bd.call(t,r)&&!Qd.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Vd,type:e,key:o,ref:l,props:i,_owner:Yd.current}}Yi.Fragment=Hd;Yi.jsx=zu;Yi.jsxs=zu;Pu.exports=Yi;var E=Pu.exports,Jo={},ju={exports:{}},De={},Fu={exports:{}},Bu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,D){var A=O.length;O.push(D);e:for(;0<A;){var G=A-1>>>1,re=O[G];if(0<i(re,D))O[G]=D,O[A]=re,A=G;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var D=O[0],A=O.pop();if(A!==D){O[0]=A;e:for(var G=0,re=O.length,Ir=re>>>1;G<Ir;){var zt=2*(G+1)-1,vo=O[zt],jt=zt+1,Ur=O[jt];if(0>i(vo,A))jt<re&&0>i(Ur,vo)?(O[G]=Ur,O[jt]=A,G=jt):(O[G]=vo,O[zt]=A,G=zt);else if(jt<re&&0>i(Ur,A))O[G]=Ur,O[jt]=A,G=jt;else break e}}return D}function i(O,D){var A=O.sortIndex-D.sortIndex;return A!==0?A:O.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],f=1,d=null,p=3,v=!1,g=!1,w=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(O){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=O)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function S(O){if(w=!1,m(O),!g)if(n(a)!==null)g=!0,go(C);else{var D=n(u);D!==null&&yo(S,D.startTime-O)}}function C(O,D){g=!1,w&&(w=!1,h(R),R=-1),v=!0;var A=p;try{for(m(D),d=n(a);d!==null&&(!(d.expirationTime>D)||O&&!$());){var G=d.callback;if(typeof G=="function"){d.callback=null,p=d.priorityLevel;var re=G(d.expirationTime<=D);D=e.unstable_now(),typeof re=="function"?d.callback=re:d===n(a)&&r(a),m(D)}else r(a);d=n(a)}if(d!==null)var Ir=!0;else{var zt=n(u);zt!==null&&yo(S,zt.startTime-D),Ir=!1}return Ir}finally{d=null,p=A,v=!1}}var T=!1,_=null,R=-1,F=5,L=-1;function $(){return!(e.unstable_now()-L<F)}function we(){if(_!==null){var O=e.unstable_now();L=O;var D=!0;try{D=_(!0,O)}finally{D?Ge():(T=!1,_=null)}}else T=!1}var Ge;if(typeof c=="function")Ge=function(){c(we)};else if(typeof MessageChannel<"u"){var Xs=new MessageChannel,kd=Xs.port2;Xs.port1.onmessage=we,Ge=function(){kd.postMessage(null)}}else Ge=function(){x(we,0)};function go(O){_=O,T||(T=!0,Ge())}function yo(O,D){R=x(function(){O(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){g||v||(g=!0,go(C))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(O){switch(p){case 1:case 2:case 3:var D=3;break;default:D=p}var A=p;p=D;try{return O()}finally{p=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,D){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var A=p;p=O;try{return D()}finally{p=A}},e.unstable_scheduleCallback=function(O,D,A){var G=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?G+A:G):A=G,O){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=A+re,O={id:f++,callback:D,priorityLevel:O,startTime:A,expirationTime:re,sortIndex:-1},A>G?(O.sortIndex=A,t(u,O),n(a)===null&&O===n(u)&&(w?(h(R),R=-1):w=!0,yo(S,A-G))):(O.sortIndex=re,t(a,O),g||v||(g=!0,go(C))),O},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(O){var D=p;return function(){var A=p;p=D;try{return O.apply(this,arguments)}finally{p=A}}}})(Bu);Fu.exports=Bu;var qd=Fu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd=K,Ne=qd;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var $u=new Set,ar={};function nn(e,t){Tn(e,t),Tn(e+"Capture",t)}function Tn(e,t){for(ar[e]=t,e=0;e<t.length;e++)$u.add(t[e])}var ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zo=Object.prototype.hasOwnProperty,Kd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ta={},na={};function Xd(e){return Zo.call(na,e)?!0:Zo.call(ta,e)?!1:Kd.test(e)?na[e]=!0:(ta[e]=!0,!1)}function Jd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Zd(e,t,n,r){if(t===null||typeof t>"u"||Jd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,i,o,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var ae={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ae[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ae[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ae[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ae[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ae[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ae[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ae[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ae[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ae[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var rs=/[\-:]([a-z])/g;function is(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(rs,is);ae[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(rs,is);ae[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(rs,is);ae[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ae[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ae.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ae[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function os(e,t,n,r){var i=ae.hasOwnProperty(t)?ae[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Zd(t,n,i,r)&&(n=null),r||i===null?Xd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ht=Gd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,jr=Symbol.for("react.element"),sn=Symbol.for("react.portal"),an=Symbol.for("react.fragment"),ls=Symbol.for("react.strict_mode"),el=Symbol.for("react.profiler"),Wu=Symbol.for("react.provider"),Vu=Symbol.for("react.context"),ss=Symbol.for("react.forward_ref"),tl=Symbol.for("react.suspense"),nl=Symbol.for("react.suspense_list"),as=Symbol.for("react.memo"),yt=Symbol.for("react.lazy"),Hu=Symbol.for("react.offscreen"),ra=Symbol.iterator;function zn(e){return e===null||typeof e!="object"?null:(e=ra&&e[ra]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,So;function Gn(e){if(So===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);So=t&&t[1]||""}return`
`+So+e}var Eo=!1;function ko(e,t){if(!e||Eo)return"";Eo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),l=i.length-1,s=o.length-1;1<=l&&0<=s&&i[l]!==o[s];)s--;for(;1<=l&&0<=s;l--,s--)if(i[l]!==o[s]){if(l!==1||s!==1)do if(l--,s--,0>s||i[l]!==o[s]){var a=`
`+i[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Eo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Gn(e):""}function ep(e){switch(e.tag){case 5:return Gn(e.type);case 16:return Gn("Lazy");case 13:return Gn("Suspense");case 19:return Gn("SuspenseList");case 0:case 2:case 15:return e=ko(e.type,!1),e;case 11:return e=ko(e.type.render,!1),e;case 1:return e=ko(e.type,!0),e;default:return""}}function rl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case an:return"Fragment";case sn:return"Portal";case el:return"Profiler";case ls:return"StrictMode";case tl:return"Suspense";case nl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Vu:return(e.displayName||"Context")+".Consumer";case Wu:return(e._context.displayName||"Context")+".Provider";case ss:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case as:return t=e.displayName||null,t!==null?t:rl(e.type)||"Memo";case yt:t=e._payload,e=e._init;try{return rl(e(t))}catch{}}return null}function tp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return rl(t);case 8:return t===ls?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function bu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function np(e){var t=bu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(l){r=""+l,o.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Fr(e){e._valueTracker||(e._valueTracker=np(e))}function Yu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=bu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function yi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function il(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ia(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Qu(e,t){t=t.checked,t!=null&&os(e,"checked",t,!1)}function ol(e,t){Qu(e,t);var n=Dt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ll(e,t.type,n):t.hasOwnProperty("defaultValue")&&ll(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function oa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ll(e,t,n){(t!=="number"||yi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Kn=Array.isArray;function wn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function sl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function la(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(Kn(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function qu(e,t){var n=Dt(t.value),r=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function sa(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Gu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function al(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Gu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Br,Ku=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Br=Br||document.createElement("div"),Br.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Br.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ur(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},rp=["Webkit","ms","Moz","O"];Object.keys(Zn).forEach(function(e){rp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zn[t]=Zn[e]})});function Xu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Zn.hasOwnProperty(e)&&Zn[e]?(""+t).trim():t+"px"}function Ju(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Xu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var ip=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ul(e,t){if(t){if(ip[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function cl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var fl=null;function us(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var dl=null,Sn=null,En=null;function aa(e){if(e=Rr(e)){if(typeof dl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Xi(t),dl(e.stateNode,e.type,t))}}function Zu(e){Sn?En?En.push(e):En=[e]:Sn=e}function ec(){if(Sn){var e=Sn,t=En;if(En=Sn=null,aa(e),t)for(e=0;e<t.length;e++)aa(t[e])}}function tc(e,t){return e(t)}function nc(){}var xo=!1;function rc(e,t,n){if(xo)return e(t,n);xo=!0;try{return tc(e,t,n)}finally{xo=!1,(Sn!==null||En!==null)&&(nc(),ec())}}function cr(e,t){var n=e.stateNode;if(n===null)return null;var r=Xi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var pl=!1;if(ut)try{var jn={};Object.defineProperty(jn,"passive",{get:function(){pl=!0}}),window.addEventListener("test",jn,jn),window.removeEventListener("test",jn,jn)}catch{pl=!1}function op(e,t,n,r,i,o,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var er=!1,vi=null,wi=!1,hl=null,lp={onError:function(e){er=!0,vi=e}};function sp(e,t,n,r,i,o,l,s,a){er=!1,vi=null,op.apply(lp,arguments)}function ap(e,t,n,r,i,o,l,s,a){if(sp.apply(this,arguments),er){if(er){var u=vi;er=!1,vi=null}else throw Error(k(198));wi||(wi=!0,hl=u)}}function rn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ic(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ua(e){if(rn(e)!==e)throw Error(k(188))}function up(e){var t=e.alternate;if(!t){if(t=rn(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return ua(i),e;if(o===r)return ua(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var l=!1,s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l){for(s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function oc(e){return e=up(e),e!==null?lc(e):null}function lc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=lc(e);if(t!==null)return t;e=e.sibling}return null}var sc=Ne.unstable_scheduleCallback,ca=Ne.unstable_cancelCallback,cp=Ne.unstable_shouldYield,fp=Ne.unstable_requestPaint,X=Ne.unstable_now,dp=Ne.unstable_getCurrentPriorityLevel,cs=Ne.unstable_ImmediatePriority,ac=Ne.unstable_UserBlockingPriority,Si=Ne.unstable_NormalPriority,pp=Ne.unstable_LowPriority,uc=Ne.unstable_IdlePriority,Qi=null,et=null;function hp(e){if(et&&typeof et.onCommitFiberRoot=="function")try{et.onCommitFiberRoot(Qi,e,void 0,(e.current.flags&128)===128)}catch{}}var be=Math.clz32?Math.clz32:yp,mp=Math.log,gp=Math.LN2;function yp(e){return e>>>=0,e===0?32:31-(mp(e)/gp|0)|0}var $r=64,Wr=4194304;function Xn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ei(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~i;s!==0?r=Xn(s):(o&=l,o!==0&&(r=Xn(o)))}else l=n&~i,l!==0?r=Xn(l):o!==0&&(r=Xn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-be(t),i=1<<n,r|=e[n],t&=~i;return r}function vp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function wp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-be(o),s=1<<l,a=i[l];a===-1?(!(s&n)||s&r)&&(i[l]=vp(s,t)):a<=t&&(e.expiredLanes|=s),o&=~s}}function ml(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function cc(){var e=$r;return $r<<=1,!($r&4194240)&&($r=64),e}function Co(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-be(t),e[t]=n}function Sp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-be(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function fs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-be(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var j=0;function fc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var dc,ds,pc,hc,mc,gl=!1,Vr=[],xt=null,Ct=null,Tt=null,fr=new Map,dr=new Map,wt=[],Ep="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function fa(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":Ct=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":fr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":dr.delete(t.pointerId)}}function Fn(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Rr(t),t!==null&&ds(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function kp(e,t,n,r,i){switch(t){case"focusin":return xt=Fn(xt,e,t,n,r,i),!0;case"dragenter":return Ct=Fn(Ct,e,t,n,r,i),!0;case"mouseover":return Tt=Fn(Tt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return fr.set(o,Fn(fr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,dr.set(o,Fn(dr.get(o)||null,e,t,n,r,i)),!0}return!1}function gc(e){var t=Vt(e.target);if(t!==null){var n=rn(t);if(n!==null){if(t=n.tag,t===13){if(t=ic(n),t!==null){e.blockedOn=t,mc(e.priority,function(){pc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=yl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);fl=r,n.target.dispatchEvent(r),fl=null}else return t=Rr(n),t!==null&&ds(t),e.blockedOn=n,!1;t.shift()}return!0}function da(e,t,n){ri(e)&&n.delete(t)}function xp(){gl=!1,xt!==null&&ri(xt)&&(xt=null),Ct!==null&&ri(Ct)&&(Ct=null),Tt!==null&&ri(Tt)&&(Tt=null),fr.forEach(da),dr.forEach(da)}function Bn(e,t){e.blockedOn===t&&(e.blockedOn=null,gl||(gl=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,xp)))}function pr(e){function t(i){return Bn(i,e)}if(0<Vr.length){Bn(Vr[0],e);for(var n=1;n<Vr.length;n++){var r=Vr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xt!==null&&Bn(xt,e),Ct!==null&&Bn(Ct,e),Tt!==null&&Bn(Tt,e),fr.forEach(t),dr.forEach(t),n=0;n<wt.length;n++)r=wt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<wt.length&&(n=wt[0],n.blockedOn===null);)gc(n),n.blockedOn===null&&wt.shift()}var kn=ht.ReactCurrentBatchConfig,ki=!0;function Cp(e,t,n,r){var i=j,o=kn.transition;kn.transition=null;try{j=1,ps(e,t,n,r)}finally{j=i,kn.transition=o}}function Tp(e,t,n,r){var i=j,o=kn.transition;kn.transition=null;try{j=4,ps(e,t,n,r)}finally{j=i,kn.transition=o}}function ps(e,t,n,r){if(ki){var i=yl(e,t,n,r);if(i===null)Mo(e,t,r,xi,n),fa(e,r);else if(kp(i,e,t,n,r))r.stopPropagation();else if(fa(e,r),t&4&&-1<Ep.indexOf(e)){for(;i!==null;){var o=Rr(i);if(o!==null&&dc(o),o=yl(e,t,n,r),o===null&&Mo(e,t,r,xi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Mo(e,t,r,null,n)}}var xi=null;function yl(e,t,n,r){if(xi=null,e=us(r),e=Vt(e),e!==null)if(t=rn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ic(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xi=e,null}function yc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(dp()){case cs:return 1;case ac:return 4;case Si:case pp:return 16;case uc:return 536870912;default:return 16}default:return 16}}var Et=null,hs=null,ii=null;function vc(){if(ii)return ii;var e,t=hs,n=t.length,r,i="value"in Et?Et.value:Et.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===i[o-r];r++);return ii=i.slice(e,1<r?1-r:void 0)}function oi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Hr(){return!0}function pa(){return!1}function Ae(e){function t(n,r,i,o,l){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Hr:pa,this.isPropagationStopped=pa,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hr)},persist:function(){},isPersistent:Hr}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ms=Ae(Ln),Or=Q({},Ln,{view:0,detail:0}),_p=Ae(Or),To,_o,$n,qi=Q({},Or,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$n&&($n&&e.type==="mousemove"?(To=e.screenX-$n.screenX,_o=e.screenY-$n.screenY):_o=To=0,$n=e),To)},movementY:function(e){return"movementY"in e?e.movementY:_o}}),ha=Ae(qi),Pp=Q({},qi,{dataTransfer:0}),Op=Ae(Pp),Rp=Q({},Or,{relatedTarget:0}),Po=Ae(Rp),Np=Q({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),Dp=Ae(Np),Ap=Q({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Lp=Ae(Ap),Mp=Q({},Ln,{data:0}),ma=Ae(Mp),Ip={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Up={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},zp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=zp[e])?!!t[e]:!1}function gs(){return jp}var Fp=Q({},Or,{key:function(e){if(e.key){var t=Ip[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=oi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Up[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gs,charCode:function(e){return e.type==="keypress"?oi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?oi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bp=Ae(Fp),$p=Q({},qi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ga=Ae($p),Wp=Q({},Or,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gs}),Vp=Ae(Wp),Hp=Q({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),bp=Ae(Hp),Yp=Q({},qi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Qp=Ae(Yp),qp=[9,13,27,32],ys=ut&&"CompositionEvent"in window,tr=null;ut&&"documentMode"in document&&(tr=document.documentMode);var Gp=ut&&"TextEvent"in window&&!tr,wc=ut&&(!ys||tr&&8<tr&&11>=tr),ya=" ",va=!1;function Sc(e,t){switch(e){case"keyup":return qp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ec(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var un=!1;function Kp(e,t){switch(e){case"compositionend":return Ec(t);case"keypress":return t.which!==32?null:(va=!0,ya);case"textInput":return e=t.data,e===ya&&va?null:e;default:return null}}function Xp(e,t){if(un)return e==="compositionend"||!ys&&Sc(e,t)?(e=vc(),ii=hs=Et=null,un=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return wc&&t.locale!=="ko"?null:t.data;default:return null}}var Jp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jp[e.type]:t==="textarea"}function kc(e,t,n,r){Zu(r),t=Ci(t,"onChange"),0<t.length&&(n=new ms("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var nr=null,hr=null;function Zp(e){Lc(e,0)}function Gi(e){var t=dn(e);if(Yu(t))return e}function eh(e,t){if(e==="change")return t}var xc=!1;if(ut){var Oo;if(ut){var Ro="oninput"in document;if(!Ro){var Sa=document.createElement("div");Sa.setAttribute("oninput","return;"),Ro=typeof Sa.oninput=="function"}Oo=Ro}else Oo=!1;xc=Oo&&(!document.documentMode||9<document.documentMode)}function Ea(){nr&&(nr.detachEvent("onpropertychange",Cc),hr=nr=null)}function Cc(e){if(e.propertyName==="value"&&Gi(hr)){var t=[];kc(t,hr,e,us(e)),rc(Zp,t)}}function th(e,t,n){e==="focusin"?(Ea(),nr=t,hr=n,nr.attachEvent("onpropertychange",Cc)):e==="focusout"&&Ea()}function nh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Gi(hr)}function rh(e,t){if(e==="click")return Gi(t)}function ih(e,t){if(e==="input"||e==="change")return Gi(t)}function oh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qe=typeof Object.is=="function"?Object.is:oh;function mr(e,t){if(Qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Zo.call(t,i)||!Qe(e[i],t[i]))return!1}return!0}function ka(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xa(e,t){var n=ka(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ka(n)}}function Tc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Tc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function _c(){for(var e=window,t=yi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=yi(e.document)}return t}function vs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function lh(e){var t=_c(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Tc(n.ownerDocument.documentElement,n)){if(r!==null&&vs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=xa(n,o);var l=xa(n,r);i&&l&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var sh=ut&&"documentMode"in document&&11>=document.documentMode,cn=null,vl=null,rr=null,wl=!1;function Ca(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wl||cn==null||cn!==yi(r)||(r=cn,"selectionStart"in r&&vs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),rr&&mr(rr,r)||(rr=r,r=Ci(vl,"onSelect"),0<r.length&&(t=new ms("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=cn)))}function br(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var fn={animationend:br("Animation","AnimationEnd"),animationiteration:br("Animation","AnimationIteration"),animationstart:br("Animation","AnimationStart"),transitionend:br("Transition","TransitionEnd")},No={},Pc={};ut&&(Pc=document.createElement("div").style,"AnimationEvent"in window||(delete fn.animationend.animation,delete fn.animationiteration.animation,delete fn.animationstart.animation),"TransitionEvent"in window||delete fn.transitionend.transition);function Ki(e){if(No[e])return No[e];if(!fn[e])return e;var t=fn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Pc)return No[e]=t[n];return e}var Oc=Ki("animationend"),Rc=Ki("animationiteration"),Nc=Ki("animationstart"),Dc=Ki("transitionend"),Ac=new Map,Ta="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mt(e,t){Ac.set(e,t),nn(t,[e])}for(var Do=0;Do<Ta.length;Do++){var Ao=Ta[Do],ah=Ao.toLowerCase(),uh=Ao[0].toUpperCase()+Ao.slice(1);Mt(ah,"on"+uh)}Mt(Oc,"onAnimationEnd");Mt(Rc,"onAnimationIteration");Mt(Nc,"onAnimationStart");Mt("dblclick","onDoubleClick");Mt("focusin","onFocus");Mt("focusout","onBlur");Mt(Dc,"onTransitionEnd");Tn("onMouseEnter",["mouseout","mouseover"]);Tn("onMouseLeave",["mouseout","mouseover"]);Tn("onPointerEnter",["pointerout","pointerover"]);Tn("onPointerLeave",["pointerout","pointerover"]);nn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));nn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));nn("onBeforeInput",["compositionend","keypress","textInput","paste"]);nn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));nn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));nn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ch=new Set("cancel close invalid load scroll toggle".split(" ").concat(Jn));function _a(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ap(r,t,void 0,e),e.currentTarget=null}function Lc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==o&&i.isPropagationStopped())break e;_a(i,s,u),o=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==o&&i.isPropagationStopped())break e;_a(i,s,u),o=a}}}if(wi)throw e=hl,wi=!1,hl=null,e}function W(e,t){var n=t[Cl];n===void 0&&(n=t[Cl]=new Set);var r=e+"__bubble";n.has(r)||(Mc(t,e,2,!1),n.add(r))}function Lo(e,t,n){var r=0;t&&(r|=4),Mc(n,e,r,t)}var Yr="_reactListening"+Math.random().toString(36).slice(2);function gr(e){if(!e[Yr]){e[Yr]=!0,$u.forEach(function(n){n!=="selectionchange"&&(ch.has(n)||Lo(n,!1,e),Lo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Yr]||(t[Yr]=!0,Lo("selectionchange",!1,t))}}function Mc(e,t,n,r){switch(yc(t)){case 1:var i=Cp;break;case 4:i=Tp;break;default:i=ps}n=i.bind(null,t,n,e),i=void 0,!pl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Mo(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;l=l.return}for(;s!==null;){if(l=Vt(s),l===null)return;if(a=l.tag,a===5||a===6){r=o=l;continue e}s=s.parentNode}}r=r.return}rc(function(){var u=o,f=us(n),d=[];e:{var p=Ac.get(e);if(p!==void 0){var v=ms,g=e;switch(e){case"keypress":if(oi(n)===0)break e;case"keydown":case"keyup":v=Bp;break;case"focusin":g="focus",v=Po;break;case"focusout":g="blur",v=Po;break;case"beforeblur":case"afterblur":v=Po;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=ha;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=Op;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=Vp;break;case Oc:case Rc:case Nc:v=Dp;break;case Dc:v=bp;break;case"scroll":v=_p;break;case"wheel":v=Qp;break;case"copy":case"cut":case"paste":v=Lp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=ga}var w=(t&4)!==0,x=!w&&e==="scroll",h=w?p!==null?p+"Capture":null:p;w=[];for(var c=u,m;c!==null;){m=c;var S=m.stateNode;if(m.tag===5&&S!==null&&(m=S,h!==null&&(S=cr(c,h),S!=null&&w.push(yr(c,S,m)))),x)break;c=c.return}0<w.length&&(p=new v(p,g,null,n,f),d.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",p&&n!==fl&&(g=n.relatedTarget||n.fromElement)&&(Vt(g)||g[ct]))break e;if((v||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,v?(g=n.relatedTarget||n.toElement,v=u,g=g?Vt(g):null,g!==null&&(x=rn(g),g!==x||g.tag!==5&&g.tag!==6)&&(g=null)):(v=null,g=u),v!==g)){if(w=ha,S="onMouseLeave",h="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(w=ga,S="onPointerLeave",h="onPointerEnter",c="pointer"),x=v==null?p:dn(v),m=g==null?p:dn(g),p=new w(S,c+"leave",v,n,f),p.target=x,p.relatedTarget=m,S=null,Vt(f)===u&&(w=new w(h,c+"enter",g,n,f),w.target=m,w.relatedTarget=x,S=w),x=S,v&&g)t:{for(w=v,h=g,c=0,m=w;m;m=on(m))c++;for(m=0,S=h;S;S=on(S))m++;for(;0<c-m;)w=on(w),c--;for(;0<m-c;)h=on(h),m--;for(;c--;){if(w===h||h!==null&&w===h.alternate)break t;w=on(w),h=on(h)}w=null}else w=null;v!==null&&Pa(d,p,v,w,!1),g!==null&&x!==null&&Pa(d,x,g,w,!0)}}e:{if(p=u?dn(u):window,v=p.nodeName&&p.nodeName.toLowerCase(),v==="select"||v==="input"&&p.type==="file")var C=eh;else if(wa(p))if(xc)C=ih;else{C=nh;var T=th}else(v=p.nodeName)&&v.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(C=rh);if(C&&(C=C(e,u))){kc(d,C,n,f);break e}T&&T(e,p,u),e==="focusout"&&(T=p._wrapperState)&&T.controlled&&p.type==="number"&&ll(p,"number",p.value)}switch(T=u?dn(u):window,e){case"focusin":(wa(T)||T.contentEditable==="true")&&(cn=T,vl=u,rr=null);break;case"focusout":rr=vl=cn=null;break;case"mousedown":wl=!0;break;case"contextmenu":case"mouseup":case"dragend":wl=!1,Ca(d,n,f);break;case"selectionchange":if(sh)break;case"keydown":case"keyup":Ca(d,n,f)}var _;if(ys)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else un?Sc(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(wc&&n.locale!=="ko"&&(un||R!=="onCompositionStart"?R==="onCompositionEnd"&&un&&(_=vc()):(Et=f,hs="value"in Et?Et.value:Et.textContent,un=!0)),T=Ci(u,R),0<T.length&&(R=new ma(R,e,null,n,f),d.push({event:R,listeners:T}),_?R.data=_:(_=Ec(n),_!==null&&(R.data=_)))),(_=Gp?Kp(e,n):Xp(e,n))&&(u=Ci(u,"onBeforeInput"),0<u.length&&(f=new ma("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=_))}Lc(d,t)})}function yr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ci(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=cr(e,n),o!=null&&r.unshift(yr(e,o,i)),o=cr(e,t),o!=null&&r.push(yr(e,o,i))),e=e.return}return r}function on(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pa(e,t,n,r,i){for(var o=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,i?(a=cr(n,o),a!=null&&l.unshift(yr(n,a,s))):i||(a=cr(n,o),a!=null&&l.push(yr(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var fh=/\r\n?/g,dh=/\u0000|\uFFFD/g;function Oa(e){return(typeof e=="string"?e:""+e).replace(fh,`
`).replace(dh,"")}function Qr(e,t,n){if(t=Oa(t),Oa(e)!==t&&n)throw Error(k(425))}function Ti(){}var Sl=null,El=null;function kl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var xl=typeof setTimeout=="function"?setTimeout:void 0,ph=typeof clearTimeout=="function"?clearTimeout:void 0,Ra=typeof Promise=="function"?Promise:void 0,hh=typeof queueMicrotask=="function"?queueMicrotask:typeof Ra<"u"?function(e){return Ra.resolve(null).then(e).catch(mh)}:xl;function mh(e){setTimeout(function(){throw e})}function Io(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),pr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);pr(t)}function _t(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Na(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Mn=Math.random().toString(36).slice(2),Ze="__reactFiber$"+Mn,vr="__reactProps$"+Mn,ct="__reactContainer$"+Mn,Cl="__reactEvents$"+Mn,gh="__reactListeners$"+Mn,yh="__reactHandles$"+Mn;function Vt(e){var t=e[Ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ct]||n[Ze]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Na(e);e!==null;){if(n=e[Ze])return n;e=Na(e)}return t}e=n,n=e.parentNode}return null}function Rr(e){return e=e[Ze]||e[ct],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function dn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Xi(e){return e[vr]||null}var Tl=[],pn=-1;function It(e){return{current:e}}function V(e){0>pn||(e.current=Tl[pn],Tl[pn]=null,pn--)}function B(e,t){pn++,Tl[pn]=e.current,e.current=t}var At={},pe=It(At),ke=It(!1),Kt=At;function _n(e,t){var n=e.type.contextTypes;if(!n)return At;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function xe(e){return e=e.childContextTypes,e!=null}function _i(){V(ke),V(pe)}function Da(e,t,n){if(pe.current!==At)throw Error(k(168));B(pe,t),B(ke,n)}function Ic(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,tp(e)||"Unknown",i));return Q({},n,r)}function Pi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||At,Kt=pe.current,B(pe,e),B(ke,ke.current),!0}function Aa(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Ic(e,t,Kt),r.__reactInternalMemoizedMergedChildContext=e,V(ke),V(pe),B(pe,e)):V(ke),B(ke,n)}var ot=null,Ji=!1,Uo=!1;function Uc(e){ot===null?ot=[e]:ot.push(e)}function vh(e){Ji=!0,Uc(e)}function Ut(){if(!Uo&&ot!==null){Uo=!0;var e=0,t=j;try{var n=ot;for(j=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ot=null,Ji=!1}catch(i){throw ot!==null&&(ot=ot.slice(e+1)),sc(cs,Ut),i}finally{j=t,Uo=!1}}return null}var hn=[],mn=0,Oi=null,Ri=0,Le=[],Me=0,Xt=null,lt=1,st="";function Ft(e,t){hn[mn++]=Ri,hn[mn++]=Oi,Oi=e,Ri=t}function zc(e,t,n){Le[Me++]=lt,Le[Me++]=st,Le[Me++]=Xt,Xt=e;var r=lt;e=st;var i=32-be(r)-1;r&=~(1<<i),n+=1;var o=32-be(t)+i;if(30<o){var l=i-i%5;o=(r&(1<<l)-1).toString(32),r>>=l,i-=l,lt=1<<32-be(t)+i|n<<i|r,st=o+e}else lt=1<<o|n<<i|r,st=e}function ws(e){e.return!==null&&(Ft(e,1),zc(e,1,0))}function Ss(e){for(;e===Oi;)Oi=hn[--mn],hn[mn]=null,Ri=hn[--mn],hn[mn]=null;for(;e===Xt;)Xt=Le[--Me],Le[Me]=null,st=Le[--Me],Le[Me]=null,lt=Le[--Me],Le[Me]=null}var Re=null,Oe=null,H=!1,He=null;function jc(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function La(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Re=e,Oe=_t(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Re=e,Oe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Xt!==null?{id:lt,overflow:st}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Re=e,Oe=null,!0):!1;default:return!1}}function _l(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Pl(e){if(H){var t=Oe;if(t){var n=t;if(!La(e,t)){if(_l(e))throw Error(k(418));t=_t(n.nextSibling);var r=Re;t&&La(e,t)?jc(r,n):(e.flags=e.flags&-4097|2,H=!1,Re=e)}}else{if(_l(e))throw Error(k(418));e.flags=e.flags&-4097|2,H=!1,Re=e}}}function Ma(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Re=e}function qr(e){if(e!==Re)return!1;if(!H)return Ma(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!kl(e.type,e.memoizedProps)),t&&(t=Oe)){if(_l(e))throw Fc(),Error(k(418));for(;t;)jc(e,t),t=_t(t.nextSibling)}if(Ma(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Oe=_t(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Oe=null}}else Oe=Re?_t(e.stateNode.nextSibling):null;return!0}function Fc(){for(var e=Oe;e;)e=_t(e.nextSibling)}function Pn(){Oe=Re=null,H=!1}function Es(e){He===null?He=[e]:He.push(e)}var wh=ht.ReactCurrentBatchConfig;function Wn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(l){var s=i.refs;l===null?delete s[o]:s[o]=l},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function Gr(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ia(e){var t=e._init;return t(e._payload)}function Bc(e){function t(h,c){if(e){var m=h.deletions;m===null?(h.deletions=[c],h.flags|=16):m.push(c)}}function n(h,c){if(!e)return null;for(;c!==null;)t(h,c),c=c.sibling;return null}function r(h,c){for(h=new Map;c!==null;)c.key!==null?h.set(c.key,c):h.set(c.index,c),c=c.sibling;return h}function i(h,c){return h=Nt(h,c),h.index=0,h.sibling=null,h}function o(h,c,m){return h.index=m,e?(m=h.alternate,m!==null?(m=m.index,m<c?(h.flags|=2,c):m):(h.flags|=2,c)):(h.flags|=1048576,c)}function l(h){return e&&h.alternate===null&&(h.flags|=2),h}function s(h,c,m,S){return c===null||c.tag!==6?(c=Vo(m,h.mode,S),c.return=h,c):(c=i(c,m),c.return=h,c)}function a(h,c,m,S){var C=m.type;return C===an?f(h,c,m.props.children,S,m.key):c!==null&&(c.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===yt&&Ia(C)===c.type)?(S=i(c,m.props),S.ref=Wn(h,c,m),S.return=h,S):(S=di(m.type,m.key,m.props,null,h.mode,S),S.ref=Wn(h,c,m),S.return=h,S)}function u(h,c,m,S){return c===null||c.tag!==4||c.stateNode.containerInfo!==m.containerInfo||c.stateNode.implementation!==m.implementation?(c=Ho(m,h.mode,S),c.return=h,c):(c=i(c,m.children||[]),c.return=h,c)}function f(h,c,m,S,C){return c===null||c.tag!==7?(c=qt(m,h.mode,S,C),c.return=h,c):(c=i(c,m),c.return=h,c)}function d(h,c,m){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Vo(""+c,h.mode,m),c.return=h,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case jr:return m=di(c.type,c.key,c.props,null,h.mode,m),m.ref=Wn(h,null,c),m.return=h,m;case sn:return c=Ho(c,h.mode,m),c.return=h,c;case yt:var S=c._init;return d(h,S(c._payload),m)}if(Kn(c)||zn(c))return c=qt(c,h.mode,m,null),c.return=h,c;Gr(h,c)}return null}function p(h,c,m,S){var C=c!==null?c.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:s(h,c,""+m,S);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case jr:return m.key===C?a(h,c,m,S):null;case sn:return m.key===C?u(h,c,m,S):null;case yt:return C=m._init,p(h,c,C(m._payload),S)}if(Kn(m)||zn(m))return C!==null?null:f(h,c,m,S,null);Gr(h,m)}return null}function v(h,c,m,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(m)||null,s(c,h,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case jr:return h=h.get(S.key===null?m:S.key)||null,a(c,h,S,C);case sn:return h=h.get(S.key===null?m:S.key)||null,u(c,h,S,C);case yt:var T=S._init;return v(h,c,m,T(S._payload),C)}if(Kn(S)||zn(S))return h=h.get(m)||null,f(c,h,S,C,null);Gr(c,S)}return null}function g(h,c,m,S){for(var C=null,T=null,_=c,R=c=0,F=null;_!==null&&R<m.length;R++){_.index>R?(F=_,_=null):F=_.sibling;var L=p(h,_,m[R],S);if(L===null){_===null&&(_=F);break}e&&_&&L.alternate===null&&t(h,_),c=o(L,c,R),T===null?C=L:T.sibling=L,T=L,_=F}if(R===m.length)return n(h,_),H&&Ft(h,R),C;if(_===null){for(;R<m.length;R++)_=d(h,m[R],S),_!==null&&(c=o(_,c,R),T===null?C=_:T.sibling=_,T=_);return H&&Ft(h,R),C}for(_=r(h,_);R<m.length;R++)F=v(_,h,R,m[R],S),F!==null&&(e&&F.alternate!==null&&_.delete(F.key===null?R:F.key),c=o(F,c,R),T===null?C=F:T.sibling=F,T=F);return e&&_.forEach(function($){return t(h,$)}),H&&Ft(h,R),C}function w(h,c,m,S){var C=zn(m);if(typeof C!="function")throw Error(k(150));if(m=C.call(m),m==null)throw Error(k(151));for(var T=C=null,_=c,R=c=0,F=null,L=m.next();_!==null&&!L.done;R++,L=m.next()){_.index>R?(F=_,_=null):F=_.sibling;var $=p(h,_,L.value,S);if($===null){_===null&&(_=F);break}e&&_&&$.alternate===null&&t(h,_),c=o($,c,R),T===null?C=$:T.sibling=$,T=$,_=F}if(L.done)return n(h,_),H&&Ft(h,R),C;if(_===null){for(;!L.done;R++,L=m.next())L=d(h,L.value,S),L!==null&&(c=o(L,c,R),T===null?C=L:T.sibling=L,T=L);return H&&Ft(h,R),C}for(_=r(h,_);!L.done;R++,L=m.next())L=v(_,h,R,L.value,S),L!==null&&(e&&L.alternate!==null&&_.delete(L.key===null?R:L.key),c=o(L,c,R),T===null?C=L:T.sibling=L,T=L);return e&&_.forEach(function(we){return t(h,we)}),H&&Ft(h,R),C}function x(h,c,m,S){if(typeof m=="object"&&m!==null&&m.type===an&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case jr:e:{for(var C=m.key,T=c;T!==null;){if(T.key===C){if(C=m.type,C===an){if(T.tag===7){n(h,T.sibling),c=i(T,m.props.children),c.return=h,h=c;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===yt&&Ia(C)===T.type){n(h,T.sibling),c=i(T,m.props),c.ref=Wn(h,T,m),c.return=h,h=c;break e}n(h,T);break}else t(h,T);T=T.sibling}m.type===an?(c=qt(m.props.children,h.mode,S,m.key),c.return=h,h=c):(S=di(m.type,m.key,m.props,null,h.mode,S),S.ref=Wn(h,c,m),S.return=h,h=S)}return l(h);case sn:e:{for(T=m.key;c!==null;){if(c.key===T)if(c.tag===4&&c.stateNode.containerInfo===m.containerInfo&&c.stateNode.implementation===m.implementation){n(h,c.sibling),c=i(c,m.children||[]),c.return=h,h=c;break e}else{n(h,c);break}else t(h,c);c=c.sibling}c=Ho(m,h.mode,S),c.return=h,h=c}return l(h);case yt:return T=m._init,x(h,c,T(m._payload),S)}if(Kn(m))return g(h,c,m,S);if(zn(m))return w(h,c,m,S);Gr(h,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,c!==null&&c.tag===6?(n(h,c.sibling),c=i(c,m),c.return=h,h=c):(n(h,c),c=Vo(m,h.mode,S),c.return=h,h=c),l(h)):n(h,c)}return x}var On=Bc(!0),$c=Bc(!1),Ni=It(null),Di=null,gn=null,ks=null;function xs(){ks=gn=Di=null}function Cs(e){var t=Ni.current;V(Ni),e._currentValue=t}function Ol(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xn(e,t){Di=e,ks=gn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function je(e){var t=e._currentValue;if(ks!==e)if(e={context:e,memoizedValue:t,next:null},gn===null){if(Di===null)throw Error(k(308));gn=e,Di.dependencies={lanes:0,firstContext:e}}else gn=gn.next=e;return t}var Ht=null;function Ts(e){Ht===null?Ht=[e]:Ht.push(e)}function Wc(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ts(t)):(n.next=i.next,i.next=n),t.interleaved=n,ft(e,r)}function ft(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var vt=!1;function _s(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Vc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function at(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Pt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ft(e,n)}return i=r.interleaved,i===null?(t.next=t,Ts(r)):(t.next=i.next,i.next=t),r.interleaved=t,ft(e,n)}function li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fs(e,n)}}function Ua(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=l:o=o.next=l,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ai(e,t,n,r){var i=e.updateQueue;vt=!1;var o=i.firstBaseUpdate,l=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?o=u:l.next=u,l=a;var f=e.alternate;f!==null&&(f=f.updateQueue,s=f.lastBaseUpdate,s!==l&&(s===null?f.firstBaseUpdate=u:s.next=u,f.lastBaseUpdate=a))}if(o!==null){var d=i.baseState;l=0,f=u=a=null,s=o;do{var p=s.lane,v=s.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:v,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var g=e,w=s;switch(p=t,v=n,w.tag){case 1:if(g=w.payload,typeof g=="function"){d=g.call(v,d,p);break e}d=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=w.payload,p=typeof g=="function"?g.call(v,d,p):g,p==null)break e;d=Q({},d,p);break e;case 2:vt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[s]:p.push(s))}else v={eventTime:v,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},f===null?(u=f=v,a=d):f=f.next=v,l|=p;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;p=s,s=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(f===null&&(a=d),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do l|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Zt|=l,e.lanes=l,e.memoizedState=d}}function za(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var Nr={},tt=It(Nr),wr=It(Nr),Sr=It(Nr);function bt(e){if(e===Nr)throw Error(k(174));return e}function Ps(e,t){switch(B(Sr,t),B(wr,e),B(tt,Nr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:al(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=al(t,e)}V(tt),B(tt,t)}function Rn(){V(tt),V(wr),V(Sr)}function Hc(e){bt(Sr.current);var t=bt(tt.current),n=al(t,e.type);t!==n&&(B(wr,e),B(tt,n))}function Os(e){wr.current===e&&(V(tt),V(wr))}var b=It(0);function Li(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var zo=[];function Rs(){for(var e=0;e<zo.length;e++)zo[e]._workInProgressVersionPrimary=null;zo.length=0}var si=ht.ReactCurrentDispatcher,jo=ht.ReactCurrentBatchConfig,Jt=0,Y=null,te=null,ie=null,Mi=!1,ir=!1,Er=0,Sh=0;function ue(){throw Error(k(321))}function Ns(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qe(e[n],t[n]))return!1;return!0}function Ds(e,t,n,r,i,o){if(Jt=o,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,si.current=e===null||e.memoizedState===null?Ch:Th,e=n(r,i),ir){o=0;do{if(ir=!1,Er=0,25<=o)throw Error(k(301));o+=1,ie=te=null,t.updateQueue=null,si.current=_h,e=n(r,i)}while(ir)}if(si.current=Ii,t=te!==null&&te.next!==null,Jt=0,ie=te=Y=null,Mi=!1,t)throw Error(k(300));return e}function As(){var e=Er!==0;return Er=0,e}function Je(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?Y.memoizedState=ie=e:ie=ie.next=e,ie}function Fe(){if(te===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=ie===null?Y.memoizedState:ie.next;if(t!==null)ie=t,te=e;else{if(e===null)throw Error(k(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},ie===null?Y.memoizedState=ie=e:ie=ie.next=e}return ie}function kr(e,t){return typeof t=="function"?t(e):t}function Fo(e){var t=Fe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var l=i.next;i.next=o.next,o.next=l}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var s=l=null,a=null,u=o;do{var f=u.lane;if((Jt&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=d,l=r):a=a.next=d,Y.lanes|=f,Zt|=f}u=u.next}while(u!==null&&u!==o);a===null?l=r:a.next=s,Qe(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Y.lanes|=o,Zt|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Bo(e){var t=Fe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var l=i=i.next;do o=e(o,l.action),l=l.next;while(l!==i);Qe(o,t.memoizedState)||(Ee=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function bc(){}function Yc(e,t){var n=Y,r=Fe(),i=t(),o=!Qe(r.memoizedState,i);if(o&&(r.memoizedState=i,Ee=!0),r=r.queue,Ls(Gc.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,xr(9,qc.bind(null,n,r,i,t),void 0,null),oe===null)throw Error(k(349));Jt&30||Qc(n,t,i)}return i}function Qc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function qc(e,t,n,r){t.value=n,t.getSnapshot=r,Kc(t)&&Xc(e)}function Gc(e,t,n){return n(function(){Kc(t)&&Xc(e)})}function Kc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qe(e,n)}catch{return!0}}function Xc(e){var t=ft(e,1);t!==null&&Ye(t,e,1,-1)}function ja(e){var t=Je();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:kr,lastRenderedState:e},t.queue=e,e=e.dispatch=xh.bind(null,Y,e),[t.memoizedState,e]}function xr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Jc(){return Fe().memoizedState}function ai(e,t,n,r){var i=Je();Y.flags|=e,i.memoizedState=xr(1|t,n,void 0,r===void 0?null:r)}function Zi(e,t,n,r){var i=Fe();r=r===void 0?null:r;var o=void 0;if(te!==null){var l=te.memoizedState;if(o=l.destroy,r!==null&&Ns(r,l.deps)){i.memoizedState=xr(t,n,o,r);return}}Y.flags|=e,i.memoizedState=xr(1|t,n,o,r)}function Fa(e,t){return ai(8390656,8,e,t)}function Ls(e,t){return Zi(2048,8,e,t)}function Zc(e,t){return Zi(4,2,e,t)}function ef(e,t){return Zi(4,4,e,t)}function tf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function nf(e,t,n){return n=n!=null?n.concat([e]):null,Zi(4,4,tf.bind(null,t,e),n)}function Ms(){}function rf(e,t){var n=Fe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ns(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function of(e,t){var n=Fe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ns(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function lf(e,t,n){return Jt&21?(Qe(n,t)||(n=cc(),Y.lanes|=n,Zt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function Eh(e,t){var n=j;j=n!==0&&4>n?n:4,e(!0);var r=jo.transition;jo.transition={};try{e(!1),t()}finally{j=n,jo.transition=r}}function sf(){return Fe().memoizedState}function kh(e,t,n){var r=Rt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},af(e))uf(t,n);else if(n=Wc(e,t,n,r),n!==null){var i=ge();Ye(n,e,r,i),cf(n,t,r)}}function xh(e,t,n){var r=Rt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(af(e))uf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var l=t.lastRenderedState,s=o(l,n);if(i.hasEagerState=!0,i.eagerState=s,Qe(s,l)){var a=t.interleaved;a===null?(i.next=i,Ts(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Wc(e,t,i,r),n!==null&&(i=ge(),Ye(n,e,r,i),cf(n,t,r))}}function af(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function uf(e,t){ir=Mi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fs(e,n)}}var Ii={readContext:je,useCallback:ue,useContext:ue,useEffect:ue,useImperativeHandle:ue,useInsertionEffect:ue,useLayoutEffect:ue,useMemo:ue,useReducer:ue,useRef:ue,useState:ue,useDebugValue:ue,useDeferredValue:ue,useTransition:ue,useMutableSource:ue,useSyncExternalStore:ue,useId:ue,unstable_isNewReconciler:!1},Ch={readContext:je,useCallback:function(e,t){return Je().memoizedState=[e,t===void 0?null:t],e},useContext:je,useEffect:Fa,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ai(4194308,4,tf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ai(4194308,4,e,t)},useInsertionEffect:function(e,t){return ai(4,2,e,t)},useMemo:function(e,t){var n=Je();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Je();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=kh.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=Je();return e={current:e},t.memoizedState=e},useState:ja,useDebugValue:Ms,useDeferredValue:function(e){return Je().memoizedState=e},useTransition:function(){var e=ja(!1),t=e[0];return e=Eh.bind(null,e[1]),Je().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=Je();if(H){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),oe===null)throw Error(k(349));Jt&30||Qc(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Fa(Gc.bind(null,r,o,e),[e]),r.flags|=2048,xr(9,qc.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Je(),t=oe.identifierPrefix;if(H){var n=st,r=lt;n=(r&~(1<<32-be(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Er++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Sh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Th={readContext:je,useCallback:rf,useContext:je,useEffect:Ls,useImperativeHandle:nf,useInsertionEffect:Zc,useLayoutEffect:ef,useMemo:of,useReducer:Fo,useRef:Jc,useState:function(){return Fo(kr)},useDebugValue:Ms,useDeferredValue:function(e){var t=Fe();return lf(t,te.memoizedState,e)},useTransition:function(){var e=Fo(kr)[0],t=Fe().memoizedState;return[e,t]},useMutableSource:bc,useSyncExternalStore:Yc,useId:sf,unstable_isNewReconciler:!1},_h={readContext:je,useCallback:rf,useContext:je,useEffect:Ls,useImperativeHandle:nf,useInsertionEffect:Zc,useLayoutEffect:ef,useMemo:of,useReducer:Bo,useRef:Jc,useState:function(){return Bo(kr)},useDebugValue:Ms,useDeferredValue:function(e){var t=Fe();return te===null?t.memoizedState=e:lf(t,te.memoizedState,e)},useTransition:function(){var e=Bo(kr)[0],t=Fe().memoizedState;return[e,t]},useMutableSource:bc,useSyncExternalStore:Yc,useId:sf,unstable_isNewReconciler:!1};function $e(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Rl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var eo={isMounted:function(e){return(e=e._reactInternals)?rn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),i=Rt(e),o=at(r,i);o.payload=t,n!=null&&(o.callback=n),t=Pt(e,o,i),t!==null&&(Ye(t,e,i,r),li(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),i=Rt(e),o=at(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Pt(e,o,i),t!==null&&(Ye(t,e,i,r),li(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=Rt(e),i=at(n,r);i.tag=2,t!=null&&(i.callback=t),t=Pt(e,i,r),t!==null&&(Ye(t,e,r,n),li(t,e,r))}};function Ba(e,t,n,r,i,o,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,l):t.prototype&&t.prototype.isPureReactComponent?!mr(n,r)||!mr(i,o):!0}function ff(e,t,n){var r=!1,i=At,o=t.contextType;return typeof o=="object"&&o!==null?o=je(o):(i=xe(t)?Kt:pe.current,r=t.contextTypes,o=(r=r!=null)?_n(e,i):At),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=eo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function $a(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&eo.enqueueReplaceState(t,t.state,null)}function Nl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},_s(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=je(o):(o=xe(t)?Kt:pe.current,i.context=_n(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Rl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&eo.enqueueReplaceState(i,i.state,null),Ai(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Nn(e,t){try{var n="",r=t;do n+=ep(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function $o(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Dl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ph=typeof WeakMap=="function"?WeakMap:Map;function df(e,t,n){n=at(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){zi||(zi=!0,$l=r),Dl(e,t)},n}function pf(e,t,n){n=at(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Dl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Dl(e,t),typeof r!="function"&&(Ot===null?Ot=new Set([this]):Ot.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Wa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ph;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=$h.bind(null,e,t,n),t.then(e,e))}function Va(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ha(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=at(-1,1),t.tag=2,Pt(n,t,1))),n.lanes|=1),e)}var Oh=ht.ReactCurrentOwner,Ee=!1;function me(e,t,n,r){t.child=e===null?$c(t,null,n,r):On(t,e.child,n,r)}function ba(e,t,n,r,i){n=n.render;var o=t.ref;return xn(t,i),r=Ds(e,t,n,r,o,i),n=As(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,dt(e,t,i)):(H&&n&&ws(t),t.flags|=1,me(e,t,r,i),t.child)}function Ya(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Ws(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,hf(e,t,o,r,i)):(e=di(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var l=o.memoizedProps;if(n=n.compare,n=n!==null?n:mr,n(l,r)&&e.ref===t.ref)return dt(e,t,i)}return t.flags|=1,e=Nt(o,r),e.ref=t.ref,e.return=t,t.child=e}function hf(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(mr(o,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,dt(e,t,i)}return Al(e,t,n,r,i)}function mf(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(vn,Pe),Pe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(vn,Pe),Pe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(vn,Pe),Pe|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(vn,Pe),Pe|=r;return me(e,t,i,n),t.child}function gf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Al(e,t,n,r,i){var o=xe(n)?Kt:pe.current;return o=_n(t,o),xn(t,i),n=Ds(e,t,n,r,o,i),r=As(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,dt(e,t,i)):(H&&r&&ws(t),t.flags|=1,me(e,t,n,i),t.child)}function Qa(e,t,n,r,i){if(xe(n)){var o=!0;Pi(t)}else o=!1;if(xn(t,i),t.stateNode===null)ui(e,t),ff(t,n,r),Nl(t,n,r,i),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=je(u):(u=xe(n)?Kt:pe.current,u=_n(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";d||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&$a(t,l,r,u),vt=!1;var p=t.memoizedState;l.state=p,Ai(t,r,l,i),a=t.memoizedState,s!==r||p!==a||ke.current||vt?(typeof f=="function"&&(Rl(t,n,f,r),a=t.memoizedState),(s=vt||Ba(t,n,s,r,p,a,u))?(d||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Vc(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:$e(t.type,s),l.props=u,d=t.pendingProps,p=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=je(a):(a=xe(n)?Kt:pe.current,a=_n(t,a));var v=n.getDerivedStateFromProps;(f=typeof v=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==d||p!==a)&&$a(t,l,r,a),vt=!1,p=t.memoizedState,l.state=p,Ai(t,r,l,i);var g=t.memoizedState;s!==d||p!==g||ke.current||vt?(typeof v=="function"&&(Rl(t,n,v,r),g=t.memoizedState),(u=vt||Ba(t,n,u,r,p,g,a)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,g,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,g,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),l.props=r,l.state=g,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ll(e,t,n,r,o,i)}function Ll(e,t,n,r,i,o){gf(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return i&&Aa(t,n,!1),dt(e,t,o);r=t.stateNode,Oh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=On(t,e.child,null,o),t.child=On(t,null,s,o)):me(e,t,s,o),t.memoizedState=r.state,i&&Aa(t,n,!0),t.child}function yf(e){var t=e.stateNode;t.pendingContext?Da(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Da(e,t.context,!1),Ps(e,t.containerInfo)}function qa(e,t,n,r,i){return Pn(),Es(i),t.flags|=256,me(e,t,n,r),t.child}var Ml={dehydrated:null,treeContext:null,retryLane:0};function Il(e){return{baseLanes:e,cachePool:null,transitions:null}}function vf(e,t,n){var r=t.pendingProps,i=b.current,o=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(b,i&1),e===null)return Pl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,o?(r=t.mode,o=t.child,l={mode:"hidden",children:l},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=l):o=ro(l,r,0,null),e=qt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Il(n),t.memoizedState=Ml,e):Is(t,l));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return Rh(e,t,l,r,s,i,n);if(o){o=r.fallback,l=t.mode,i=e.child,s=i.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Nt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?o=Nt(s,o):(o=qt(o,l,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,l=e.child.memoizedState,l=l===null?Il(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},o.memoizedState=l,o.childLanes=e.childLanes&~n,t.memoizedState=Ml,r}return o=e.child,e=o.sibling,r=Nt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Is(e,t){return t=ro({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Kr(e,t,n,r){return r!==null&&Es(r),On(t,e.child,null,n),e=Is(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Rh(e,t,n,r,i,o,l){if(n)return t.flags&256?(t.flags&=-257,r=$o(Error(k(422))),Kr(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=ro({mode:"visible",children:r.children},i,0,null),o=qt(o,i,l,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&On(t,e.child,null,l),t.child.memoizedState=Il(l),t.memoizedState=Ml,o);if(!(t.mode&1))return Kr(e,t,l,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(k(419)),r=$o(o,r,void 0),Kr(e,t,l,r)}if(s=(l&e.childLanes)!==0,Ee||s){if(r=oe,r!==null){switch(l&-l){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|l)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,ft(e,i),Ye(r,e,i,-1))}return $s(),r=$o(Error(k(421))),Kr(e,t,l,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Wh.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Oe=_t(i.nextSibling),Re=t,H=!0,He=null,e!==null&&(Le[Me++]=lt,Le[Me++]=st,Le[Me++]=Xt,lt=e.id,st=e.overflow,Xt=t),t=Is(t,r.children),t.flags|=4096,t)}function Ga(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ol(e.return,t,n)}function Wo(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function wf(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(me(e,t,r.children,n),r=b.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ga(e,n,t);else if(e.tag===19)Ga(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(b,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Li(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Wo(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Li(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Wo(t,!0,n,null,o);break;case"together":Wo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ui(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function dt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Zt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Nt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Nt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Nh(e,t,n){switch(t.tag){case 3:yf(t),Pn();break;case 5:Hc(t);break;case 1:xe(t.type)&&Pi(t);break;case 4:Ps(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(Ni,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(b,b.current&1),t.flags|=128,null):n&t.child.childLanes?vf(e,t,n):(B(b,b.current&1),e=dt(e,t,n),e!==null?e.sibling:null);B(b,b.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return wf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(b,b.current),r)break;return null;case 22:case 23:return t.lanes=0,mf(e,t,n)}return dt(e,t,n)}var Sf,Ul,Ef,kf;Sf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ul=function(){};Ef=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,bt(tt.current);var o=null;switch(n){case"input":i=il(e,i),r=il(e,r),o=[];break;case"select":i=Q({},i,{value:void 0}),r=Q({},r,{value:void 0}),o=[];break;case"textarea":i=sl(e,i),r=sl(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ti)}ul(n,r);var l;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var s=i[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ar.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(s=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ar.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&W("scroll",e),o||s===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};kf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Vn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Dh(e,t,n){var r=t.pendingProps;switch(Ss(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return xe(t.type)&&_i(),ce(t),null;case 3:return r=t.stateNode,Rn(),V(ke),V(pe),Rs(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(qr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(Hl(He),He=null))),Ul(e,t),ce(t),null;case 5:Os(t);var i=bt(Sr.current);if(n=t.type,e!==null&&t.stateNode!=null)Ef(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return ce(t),null}if(e=bt(tt.current),qr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Ze]=t,r[vr]=o,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(i=0;i<Jn.length;i++)W(Jn[i],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":ia(r,o),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},W("invalid",r);break;case"textarea":la(r,o),W("invalid",r)}ul(n,o),i=null;for(var l in o)if(o.hasOwnProperty(l)){var s=o[l];l==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&Qr(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&Qr(r.textContent,s,e),i=["children",""+s]):ar.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&W("scroll",r)}switch(n){case"input":Fr(r),oa(r,o,!0);break;case"textarea":Fr(r),sa(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ti)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Gu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[Ze]=t,e[vr]=r,Sf(e,t,!1,!1),t.stateNode=e;e:{switch(l=cl(n,r),n){case"dialog":W("cancel",e),W("close",e),i=r;break;case"iframe":case"object":case"embed":W("load",e),i=r;break;case"video":case"audio":for(i=0;i<Jn.length;i++)W(Jn[i],e);i=r;break;case"source":W("error",e),i=r;break;case"img":case"image":case"link":W("error",e),W("load",e),i=r;break;case"details":W("toggle",e),i=r;break;case"input":ia(e,r),i=il(e,r),W("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Q({},r,{value:void 0}),W("invalid",e);break;case"textarea":la(e,r),i=sl(e,r),W("invalid",e);break;default:i=r}ul(n,i),s=i;for(o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="style"?Ju(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Ku(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&ur(e,a):typeof a=="number"&&ur(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ar.hasOwnProperty(o)?a!=null&&o==="onScroll"&&W("scroll",e):a!=null&&os(e,o,a,l))}switch(n){case"input":Fr(e),oa(e,r,!1);break;case"textarea":Fr(e),sa(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Dt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?wn(e,!!r.multiple,o,!1):r.defaultValue!=null&&wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ti)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)kf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=bt(Sr.current),bt(tt.current),qr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ze]=t,(o=r.nodeValue!==n)&&(e=Re,e!==null))switch(e.tag){case 3:Qr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Qr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ze]=t,t.stateNode=r}return ce(t),null;case 13:if(V(b),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Oe!==null&&t.mode&1&&!(t.flags&128))Fc(),Pn(),t.flags|=98560,o=!1;else if(o=qr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[Ze]=t}else Pn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),o=!1}else He!==null&&(Hl(He),He=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||b.current&1?ne===0&&(ne=3):$s())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return Rn(),Ul(e,t),e===null&&gr(t.stateNode.containerInfo),ce(t),null;case 10:return Cs(t.type._context),ce(t),null;case 17:return xe(t.type)&&_i(),ce(t),null;case 19:if(V(b),o=t.memoizedState,o===null)return ce(t),null;if(r=(t.flags&128)!==0,l=o.rendering,l===null)if(r)Vn(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Li(e),l!==null){for(t.flags|=128,Vn(o,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,l=o.alternate,l===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(b,b.current&1|2),t.child}e=e.sibling}o.tail!==null&&X()>Dn&&(t.flags|=128,r=!0,Vn(o,!1),t.lanes=4194304)}else{if(!r)if(e=Li(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Vn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!l.alternate&&!H)return ce(t),null}else 2*X()-o.renderingStartTime>Dn&&n!==1073741824&&(t.flags|=128,r=!0,Vn(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(n=o.last,n!==null?n.sibling=l:t.child=l,o.last=l)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=X(),t.sibling=null,n=b.current,B(b,r?n&1|2:n&1),t):(ce(t),null);case 22:case 23:return Bs(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Pe&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function Ah(e,t){switch(Ss(t),t.tag){case 1:return xe(t.type)&&_i(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Rn(),V(ke),V(pe),Rs(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Os(t),null;case 13:if(V(b),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));Pn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(b),null;case 4:return Rn(),null;case 10:return Cs(t.type._context),null;case 22:case 23:return Bs(),null;case 24:return null;default:return null}}var Xr=!1,fe=!1,Lh=typeof WeakSet=="function"?WeakSet:Set,P=null;function yn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function zl(e,t,n){try{n()}catch(r){q(e,t,r)}}var Ka=!1;function Mh(e,t){if(Sl=ki,e=_c(),vs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,f=0,d=e,p=null;t:for(;;){for(var v;d!==n||i!==0&&d.nodeType!==3||(s=l+i),d!==o||r!==0&&d.nodeType!==3||(a=l+r),d.nodeType===3&&(l+=d.nodeValue.length),(v=d.firstChild)!==null;)p=d,d=v;for(;;){if(d===e)break t;if(p===n&&++u===i&&(s=l),p===o&&++f===r&&(a=l),(v=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=v}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(El={focusedElem:e,selectionRange:n},ki=!1,P=t;P!==null;)if(t=P,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,P=e;else for(;P!==null;){t=P;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var w=g.memoizedProps,x=g.memoizedState,h=t.stateNode,c=h.getSnapshotBeforeUpdate(t.elementType===t.type?w:$e(t.type,w),x);h.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(S){q(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,P=e;break}P=t.return}return g=Ka,Ka=!1,g}function or(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&zl(t,n,o)}i=i.next}while(i!==r)}}function to(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function jl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function xf(e){var t=e.alternate;t!==null&&(e.alternate=null,xf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ze],delete t[vr],delete t[Cl],delete t[gh],delete t[yh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Cf(e){return e.tag===5||e.tag===3||e.tag===4}function Xa(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Cf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Fl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ti));else if(r!==4&&(e=e.child,e!==null))for(Fl(e,t,n),e=e.sibling;e!==null;)Fl(e,t,n),e=e.sibling}function Bl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Bl(e,t,n),e=e.sibling;e!==null;)Bl(e,t,n),e=e.sibling}var le=null,We=!1;function mt(e,t,n){for(n=n.child;n!==null;)Tf(e,t,n),n=n.sibling}function Tf(e,t,n){if(et&&typeof et.onCommitFiberUnmount=="function")try{et.onCommitFiberUnmount(Qi,n)}catch{}switch(n.tag){case 5:fe||yn(n,t);case 6:var r=le,i=We;le=null,mt(e,t,n),le=r,We=i,le!==null&&(We?(e=le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):le.removeChild(n.stateNode));break;case 18:le!==null&&(We?(e=le,n=n.stateNode,e.nodeType===8?Io(e.parentNode,n):e.nodeType===1&&Io(e,n),pr(e)):Io(le,n.stateNode));break;case 4:r=le,i=We,le=n.stateNode.containerInfo,We=!0,mt(e,t,n),le=r,We=i;break;case 0:case 11:case 14:case 15:if(!fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,l=o.destroy;o=o.tag,l!==void 0&&(o&2||o&4)&&zl(n,t,l),i=i.next}while(i!==r)}mt(e,t,n);break;case 1:if(!fe&&(yn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){q(n,t,s)}mt(e,t,n);break;case 21:mt(e,t,n);break;case 22:n.mode&1?(fe=(r=fe)||n.memoizedState!==null,mt(e,t,n),fe=r):mt(e,t,n);break;default:mt(e,t,n)}}function Ja(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Lh),t.forEach(function(r){var i=Vh.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Be(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:le=s.stateNode,We=!1;break e;case 3:le=s.stateNode.containerInfo,We=!0;break e;case 4:le=s.stateNode.containerInfo,We=!0;break e}s=s.return}if(le===null)throw Error(k(160));Tf(o,l,i),le=null,We=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)_f(t,e),t=t.sibling}function _f(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Be(t,e),Ke(e),r&4){try{or(3,e,e.return),to(3,e)}catch(w){q(e,e.return,w)}try{or(5,e,e.return)}catch(w){q(e,e.return,w)}}break;case 1:Be(t,e),Ke(e),r&512&&n!==null&&yn(n,n.return);break;case 5:if(Be(t,e),Ke(e),r&512&&n!==null&&yn(n,n.return),e.flags&32){var i=e.stateNode;try{ur(i,"")}catch(w){q(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,l=n!==null?n.memoizedProps:o,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&Qu(i,o),cl(s,l);var u=cl(s,o);for(l=0;l<a.length;l+=2){var f=a[l],d=a[l+1];f==="style"?Ju(i,d):f==="dangerouslySetInnerHTML"?Ku(i,d):f==="children"?ur(i,d):os(i,f,d,u)}switch(s){case"input":ol(i,o);break;case"textarea":qu(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var v=o.value;v!=null?wn(i,!!o.multiple,v,!1):p!==!!o.multiple&&(o.defaultValue!=null?wn(i,!!o.multiple,o.defaultValue,!0):wn(i,!!o.multiple,o.multiple?[]:"",!1))}i[vr]=o}catch(w){q(e,e.return,w)}}break;case 6:if(Be(t,e),Ke(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(w){q(e,e.return,w)}}break;case 3:if(Be(t,e),Ke(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{pr(t.containerInfo)}catch(w){q(e,e.return,w)}break;case 4:Be(t,e),Ke(e);break;case 13:Be(t,e),Ke(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(js=X())),r&4&&Ja(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(fe=(u=fe)||f,Be(t,e),fe=u):Be(t,e),Ke(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(P=e,f=e.child;f!==null;){for(d=P=f;P!==null;){switch(p=P,v=p.child,p.tag){case 0:case 11:case 14:case 15:or(4,p,p.return);break;case 1:yn(p,p.return);var g=p.stateNode;if(typeof g.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(w){q(r,n,w)}}break;case 5:yn(p,p.return);break;case 22:if(p.memoizedState!==null){eu(d);continue}}v!==null?(v.return=p,P=v):eu(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{i=d.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=d.stateNode,a=d.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=Xu("display",l))}catch(w){q(e,e.return,w)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){q(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Be(t,e),Ke(e),r&4&&Ja(e);break;case 21:break;default:Be(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Cf(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(ur(i,""),r.flags&=-33);var o=Xa(e);Bl(e,o,i);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Xa(e);Fl(e,s,l);break;default:throw Error(k(161))}}catch(a){q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ih(e,t,n){P=e,Pf(e)}function Pf(e,t,n){for(var r=(e.mode&1)!==0;P!==null;){var i=P,o=i.child;if(i.tag===22&&r){var l=i.memoizedState!==null||Xr;if(!l){var s=i.alternate,a=s!==null&&s.memoizedState!==null||fe;s=Xr;var u=fe;if(Xr=l,(fe=a)&&!u)for(P=i;P!==null;)l=P,a=l.child,l.tag===22&&l.memoizedState!==null?tu(i):a!==null?(a.return=l,P=a):tu(i);for(;o!==null;)P=o,Pf(o),o=o.sibling;P=i,Xr=s,fe=u}Za(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,P=o):Za(e)}}function Za(e){for(;P!==null;){var t=P;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:fe||to(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!fe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:$e(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&za(t,o,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}za(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&pr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}fe||t.flags&512&&jl(t)}catch(p){q(t,t.return,p)}}if(t===e){P=null;break}if(n=t.sibling,n!==null){n.return=t.return,P=n;break}P=t.return}}function eu(e){for(;P!==null;){var t=P;if(t===e){P=null;break}var n=t.sibling;if(n!==null){n.return=t.return,P=n;break}P=t.return}}function tu(e){for(;P!==null;){var t=P;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{to(4,t)}catch(a){q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){q(t,i,a)}}var o=t.return;try{jl(t)}catch(a){q(t,o,a)}break;case 5:var l=t.return;try{jl(t)}catch(a){q(t,l,a)}}}catch(a){q(t,t.return,a)}if(t===e){P=null;break}var s=t.sibling;if(s!==null){s.return=t.return,P=s;break}P=t.return}}var Uh=Math.ceil,Ui=ht.ReactCurrentDispatcher,Us=ht.ReactCurrentOwner,ze=ht.ReactCurrentBatchConfig,U=0,oe=null,Z=null,se=0,Pe=0,vn=It(0),ne=0,Cr=null,Zt=0,no=0,zs=0,lr=null,Se=null,js=0,Dn=1/0,rt=null,zi=!1,$l=null,Ot=null,Jr=!1,kt=null,ji=0,sr=0,Wl=null,ci=-1,fi=0;function ge(){return U&6?X():ci!==-1?ci:ci=X()}function Rt(e){return e.mode&1?U&2&&se!==0?se&-se:wh.transition!==null?(fi===0&&(fi=cc()),fi):(e=j,e!==0||(e=window.event,e=e===void 0?16:yc(e.type)),e):1}function Ye(e,t,n,r){if(50<sr)throw sr=0,Wl=null,Error(k(185));Pr(e,n,r),(!(U&2)||e!==oe)&&(e===oe&&(!(U&2)&&(no|=n),ne===4&&St(e,se)),Ce(e,r),n===1&&U===0&&!(t.mode&1)&&(Dn=X()+500,Ji&&Ut()))}function Ce(e,t){var n=e.callbackNode;wp(e,t);var r=Ei(e,e===oe?se:0);if(r===0)n!==null&&ca(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ca(n),t===1)e.tag===0?vh(nu.bind(null,e)):Uc(nu.bind(null,e)),hh(function(){!(U&6)&&Ut()}),n=null;else{switch(fc(r)){case 1:n=cs;break;case 4:n=ac;break;case 16:n=Si;break;case 536870912:n=uc;break;default:n=Si}n=If(n,Of.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Of(e,t){if(ci=-1,fi=0,U&6)throw Error(k(327));var n=e.callbackNode;if(Cn()&&e.callbackNode!==n)return null;var r=Ei(e,e===oe?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Fi(e,r);else{t=r;var i=U;U|=2;var o=Nf();(oe!==e||se!==t)&&(rt=null,Dn=X()+500,Qt(e,t));do try{Fh();break}catch(s){Rf(e,s)}while(!0);xs(),Ui.current=o,U=i,Z!==null?t=0:(oe=null,se=0,t=ne)}if(t!==0){if(t===2&&(i=ml(e),i!==0&&(r=i,t=Vl(e,i))),t===1)throw n=Cr,Qt(e,0),St(e,r),Ce(e,X()),n;if(t===6)St(e,r);else{if(i=e.current.alternate,!(r&30)&&!zh(i)&&(t=Fi(e,r),t===2&&(o=ml(e),o!==0&&(r=o,t=Vl(e,o))),t===1))throw n=Cr,Qt(e,0),St(e,r),Ce(e,X()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Bt(e,Se,rt);break;case 3:if(St(e,r),(r&130023424)===r&&(t=js+500-X(),10<t)){if(Ei(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=xl(Bt.bind(null,e,Se,rt),t);break}Bt(e,Se,rt);break;case 4:if(St(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var l=31-be(r);o=1<<l,l=t[l],l>i&&(i=l),r&=~o}if(r=i,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Uh(r/1960))-r,10<r){e.timeoutHandle=xl(Bt.bind(null,e,Se,rt),r);break}Bt(e,Se,rt);break;case 5:Bt(e,Se,rt);break;default:throw Error(k(329))}}}return Ce(e,X()),e.callbackNode===n?Of.bind(null,e):null}function Vl(e,t){var n=lr;return e.current.memoizedState.isDehydrated&&(Qt(e,t).flags|=256),e=Fi(e,t),e!==2&&(t=Se,Se=n,t!==null&&Hl(t)),e}function Hl(e){Se===null?Se=e:Se.push.apply(Se,e)}function zh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Qe(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function St(e,t){for(t&=~zs,t&=~no,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-be(t),r=1<<n;e[n]=-1,t&=~r}}function nu(e){if(U&6)throw Error(k(327));Cn();var t=Ei(e,0);if(!(t&1))return Ce(e,X()),null;var n=Fi(e,t);if(e.tag!==0&&n===2){var r=ml(e);r!==0&&(t=r,n=Vl(e,r))}if(n===1)throw n=Cr,Qt(e,0),St(e,t),Ce(e,X()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bt(e,Se,rt),Ce(e,X()),null}function Fs(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(Dn=X()+500,Ji&&Ut())}}function en(e){kt!==null&&kt.tag===0&&!(U&6)&&Cn();var t=U;U|=1;var n=ze.transition,r=j;try{if(ze.transition=null,j=1,e)return e()}finally{j=r,ze.transition=n,U=t,!(U&6)&&Ut()}}function Bs(){Pe=vn.current,V(vn)}function Qt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ph(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Ss(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&_i();break;case 3:Rn(),V(ke),V(pe),Rs();break;case 5:Os(r);break;case 4:Rn();break;case 13:V(b);break;case 19:V(b);break;case 10:Cs(r.type._context);break;case 22:case 23:Bs()}n=n.return}if(oe=e,Z=e=Nt(e.current,null),se=Pe=t,ne=0,Cr=null,zs=no=Zt=0,Se=lr=null,Ht!==null){for(t=0;t<Ht.length;t++)if(n=Ht[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var l=o.next;o.next=i,r.next=l}n.pending=r}Ht=null}return e}function Rf(e,t){do{var n=Z;try{if(xs(),si.current=Ii,Mi){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Mi=!1}if(Jt=0,ie=te=Y=null,ir=!1,Er=0,Us.current=null,n===null||n.return===null){ne=1,Cr=t,Z=null;break}e:{var o=e,l=n.return,s=n,a=t;if(t=se,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=s,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var v=Va(l);if(v!==null){v.flags&=-257,Ha(v,l,s,o,t),v.mode&1&&Wa(o,u,t),t=v,a=u;var g=t.updateQueue;if(g===null){var w=new Set;w.add(a),t.updateQueue=w}else g.add(a);break e}else{if(!(t&1)){Wa(o,u,t),$s();break e}a=Error(k(426))}}else if(H&&s.mode&1){var x=Va(l);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Ha(x,l,s,o,t),Es(Nn(a,s));break e}}o=a=Nn(a,s),ne!==4&&(ne=2),lr===null?lr=[o]:lr.push(o),o=l;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var h=df(o,a,t);Ua(o,h);break e;case 1:s=a;var c=o.type,m=o.stateNode;if(!(o.flags&128)&&(typeof c.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Ot===null||!Ot.has(m)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=pf(o,s,t);Ua(o,S);break e}}o=o.return}while(o!==null)}Af(n)}catch(C){t=C,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function Nf(){var e=Ui.current;return Ui.current=Ii,e===null?Ii:e}function $s(){(ne===0||ne===3||ne===2)&&(ne=4),oe===null||!(Zt&268435455)&&!(no&268435455)||St(oe,se)}function Fi(e,t){var n=U;U|=2;var r=Nf();(oe!==e||se!==t)&&(rt=null,Qt(e,t));do try{jh();break}catch(i){Rf(e,i)}while(!0);if(xs(),U=n,Ui.current=r,Z!==null)throw Error(k(261));return oe=null,se=0,ne}function jh(){for(;Z!==null;)Df(Z)}function Fh(){for(;Z!==null&&!cp();)Df(Z)}function Df(e){var t=Mf(e.alternate,e,Pe);e.memoizedProps=e.pendingProps,t===null?Af(e):Z=t,Us.current=null}function Af(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ah(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,Z=null;return}}else if(n=Dh(n,t,Pe),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);ne===0&&(ne=5)}function Bt(e,t,n){var r=j,i=ze.transition;try{ze.transition=null,j=1,Bh(e,t,n,r)}finally{ze.transition=i,j=r}return null}function Bh(e,t,n,r){do Cn();while(kt!==null);if(U&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Sp(e,o),e===oe&&(Z=oe=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Jr||(Jr=!0,If(Si,function(){return Cn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=ze.transition,ze.transition=null;var l=j;j=1;var s=U;U|=4,Us.current=null,Mh(e,n),_f(n,e),lh(El),ki=!!Sl,El=Sl=null,e.current=n,Ih(n),fp(),U=s,j=l,ze.transition=o}else e.current=n;if(Jr&&(Jr=!1,kt=e,ji=i),o=e.pendingLanes,o===0&&(Ot=null),hp(n.stateNode),Ce(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(zi)throw zi=!1,e=$l,$l=null,e;return ji&1&&e.tag!==0&&Cn(),o=e.pendingLanes,o&1?e===Wl?sr++:(sr=0,Wl=e):sr=0,Ut(),null}function Cn(){if(kt!==null){var e=fc(ji),t=ze.transition,n=j;try{if(ze.transition=null,j=16>e?16:e,kt===null)var r=!1;else{if(e=kt,kt=null,ji=0,U&6)throw Error(k(331));var i=U;for(U|=4,P=e.current;P!==null;){var o=P,l=o.child;if(P.flags&16){var s=o.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(P=u;P!==null;){var f=P;switch(f.tag){case 0:case 11:case 15:or(8,f,o)}var d=f.child;if(d!==null)d.return=f,P=d;else for(;P!==null;){f=P;var p=f.sibling,v=f.return;if(xf(f),f===u){P=null;break}if(p!==null){p.return=v,P=p;break}P=v}}}var g=o.alternate;if(g!==null){var w=g.child;if(w!==null){g.child=null;do{var x=w.sibling;w.sibling=null,w=x}while(w!==null)}}P=o}}if(o.subtreeFlags&2064&&l!==null)l.return=o,P=l;else e:for(;P!==null;){if(o=P,o.flags&2048)switch(o.tag){case 0:case 11:case 15:or(9,o,o.return)}var h=o.sibling;if(h!==null){h.return=o.return,P=h;break e}P=o.return}}var c=e.current;for(P=c;P!==null;){l=P;var m=l.child;if(l.subtreeFlags&2064&&m!==null)m.return=l,P=m;else e:for(l=c;P!==null;){if(s=P,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:to(9,s)}}catch(C){q(s,s.return,C)}if(s===l){P=null;break e}var S=s.sibling;if(S!==null){S.return=s.return,P=S;break e}P=s.return}}if(U=i,Ut(),et&&typeof et.onPostCommitFiberRoot=="function")try{et.onPostCommitFiberRoot(Qi,e)}catch{}r=!0}return r}finally{j=n,ze.transition=t}}return!1}function ru(e,t,n){t=Nn(n,t),t=df(e,t,1),e=Pt(e,t,1),t=ge(),e!==null&&(Pr(e,1,t),Ce(e,t))}function q(e,t,n){if(e.tag===3)ru(e,e,n);else for(;t!==null;){if(t.tag===3){ru(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ot===null||!Ot.has(r))){e=Nn(n,e),e=pf(t,e,1),t=Pt(t,e,1),e=ge(),t!==null&&(Pr(t,1,e),Ce(t,e));break}}t=t.return}}function $h(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(se&n)===n&&(ne===4||ne===3&&(se&130023424)===se&&500>X()-js?Qt(e,0):zs|=n),Ce(e,t)}function Lf(e,t){t===0&&(e.mode&1?(t=Wr,Wr<<=1,!(Wr&130023424)&&(Wr=4194304)):t=1);var n=ge();e=ft(e,t),e!==null&&(Pr(e,t,n),Ce(e,n))}function Wh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Lf(e,n)}function Vh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),Lf(e,n)}var Mf;Mf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Nh(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,H&&t.flags&1048576&&zc(t,Ri,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ui(e,t),e=t.pendingProps;var i=_n(t,pe.current);xn(t,n),i=Ds(null,t,r,e,i,n);var o=As();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xe(r)?(o=!0,Pi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,_s(t),i.updater=eo,t.stateNode=i,i._reactInternals=t,Nl(t,r,e,n),t=Ll(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ws(t),me(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ui(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=bh(r),e=$e(r,e),i){case 0:t=Al(null,t,r,e,n);break e;case 1:t=Qa(null,t,r,e,n);break e;case 11:t=ba(null,t,r,e,n);break e;case 14:t=Ya(null,t,r,$e(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:$e(r,i),Al(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:$e(r,i),Qa(e,t,r,i,n);case 3:e:{if(yf(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Vc(e,t),Ai(t,r,null,n);var l=t.memoizedState;if(r=l.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Nn(Error(k(423)),t),t=qa(e,t,r,n,i);break e}else if(r!==i){i=Nn(Error(k(424)),t),t=qa(e,t,r,n,i);break e}else for(Oe=_t(t.stateNode.containerInfo.firstChild),Re=t,H=!0,He=null,n=$c(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Pn(),r===i){t=dt(e,t,n);break e}me(e,t,r,n)}t=t.child}return t;case 5:return Hc(t),e===null&&Pl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,l=i.children,kl(r,i)?l=null:o!==null&&kl(r,o)&&(t.flags|=32),gf(e,t),me(e,t,l,n),t.child;case 6:return e===null&&Pl(t),null;case 13:return vf(e,t,n);case 4:return Ps(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=On(t,null,r,n):me(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:$e(r,i),ba(e,t,r,i,n);case 7:return me(e,t,t.pendingProps,n),t.child;case 8:return me(e,t,t.pendingProps.children,n),t.child;case 12:return me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,l=i.value,B(Ni,r._currentValue),r._currentValue=l,o!==null)if(Qe(o.value,l)){if(o.children===i.children&&!ke.current){t=dt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){l=o.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=at(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ol(o.return,n,t),s.lanes|=n;break}a=a.next}}else if(o.tag===10)l=o.type===t.type?null:o.child;else if(o.tag===18){if(l=o.return,l===null)throw Error(k(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Ol(l,n,t),l=o.sibling}else l=o.child;if(l!==null)l.return=o;else for(l=o;l!==null;){if(l===t){l=null;break}if(o=l.sibling,o!==null){o.return=l.return,l=o;break}l=l.return}o=l}me(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,xn(t,n),i=je(i),r=r(i),t.flags|=1,me(e,t,r,n),t.child;case 14:return r=t.type,i=$e(r,t.pendingProps),i=$e(r.type,i),Ya(e,t,r,i,n);case 15:return hf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:$e(r,i),ui(e,t),t.tag=1,xe(r)?(e=!0,Pi(t)):e=!1,xn(t,n),ff(t,r,i),Nl(t,r,i,n),Ll(null,t,r,!0,e,n);case 19:return wf(e,t,n);case 22:return mf(e,t,n)}throw Error(k(156,t.tag))};function If(e,t){return sc(e,t)}function Hh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new Hh(e,t,n,r)}function Ws(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bh(e){if(typeof e=="function")return Ws(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ss)return 11;if(e===as)return 14}return 2}function Nt(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function di(e,t,n,r,i,o){var l=2;if(r=e,typeof e=="function")Ws(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case an:return qt(n.children,i,o,t);case ls:l=8,i|=8;break;case el:return e=Ue(12,n,t,i|2),e.elementType=el,e.lanes=o,e;case tl:return e=Ue(13,n,t,i),e.elementType=tl,e.lanes=o,e;case nl:return e=Ue(19,n,t,i),e.elementType=nl,e.lanes=o,e;case Hu:return ro(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Wu:l=10;break e;case Vu:l=9;break e;case ss:l=11;break e;case as:l=14;break e;case yt:l=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=Ue(l,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function qt(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function ro(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Hu,e.lanes=n,e.stateNode={isHidden:!1},e}function Vo(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function Ho(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Yh(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Co(0),this.expirationTimes=Co(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Co(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Vs(e,t,n,r,i,o,l,s,a){return e=new Yh(e,t,n,s,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},_s(o),e}function Qh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Uf(e){if(!e)return At;e=e._reactInternals;e:{if(rn(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(xe(n))return Ic(e,n,t)}return t}function zf(e,t,n,r,i,o,l,s,a){return e=Vs(n,r,!0,e,i,o,l,s,a),e.context=Uf(null),n=e.current,r=ge(),i=Rt(n),o=at(r,i),o.callback=t??null,Pt(n,o,i),e.current.lanes=i,Pr(e,i,r),Ce(e,r),e}function io(e,t,n,r){var i=t.current,o=ge(),l=Rt(i);return n=Uf(n),t.context===null?t.context=n:t.pendingContext=n,t=at(o,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Pt(i,t,l),e!==null&&(Ye(e,i,l,o),li(e,i,l)),l}function Bi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function iu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Hs(e,t){iu(e,t),(e=e.alternate)&&iu(e,t)}function qh(){return null}var jf=typeof reportError=="function"?reportError:function(e){console.error(e)};function bs(e){this._internalRoot=e}oo.prototype.render=bs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));io(e,t,null,null)};oo.prototype.unmount=bs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;en(function(){io(null,e,null,null)}),t[ct]=null}};function oo(e){this._internalRoot=e}oo.prototype.unstable_scheduleHydration=function(e){if(e){var t=hc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wt.length&&t!==0&&t<wt[n].priority;n++);wt.splice(n,0,e),n===0&&gc(e)}};function Ys(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function lo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ou(){}function Gh(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Bi(l);o.call(u)}}var l=zf(t,r,e,0,null,!1,!1,"",ou);return e._reactRootContainer=l,e[ct]=l.current,gr(e.nodeType===8?e.parentNode:e),en(),l}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var u=Bi(a);s.call(u)}}var a=Vs(e,0,!1,null,null,!1,!1,"",ou);return e._reactRootContainer=a,e[ct]=a.current,gr(e.nodeType===8?e.parentNode:e),en(function(){io(t,a,n,r)}),a}function so(e,t,n,r,i){var o=n._reactRootContainer;if(o){var l=o;if(typeof i=="function"){var s=i;i=function(){var a=Bi(l);s.call(a)}}io(t,l,e,i)}else l=Gh(n,t,e,i,r);return Bi(l)}dc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Xn(t.pendingLanes);n!==0&&(fs(t,n|1),Ce(t,X()),!(U&6)&&(Dn=X()+500,Ut()))}break;case 13:en(function(){var r=ft(e,1);if(r!==null){var i=ge();Ye(r,e,1,i)}}),Hs(e,1)}};ds=function(e){if(e.tag===13){var t=ft(e,134217728);if(t!==null){var n=ge();Ye(t,e,134217728,n)}Hs(e,134217728)}};pc=function(e){if(e.tag===13){var t=Rt(e),n=ft(e,t);if(n!==null){var r=ge();Ye(n,e,t,r)}Hs(e,t)}};hc=function(){return j};mc=function(e,t){var n=j;try{return j=e,t()}finally{j=n}};dl=function(e,t,n){switch(t){case"input":if(ol(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Xi(r);if(!i)throw Error(k(90));Yu(r),ol(r,i)}}}break;case"textarea":qu(e,n);break;case"select":t=n.value,t!=null&&wn(e,!!n.multiple,t,!1)}};tc=Fs;nc=en;var Kh={usingClientEntryPoint:!1,Events:[Rr,dn,Xi,Zu,ec,Fs]},Hn={findFiberByHostInstance:Vt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Xh={bundleType:Hn.bundleType,version:Hn.version,rendererPackageName:Hn.rendererPackageName,rendererConfig:Hn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ht.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=oc(e),e===null?null:e.stateNode},findFiberByHostInstance:Hn.findFiberByHostInstance||qh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Zr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zr.isDisabled&&Zr.supportsFiber)try{Qi=Zr.inject(Xh),et=Zr}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kh;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ys(t))throw Error(k(200));return Qh(e,t,null,n)};De.createRoot=function(e,t){if(!Ys(e))throw Error(k(299));var n=!1,r="",i=jf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Vs(e,1,!1,null,null,n,!1,r,i),e[ct]=t.current,gr(e.nodeType===8?e.parentNode:e),new bs(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=oc(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return en(e)};De.hydrate=function(e,t,n){if(!lo(t))throw Error(k(200));return so(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!Ys(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",l=jf;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=zf(t,null,e,1,n??null,i,!1,o,l),e[ct]=t.current,gr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new oo(t)};De.render=function(e,t,n){if(!lo(t))throw Error(k(200));return so(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!lo(e))throw Error(k(40));return e._reactRootContainer?(en(function(){so(null,null,e,!1,function(){e._reactRootContainer=null,e[ct]=null})}),!0):!1};De.unstable_batchedUpdates=Fs;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!lo(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return so(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function Ff(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ff)}catch(e){console.error(e)}}Ff(),ju.exports=De;var Jh=ju.exports,lu=Jh;Jo.createRoot=lu.createRoot,Jo.hydrateRoot=lu.hydrateRoot;const bo={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_DEPLOYMENT_MODE:"standalone",VITE_THEME_MODE:void 0};var pi=(e=>(e.WEB_RESOURCE="web_resource",e.EMBEDDED_SPA="embedded_spa",e.STANDALONE_MFE="standalone_mfe",e))(pi||{}),pt=(e=>(e.CRM="crm",e.MFE="mfe",e))(pt||{});function su(e){switch(e){case"standalone":return"standalone_mfe";case"web_resource":return"web_resource";case"embedded_spa":return"embedded_spa";case"standalone_mfe":return"standalone_mfe";default:return"web_resource"}}const Wt=class Wt{constructor(){ee(this,"_detectedMode",null);ee(this,"_config",null)}static getInstance(){return Wt._instance||(Wt._instance=new Wt),Wt._instance}detectDeploymentMode(){if(this._detectedMode)return this._detectedMode;const t=this.getConfigurationOverride();if(t)return this._detectedMode=t,this._detectedMode;const n=this.isDynamics365Environment(),r=this.isEmbeddedSPAEnvironment();return n?this._detectedMode="web_resource":r?this._detectedMode="embedded_spa":this._detectedMode="standalone_mfe",this._detectedMode}detectThemeMode(){const t=this.detectDeploymentMode(),n=this.getThemeOverride();if(n)return n;switch(t){case"web_resource":case"embedded_spa":return"crm";case"standalone_mfe":return"mfe";default:return"crm"}}isDynamics365Environment(){try{if(typeof window<"u"){if(window.Xrm&&window.Xrm.WebApi)return!0;if(window.parent&&window.parent!==window)try{if(window.parent.Xrm)return!0}catch{}const t=window.location.href;if(t.includes("/WebResources/")||t.includes("/_static/"))return!0}return!1}catch(t){return console.warn("Error detecting Dynamics 365 environment:",t),!1}}isEmbeddedSPAEnvironment(){try{if(typeof window<"u"){const t=window.location.href;if((t.includes(".dynamics.com")||t.includes(".crm.dynamics.com"))&&!t.includes("/WebResources/")&&!t.includes("/_static/"))return!0;const n=new URLSearchParams(window.location.search);if(n.has("embedded")||n.has("spa_mode")||document.referrer&&(document.referrer.includes(".dynamics.com")||document.referrer.includes(".crm.dynamics.com")))return!0}return!1}catch(t){return console.warn("Error detecting embedded SPA environment:",t),!1}}getConfigurationOverride(){try{const t=this.getEnvironmentVariable("VITE_DEPLOYMENT_MODE");if(t)return su(t);if(typeof window<"u"){const r=new URLSearchParams(window.location.search).get("deploymentMode");if(r)return su(r)}return null}catch(t){return console.warn("Error getting configuration override:",t),null}}getThemeOverride(){try{const t=this.getEnvironmentVariable("VITE_THEME_MODE");if(t&&Object.values(pt).includes(t))return t;if(typeof window<"u"){const r=new URLSearchParams(window.location.search).get("themeMode");if(r&&Object.values(pt).includes(r))return r}return null}catch(t){return console.warn("Error getting theme override:",t),null}}getEnvironmentVariable(t){try{return(bo==null?void 0:bo[t])||null}catch{return null}}getDeploymentConfig(){if(this._config)return this._config;const t=this.detectDeploymentMode();return this._config=this.createConfigForMode(t),this._config}createConfigForMode(t){const n=this.detectThemeMode(),r=this.createThemeConfig(n),i={mode:t,theme:r,features:{enableLogging:this.getEnvironmentVariable("VITE_ENABLE_LOGGING")==="true"||t!=="web_resource",enableOfflineMode:this.getEnvironmentVariable("VITE_ENABLE_OFFLINE")==="true"||!1,enableTelemetry:this.getEnvironmentVariable("VITE_ENABLE_TELEMETRY")==="true"||!1,enableThemeSwitching:this.getEnvironmentVariable("VITE_ENABLE_THEME_SWITCHING")==="true"||!1}};switch(t){case"web_resource":return{...i,apiBaseUrl:"",authMethod:"dynamics365",dynamicsConfig:{serverUrl:this.getEnvironmentVariable("VITE_DYNAMICS_SERVER_URL")||"",version:this.getEnvironmentVariable("VITE_DYNAMICS_API_VERSION")||"9.2"}};case"embedded_spa":return{...i,apiBaseUrl:this.getEnvironmentVariable("VITE_API_BASE_URL")||"https://your-org.api.crm.dynamics.com/api/data/v9.2",authMethod:"msal",msalConfig:{clientId:this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID")||"",authority:this.getEnvironmentVariable("VITE_MSAL_AUTHORITY")||"https://login.microsoftonline.com/common",redirectUri:this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI")||window.location.origin,scopes:(this.getEnvironmentVariable("VITE_MSAL_SCOPES")||"https://your-org.crm.dynamics.com/.default").split(",")}};case"standalone_mfe":return{...i,apiBaseUrl:this.getEnvironmentVariable("VITE_API_BASE_URL")||"https://your-org.api.crm.dynamics.com/api/data/v9.2",authMethod:"msal",msalConfig:{clientId:this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID")||"",authority:this.getEnvironmentVariable("VITE_MSAL_AUTHORITY")||"https://login.microsoftonline.com/common",redirectUri:this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI")||window.location.origin,scopes:(this.getEnvironmentVariable("VITE_MSAL_SCOPES")||"https://your-org.crm.dynamics.com/.default").split(",")}};default:throw new Error(`Unsupported deployment mode: ${t}`)}}createThemeConfig(t){switch(t){case"crm":return{mode:"crm",primaryColor:"#0078d4",secondaryColor:"#106ebe",backgroundColor:"#ffffff",textColor:"#323130",borderColor:"#8a8886",fontFamily:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',customProperties:{"--crm-header-bg":"#0078d4","--crm-sidebar-bg":"#f3f2f1","--crm-card-bg":"#ffffff","--crm-border-radius":"2px","--crm-shadow":"0 2px 4px rgba(0, 0, 0, 0.1)","--crm-spacing-xs":"4px","--crm-spacing-sm":"8px","--crm-spacing-md":"16px","--crm-spacing-lg":"24px","--crm-spacing-xl":"32px"}};case"mfe":return{mode:"mfe",primaryColor:"#6366f1",secondaryColor:"#4f46e5",backgroundColor:"#f8fafc",textColor:"#1e293b",borderColor:"#e2e8f0",fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',customProperties:{"--mfe-header-bg":"#1e293b","--mfe-sidebar-bg":"#f1f5f9","--mfe-card-bg":"#ffffff","--mfe-border-radius":"8px","--mfe-shadow":"0 4px 6px -1px rgba(0, 0, 0, 0.1)","--mfe-spacing-xs":"4px","--mfe-spacing-sm":"8px","--mfe-spacing-md":"16px","--mfe-spacing-lg":"24px","--mfe-spacing-xl":"32px"}};default:throw new Error(`Unsupported theme mode: ${t}`)}}forceDeploymentMode(t){this._detectedMode=t,this._config=null}reset(){this._detectedMode=null,this._config=null}};ee(Wt,"_instance");let bl=Wt;function Zh(){return bl.getInstance().getDeploymentConfig()}class em{constructor(t={}){ee(this,"config");ee(this,"entries",[]);this.config={level:1,enableConsole:!0,enableStorage:!1,maxStorageEntries:1e3,...t},this.config.enableStorage&&this.loadFromStorage()}shouldLog(t){return t>=this.config.level}createEntry(t,n,r){return{level:t,message:n,timestamp:new Date,data:r,source:this.config.source}}logToConsole(t){if(!this.config.enableConsole)return;const n=t.timestamp.toISOString(),r=t.source?`[${t.source}]`:"",i=`${n} ${r}`;switch(t.level){case 0:console.debug(i,t.message,t.data);break;case 1:console.info(i,t.message,t.data);break;case 2:console.warn(i,t.message,t.data);break;case 3:console.error(i,t.message,t.data);break}}storeEntry(t){if(this.config.enableStorage){this.entries.push(t),this.entries.length>this.config.maxStorageEntries&&(this.entries=this.entries.slice(-this.config.maxStorageEntries));try{localStorage.setItem("logger_entries",JSON.stringify(this.entries))}catch(n){console.warn("Failed to save log entries to localStorage:",n)}}}loadFromStorage(){try{const t=localStorage.getItem("logger_entries");t&&(this.entries=JSON.parse(t).map(n=>({...n,timestamp:new Date(n.timestamp)})))}catch(t){console.warn("Failed to load log entries from localStorage:",t),this.entries=[]}}log(t,n,r){if(!this.shouldLog(t))return;const i=this.createEntry(t,n,r);this.logToConsole(i),this.storeEntry(i)}debug(t,n){this.log(0,t,n)}info(t,n){this.log(1,t,n)}warn(t,n){this.log(2,t,n)}error(t,n){this.log(3,t,n)}getEntries(){return[...this.entries]}clear(){this.entries=[],this.config.enableStorage&&localStorage.removeItem("logger_entries")}export(){return JSON.stringify(this.entries,null,2)}configure(t){this.config={...this.config,...t}}}const au=()=>{try{return!1}catch{return!1}},M=new em({level:au()?0:1,enableConsole:!0,enableStorage:au(),source:"CRM-App"}),tm=K.createContext(null),nm=()=>{const e=K.useContext(tm);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},Bf=()=>{const{currentTheme:e,themeConfig:t}=nm();return{currentTheme:e,themeConfig:t,getThemeClass:o=>`${o} theme-${e}`,getThemeStyle:o=>o[e]||{},getCSSVariable:o=>getComputedStyle(document.documentElement).getPropertyValue(o)}},Ie=({children:e,onClick:t,variant:n="primary",size:r="medium",disabled:i=!1,type:o="button",className:l="",style:s,...a})=>{const{getThemeClass:u}=Bf(),f="theme-button",d={primary:"theme-button-primary",secondary:"theme-button-secondary",danger:"theme-button-danger"},p={small:"theme-button-sm",medium:"theme-button-base",large:"theme-button-lg"},v=i?"disabled":"",g=[u(f),u(d[n]),u(p[r]),v,l].filter(Boolean).join(" ");return E.jsx("button",{type:o,className:g,style:s,onClick:t,disabled:i,...a,children:e})},$f=({size:e="medium",color:t="primary",className:n="",text:r})=>{const{getThemeClass:i}=Bf(),o={small:"theme-spinner-sm",medium:"theme-spinner",large:"theme-spinner-lg"},l={primary:"theme-spinner-primary",secondary:"theme-spinner-secondary",white:"theme-spinner-white"},s=[i("theme-spinner"),i(o[e]),i(l[t]),n].filter(Boolean).join(" ");return E.jsxs("div",{className:"flex flex-col items-center justify-center",children:[E.jsxs("svg",{className:s,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[E.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),E.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r&&E.jsx("p",{className:`mt-2 text-sm ${l[t]}`,children:r})]})};pt.CRM+"",pt.MFE+"";pt.CRM+"",pt.MFE+"";pt.CRM+"",pt.MFE+"";const rm=`
.theme-switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.theme-switcher-sm {
  font-size: 0.875rem;
}

.theme-switcher-md {
  font-size: 1rem;
}

.theme-switcher-lg {
  font-size: 1.125rem;
}

/* Toggle variant */
.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-toggle-button:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

/* Buttons variant */
.theme-switcher-buttons {
  display: flex;
  gap: 4px;
  padding: 2px;
  background-color: var(--theme-bg-tertiary);
  border-radius: var(--theme-radius-lg);
}

.theme-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--theme-radius-base);
  background-color: transparent;
  color: var(--theme-text-secondary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-button:hover:not(.disabled) {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.theme-button.active {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

/* Dropdown variant */
.theme-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-dropdown-trigger:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

.theme-dropdown-arrow {
  font-size: 0.75em;
  transition: transform var(--theme-transition-base);
}

.theme-dropdown-trigger.open .theme-dropdown-arrow {
  transform: rotate(180deg);
}

.theme-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-lg);
  box-shadow: var(--theme-shadow-lg);
  z-index: var(--theme-z-dropdown);
  overflow: hidden;
}

.theme-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background-color: transparent;
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  text-align: left;
}

.theme-dropdown-item:hover {
  background-color: var(--theme-bg-secondary);
}

.theme-dropdown-item.active {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.theme-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.theme-description {
  font-size: 0.875em;
  color: var(--theme-text-secondary);
}

.theme-check {
  color: var(--theme-primary);
  font-weight: bold;
}

.theme-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: calc(var(--theme-z-dropdown) - 1);
}

/* Disabled state */
.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* Error indicator */
.theme-error {
  color: var(--theme-error);
  font-size: 1.2em;
  cursor: help;
}
`;if(typeof document<"u"){const e=document.createElement("style");e.textContent=rm,document.head.appendChild(e)}function Wf(e,t){return function(){return e.apply(t,arguments)}}const{toString:im}=Object.prototype,{getPrototypeOf:Qs}=Object,{iterator:ao,toStringTag:Vf}=Symbol,uo=(e=>t=>{const n=im.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),qe=e=>(e=e.toLowerCase(),t=>uo(t)===e),co=e=>t=>typeof t===e,{isArray:In}=Array,Tr=co("undefined");function Dr(e){return e!==null&&!Tr(e)&&e.constructor!==null&&!Tr(e.constructor)&&Te(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Hf=qe("ArrayBuffer");function om(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Hf(e.buffer),t}const lm=co("string"),Te=co("function"),bf=co("number"),Ar=e=>e!==null&&typeof e=="object",sm=e=>e===!0||e===!1,hi=e=>{if(uo(e)!=="object")return!1;const t=Qs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Vf in e)&&!(ao in e)},am=e=>{if(!Ar(e)||Dr(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},um=qe("Date"),cm=qe("File"),fm=qe("Blob"),dm=qe("FileList"),pm=e=>Ar(e)&&Te(e.pipe),hm=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Te(e.append)&&((t=uo(e))==="formdata"||t==="object"&&Te(e.toString)&&e.toString()==="[object FormData]"))},mm=qe("URLSearchParams"),[gm,ym,vm,wm]=["ReadableStream","Request","Response","Headers"].map(qe),Sm=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Lr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),In(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{if(Dr(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),l=o.length;let s;for(r=0;r<l;r++)s=o[r],t.call(null,e[s],s,e)}}function Yf(e,t){if(Dr(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Yt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Qf=e=>!Tr(e)&&e!==Yt;function Yl(){const{caseless:e}=Qf(this)&&this||{},t={},n=(r,i)=>{const o=e&&Yf(t,i)||i;hi(t[o])&&hi(r)?t[o]=Yl(t[o],r):hi(r)?t[o]=Yl({},r):In(r)?t[o]=r.slice():t[o]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&Lr(arguments[r],n);return t}const Em=(e,t,n,{allOwnKeys:r}={})=>(Lr(t,(i,o)=>{n&&Te(i)?e[o]=Wf(i,n):e[o]=i},{allOwnKeys:r}),e),km=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),xm=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Cm=(e,t,n,r)=>{let i,o,l;const s={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)l=i[o],(!r||r(l,e,t))&&!s[l]&&(t[l]=e[l],s[l]=!0);e=n!==!1&&Qs(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Tm=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},_m=e=>{if(!e)return null;if(In(e))return e;let t=e.length;if(!bf(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Pm=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Qs(Uint8Array)),Om=(e,t)=>{const r=(e&&e[ao]).call(e);let i;for(;(i=r.next())&&!i.done;){const o=i.value;t.call(e,o[0],o[1])}},Rm=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Nm=qe("HTMLFormElement"),Dm=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),uu=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Am=qe("RegExp"),qf=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Lr(n,(i,o)=>{let l;(l=t(i,o,e))!==!1&&(r[o]=l||i)}),Object.defineProperties(e,r)},Lm=e=>{qf(e,(t,n)=>{if(Te(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Te(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Mm=(e,t)=>{const n={},r=i=>{i.forEach(o=>{n[o]=!0})};return In(e)?r(e):r(String(e).split(t)),n},Im=()=>{},Um=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function zm(e){return!!(e&&Te(e.append)&&e[Vf]==="FormData"&&e[ao])}const jm=e=>{const t=new Array(10),n=(r,i)=>{if(Ar(r)){if(t.indexOf(r)>=0)return;if(Dr(r))return r;if(!("toJSON"in r)){t[i]=r;const o=In(r)?[]:{};return Lr(r,(l,s)=>{const a=n(l,i+1);!Tr(a)&&(o[s]=a)}),t[i]=void 0,o}}return r};return n(e,0)},Fm=qe("AsyncFunction"),Bm=e=>e&&(Ar(e)||Te(e))&&Te(e.then)&&Te(e.catch),Gf=((e,t)=>e?setImmediate:t?((n,r)=>(Yt.addEventListener("message",({source:i,data:o})=>{i===Yt&&o===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Yt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Te(Yt.postMessage)),$m=typeof queueMicrotask<"u"?queueMicrotask.bind(Yt):typeof process<"u"&&process.nextTick||Gf,Wm=e=>e!=null&&Te(e[ao]),y={isArray:In,isArrayBuffer:Hf,isBuffer:Dr,isFormData:hm,isArrayBufferView:om,isString:lm,isNumber:bf,isBoolean:sm,isObject:Ar,isPlainObject:hi,isEmptyObject:am,isReadableStream:gm,isRequest:ym,isResponse:vm,isHeaders:wm,isUndefined:Tr,isDate:um,isFile:cm,isBlob:fm,isRegExp:Am,isFunction:Te,isStream:pm,isURLSearchParams:mm,isTypedArray:Pm,isFileList:dm,forEach:Lr,merge:Yl,extend:Em,trim:Sm,stripBOM:km,inherits:xm,toFlatObject:Cm,kindOf:uo,kindOfTest:qe,endsWith:Tm,toArray:_m,forEachEntry:Om,matchAll:Rm,isHTMLForm:Nm,hasOwnProperty:uu,hasOwnProp:uu,reduceDescriptors:qf,freezeMethods:Lm,toObjectSet:Mm,toCamelCase:Dm,noop:Im,toFiniteNumber:Um,findKey:Yf,global:Yt,isContextDefined:Qf,isSpecCompliantForm:zm,toJSONObject:jm,isAsyncFn:Fm,isThenable:Bm,setImmediate:Gf,asap:$m,isIterable:Wm};function N(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}y.inherits(N,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const Kf=N.prototype,Xf={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Xf[e]={value:e}});Object.defineProperties(N,Xf);Object.defineProperty(Kf,"isAxiosError",{value:!0});N.from=(e,t,n,r,i,o)=>{const l=Object.create(Kf);return y.toFlatObject(e,l,function(a){return a!==Error.prototype},s=>s!=="isAxiosError"),N.call(l,e.message,t,n,r,i),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const Vm=null;function Ql(e){return y.isPlainObject(e)||y.isArray(e)}function Jf(e){return y.endsWith(e,"[]")?e.slice(0,-2):e}function cu(e,t,n){return e?e.concat(t).map(function(i,o){return i=Jf(i),!n&&o?"["+i+"]":i}).join(n?".":""):t}function Hm(e){return y.isArray(e)&&!e.some(Ql)}const bm=y.toFlatObject(y,{},null,function(t){return/^is[A-Z]/.test(t)});function fo(e,t,n){if(!y.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,x){return!y.isUndefined(x[w])});const r=n.metaTokens,i=n.visitor||f,o=n.dots,l=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(t);if(!y.isFunction(i))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(y.isDate(g))return g.toISOString();if(y.isBoolean(g))return g.toString();if(!a&&y.isBlob(g))throw new N("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(g)||y.isTypedArray(g)?a&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function f(g,w,x){let h=g;if(g&&!x&&typeof g=="object"){if(y.endsWith(w,"{}"))w=r?w:w.slice(0,-2),g=JSON.stringify(g);else if(y.isArray(g)&&Hm(g)||(y.isFileList(g)||y.endsWith(w,"[]"))&&(h=y.toArray(g)))return w=Jf(w),h.forEach(function(m,S){!(y.isUndefined(m)||m===null)&&t.append(l===!0?cu([w],S,o):l===null?w:w+"[]",u(m))}),!1}return Ql(g)?!0:(t.append(cu(x,w,o),u(g)),!1)}const d=[],p=Object.assign(bm,{defaultVisitor:f,convertValue:u,isVisitable:Ql});function v(g,w){if(!y.isUndefined(g)){if(d.indexOf(g)!==-1)throw Error("Circular reference detected in "+w.join("."));d.push(g),y.forEach(g,function(h,c){(!(y.isUndefined(h)||h===null)&&i.call(t,h,y.isString(c)?c.trim():c,w,p))===!0&&v(h,w?w.concat(c):[c])}),d.pop()}}if(!y.isObject(e))throw new TypeError("data must be an object");return v(e),t}function fu(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function qs(e,t){this._pairs=[],e&&fo(e,this,t)}const Zf=qs.prototype;Zf.append=function(t,n){this._pairs.push([t,n])};Zf.toString=function(t){const n=t?function(r){return t.call(this,r,fu)}:fu;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Ym(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ed(e,t,n){if(!t)return e;const r=n&&n.encode||Ym;y.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(i?o=i(t,n):o=y.isURLSearchParams(t)?t.toString():new qs(t,n).toString(r),o){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class du{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){y.forEach(this.handlers,function(r){r!==null&&t(r)})}}const td={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Qm=typeof URLSearchParams<"u"?URLSearchParams:qs,qm=typeof FormData<"u"?FormData:null,Gm=typeof Blob<"u"?Blob:null,Km={isBrowser:!0,classes:{URLSearchParams:Qm,FormData:qm,Blob:Gm},protocols:["http","https","file","blob","url","data"]},Gs=typeof window<"u"&&typeof document<"u",ql=typeof navigator=="object"&&navigator||void 0,Xm=Gs&&(!ql||["ReactNative","NativeScript","NS"].indexOf(ql.product)<0),Jm=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Zm=Gs&&window.location.href||"http://localhost",eg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Gs,hasStandardBrowserEnv:Xm,hasStandardBrowserWebWorkerEnv:Jm,navigator:ql,origin:Zm},Symbol.toStringTag,{value:"Module"})),de={...eg,...Km};function tg(e,t){return fo(e,new de.classes.URLSearchParams,{visitor:function(n,r,i,o){return de.isNode&&y.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function ng(e){return y.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function rg(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}function nd(e){function t(n,r,i,o){let l=n[o++];if(l==="__proto__")return!0;const s=Number.isFinite(+l),a=o>=n.length;return l=!l&&y.isArray(i)?i.length:l,a?(y.hasOwnProp(i,l)?i[l]=[i[l],r]:i[l]=r,!s):((!i[l]||!y.isObject(i[l]))&&(i[l]=[]),t(n,r,i[l],o)&&y.isArray(i[l])&&(i[l]=rg(i[l])),!s)}if(y.isFormData(e)&&y.isFunction(e.entries)){const n={};return y.forEachEntry(e,(r,i)=>{t(ng(r),i,n,0)}),n}return null}function ig(e,t,n){if(y.isString(e))try{return(t||JSON.parse)(e),y.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Mr={transitional:td,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,o=y.isObject(t);if(o&&y.isHTMLForm(t)&&(t=new FormData(t)),y.isFormData(t))return i?JSON.stringify(nd(t)):t;if(y.isArrayBuffer(t)||y.isBuffer(t)||y.isStream(t)||y.isFile(t)||y.isBlob(t)||y.isReadableStream(t))return t;if(y.isArrayBufferView(t))return t.buffer;if(y.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let s;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return tg(t,this.formSerializer).toString();if((s=y.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return fo(s?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||i?(n.setContentType("application/json",!1),ig(t)):t}],transformResponse:[function(t){const n=this.transitional||Mr.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(y.isResponse(t)||y.isReadableStream(t))return t;if(t&&y.isString(t)&&(r&&!this.responseType||i)){const l=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(s){if(l)throw s.name==="SyntaxError"?N.from(s,N.ERR_BAD_RESPONSE,this,null,this.response):s}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:de.classes.FormData,Blob:de.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],e=>{Mr.headers[e]={}});const og=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),lg=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(l){i=l.indexOf(":"),n=l.substring(0,i).trim().toLowerCase(),r=l.substring(i+1).trim(),!(!n||t[n]&&og[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},pu=Symbol("internals");function bn(e){return e&&String(e).trim().toLowerCase()}function mi(e){return e===!1||e==null?e:y.isArray(e)?e.map(mi):String(e)}function sg(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const ag=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Yo(e,t,n,r,i){if(y.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!y.isString(t)){if(y.isString(r))return t.indexOf(r)!==-1;if(y.isRegExp(r))return r.test(t)}}function ug(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function cg(e,t){const n=y.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,o,l){return this[r].call(this,t,i,o,l)},configurable:!0})})}let _e=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function o(s,a,u){const f=bn(a);if(!f)throw new Error("header name must be a non-empty string");const d=y.findKey(i,f);(!d||i[d]===void 0||u===!0||u===void 0&&i[d]!==!1)&&(i[d||a]=mi(s))}const l=(s,a)=>y.forEach(s,(u,f)=>o(u,f,a));if(y.isPlainObject(t)||t instanceof this.constructor)l(t,n);else if(y.isString(t)&&(t=t.trim())&&!ag(t))l(lg(t),n);else if(y.isObject(t)&&y.isIterable(t)){let s={},a,u;for(const f of t){if(!y.isArray(f))throw TypeError("Object iterator must return a key-value pair");s[u=f[0]]=(a=s[u])?y.isArray(a)?[...a,f[1]]:[a,f[1]]:f[1]}l(s,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=bn(t),t){const r=y.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return sg(i);if(y.isFunction(n))return n.call(this,i,r);if(y.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=bn(t),t){const r=y.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Yo(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function o(l){if(l=bn(l),l){const s=y.findKey(r,l);s&&(!n||Yo(r,r[s],s,n))&&(delete r[s],i=!0)}}return y.isArray(t)?t.forEach(o):o(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const o=n[r];(!t||Yo(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const n=this,r={};return y.forEach(this,(i,o)=>{const l=y.findKey(r,o);if(l){n[l]=mi(i),delete n[o];return}const s=t?ug(o):String(o).trim();s!==o&&delete n[o],n[s]=mi(i),r[s]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return y.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&y.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[pu]=this[pu]={accessors:{}}).accessors,i=this.prototype;function o(l){const s=bn(l);r[s]||(cg(i,l),r[s]=!0)}return y.isArray(t)?t.forEach(o):o(t),this}};_e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(_e.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});y.freezeMethods(_e);function Qo(e,t){const n=this||Mr,r=t||n,i=_e.from(r.headers);let o=r.data;return y.forEach(e,function(s){o=s.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function rd(e){return!!(e&&e.__CANCEL__)}function Un(e,t,n){N.call(this,e??"canceled",N.ERR_CANCELED,t,n),this.name="CanceledError"}y.inherits(Un,N,{__CANCEL__:!0});function id(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new N("Request failed with status code "+n.status,[N.ERR_BAD_REQUEST,N.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function fg(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function dg(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,o=0,l;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),f=r[o];l||(l=u),n[i]=a,r[i]=u;let d=o,p=0;for(;d!==i;)p+=n[d++],d=d%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),u-l<t)return;const v=f&&u-f;return v?Math.round(p*1e3/v):void 0}}function pg(e,t){let n=0,r=1e3/t,i,o;const l=(u,f=Date.now())=>{n=f,i=null,o&&(clearTimeout(o),o=null),e(...u)};return[(...u)=>{const f=Date.now(),d=f-n;d>=r?l(u,f):(i=u,o||(o=setTimeout(()=>{o=null,l(i)},r-d)))},()=>i&&l(i)]}const $i=(e,t,n=3)=>{let r=0;const i=dg(50,250);return pg(o=>{const l=o.loaded,s=o.lengthComputable?o.total:void 0,a=l-r,u=i(a),f=l<=s;r=l;const d={loaded:l,total:s,progress:s?l/s:void 0,bytes:a,rate:u||void 0,estimated:u&&s&&f?(s-l)/u:void 0,event:o,lengthComputable:s!=null,[t?"download":"upload"]:!0};e(d)},n)},hu=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},mu=e=>(...t)=>y.asap(()=>e(...t)),hg=de.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,de.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(de.origin),de.navigator&&/(msie|trident)/i.test(de.navigator.userAgent)):()=>!0,mg=de.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const l=[e+"="+encodeURIComponent(t)];y.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),y.isString(r)&&l.push("path="+r),y.isString(i)&&l.push("domain="+i),o===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function gg(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function yg(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function od(e,t,n){let r=!gg(t);return e&&(r||n==!1)?yg(e,t):t}const gu=e=>e instanceof _e?{...e}:e;function tn(e,t){t=t||{};const n={};function r(u,f,d,p){return y.isPlainObject(u)&&y.isPlainObject(f)?y.merge.call({caseless:p},u,f):y.isPlainObject(f)?y.merge({},f):y.isArray(f)?f.slice():f}function i(u,f,d,p){if(y.isUndefined(f)){if(!y.isUndefined(u))return r(void 0,u,d,p)}else return r(u,f,d,p)}function o(u,f){if(!y.isUndefined(f))return r(void 0,f)}function l(u,f){if(y.isUndefined(f)){if(!y.isUndefined(u))return r(void 0,u)}else return r(void 0,f)}function s(u,f,d){if(d in t)return r(u,f);if(d in e)return r(void 0,u)}const a={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:s,headers:(u,f,d)=>i(gu(u),gu(f),d,!0)};return y.forEach(Object.keys({...e,...t}),function(f){const d=a[f]||i,p=d(e[f],t[f],f);y.isUndefined(p)&&d!==s||(n[f]=p)}),n}const ld=e=>{const t=tn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:o,headers:l,auth:s}=t;t.headers=l=_e.from(l),t.url=ed(od(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&l.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):"")));let a;if(y.isFormData(n)){if(de.hasStandardBrowserEnv||de.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((a=l.getContentType())!==!1){const[u,...f]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];l.setContentType([u||"multipart/form-data",...f].join("; "))}}if(de.hasStandardBrowserEnv&&(r&&y.isFunction(r)&&(r=r(t)),r||r!==!1&&hg(t.url))){const u=i&&o&&mg.read(o);u&&l.set(i,u)}return t},vg=typeof XMLHttpRequest<"u",wg=vg&&function(e){return new Promise(function(n,r){const i=ld(e);let o=i.data;const l=_e.from(i.headers).normalize();let{responseType:s,onUploadProgress:a,onDownloadProgress:u}=i,f,d,p,v,g;function w(){v&&v(),g&&g(),i.cancelToken&&i.cancelToken.unsubscribe(f),i.signal&&i.signal.removeEventListener("abort",f)}let x=new XMLHttpRequest;x.open(i.method.toUpperCase(),i.url,!0),x.timeout=i.timeout;function h(){if(!x)return;const m=_e.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),C={data:!s||s==="text"||s==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:m,config:e,request:x};id(function(_){n(_),w()},function(_){r(_),w()},C),x=null}"onloadend"in x?x.onloadend=h:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(h)},x.onabort=function(){x&&(r(new N("Request aborted",N.ECONNABORTED,e,x)),x=null)},x.onerror=function(){r(new N("Network Error",N.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let S=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const C=i.transitional||td;i.timeoutErrorMessage&&(S=i.timeoutErrorMessage),r(new N(S,C.clarifyTimeoutError?N.ETIMEDOUT:N.ECONNABORTED,e,x)),x=null},o===void 0&&l.setContentType(null),"setRequestHeader"in x&&y.forEach(l.toJSON(),function(S,C){x.setRequestHeader(C,S)}),y.isUndefined(i.withCredentials)||(x.withCredentials=!!i.withCredentials),s&&s!=="json"&&(x.responseType=i.responseType),u&&([p,g]=$i(u,!0),x.addEventListener("progress",p)),a&&x.upload&&([d,v]=$i(a),x.upload.addEventListener("progress",d),x.upload.addEventListener("loadend",v)),(i.cancelToken||i.signal)&&(f=m=>{x&&(r(!m||m.type?new Un(null,e,x):m),x.abort(),x=null)},i.cancelToken&&i.cancelToken.subscribe(f),i.signal&&(i.signal.aborted?f():i.signal.addEventListener("abort",f)));const c=fg(i.url);if(c&&de.protocols.indexOf(c)===-1){r(new N("Unsupported protocol "+c+":",N.ERR_BAD_REQUEST,e));return}x.send(o||null)})},Sg=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const o=function(u){if(!i){i=!0,s();const f=u instanceof Error?u:this.reason;r.abort(f instanceof N?f:new Un(f instanceof Error?f.message:f))}};let l=t&&setTimeout(()=>{l=null,o(new N(`timeout ${t} of ms exceeded`,N.ETIMEDOUT))},t);const s=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>y.asap(s),a}},Eg=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},kg=async function*(e,t){for await(const n of xg(e))yield*Eg(n,t)},xg=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},yu=(e,t,n,r)=>{const i=kg(e,t);let o=0,l,s=a=>{l||(l=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:f}=await i.next();if(u){s(),a.close();return}let d=f.byteLength;if(n){let p=o+=d;n(p)}a.enqueue(new Uint8Array(f))}catch(u){throw s(u),u}},cancel(a){return s(a),i.return()}},{highWaterMark:2})},po=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",sd=po&&typeof ReadableStream=="function",Cg=po&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ad=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Tg=sd&&ad(()=>{let e=!1;const t=new Request(de.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),vu=64*1024,Gl=sd&&ad(()=>y.isReadableStream(new Response("").body)),Wi={stream:Gl&&(e=>e.body)};po&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Wi[t]&&(Wi[t]=y.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new N(`Response type '${t}' is not supported`,N.ERR_NOT_SUPPORT,r)})})})(new Response);const _g=async e=>{if(e==null)return 0;if(y.isBlob(e))return e.size;if(y.isSpecCompliantForm(e))return(await new Request(de.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(y.isArrayBufferView(e)||y.isArrayBuffer(e))return e.byteLength;if(y.isURLSearchParams(e)&&(e=e+""),y.isString(e))return(await Cg(e)).byteLength},Pg=async(e,t)=>{const n=y.toFiniteNumber(e.getContentLength());return n??_g(t)},Og=po&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:o,timeout:l,onDownloadProgress:s,onUploadProgress:a,responseType:u,headers:f,withCredentials:d="same-origin",fetchOptions:p}=ld(e);u=u?(u+"").toLowerCase():"text";let v=Sg([i,o&&o.toAbortSignal()],l),g;const w=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let x;try{if(a&&Tg&&n!=="get"&&n!=="head"&&(x=await Pg(f,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(y.isFormData(r)&&(T=C.headers.get("content-type"))&&f.setContentType(T),C.body){const[_,R]=hu(x,$i(mu(a)));r=yu(C.body,vu,_,R)}}y.isString(d)||(d=d?"include":"omit");const h="credentials"in Request.prototype;g=new Request(t,{...p,signal:v,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:h?d:void 0});let c=await fetch(g,p);const m=Gl&&(u==="stream"||u==="response");if(Gl&&(s||m&&w)){const C={};["status","statusText","headers"].forEach(F=>{C[F]=c[F]});const T=y.toFiniteNumber(c.headers.get("content-length")),[_,R]=s&&hu(T,$i(mu(s),!0))||[];c=new Response(yu(c.body,vu,_,()=>{R&&R(),w&&w()}),C)}u=u||"text";let S=await Wi[y.findKey(Wi,u)||"text"](c,e);return!m&&w&&w(),await new Promise((C,T)=>{id(C,T,{data:S,headers:_e.from(c.headers),status:c.status,statusText:c.statusText,config:e,request:g})})}catch(h){throw w&&w(),h&&h.name==="TypeError"&&/Load failed|fetch/i.test(h.message)?Object.assign(new N("Network Error",N.ERR_NETWORK,e,g),{cause:h.cause||h}):N.from(h,h&&h.code,e,g)}}),Kl={http:Vm,xhr:wg,fetch:Og};y.forEach(Kl,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const wu=e=>`- ${e}`,Rg=e=>y.isFunction(e)||e===null||e===!1,ud={getAdapter:e=>{e=y.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){n=e[o];let l;if(r=n,!Rg(n)&&(r=Kl[(l=String(n)).toLowerCase()],r===void 0))throw new N(`Unknown adapter '${l}'`);if(r)break;i[l||"#"+o]=r}if(!r){const o=Object.entries(i).map(([s,a])=>`adapter ${s} `+(a===!1?"is not supported by the environment":"is not available in the build"));let l=t?o.length>1?`since :
`+o.map(wu).join(`
`):" "+wu(o[0]):"as no adapter specified";throw new N("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return r},adapters:Kl};function qo(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Un(null,e)}function Su(e){return qo(e),e.headers=_e.from(e.headers),e.data=Qo.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ud.getAdapter(e.adapter||Mr.adapter)(e).then(function(r){return qo(e),r.data=Qo.call(e,e.transformResponse,r),r.headers=_e.from(r.headers),r},function(r){return rd(r)||(qo(e),r&&r.response&&(r.response.data=Qo.call(e,e.transformResponse,r.response),r.response.headers=_e.from(r.response.headers))),Promise.reject(r)})}const cd="1.11.0",ho={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ho[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Eu={};ho.transitional=function(t,n,r){function i(o,l){return"[Axios v"+cd+"] Transitional option '"+o+"'"+l+(r?". "+r:"")}return(o,l,s)=>{if(t===!1)throw new N(i(l," has been removed"+(n?" in "+n:"")),N.ERR_DEPRECATED);return n&&!Eu[l]&&(Eu[l]=!0,console.warn(i(l," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,l,s):!0}};ho.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Ng(e,t,n){if(typeof e!="object")throw new N("options must be an object",N.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],l=t[o];if(l){const s=e[o],a=s===void 0||l(s,o,e);if(a!==!0)throw new N("option "+o+" must be "+a,N.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new N("Unknown option "+o,N.ERR_BAD_OPTION)}}const gi={assertOptions:Ng,validators:ho},Xe=gi.validators;let Gt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new du,response:new du}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=tn(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:o}=n;r!==void 0&&gi.assertOptions(r,{silentJSONParsing:Xe.transitional(Xe.boolean),forcedJSONParsing:Xe.transitional(Xe.boolean),clarifyTimeoutError:Xe.transitional(Xe.boolean)},!1),i!=null&&(y.isFunction(i)?n.paramsSerializer={serialize:i}:gi.assertOptions(i,{encode:Xe.function,serialize:Xe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),gi.assertOptions(n,{baseUrl:Xe.spelling("baseURL"),withXsrfToken:Xe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let l=o&&y.merge(o.common,o[n.method]);o&&y.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),n.headers=_e.concat(l,o);const s=[];let a=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(n)===!1||(a=a&&w.synchronous,s.unshift(w.fulfilled,w.rejected))});const u=[];this.interceptors.response.forEach(function(w){u.push(w.fulfilled,w.rejected)});let f,d=0,p;if(!a){const g=[Su.bind(this),void 0];for(g.unshift(...s),g.push(...u),p=g.length,f=Promise.resolve(n);d<p;)f=f.then(g[d++],g[d++]);return f}p=s.length;let v=n;for(d=0;d<p;){const g=s[d++],w=s[d++];try{v=g(v)}catch(x){w.call(this,x);break}}try{f=Su.call(this,v)}catch(g){return Promise.reject(g)}for(d=0,p=u.length;d<p;)f=f.then(u[d++],u[d++]);return f}getUri(t){t=tn(this.defaults,t);const n=od(t.baseURL,t.url,t.allowAbsoluteUrls);return ed(n,t.params,t.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(t){Gt.prototype[t]=function(n,r){return this.request(tn(r||{},{method:t,url:n,data:(r||{}).data}))}});y.forEach(["post","put","patch"],function(t){function n(r){return function(o,l,s){return this.request(tn(s||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:l}))}}Gt.prototype[t]=n(),Gt.prototype[t+"Form"]=n(!0)});let Dg=class fd{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(i=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](i);r._listeners=null}),this.promise.then=i=>{let o;const l=new Promise(s=>{r.subscribe(s),o=s}).then(i);return l.cancel=function(){r.unsubscribe(o)},l},t(function(o,l,s){r.reason||(r.reason=new Un(o,l,s),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new fd(function(i){t=i}),cancel:t}}};function Ag(e){return function(n){return e.apply(null,n)}}function Lg(e){return y.isObject(e)&&e.isAxiosError===!0}const Xl={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Xl).forEach(([e,t])=>{Xl[t]=e});function dd(e){const t=new Gt(e),n=Wf(Gt.prototype.request,t);return y.extend(n,Gt.prototype,t,{allOwnKeys:!0}),y.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return dd(tn(e,i))},n}const J=dd(Mr);J.Axios=Gt;J.CanceledError=Un;J.CancelToken=Dg;J.isCancel=rd;J.VERSION=cd;J.toFormData=fo;J.AxiosError=N;J.Cancel=J.CanceledError;J.all=function(t){return Promise.all(t)};J.spread=Ag;J.isAxiosError=Lg;J.mergeConfig=tn;J.AxiosHeaders=_e;J.formToJSON=e=>nd(y.isHTMLForm(e)?new FormData(e):e);J.getAdapter=ud.getAdapter;J.HttpStatusCode=Xl;J.default=J;const{Axios:wv,AxiosError:Sv,CanceledError:Ev,isCancel:kv,CancelToken:xv,VERSION:Cv,all:Tv,Cancel:_v,isAxiosError:Pv,spread:Ov,toFormData:Rv,AxiosHeaders:Nv,HttpStatusCode:Dv,formToJSON:Av,getAdapter:Lv,mergeConfig:Mv}=J;class Ks extends Error{constructor(n,r,i){super(n);ee(this,"statusCode");ee(this,"response");this.name="ApiError",this.statusCode=r,this.response=i}}class Mg extends Ks{constructor(t="Authentication failed"){super(t,401),this.name="AuthenticationError"}}class Ig extends Ks{constructor(t="Network error"){super(t,0),this.name="NetworkError"}}var Ve=(e=>(e.LOGIN_START="login_start",e.LOGIN_SUCCESS="login_success",e.LOGIN_FAILURE="login_failure",e.LOGOUT="logout",e.TOKEN_REFRESH="token_refresh",e.TOKEN_EXPIRED="token_expired",e.AUTH_ERROR="auth_error",e))(Ve||{});class Ug{constructor(){ee(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});ee(this,"listeners",[]);ee(this,"d365Context",null)}async initialize(){this.setLoading(!0);try{await this.waitForDynamicsContext();const t=await this.getDynamicsUser();if(t)this.authState={user:t,isAuthenticated:!0,isLoading:!1,error:null,token:null},this.emitAuthEvent(Ve.LOGIN_SUCCESS,{user:t}),M.info("Dynamics 365 authentication initialized successfully");else throw new Error("Failed to get user information from Dynamics 365")}catch(t){const n=t instanceof Error?t.message:"Unknown authentication error";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:n,token:null},this.emitAuthEvent(Ve.AUTH_ERROR,{error:n}),M.error("Dynamics 365 authentication initialization failed:",t)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(t){try{return this.authState.isAuthenticated&&this.authState.user?{success:!0,user:this.authState.user,token:this.authState.token||void 0}:(await this.initialize(),{success:this.authState.isAuthenticated,user:this.authState.user||void 0,error:this.authState.error||void 0})}catch(n){return{success:!1,error:n instanceof Error?n.message:"Login failed"}}}async logout(){this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(Ve.LOGOUT),this.notifyListeners(),M.info("Dynamics 365 authentication state cleared")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{return await this.getDynamicsUser()}catch(t){return M.error("Failed to get current user:",t),null}}async getAccessToken(){return null}async refreshToken(){return this.authState.isAuthenticated}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(t){return this.listeners.push(t),()=>{const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}}async waitForDynamicsContext(t=1e4){return new Promise((n,r)=>{const i=Date.now(),o=()=>{if(this.isDynamicsContextAvailable()){n();return}if(Date.now()-i>t){r(new Error("Timeout waiting for Dynamics 365 context"));return}setTimeout(o,100)};o()})}isDynamicsContextAvailable(){try{return!!window.Xrm&&!!window.Xrm.WebApi}catch{return!1}}async getDynamicsUser(){try{const t=window.Xrm;if(!t||!t.WebApi)throw new Error("Dynamics 365 context not available");const n=await t.WebApi.retrieveRecord("systemuser",t.Utility.getGlobalContext().getUserId(),"?$select=systemuserid,fullname,internalemailaddress"),r=await t.WebApi.retrieveMultipleRecords("role","?$filter=_parentroleid_value eq null&$select=name"),i=t.Utility.getGlobalContext().getOrganizationSettings();return this.d365Context={userId:n.systemuserid,userRoles:r.entities.map(o=>o.name),organizationId:i.organizationId,organizationName:i.uniqueName,serverUrl:t.Utility.getGlobalContext().getClientUrl(),version:t.Utility.getGlobalContext().getVersion()},{id:n.systemuserid,email:n.internalemailaddress,name:n.fullname,roles:this.d365Context.userRoles,organizationId:this.d365Context.organizationId}}catch(t){throw M.error("Failed to get Dynamics 365 user information:",t),t}}setLoading(t){this.authState.isLoading=t,this.notifyListeners()}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getAuthState())}catch(n){M.error("Error in auth state listener:",n)}})}emitAuthEvent(t,n){const r={event:t,timestamp:new Date,...n};M.debug("Auth event emitted:",r)}getDynamics365Context(){return this.d365Context}}const zg="modulepreload",jg=function(e){return"/"+e},ku={},Fg=function(t,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),s=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));i=Promise.allSettled(n.map(a=>{if(a=jg(a),a in ku)return;ku[a]=!0;const u=a.endsWith(".css"),f=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${f}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":zg,u||(d.as="script"),d.crossOrigin="",d.href=a,s&&d.setAttribute("nonce",s),document.head.appendChild(d),u)return new Promise((p,v)=>{d.addEventListener("load",p),d.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${a}`)))})}))}function o(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return i.then(l=>{for(const s of l||[])s.status==="rejected"&&o(s.reason);return t().catch(o)})};class Bg{constructor(t){ee(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});ee(this,"listeners",[]);ee(this,"msalInstance",null);ee(this,"config");ee(this,"tokenRefreshTimer",null);this.config=t}async initialize(){this.setLoading(!0);try{await this.initializeMSAL(),await this.checkExistingAuth(),M.info("MSAL authentication initialized successfully")}catch(t){const n=t instanceof Error?t.message:"MSAL initialization failed";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:n,token:null},this.emitAuthEvent(Ve.AUTH_ERROR,{error:n}),M.error("MSAL authentication initialization failed:",t)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(t){this.setLoading(!0),this.emitAuthEvent(Ve.LOGIN_START);try{if(!this.msalInstance)throw new Error("MSAL not initialized");const n={scopes:this.config.scopes,prompt:t!=null&&t.interactive?"select_account":void 0};let r;try{r=await this.msalInstance.acquireTokenSilent(n)}catch{r=await this.msalInstance.loginPopup(n)}const i=await this.processAuthResult(r);if(i)return this.authState={user:i,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(r)},this.startTokenRefreshTimer(),this.emitAuthEvent(Ve.LOGIN_SUCCESS,{user:i}),{success:!0,user:i,token:this.authState.token||void 0};throw new Error("Failed to process authentication result")}catch(n){const r=n instanceof Error?n.message:"Login failed";return this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:r,token:null},this.emitAuthEvent(Ve.LOGIN_FAILURE,{error:r}),{success:!1,error:r}}finally{this.notifyListeners()}}async logout(){try{this.tokenRefreshTimer&&(clearTimeout(this.tokenRefreshTimer),this.tokenRefreshTimer=null),this.msalInstance&&await this.msalInstance.logout({postLogoutRedirectUri:this.config.redirectUri})}catch(t){M.error("Logout error:",t)}this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(Ve.LOGOUT),this.notifyListeners(),M.info("User logged out successfully")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{if(!this.msalInstance)return null;const t=this.msalInstance.getActiveAccount();return t?this.createUserFromAccount(t):null}catch(t){return M.error("Failed to get current user:",t),null}}async getAccessToken(){try{if(!this.msalInstance)return null;if(this.authState.token&&this.authState.token.expiresAt>new Date)return this.authState.token.accessToken;const t={scopes:this.config.scopes,account:this.msalInstance.getActiveAccount()},n=await this.msalInstance.acquireTokenSilent(t);return n?(this.authState.token=this.createTokenFromAuthResult(n),this.notifyListeners(),n.accessToken):null}catch(t){return M.error("Failed to get access token:",t),this.emitAuthEvent(Ve.TOKEN_EXPIRED),null}}async refreshToken(){try{return await this.getAccessToken()?(this.emitAuthEvent(Ve.TOKEN_REFRESH),!0):!1}catch(t){return M.error("Token refresh failed:",t),!1}}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(t){return this.listeners.push(t),()=>{const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}}async initializeMSAL(){try{const{PublicClientApplication:t}=await Fg(async()=>{const{PublicClientApplication:r}=await import("./index-Cy8XKFxw.js");return{PublicClientApplication:r}},[]),n={auth:{clientId:this.config.clientId,authority:this.config.authority,redirectUri:this.config.redirectUri},cache:{cacheLocation:this.config.cacheLocation||"localStorage",storeAuthStateInCookie:!1}};this.msalInstance=new t(n),await this.msalInstance.initialize()}catch(t){if(t instanceof Error&&t.message.includes("Failed to resolve module"))M.warn("MSAL library not available, using mock implementation"),this.msalInstance=this.createMockMSALInstance();else throw t}}async checkExistingAuth(){if(this.msalInstance)try{const t=this.msalInstance.getAllAccounts();if(t.length>0){this.msalInstance.setActiveAccount(t[0]);const n={scopes:this.config.scopes,account:t[0]},r=await this.msalInstance.acquireTokenSilent(n);if(r){const i=this.createUserFromAccount(t[0]);this.authState={user:i,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(r)},this.startTokenRefreshTimer()}}}catch(t){M.debug("No existing valid authentication found:",t)}}async processAuthResult(t){var n;return!t||!t.account?null:((n=this.msalInstance)==null||n.setActiveAccount(t.account),this.createUserFromAccount(t.account))}createUserFromAccount(t){return{id:t.homeAccountId||t.localAccountId,email:t.username,name:t.name||t.username,roles:[],tenantId:t.tenantId}}createTokenFromAuthResult(t){return{accessToken:t.accessToken,expiresAt:new Date(t.expiresOn),scopes:t.scopes||this.config.scopes}}startTokenRefreshTimer(){if(this.tokenRefreshTimer&&clearTimeout(this.tokenRefreshTimer),this.authState.token){const t=this.authState.token.expiresAt.getTime()-Date.now()-3e5;t>0&&(this.tokenRefreshTimer=setTimeout(()=>{this.refreshToken()},t))}}setLoading(t){this.authState.isLoading=t,this.notifyListeners()}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getAuthState())}catch(n){M.error("Error in auth state listener:",n)}})}emitAuthEvent(t,n){const r={event:t,timestamp:new Date,...n};M.debug("Auth event emitted:",r)}createMockMSALInstance(){return{initialize:async()=>{},loginPopup:async()=>({account:{username:"<EMAIL>",name:"Mock User"},accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),loginRedirect:async()=>{},logout:async()=>{},acquireTokenSilent:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),acquireTokenPopup:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),getAllAccounts:()=>[],getActiveAccount:()=>null,setActiveAccount:()=>{}}}}const it=class it{static getInstance(){return it._instance||(it._instance=it.createAuthService()),it._instance}static createAuthService(){const t=Zh();switch(M.info(`Creating authentication service for ${t.mode} deployment mode`),t.mode){case pi.WEB_RESOURCE:return new Ug;case pi.EMBEDDED_SPA:case pi.STANDALONE_MFE:if(!t.msalConfig)throw new Error("MSAL configuration is required for standalone deployment mode");return new Bg(t.msalConfig);default:throw new Error(`Unsupported deployment mode: ${t.mode}`)}}static reset(){it._instance=null}static forceInstance(t){it._instance=t}};ee(it,"_instance",null);let Jl=it;function pd(){return Jl.getInstance()}const Go={};class $g{constructor(t){ee(this,"client");ee(this,"authService",pd());this.client=J.create({baseURL:t.baseURL,timeout:t.timeout||3e4,headers:{"Content-Type":"application/json","OData-MaxVersion":"4.0","OData-Version":"4.0",Accept:"application/json",Prefer:"return=representation",...t.headers}}),this.setupInterceptors()}async initialize(){try{await this.authService.initialize(),M.info("External API client initialized successfully")}catch(t){throw M.error("Failed to initialize external API client:",t),t}}setupInterceptors(){this.client.interceptors.request.use(async t=>{var r;M.info(`API Request: ${(r=t.method)==null?void 0:r.toUpperCase()} ${t.url}`);const n=await this.getAuthToken();return n&&(t.headers=t.headers||{},t.headers.Authorization=`Bearer ${n}`),t},t=>(M.error("API Request Error:",t),Promise.reject(this.createApiError(t)))),this.client.interceptors.response.use(t=>(M.info(`API Response: ${t.status} ${t.config.url}`),t),async t=>{var n;if(M.error("API Response Error:",t),((n=t.response)==null?void 0:n.status)===401&&await this.handleUnauthorized()&&t.config){const i=await this.getAuthToken();if(i)return t.config.headers.Authorization=`Bearer ${i}`,this.client.request(t.config)}return Promise.reject(this.createApiError(t))})}async getAuthToken(){try{return await this.authService.getAccessToken()}catch(t){return M.error("Failed to get auth token:",t),null}}async handleUnauthorized(){try{return await this.authService.refreshToken()?(M.info("Token refreshed successfully"),!0):(M.warn("Token refresh failed, triggering re-authentication"),await this.authService.login({interactive:!0}),!0)}catch(t){return M.error("Failed to handle unauthorized access:",t),!1}}async get(t,n){try{const r=this.convertConfig(n),i=await this.client.get(t,r);return{data:i.data,success:!0,statusCode:i.status}}catch(r){return this.handleError(r)}}async post(t,n,r){try{const i=this.convertConfig(r),o=await this.client.post(t,n,i);return{data:o.data,success:!0,statusCode:o.status}}catch(i){return this.handleError(i)}}async put(t,n,r){try{const i=this.convertConfig(r),o=await this.client.put(t,n,i);return{data:o.data,success:!0,statusCode:o.status}}catch(i){return this.handleError(i)}}async patch(t,n,r){try{const i=this.convertConfig(r),o=await this.client.patch(t,n,i);return{data:o.data,success:!0,statusCode:o.status}}catch(i){return this.handleError(i)}}async delete(t,n){try{const r=this.convertConfig(n),i=await this.client.delete(t,r);return{data:i.data,success:!0,statusCode:i.status}}catch(r){return this.handleError(r)}}async retrieveRecord(t,n,r){const i=this.buildQueryString(r),o=`${t}(${n})${i}`;return this.get(o)}async retrieveMultipleRecords(t,n){try{const r=this.buildQueryString(n),i=`${t}${r}`,o=await this.get(i);return o.success&&o.data?{data:o.data.value,success:!0,statusCode:o.statusCode,pagination:{page:Math.floor(((n==null?void 0:n.skip)||0)/((n==null?void 0:n.top)||50))+1,pageSize:o.data.value.length,totalCount:o.data["@odata.count"]||o.data.value.length,hasNext:!!o.data["@odata.nextLink"],hasPrevious:((n==null?void 0:n.skip)||0)>0}}:this.handleErrorPaginated(new Error("Invalid response format"))}catch(r){return this.handleErrorPaginated(r)}}async createRecord(t,n){return this.post(t,n)}async updateRecord(t,n,r){const i=`${t}(${n})`;return this.patch(i,r)}async deleteRecord(t,n){const r=`${t}(${n})`;return this.delete(r)}async executeFunction(t,n){let r=t;if(n){const i=Object.keys(n).map(o=>`${o}=${encodeURIComponent(n[o])}`).join(",");r+=`(${i})`}return this.get(r)}async executeBatch(t){try{const n=this.generateBatchId(),r=this.generateChangesetId(),i=this.buildBatchBody(t,n,r),o=await this.client.post("$batch",i,{headers:{"Content-Type":`multipart/mixed; boundary=batch_${n}`}}),l=this.parseBatchResponse(o.data);return{responses:l,success:l.every(s=>s.success)}}catch(n){return M.error("Batch execution failed:",n),{responses:[],success:!1,errors:[n instanceof Error?n.message:"Batch execution failed"]}}}convertConfig(t){return t?{headers:t.headers,timeout:t.timeout}:{}}buildQueryString(t){if(!t)return"";const n=[];return t.select&&n.push(`$select=${t.select.join(",")}`),t.filter&&n.push(`$filter=${encodeURIComponent(t.filter)}`),t.orderBy&&n.push(`$orderby=${encodeURIComponent(t.orderBy)}`),t.expand&&n.push(`$expand=${t.expand.join(",")}`),t.top&&n.push(`$top=${t.top}`),t.skip&&n.push(`$skip=${t.skip}`),n.length>0?`?${n.join("&")}`:""}generateBatchId(){return Math.random().toString(36).substring(2,15)}generateChangesetId(){return Math.random().toString(36).substring(2,15)}buildBatchBody(t,n,r){return t.map(i=>`--batch_${n}
Content-Type: application/http

${i.method} ${i.url} HTTP/1.1

`).join("")}parseBatchResponse(t){return[]}createApiError(t){var n,r,i,o,l,s;return t.code==="ECONNABORTED"||t.code==="ENOTFOUND"?new Ig(t.message):((n=t.response)==null?void 0:n.status)===401?new Mg(((r=t.response.data)==null?void 0:r.message)||"Authentication failed"):new Ks(((o=(i=t.response)==null?void 0:i.data)==null?void 0:o.message)||t.message||"An error occurred",((l=t.response)==null?void 0:l.status)||500,(s=t.response)==null?void 0:s.data)}handleError(t){const n=this.createApiError(t);return{data:null,success:!1,message:n.message,statusCode:n.statusCode,errors:[n.message]}}handleErrorPaginated(t){return{...this.handleError(t),data:[],pagination:{page:1,pageSize:0,totalCount:0,hasNext:!1,hasPrevious:!1}}}}const Wg=()=>(Go==null?void 0:Go.VITE_API_BASE_URL)||"http://localhost:3001/api";new $g({baseURL:Wg()});const Vg=()=>{const[e,t]=K.useState({user:null,isAuthenticated:!1,isLoading:!0,error:null,token:null}),n=pd();K.useEffect(()=>{const v=(async()=>{try{await n.initialize();const g=n.onAuthStateChanged(w=>{t(w)});return t(n.getAuthState()),g}catch(g){M.error("Auth initialization error:",g),t(w=>({...w,error:g instanceof Error?g.message:"Authentication initialization failed",isLoading:!1}))}})();return()=>{v.then(g=>{g&&g()})}},[n]);const r=K.useCallback(async p=>await n.login(p),[n]),i=K.useCallback(async()=>{await n.logout()},[n]),o=K.useCallback(async()=>await n.refreshToken(),[n]),l=K.useCallback(async()=>await n.getCurrentUser(),[n]),s=K.useCallback(async()=>await n.getAccessToken(),[n]),a=K.useCallback(()=>n.isAuthenticated(),[n]),u=K.useCallback(p=>n.onAuthStateChanged(p),[n]),f=K.useCallback(()=>n.getAuthState(),[n]),d=K.useCallback(async()=>n.initialize(),[n]);return{...e,login:r,logout:i,refreshToken:o,getCurrentUser:l,getAccessToken:s,checkAuthenticated:a,onAuthStateChanged:u,getAuthState:f,initialize:d}};function Vi(e){"@babel/helpers - typeof";return Vi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vi(e)}function Lt(e){if(e===null||e===!0||e===!1)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function he(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function nt(e){he(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||Vi(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function Hg(e,t){he(2,arguments);var n=nt(e).getTime(),r=Lt(t);return new Date(n+r)}var bg={};function mo(){return bg}function Yg(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}var hd=6e4,md=36e5;function Qg(e){return he(1,arguments),e instanceof Date||Vi(e)==="object"&&Object.prototype.toString.call(e)==="[object Date]"}function gd(e){if(he(1,arguments),!Qg(e)&&typeof e!="number")return!1;var t=nt(e);return!isNaN(Number(t))}function qg(e,t){he(2,arguments);var n=Lt(t);return Hg(e,-n)}var Gg=864e5;function Kg(e){he(1,arguments);var t=nt(e),n=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var r=t.getTime(),i=n-r;return Math.floor(i/Gg)+1}function Hi(e){he(1,arguments);var t=1,n=nt(e),r=n.getUTCDay(),i=(r<t?7:0)+r-t;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function yd(e){he(1,arguments);var t=nt(e),n=t.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=Hi(r),o=new Date(0);o.setUTCFullYear(n,0,4),o.setUTCHours(0,0,0,0);var l=Hi(o);return t.getTime()>=i.getTime()?n+1:t.getTime()>=l.getTime()?n:n-1}function Xg(e){he(1,arguments);var t=yd(e),n=new Date(0);n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0);var r=Hi(n);return r}var Jg=6048e5;function Zg(e){he(1,arguments);var t=nt(e),n=Hi(t).getTime()-Xg(t).getTime();return Math.round(n/Jg)+1}function bi(e,t){var n,r,i,o,l,s,a,u;he(1,arguments);var f=mo(),d=Lt((n=(r=(i=(o=t==null?void 0:t.weekStartsOn)!==null&&o!==void 0?o:t==null||(l=t.locale)===null||l===void 0||(s=l.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&i!==void 0?i:f.weekStartsOn)!==null&&r!==void 0?r:(a=f.locale)===null||a===void 0||(u=a.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(d>=0&&d<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var p=nt(e),v=p.getUTCDay(),g=(v<d?7:0)+v-d;return p.setUTCDate(p.getUTCDate()-g),p.setUTCHours(0,0,0,0),p}function vd(e,t){var n,r,i,o,l,s,a,u;he(1,arguments);var f=nt(e),d=f.getUTCFullYear(),p=mo(),v=Lt((n=(r=(i=(o=t==null?void 0:t.firstWeekContainsDate)!==null&&o!==void 0?o:t==null||(l=t.locale)===null||l===void 0||(s=l.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&i!==void 0?i:p.firstWeekContainsDate)!==null&&r!==void 0?r:(a=p.locale)===null||a===void 0||(u=a.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(v>=1&&v<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var g=new Date(0);g.setUTCFullYear(d+1,0,v),g.setUTCHours(0,0,0,0);var w=bi(g,t),x=new Date(0);x.setUTCFullYear(d,0,v),x.setUTCHours(0,0,0,0);var h=bi(x,t);return f.getTime()>=w.getTime()?d+1:f.getTime()>=h.getTime()?d:d-1}function ey(e,t){var n,r,i,o,l,s,a,u;he(1,arguments);var f=mo(),d=Lt((n=(r=(i=(o=t==null?void 0:t.firstWeekContainsDate)!==null&&o!==void 0?o:t==null||(l=t.locale)===null||l===void 0||(s=l.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(a=f.locale)===null||a===void 0||(u=a.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),p=vd(e,t),v=new Date(0);v.setUTCFullYear(p,0,d),v.setUTCHours(0,0,0,0);var g=bi(v,t);return g}var ty=6048e5;function ny(e,t){he(1,arguments);var n=nt(e),r=bi(n,t).getTime()-ey(n,t).getTime();return Math.round(r/ty)+1}function z(e,t){for(var n=e<0?"-":"",r=Math.abs(e).toString();r.length<t;)r="0"+r;return n+r}var gt={y:function(t,n){var r=t.getUTCFullYear(),i=r>0?r:1-r;return z(n==="yy"?i%100:i,n.length)},M:function(t,n){var r=t.getUTCMonth();return n==="M"?String(r+1):z(r+1,2)},d:function(t,n){return z(t.getUTCDate(),n.length)},a:function(t,n){var r=t.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(t,n){return z(t.getUTCHours()%12||12,n.length)},H:function(t,n){return z(t.getUTCHours(),n.length)},m:function(t,n){return z(t.getUTCMinutes(),n.length)},s:function(t,n){return z(t.getUTCSeconds(),n.length)},S:function(t,n){var r=n.length,i=t.getUTCMilliseconds(),o=Math.floor(i*Math.pow(10,r-3));return z(o,n.length)}},ln={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},ry={G:function(t,n,r){var i=t.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(t,n,r){if(n==="yo"){var i=t.getUTCFullYear(),o=i>0?i:1-i;return r.ordinalNumber(o,{unit:"year"})}return gt.y(t,n)},Y:function(t,n,r,i){var o=vd(t,i),l=o>0?o:1-o;if(n==="YY"){var s=l%100;return z(s,2)}return n==="Yo"?r.ordinalNumber(l,{unit:"year"}):z(l,n.length)},R:function(t,n){var r=yd(t);return z(r,n.length)},u:function(t,n){var r=t.getUTCFullYear();return z(r,n.length)},Q:function(t,n,r){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return z(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(t,n,r){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return z(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(t,n,r){var i=t.getUTCMonth();switch(n){case"M":case"MM":return gt.M(t,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(t,n,r){var i=t.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return z(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(t,n,r,i){var o=ny(t,i);return n==="wo"?r.ordinalNumber(o,{unit:"week"}):z(o,n.length)},I:function(t,n,r){var i=Zg(t);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):z(i,n.length)},d:function(t,n,r){return n==="do"?r.ordinalNumber(t.getUTCDate(),{unit:"date"}):gt.d(t,n)},D:function(t,n,r){var i=Kg(t);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):z(i,n.length)},E:function(t,n,r){var i=t.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(t,n,r,i){var o=t.getUTCDay(),l=(o-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(l);case"ee":return z(l,2);case"eo":return r.ordinalNumber(l,{unit:"day"});case"eee":return r.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(o,{width:"short",context:"formatting"});case"eeee":default:return r.day(o,{width:"wide",context:"formatting"})}},c:function(t,n,r,i){var o=t.getUTCDay(),l=(o-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(l);case"cc":return z(l,n.length);case"co":return r.ordinalNumber(l,{unit:"day"});case"ccc":return r.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(o,{width:"narrow",context:"standalone"});case"cccccc":return r.day(o,{width:"short",context:"standalone"});case"cccc":default:return r.day(o,{width:"wide",context:"standalone"})}},i:function(t,n,r){var i=t.getUTCDay(),o=i===0?7:i;switch(n){case"i":return String(o);case"ii":return z(o,n.length);case"io":return r.ordinalNumber(o,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(t,n,r){var i=t.getUTCHours(),o=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(t,n,r){var i=t.getUTCHours(),o;switch(i===12?o=ln.noon:i===0?o=ln.midnight:o=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(t,n,r){var i=t.getUTCHours(),o;switch(i>=17?o=ln.evening:i>=12?o=ln.afternoon:i>=4?o=ln.morning:o=ln.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(t,n,r){if(n==="ho"){var i=t.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return gt.h(t,n)},H:function(t,n,r){return n==="Ho"?r.ordinalNumber(t.getUTCHours(),{unit:"hour"}):gt.H(t,n)},K:function(t,n,r){var i=t.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):z(i,n.length)},k:function(t,n,r){var i=t.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):z(i,n.length)},m:function(t,n,r){return n==="mo"?r.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):gt.m(t,n)},s:function(t,n,r){return n==="so"?r.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):gt.s(t,n)},S:function(t,n){return gt.S(t,n)},X:function(t,n,r,i){var o=i._originalDate||t,l=o.getTimezoneOffset();if(l===0)return"Z";switch(n){case"X":return Cu(l);case"XXXX":case"XX":return $t(l);case"XXXXX":case"XXX":default:return $t(l,":")}},x:function(t,n,r,i){var o=i._originalDate||t,l=o.getTimezoneOffset();switch(n){case"x":return Cu(l);case"xxxx":case"xx":return $t(l);case"xxxxx":case"xxx":default:return $t(l,":")}},O:function(t,n,r,i){var o=i._originalDate||t,l=o.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+xu(l,":");case"OOOO":default:return"GMT"+$t(l,":")}},z:function(t,n,r,i){var o=i._originalDate||t,l=o.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+xu(l,":");case"zzzz":default:return"GMT"+$t(l,":")}},t:function(t,n,r,i){var o=i._originalDate||t,l=Math.floor(o.getTime()/1e3);return z(l,n.length)},T:function(t,n,r,i){var o=i._originalDate||t,l=o.getTime();return z(l,n.length)}};function xu(e,t){var n=e>0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),o=r%60;if(o===0)return n+String(i);var l=t;return n+String(i)+l+z(o,2)}function Cu(e,t){if(e%60===0){var n=e>0?"-":"+";return n+z(Math.abs(e)/60,2)}return $t(e,t)}function $t(e,t){var n=t||"",r=e>0?"-":"+",i=Math.abs(e),o=z(Math.floor(i/60),2),l=z(i%60,2);return r+o+n+l}var Tu=function(t,n){switch(t){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},wd=function(t,n){switch(t){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},iy=function(t,n){var r=t.match(/(P+)(p+)?/)||[],i=r[1],o=r[2];if(!o)return Tu(t,n);var l;switch(i){case"P":l=n.dateTime({width:"short"});break;case"PP":l=n.dateTime({width:"medium"});break;case"PPP":l=n.dateTime({width:"long"});break;case"PPPP":default:l=n.dateTime({width:"full"});break}return l.replace("{{date}}",Tu(i,n)).replace("{{time}}",wd(o,n))},oy={p:wd,P:iy},ly=["D","DD"],sy=["YY","YYYY"];function ay(e){return ly.indexOf(e)!==-1}function uy(e){return sy.indexOf(e)!==-1}function _u(e,t,n){if(e==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var cy={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},fy=function(t,n,r){var i,o=cy[t];return typeof o=="string"?i=o:n===1?i=o.one:i=o.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function Ko(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth,r=e.formats[n]||e.formats[e.defaultWidth];return r}}var dy={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},py={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},hy={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},my={date:Ko({formats:dy,defaultWidth:"full"}),time:Ko({formats:py,defaultWidth:"full"}),dateTime:Ko({formats:hy,defaultWidth:"full"})},gy={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},yy=function(t,n,r,i){return gy[t]};function Yn(e){return function(t,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,l=n!=null&&n.width?String(n.width):o;i=e.formattingValues[l]||e.formattingValues[o]}else{var s=e.defaultWidth,a=n!=null&&n.width?String(n.width):e.defaultWidth;i=e.values[a]||e.values[s]}var u=e.argumentCallback?e.argumentCallback(t):t;return i[u]}}var vy={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},wy={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Sy={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Ey={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ky={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},xy={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Cy=function(t,n){var r=Number(t),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},Ty={ordinalNumber:Cy,era:Yn({values:vy,defaultWidth:"wide"}),quarter:Yn({values:wy,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:Yn({values:Sy,defaultWidth:"wide"}),day:Yn({values:Ey,defaultWidth:"wide"}),dayPeriod:Yn({values:ky,defaultWidth:"wide",formattingValues:xy,defaultFormattingWidth:"wide"})};function Qn(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;var l=o[0],s=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],a=Array.isArray(s)?Py(s,function(d){return d.test(l)}):_y(s,function(d){return d.test(l)}),u;u=e.valueCallback?e.valueCallback(a):a,u=n.valueCallback?n.valueCallback(u):u;var f=t.slice(l.length);return{value:u,rest:f}}}function _y(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}function Py(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}function Oy(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;var i=r[0],o=t.match(e.parsePattern);if(!o)return null;var l=e.valueCallback?e.valueCallback(o[0]):o[0];l=n.valueCallback?n.valueCallback(l):l;var s=t.slice(i.length);return{value:l,rest:s}}}var Ry=/^(\d+)(th|st|nd|rd)?/i,Ny=/\d+/i,Dy={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Ay={any:[/^b/i,/^(a|c)/i]},Ly={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},My={any:[/1/i,/2/i,/3/i,/4/i]},Iy={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Uy={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},zy={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},jy={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Fy={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},By={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},$y={ordinalNumber:Oy({matchPattern:Ry,parsePattern:Ny,valueCallback:function(t){return parseInt(t,10)}}),era:Qn({matchPatterns:Dy,defaultMatchWidth:"wide",parsePatterns:Ay,defaultParseWidth:"any"}),quarter:Qn({matchPatterns:Ly,defaultMatchWidth:"wide",parsePatterns:My,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:Qn({matchPatterns:Iy,defaultMatchWidth:"wide",parsePatterns:Uy,defaultParseWidth:"any"}),day:Qn({matchPatterns:zy,defaultMatchWidth:"wide",parsePatterns:jy,defaultParseWidth:"any"}),dayPeriod:Qn({matchPatterns:Fy,defaultMatchWidth:"any",parsePatterns:By,defaultParseWidth:"any"})},Wy={code:"en-US",formatDistance:fy,formatLong:my,formatRelative:yy,localize:Ty,match:$y,options:{weekStartsOn:0,firstWeekContainsDate:1}},Vy=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Hy=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,by=/^'([^]*?)'?$/,Yy=/''/g,Qy=/[a-zA-Z]/;function qy(e,t,n){var r,i,o,l,s,a,u,f,d,p,v,g,w,x;he(2,arguments);var h=String(t),c=mo(),m=(r=(i=void 0)!==null&&i!==void 0?i:c.locale)!==null&&r!==void 0?r:Wy,S=Lt((o=(l=(s=(a=void 0)!==null&&a!==void 0?a:void 0)!==null&&s!==void 0?s:c.firstWeekContainsDate)!==null&&l!==void 0?l:(u=c.locale)===null||u===void 0||(f=u.options)===null||f===void 0?void 0:f.firstWeekContainsDate)!==null&&o!==void 0?o:1);if(!(S>=1&&S<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var C=Lt((d=(p=(v=(g=void 0)!==null&&g!==void 0?g:void 0)!==null&&v!==void 0?v:c.weekStartsOn)!==null&&p!==void 0?p:(w=c.locale)===null||w===void 0||(x=w.options)===null||x===void 0?void 0:x.weekStartsOn)!==null&&d!==void 0?d:0);if(!(C>=0&&C<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!m.localize)throw new RangeError("locale must contain localize property");if(!m.formatLong)throw new RangeError("locale must contain formatLong property");var T=nt(e);if(!gd(T))throw new RangeError("Invalid time value");var _=Yg(T),R=qg(T,_),F={firstWeekContainsDate:S,weekStartsOn:C,locale:m,_originalDate:T},L=h.match(Hy).map(function($){var we=$[0];if(we==="p"||we==="P"){var Ge=oy[we];return Ge($,m.formatLong)}return $}).join("").match(Vy).map(function($){if($==="''")return"'";var we=$[0];if(we==="'")return Gy($);var Ge=ry[we];if(Ge)return uy($)&&_u($,t,String(e)),ay($)&&_u($,t,String(e)),Ge(R,$,m.localize,F);if(we.match(Qy))throw new RangeError("Format string contains an unescaped latin alphabet character `"+we+"`");return $}).join("");return L}function Gy(e){var t=e.match(by);return t?t[1].replace(Yy,"'"):e}function Ky(e,t){var n;he(1,arguments);var r=Lt((n=void 0)!==null&&n!==void 0?n:2);if(r!==2&&r!==1&&r!==0)throw new RangeError("additionalDigits must be 0, 1 or 2");if(!(typeof e=="string"||Object.prototype.toString.call(e)==="[object String]"))return new Date(NaN);var i=ev(e),o;if(i.date){var l=tv(i.date,r);o=nv(l.restDateString,l.year)}if(!o||isNaN(o.getTime()))return new Date(NaN);var s=o.getTime(),a=0,u;if(i.time&&(a=rv(i.time),isNaN(a)))return new Date(NaN);if(i.timezone){if(u=iv(i.timezone),isNaN(u))return new Date(NaN)}else{var f=new Date(s+a),d=new Date(0);return d.setFullYear(f.getUTCFullYear(),f.getUTCMonth(),f.getUTCDate()),d.setHours(f.getUTCHours(),f.getUTCMinutes(),f.getUTCSeconds(),f.getUTCMilliseconds()),d}return new Date(s+a+u)}var ei={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Xy=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Jy=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Zy=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ev(e){var t={},n=e.split(ei.dateTimeDelimiter),r;if(n.length>2)return t;if(/:/.test(n[0])?r=n[0]:(t.date=n[0],r=n[1],ei.timeZoneDelimiter.test(t.date)&&(t.date=e.split(ei.timeZoneDelimiter)[0],r=e.substr(t.date.length,e.length))),r){var i=ei.timezone.exec(r);i?(t.time=r.replace(i[1],""),t.timezone=i[1]):t.time=r}return t}function tv(e,t){var n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};var i=r[1]?parseInt(r[1]):null,o=r[2]?parseInt(r[2]):null;return{year:o===null?i:o*100,restDateString:e.slice((r[1]||r[2]).length)}}function nv(e,t){if(t===null)return new Date(NaN);var n=e.match(Xy);if(!n)return new Date(NaN);var r=!!n[4],i=qn(n[1]),o=qn(n[2])-1,l=qn(n[3]),s=qn(n[4]),a=qn(n[5])-1;if(r)return uv(t,s,a)?ov(t,s,a):new Date(NaN);var u=new Date(0);return!sv(t,o,l)||!av(t,i)?new Date(NaN):(u.setUTCFullYear(t,o,Math.max(i,l)),u)}function qn(e){return e?parseInt(e):1}function rv(e){var t=e.match(Jy);if(!t)return NaN;var n=Xo(t[1]),r=Xo(t[2]),i=Xo(t[3]);return cv(n,r,i)?n*md+r*hd+i*1e3:NaN}function Xo(e){return e&&parseFloat(e.replace(",","."))||0}function iv(e){if(e==="Z")return 0;var t=e.match(Zy);if(!t)return 0;var n=t[1]==="+"?-1:1,r=parseInt(t[2]),i=t[3]&&parseInt(t[3])||0;return fv(r,i)?n*(r*md+i*hd):NaN}function ov(e,t,n){var r=new Date(0);r.setUTCFullYear(e,0,4);var i=r.getUTCDay()||7,o=(t-1)*7+n+1-i;return r.setUTCDate(r.getUTCDate()+o),r}var lv=[31,null,31,30,31,30,31,31,30,31,30,31];function Sd(e){return e%400===0||e%4===0&&e%100!==0}function sv(e,t,n){return t>=0&&t<=11&&n>=1&&n<=(lv[t]||(Sd(e)?29:28))}function av(e,t){return t>=1&&t<=(Sd(e)?366:365)}function uv(e,t,n){return t>=1&&t<=53&&n>=0&&n<=6}function cv(e,t,n){return e===24?t===0&&n===0:n>=0&&n<60&&t>=0&&t<60&&e>=0&&e<25}function fv(e,t){return t>=0&&t<=59}const Ed=(e,t="MMM dd, yyyy")=>{try{const n=dv(e);return gd(n)?qy(n,t):"Invalid Date"}catch(n){return console.error("Error formatting date:",n),"Invalid Date"}},dv=e=>{if(e instanceof Date)return e;if(typeof e=="string")return e.includes("T")||e.includes("Z")?Ky(e):new Date(e);if(typeof e=="number")return new Date(e);throw new Error("Invalid date input")},pv=({transcript:e,onGenerateSummary:t})=>{const[n,r]=K.useState(!1),i=async()=>{r(!0);try{await t(e.id)}finally{r(!1)}};return E.jsxs("div",{style:{flex:1,padding:"2rem",backgroundColor:"#fff",borderRight:"1px solid #e5e7eb"},children:[E.jsxs("div",{style:{marginBottom:"2rem"},children:[E.jsx("h2",{style:{margin:"0 0 0.5rem 0",color:"#1f2937"},children:e.title}),E.jsxs("div",{style:{display:"flex",gap:"1rem",fontSize:"0.875rem",color:"#6b7280",marginBottom:"1rem"},children:[E.jsxs("span",{children:["Created: ",Ed(e.createdAt,"MMM dd, yyyy h:mm a")]}),E.jsxs("span",{children:["Duration: ",Math.floor(e.duration/60),"m ",e.duration%60,"s"]})]}),E.jsxs("div",{style:{display:"flex",gap:"0.5rem"},children:[E.jsx(Ie,{onClick:i,disabled:n,size:"small",children:n?"Generating...":"Generate Summary"}),E.jsx(Ie,{variant:"secondary",size:"small",children:"Export"}),E.jsx(Ie,{variant:"secondary",size:"small",children:"Share"})]})]}),E.jsxs("div",{style:{backgroundColor:"#f9fafb",border:"1px solid #e5e7eb",borderRadius:"0.5rem",padding:"1.5rem",minHeight:"400px"},children:[E.jsx("h3",{style:{margin:"0 0 1rem 0",color:"#374151"},children:"Transcript"}),n&&E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"1rem",padding:"0.75rem",backgroundColor:"#dbeafe",borderRadius:"0.375rem"},children:[E.jsx($f,{size:"small"}),E.jsx("span",{style:{fontSize:"0.875rem",color:"#1e40af"},children:"Generating AI summary..."})]}),E.jsx("div",{style:{lineHeight:"1.6",color:"#374151",whiteSpace:"pre-wrap"},children:e.content})]}),E.jsxs("div",{style:{marginTop:"1rem",display:"flex",gap:"0.5rem",fontSize:"0.875rem"},children:[E.jsx(Ie,{variant:"secondary",size:"small",children:"Search in Transcript"}),E.jsx(Ie,{variant:"secondary",size:"small",children:"Add Timestamp"}),E.jsx(Ie,{variant:"secondary",size:"small",children:"Highlight Text"})]})]})},hv=({transcript:e})=>E.jsxs("div",{style:{width:"350px",padding:"2rem",backgroundColor:"#f9fafb",borderLeft:"1px solid #e5e7eb"},children:[E.jsx("h3",{style:{margin:"0 0 1rem 0",color:"#1f2937"},children:"Summary & Insights"}),E.jsxs("div",{style:{marginBottom:"2rem"},children:[E.jsx("h4",{style:{margin:"0 0 0.5rem 0",fontSize:"0.875rem",fontWeight:"600",color:"#374151"},children:"AI Summary"}),E.jsx("div",{style:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"0.375rem",padding:"1rem",minHeight:"100px"},children:e.summary?E.jsx("p",{style:{margin:0,lineHeight:"1.5",color:"#374151",fontSize:"0.875rem"},children:e.summary}):E.jsx("p",{style:{margin:0,color:"#9ca3af",fontStyle:"italic",fontSize:"0.875rem"},children:'No summary available. Click "Generate Summary" to create one.'})})]}),E.jsxs("div",{style:{marginBottom:"2rem"},children:[E.jsx("h4",{style:{margin:"0 0 0.5rem 0",fontSize:"0.875rem",fontWeight:"600",color:"#374151"},children:"Key Points"}),E.jsx("div",{style:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"0.375rem",padding:"1rem"},children:E.jsxs("ul",{style:{margin:0,paddingLeft:"1rem",fontSize:"0.875rem",color:"#374151"},children:[E.jsx("li",{children:"Customer inquiry about billing"}),E.jsx("li",{children:"Payment method updated"}),E.jsx("li",{children:"Issue resolved successfully"})]})})]}),E.jsxs("div",{style:{marginBottom:"2rem"},children:[E.jsx("h4",{style:{margin:"0 0 0.5rem 0",fontSize:"0.875rem",fontWeight:"600",color:"#374151"},children:"Action Items"}),E.jsx("div",{style:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"0.375rem",padding:"1rem"},children:E.jsxs("div",{style:{fontSize:"0.875rem"},children:[E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"0.5rem"},children:[E.jsx("input",{type:"checkbox"}),E.jsx("span",{children:"Follow up with customer in 24 hours"})]}),E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"0.5rem"},children:[E.jsx("input",{type:"checkbox"}),E.jsx("span",{children:"Update customer record"})]}),E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[E.jsx("input",{type:"checkbox"}),E.jsx("span",{children:"Send confirmation email"})]})]})})]}),E.jsxs("div",{style:{marginBottom:"2rem"},children:[E.jsx("h4",{style:{margin:"0 0 0.5rem 0",fontSize:"0.875rem",fontWeight:"600",color:"#374151"},children:"Sentiment Analysis"}),E.jsx("div",{style:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"0.375rem",padding:"1rem"},children:E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[E.jsx("div",{style:{width:"12px",height:"12px",backgroundColor:"#10b981",borderRadius:"50%"}}),E.jsx("span",{style:{fontSize:"0.875rem",color:"#374151"},children:"Positive (85%)"})]})})]}),E.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"0.5rem"},children:[E.jsx(Ie,{size:"small",style:{width:"100%"},children:"Export Summary"}),E.jsx(Ie,{variant:"secondary",size:"small",style:{width:"100%"},children:"Add to CRM"}),E.jsx(Ie,{variant:"secondary",size:"small",style:{width:"100%"},children:"Schedule Follow-up"})]})]});function mv(){const[e,t]=K.useState([]),[n,r]=K.useState(null),[i,o]=K.useState(!0),{user:l,isAuthenticated:s,login:a}=Vg();K.useEffect(()=>{M.info("Transcript and Summary app initialized"),u()},[]);const u=async()=>{o(!0);try{await new Promise(v=>setTimeout(v,1e3));const p=[{id:"1",title:"Customer Support Call - John Doe",content:"Customer called regarding billing issue. Resolved by updating payment method.",createdAt:new Date().toISOString(),duration:180,summary:"Billing issue resolved by updating payment method."},{id:"2",title:"Sales Call - Jane Smith",content:"Discussed product features and pricing. Customer interested in enterprise plan.",createdAt:new Date(Date.now()-864e5).toISOString(),duration:300,summary:"Sales opportunity for enterprise plan."}];t(p),M.info("Transcripts loaded successfully",{count:p.length})}catch(p){M.error("Failed to load transcripts",p)}finally{o(!1)}},f=p=>{r(p),M.info("Transcript selected",{id:p.id,title:p.title})},d=async p=>{M.info("Generating summary for transcript",{transcriptId:p}),await new Promise(g=>setTimeout(g,2e3));const v=e.map(g=>g.id===p?{...g,summary:"AI-generated summary: Key points and action items from the conversation."}:g);t(v),(n==null?void 0:n.id)===p&&r(v.find(g=>g.id===p)||null),M.info("Summary generated successfully",{transcriptId:p})};return s?E.jsxs("div",{style:{display:"flex",flexDirection:"column",minHeight:"100vh"},children:[E.jsx("header",{style:{backgroundColor:"#fff",padding:"1rem 2rem",borderBottom:"1px solid #e5e7eb",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)"},children:E.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[E.jsx("h1",{style:{margin:0,color:"#1f2937"},children:"Transcript and Summary"}),E.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[E.jsxs("span",{children:["Welcome, ",(l==null?void 0:l.name)||"User"]}),E.jsx(Ie,{variant:"secondary",size:"small",children:"Logout"})]})]})}),E.jsxs("div",{style:{display:"flex",flex:1},children:[E.jsxs("aside",{style:{width:"300px",backgroundColor:"#f9fafb",borderRight:"1px solid #e5e7eb",padding:"1rem"},children:[E.jsx("div",{style:{marginBottom:"1rem"},children:E.jsx(Ie,{onClick:u,size:"small",style:{width:"100%"},children:"Refresh Transcripts"})}),i?E.jsx($f,{text:"Loading transcripts..."}):E.jsxs("div",{children:[E.jsx("h3",{style:{marginBottom:"1rem",color:"#374151"},children:"Recent Transcripts"}),e.map(p=>E.jsxs("div",{onClick:()=>f(p),style:{padding:"0.75rem",marginBottom:"0.5rem",backgroundColor:(n==null?void 0:n.id)===p.id?"#dbeafe":"#fff",border:"1px solid #e5e7eb",borderRadius:"0.375rem",cursor:"pointer",transition:"background-color 0.2s"},children:[E.jsx("h4",{style:{margin:"0 0 0.25rem 0",fontSize:"0.875rem",fontWeight:"600"},children:p.title}),E.jsxs("p",{style:{margin:"0",fontSize:"0.75rem",color:"#6b7280"},children:[Ed(p.createdAt)," • ",Math.floor(p.duration/60),"m ",p.duration%60,"s"]})]},p.id))]})]}),E.jsx("main",{style:{flex:1,display:"flex"},children:n?E.jsxs(E.Fragment,{children:[E.jsx(pv,{transcript:n,onGenerateSummary:d}),E.jsx(hv,{transcript:n})]}):E.jsx("div",{style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",color:"#6b7280"},children:E.jsx("p",{children:"Select a transcript to view details"})})})]})]}):E.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px"},children:[E.jsx("h1",{children:"Transcript and Summary"}),E.jsx("p",{children:"Please log in to access the application."}),E.jsx(Ie,{onClick:()=>a({email:"<EMAIL>",password:"password"}),variant:"primary",children:"Demo Login"})]})}Jo.createRoot(document.getElementById("root")).render(E.jsx($d.StrictMode,{children:E.jsx(mv,{})}));
//# sourceMappingURL=index-Dx--D9UN.js.map
