{"version": 3, "file": "NativeInteractionClient.mjs", "sources": ["../../src/interaction_client/NativeInteractionClient.ts"], "sourcesContent": [null], "names": ["NativeAuthErrorCodes.userSwitch", "BrowserAuthErrorCodes.nativePromptNotSupported", "BrowserAuthErrorCodes.invalidPopTokenRequest"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AA2EG,MAAO,uBAAwB,SAAQ,qBAAqB,CAAA;IAS9D,WACI,CAAA,MAA4B,EAC5B,cAAmC,EACnC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,KAAY,EACZ,iBAAqC,EACrC,QAA8B,EAC9B,SAAiB,EACjB,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,cAAc,EACd,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,QAAQ,EACR,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACrC,QAAA,IAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAC1C,MAAM,EACN,IAAI,CAAC,oBAAoB,EACzB,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,QAAQ,EACR,aAAa,CAChB,CAAC;QACF,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAC/D,IAAI,CAAC,KAAK,CACb,CAAC;AAEF,QAAA,MAAM,aAAa,GACf,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE;AAC1C,YAAA,eAAe,CAAC,sBAAsB;AAClC,cAAE,QAAQ;cACR,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,EAAE,MAAM;AACpD,kBAAE,SAAS;kBACT,SAAS,CAAC;AACpB,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,kBAAkB,CAAC;YAClD,WAAW,EAAE,gBAAgB,CAAC,QAAQ;AACtC,YAAA,cAAc,EAAE,OAAO;AACvB,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE;AACpE,SAAA,CAAC,CAAC;KACN;AAED;;;;AAIG;AACK,IAAA,cAAc,CAAC,OAA2B,EAAA;QAC9C,OAAO,CAAC,eAAe,GAAG;YACtB,GAAG,OAAO,CAAC,eAAe;AAC1B,YAAA,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI;SACrD,CAAC;KACL;AAED;;;AAGG;IACH,MAAM,YAAY,CACd,OAAwD,EAAA;AAExD,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,mCAAmC,EACrD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;;AAGpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAC/D,iBAAiB,CAAC,mCAAmC,EACrD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAE5C,IAAI;;YAEA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;;YAGlE,IAAI;AACA,gBAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC5C,IAAI,CAAC,SAAS,EACd,aAAa,CAChB,CAAC;gBACF,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,cAAc,EAAE,KAAK;AACrB,oBAAA,SAAS,EAAE,IAAI;AAClB,iBAAA,CAAC,CAAC;AACH,gBAAA,OAAO,MAAM,CAAC;AACjB,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAER,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,4EAA4E,CAC/E,CAAC;AACL,aAAA;AAED,YAAA,MAAM,EAAE,GAAG,kBAAkB,EAAE,GAAG,aAAa,CAAC;;AAGhD,YAAA,MAAM,WAAW,GAA+B;gBAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,gBAAA,OAAO,EAAE,kBAAkB;aAC9B,CAAC;YAEF,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,iBAAiB,GACnB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAE1C,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAClC,iBAAiB,EACjB,aAAa,EACb,YAAY,CACf;AACI,iBAAA,IAAI,CAAC,CAAC,MAA4B,KAAI;gBACnC,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,cAAc,EAAE,IAAI;oBACpB,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iBAAA,CAAC,CAAC;AACH,gBAAA,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,EAAE,CAAC;AACzD,gBAAA,OAAO,MAAM,CAAC;AAClB,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,KAAgB,KAAI;gBACxB,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oBAAA,cAAc,EAAE,IAAI;AACvB,iBAAA,CAAC,CAAC;AACH,gBAAA,MAAM,KAAK,CAAC;AAChB,aAAC,CAAC,CAAC;AACV,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,eAAe,EAAE;gBAC9B,IAAI,CAAC,sBAAsB,CAAC,wBAAwB,CAChD,CAAC,CAAC,SAAS,CACd,CAAC;AACL,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;;AAKG;IACK,wBAAwB,CAC5B,OAA2B,EAC3B,aAA0B,EAAA;QAE1B,OAAO;YACH,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;AACpD,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,YAAY,EAAE,KAAK;SACtB,CAAC;KACL;AAED;;;;;AAKG;AACO,IAAA,MAAM,sBAAsB,CAClC,eAAuB,EACvB,OAA2B,EAAA;QAE3B,IAAI,CAAC,eAAe,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACF,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;AACpE,SAAA;;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAClD;YACI,eAAe;AAClB,SAAA,EACD,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE;AACV,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;AACpE,SAAA;;QAGD,IAAI;YACA,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAC/C,OAAO,EACP,OAAO,CACV,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACpD,aAAa,CAChB,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG;AAChB,gBAAA,GAAG,OAAO;gBACV,aAAa,EAAE,MAAM,EAAE,aAA4B;gBACnD,OAAO,EAAE,MAAM,EAAE,OAAO;aAC3B,CAAC;YAEF,OAAO;AACH,gBAAA,GAAG,MAAM;AACT,gBAAA,OAAO,EAAE,WAAW;aACvB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,MAAM,oBAAoB,CACtB,OAAwB,EACxB,eAA2C,EAAA;AAE3C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wDAAwD,CAC3D,CAAC;AAEF,QAAA,MAAM,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QAC3C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC;QAE9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACpD,mBAAmB,CACtB,CAAC;AAEF,QAAA,MAAM,WAAW,GAA+B;YAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,YAAA,OAAO,EAAE,aAAa;SACzB,CAAC;QAEF,IAAI;YACA,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC7D,YAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;AACzC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;YAER,IAAI,CAAC,YAAY,eAAe,EAAE;gBAC9B,IAAI,CAAC,sBAAsB,CAAC,wBAAwB,CAChD,CAAC,CAAC,SAAS,CACd,CAAC;AACF,gBAAA,IAAI,sBAAsB,CAAC,CAAC,CAAC,EAAE;AAC3B,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,cAAc,EACjC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAC7B,IAAI,CACP,CAAC;AAEF,QAAA,MAAM,iBAAiB,GAAsB;YACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;AACjC,YAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,YAAA,SAAS,EAAE,KAAK;SACnB,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB;AAC1D,cAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;cACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AACvC,QAAA,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,WAAW,EACX,iBAAiB,CACpB,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,MAAM,qBAAqB,CACvB,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,yDAAyD,CAC5D,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,uFAAuF,CAC1F,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACnE,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wGAAwG,CAC3G,CAAC;YACF,IAAI,iBAAiB,IAAI,aAAa,EAAE;gBACpC,iBAAiB,EAAE,SAAS,CACxB,EAAE,SAAS,EAAE,mBAAmB,EAAE,EAClC,aAAa,CAChB,CAAC;AACL,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,aAAa,CAAC;AAC7C,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sMAAsM,CACzM,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAChC,kBAAkB,CAAC,cAAc,CACpC,CACJ,CAAC;AAEF,QAAA,MAAM,WAAW,GAA+B;YAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,YAAA,OAAO,EAAE,OAAO;SACnB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAE5C,IAAI;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mFAAmF,CACtF,CAAC;YACF,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC7D,YAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;AACtC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CACpC,QAA0B,EAC1B,OAAO,EACP,YAAY,CACf,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,YAAA,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC;AACzB,YAAA,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,EAAE,CAAC;AACzD,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC9D,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;KACvD;AAED;;;;;AAKG;AACO,IAAA,MAAM,oBAAoB,CAChC,QAAwB,EACxB,OAA2B,EAC3B,YAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wDAAwD,CAC3D,CAAC;;AAGF,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAC9C,QAAQ,CAAC,QAAQ,EACjB,YAAY,CACf,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,2BAA2B,CAC1D,QAAQ,EACR,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,mBAAmB,GACrB,IAAI,CAAC,cAAc,CAAC,wBAAwB,CACxC;YACI,eAAe,EAAE,OAAO,CAAC,SAAS;AACrC,SAAA,EACD,IAAI,CAAC,aAAa,CACrB,EAAE,aAAa,CAAC;QAErB,IACI,qBAAqB,KAAK,mBAAmB;YAC7C,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,EAC3C;;AAEE,YAAA,MAAM,qBAAqB,CAACA,UAA+B,CAAC,CAAC;AAChE,SAAA;;AAGD,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAChD,gBAAgB,EAAE,OAAO,CAAC,SAAS;AACtC,SAAA,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,mBAAmB,CACnC,IAAI,CAAC,cAAc,EACnB,SAAS,EACT,qBAAqB,EACrB,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,aAAa,EACb,QAAQ,CAAC,WAAW,EACpB,SAAS;AACT,QAAA,aAAa,CAAC,GAAG,EACjB,SAAS;QACT,QAAQ,CAAC,OAAO,CAAC,EAAE,EACnB,IAAI,CAAC,MAAM,CACd,CAAC;;QAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAClD,QAAQ,EACR,OAAO,EACP,aAAa,EACb,WAAW,EACX,SAAS,CAAC,kBAAkB,EAC5B,YAAY,CACf,CAAC;;AAGF,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAClB,QAAQ,EACR,OAAO,EACP,qBAAqB,EACrB,aAAa,EACb,QAAQ,CAAC,YAAY,EACrB,MAAM,CAAC,QAAQ,EACf,YAAY,CACf,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;;AAKG;IACO,2BAA2B,CACjC,QAAwB,EACxB,aAA0B,EAAA;;AAG1B,QAAA,MAAM,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAC7D,QAAQ,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,EAC9C,aAAa,CAAC,OAAO,EACrB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,EAClB,aAAa,CAChB,CAAC;AAEF,QAAA,OAAO,qBAAqB,CAAC;KAChC;AAED;;;;;AAKG;IACH,cAAc,CACV,QAAwB,EACxB,OAA2B,EAAA;QAE3B,OAAO,QAAQ,CAAC,KAAK;cACf,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;cACnC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC5C;AAED;;;;AAIG;AACH,IAAA,MAAM,sBAAsB,CACxB,QAAwB,EACxB,OAA2B,EAAA;AAE3B,QAAA,IACI,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;YAC9C,OAAO,CAAC,YAAY,EACtB;AACE;;;AAGG;;YAGH,IAAI,QAAQ,CAAC,GAAG,EAAE;AACd,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,4DAA4D,CAC/D,CAAC;gBACF,OAAO,QAAQ,CAAC,GAAG,CAAC;AACvB,aAAA;;YAGD,MAAM,iBAAiB,GAAsB,IAAI,iBAAiB,CAC9D,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,YAAA,MAAM,aAAa,GAAgC;gBAC/C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC7B,CAAC;AAEF;;;AAGG;AACH,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAChB,gBAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAClE,aAAA;AACD,YAAA,OAAO,iBAAiB,CAAC,YAAY,CACjC,QAAQ,CAAC,YAAY,EACrB,OAAO,CAAC,KAAK,EACb,aAAa,CAChB,CAAC;AACL,SAAA;AAAM,aAAA;YACH,OAAO,QAAQ,CAAC,YAAY,CAAC;AAChC,SAAA;KACJ;AAED;;;;;;;;;AASG;AACO,IAAA,MAAM,4BAA4B,CACxC,QAAwB,EACxB,OAA2B,EAC3B,aAA0B,EAC1B,aAA4B,EAC5B,SAAiB,EACjB,YAAoB,EAAA;;QAGpB,MAAM,IAAI,GAAG,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;;AAG3D,QAAA,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK;cAC/B,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;cACnC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEzC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;AAC5D,QAAA,MAAM,GAAG,GACL,iBAAiB,CAAC,KAAK,CAAC;AACxB,YAAA,aAAa,CAAC,GAAG;AACjB,YAAA,aAAa,CAAC,GAAG;YACjB,SAAS,CAAC,YAAY,CAAC;AAC3B,QAAA,MAAM,GAAG,GACL,iBAAiB,CAAC,UAAU,CAAC;AAC7B,YAAA,aAAa,CAAC,GAAG;YACjB,SAAS,CAAC,YAAY,CAAC;QAE3B,MAAM,WAAW,GAAuB,8BAA8B,CAClE,aAAa,CAAC,cAAc,EAAE,EAC9B,SAAS;AACT,QAAA,aAAa,EACb,QAAQ,CAAC,QAAQ,CACpB,CAAC;AAEF;;;AAGG;QACH,IAAI,WAAW,CAAC,eAAe,KAAK,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE;YACrD,WAAW,CAAC,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;AACrD,SAAA;;QAGD,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACzD,QAAQ,EACR,OAAO,CACV,CAAC;QACF,MAAM,SAAS,GACX,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACxC,oBAAoB,CAAC,GAAG;AAC1B,cAAE,oBAAoB,CAAC,MAAM,CAAC;AAEtC,QAAA,MAAM,MAAM,GAAyB;AACjC,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,MAAM,EAAE,cAAc,CAAC,OAAO,EAAE;AAChC,YAAA,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAC1B,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,WAAW,EAAE,mBAAmB;AAChC,YAAA,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,KAAK;AACxD,YAAA,SAAS,EAAE,IAAI,IAAI,CACf,MAAM,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CACpD;AACD,YAAA,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,KAAK,EAAE,QAAQ,CAAC,KAAK;AACrB,YAAA,gBAAgB,EAAE,IAAI;SACzB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,aAA4B,EAAA;;QAErC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAGlE,QAAA,IAAI,CAAC,cAAc;AACd,aAAA,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC;AACvD,aAAA,KAAK,CAAC,CAAC,CAAC,KAAI;YACT,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAuE,oEAAA,EAAA,CAAC,CAAE,CAAA,CAC7E,CAAC;AACN,SAAC,CAAC,CAAC;KACV;AAED;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CACb,QAAwB,EACxB,OAA2B,EAC3B,qBAA6B,EAC7B,aAA0B,EAC1B,mBAA2B,EAC3B,QAAgB,EAChB,YAAoB,EAAA;AAEpB,QAAA,MAAM,aAAa,GACf,YAAY,CAAC,mBAAmB,CAC5B,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,QAAQ,CAAC,QAAQ,IAAI,EAAE,EACvB,OAAO,CAAC,QAAQ,EAChB,aAAa,CAAC,GAAG,IAAI,EAAE,CAC1B,CAAC;;QAGN,MAAM,SAAS,GACX,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACxC,SAAS,CAAC,kBAAkB;AAC9B,cAAE,CAAC,OAAO,QAAQ,CAAC,UAAU,KAAK,QAAQ;kBAClC,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC;AACnC,kBAAE,QAAQ,CAAC,UAAU,KAAK,CAAC,CAAC;AAC1C,QAAA,MAAM,sBAAsB,GAAG,YAAY,GAAG,SAAS,CAAC;QACxD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE9D,MAAM,iBAAiB,GACnB,YAAY,CAAC,uBAAuB,CAChC,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,mBAAmB,EACnB,OAAO,CAAC,QAAQ,EAChB,aAAa,CAAC,GAAG,IAAI,QAAQ,EAC7B,cAAc,CAAC,WAAW,EAAE,EAC5B,sBAAsB,EACtB,CAAC,EACD,YAAY,EACZ,SAAS,EACT,OAAO,CAAC,SAAiC,EACzC,SAAS,EACT,OAAO,CAAC,KAAK,CAChB,CAAC;AAEN,QAAA,MAAM,iBAAiB,GAAG;AACtB,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,WAAW,EAAE,iBAAiB;SACjC,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAC1C,iBAAiB,EACjB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,YAAY,CACvB,CAAC;KACL;AAES,IAAA,8BAA8B,CACpC,QAAwB,EAAA;QAExB,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,EAAE;AACP,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B;AACI,YAAA,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE;AACvD,YAAA,gBAAgB,EACZ,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE;YACnD,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB;YAClD,oBAAoB,EAAE,IAAI,CAAC,mBAAmB;YAC9C,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,eAAe;YACxC,gBAAgB,EAAE,IAAI,CAAC,cAAc;YACrC,aAAa,EAAE,IAAI,CAAC,UAAU;YAC9B,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;YAC5C,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,gBAAgB,EAAE,IAAI,CAAC,aAAa;YACpC,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB;AAC5C,SAAA,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;AACK,IAAA,sBAAsB,CAAC,QAAgB,EAAA;AAC3C,QAAA,IACI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC;AACvC,YAAA,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC;AACnC,YAAA,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACtC,YAAA,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAClC,YAAA,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC;AAChC,YAAA,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,EACvC;AACE,YAAA,OAAO,QAA0B,CAAC;AACrC,SAAA;AAAM,aAAA;YACH,MAAM,eAAe,CACjB,cAAc,CAAC,eAAe,EAC9B,uCAAuC,CAC1C,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,QAAwB,EAAA;AAChD,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE;YAC1B,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC/C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,gFAAgF,CACnF,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACO,IAAA,mBAAmB,CAAC,IAAU,EAAA;AACpC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gIAAgI,CACnI,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KAC3B;AAED;;;AAGG;IACO,MAAM,uBAAuB,CACnC,OAAwC,EAAA;AAExC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,0DAA0D,CAC7D,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAClB,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QAEpD,IAAI,OAAO,CAAC,OAAO,EAAE;;YAEjB,MAAM,IAAI,CAAC,sBAAsB,CAAC;gBAC9B,gBAAgB;gBAChB,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;gBACnD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,MAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3D,kBAAkB,CAAC,aAAa,EAAE,CAAC;;QAGnC,MAAM,EAAE,MAAM,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAA,QAAQ,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAE3C,MAAM,SAAS,GAAG,MAAK;;YAEnB,QAAQ,IAAI,CAAC,KAAK;gBACd,KAAK,KAAK,CAAC,SAAS,CAAC;gBACrB,KAAK,KAAK,CAAC,6BAA6B;AACpC,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,6DAA6D,CAChE,CAAC;oBACF,OAAO,WAAW,CAAC,IAAI,CAAC;AAG/B,aAAA;;AAGD,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,kDAAkD,CACrD,CAAC;AACF,gBAAA,OAAO,SAAS,CAAC;AACpB,aAAA;;YAGD,QAAQ,OAAO,CAAC,MAAM;gBAClB,KAAK,WAAW,CAAC,IAAI,CAAC;gBACtB,KAAK,WAAW,CAAC,OAAO,CAAC;gBACzB,KAAK,WAAW,CAAC,KAAK;AAClB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,gEAAgE,CACnE,CAAC;oBACF,OAAO,OAAO,CAAC,MAAM,CAAC;AAC1B,gBAAA;oBACI,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAqC,kCAAA,EAAA,OAAO,CAAC,MAAM,CAAqC,mCAAA,CAAA,CAC3F,CAAC;AACF,oBAAA,MAAM,sBAAsB,CACxBC,wBAA8C,CACjD,CAAC;AACT,aAAA;AACL,SAAC,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAuB;AACzC,YAAA,GAAG,mBAAmB;YACtB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACnC,SAAS,EAAE,kBAAkB,CAAC,SAAS;AACvC,YAAA,KAAK,EAAE,QAAQ,CAAC,WAAW,EAAE;YAC7B,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC;YACrD,MAAM,EAAE,SAAS,EAAE;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,OAAO,CAAC,oBAAoB;YACvC,oBAAoB,EAAE,QAAQ,CAAC,KAAK;AACpC,YAAA,eAAe,EAAE;gBACb,GAAG,OAAO,CAAC,oBAAoB;gBAC/B,GAAG,OAAO,CAAC,oBAAoB;AAClC,aAAA;AACD,YAAA,mBAAmB,EAAE,KAAK;YAC1B,KAAK,EAAE,OAAO,CAAC,MAAM;SACxB,CAAC;;QAGF,IAAI,gBAAgB,CAAC,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;AACnD,YAAA,MAAM,sBAAsB,CACxBC,sBAA4C,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;AAC/C,QAAA,gBAAgB,CAAC,eAAe;AAC5B,YAAA,gBAAgB,CAAC,eAAe,IAAI,EAAE,CAAC;QAC3C,gBAAgB,CAAC,eAAe,CAAC,SAAS;YACtC,eAAe,CAAC,cAAc,CAAC;AAEnC,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;;AAE3D,YAAA,MAAM,aAAa,GAAgC;gBAC/C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC7B,CAAC;YAEF,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;AAGpE,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AACzB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AAC9C,gBAAA,gBAAgB,CAAC,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC;AACjD,gBAAA,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;AACxC,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAC3C,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAClD,CAAC;AACF,gBAAA,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;AACzC,aAAA;;AAGD,YAAA,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;AACxC,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;AAEtC,QAAA,OAAO,gBAAgB,CAAC;KAC3B;AAED;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,OAA2B,EAAA;AACvD,QAAA,MAAM,oBAAoB,GACtB,OAAO,CAAC,eAAe;YACvB,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,gBAAgB,CACtC;YACD,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,mBAAmB,CACzC;YACD,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,SAAS,CAC/B,CAAC;AAEN,QAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,oBAAoB,EAAE;YACpD,OAAO;AACV,SAAA;QAED,IAAI,eAAe,GAAW,EAAE,CAAC;AACjC,QAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,WAAW,CAAC;QAE/C,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AACnD,YAAA,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAC9C,SAAA;aAAM,IAAI,OAAO,CAAC,eAAe,EAAE;AAChC,YAAA,OAAO,CAAC,WAAW;AACf,gBAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;YACpE,eAAe;AACX,gBAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC7D,SAAA;QAED,OAAO,CAAC,eAAe,GAAG;YACtB,eAAe;YACf,kBAAkB;SACrB,CAAC;AAEF,QAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;AACI,YAAA,gBAAgB,EAAE,eAAe;AACjC,YAAA,mBAAmB,EAAE,kBAAkB;AAC1C,SAAA,EACD,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;AACJ;;;;"}