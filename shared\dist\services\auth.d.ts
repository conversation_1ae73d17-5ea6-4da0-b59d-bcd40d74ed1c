export interface User {
    id: string;
    email: string;
    name: string;
    roles: string[];
}
export interface LoginCredentials {
    email: string;
    password: string;
}
export interface AuthState {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
}
export interface AuthService {
    login: (credentials: LoginCredentials) => Promise<boolean>;
    logout: () => Promise<void>;
    refreshToken: () => Promise<boolean>;
    getCurrentUser: () => Promise<User | null>;
}
declare class AuthServiceImpl implements AuthService {
    login(credentials: LoginCredentials): Promise<boolean>;
    logout(): Promise<void>;
    refreshToken(): Promise<boolean>;
    getCurrentUser(): Promise<User | null>;
}
export declare const authService: AuthServiceImpl;
/**
 * Custom hook for authentication
 */
export declare const useAuth: () => AuthState & AuthService;
export {};
//# sourceMappingURL=auth.d.ts.map