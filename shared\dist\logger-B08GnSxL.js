"use strict";var g=Object.defineProperty;var c=(s,e,t)=>e in s?g(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var i=(s,e,t)=>c(s,typeof e!="symbol"?e+"":e,t);var n=(s=>(s[s.DEBUG=0]="DEBUG",s[s.INFO=1]="INFO",s[s.WARN=2]="WARN",s[s.ERROR=3]="ERROR",s))(n||{});class l{constructor(e={}){i(this,"config");i(this,"entries",[]);this.config={level:1,enableConsole:!0,enableStorage:!1,maxStorageEntries:1e3,...e},this.config.enableStorage&&this.loadFromStorage()}shouldLog(e){return e>=this.config.level}createEntry(e,t,r){return{level:e,message:t,timestamp:new Date,data:r,source:this.config.source}}logToConsole(e){if(!this.config.enableConsole)return;const t=e.timestamp.toISOString(),r=e.source?`[${e.source}]`:"",o=`${t} ${r}`;switch(e.level){case 0:console.debug(o,e.message,e.data);break;case 1:console.info(o,e.message,e.data);break;case 2:console.warn(o,e.message,e.data);break;case 3:console.error(o,e.message,e.data);break}}storeEntry(e){if(this.config.enableStorage){this.entries.push(e),this.entries.length>this.config.maxStorageEntries&&(this.entries=this.entries.slice(-this.config.maxStorageEntries));try{localStorage.setItem("logger_entries",JSON.stringify(this.entries))}catch(t){console.warn("Failed to save log entries to localStorage:",t)}}}loadFromStorage(){try{const e=localStorage.getItem("logger_entries");e&&(this.entries=JSON.parse(e).map(t=>({...t,timestamp:new Date(t.timestamp)})))}catch(e){console.warn("Failed to load log entries from localStorage:",e),this.entries=[]}}log(e,t,r){if(!this.shouldLog(e))return;const o=this.createEntry(e,t,r);this.logToConsole(o),this.storeEntry(o)}debug(e,t){this.log(0,e,t)}info(e,t){this.log(1,e,t)}warn(e,t){this.log(2,e,t)}error(e,t){this.log(3,e,t)}getEntries(){return[...this.entries]}clear(){this.entries=[],this.config.enableStorage&&localStorage.removeItem("logger_entries")}export(){return JSON.stringify(this.entries,null,2)}configure(e){this.config={...this.config,...e}}}const a=()=>{try{return!1}catch{return!1}},h=new l({level:a()?0:1,enableConsole:!0,enableStorage:a(),source:"CRM-App"});exports.LogLevel=n;exports.Logger=l;exports.logger=h;
