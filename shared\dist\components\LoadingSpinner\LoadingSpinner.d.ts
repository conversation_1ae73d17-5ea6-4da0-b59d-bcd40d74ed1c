import { default as React } from 'react';

export interface LoadingSpinnerProps {
    /**
     * Size of the spinner
     */
    size?: 'small' | 'medium' | 'large';
    /**
     * Color of the spinner
     */
    color?: 'primary' | 'secondary' | 'white';
    /**
     * Additional CSS classes
     */
    className?: string;
    /**
     * Loading text to display
     */
    text?: string;
}
/**
 * Loading spinner component
 */
export declare const LoadingSpinner: React.FC<LoadingSpinnerProps>;
//# sourceMappingURL=LoadingSpinner.d.ts.map