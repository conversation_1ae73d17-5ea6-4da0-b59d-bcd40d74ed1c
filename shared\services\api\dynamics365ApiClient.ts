/**
 * Dynamics 365 API Client
 * 
 * Handles API operations within Dynamics 365 web resource context using Xrm.WebApi
 */

import {
  IApiClient,
  ApiResponse,
  PaginatedResponse,
  ApiRequestConfig,
  QueryOptions,
  BatchRequest,
  BatchResponse,
  ApiError,
  AuthenticationError
} from './apiTypes';
import { logger } from '../../utils/logger';

export class Dynamics365ApiClient implements IApiClient {
  private xrmWebApi: any = null;

  async initialize(): Promise<void> {
    try {
      // Wait for Dynamics 365 context to be available
      await this.waitForXrmContext();
      
      this.xrmWebApi = (window as any).Xrm.WebApi;
      
      if (!this.xrmWebApi) {
        throw new Error('Xrm.WebApi is not available');
      }
      
      logger.info('Dynamics 365 API client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Dynamics 365 API client:', error);
      throw error;
    }
  }

  async get<T>(url: string, config?: ApiRequestConfig): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      // For Dynamics 365, we'll use the WebApi methods instead of raw HTTP
      // This is a simplified implementation - in practice, you'd parse the URL
      // to determine the appropriate WebApi method
      const response = await this.xrmWebApi.retrieveRecord('systemuser', url);
      
      return {
        data: response,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async post<T>(url: string, data?: any, config?: ApiRequestConfig): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      // Parse URL to extract entity name for createRecord
      const entityName = this.extractEntityNameFromUrl(url);
      const response = await this.xrmWebApi.createRecord(entityName, data);
      
      return {
        data: response,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async put<T>(url: string, data?: any, config?: ApiRequestConfig): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      const { entityName, id } = this.parseEntityUrl(url);
      await this.xrmWebApi.updateRecord(entityName, id, data);
      
      return {
        data: null as T,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async patch<T>(url: string, data?: any, config?: ApiRequestConfig): Promise<ApiResponse<T>> {
    // PATCH is the same as PUT in Dynamics 365
    return this.put<T>(url, data, config);
  }

  async delete<T>(url: string, config?: ApiRequestConfig): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      const { entityName, id } = this.parseEntityUrl(url);
      await this.xrmWebApi.deleteRecord(entityName, id);
      
      return {
        data: null as T,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async retrieveRecord<T>(entityName: string, id: string, options?: QueryOptions): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      const queryString = this.buildQueryString(options);
      const response = await this.xrmWebApi.retrieveRecord(entityName, id, queryString);
      
      return {
        data: response,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async retrieveMultipleRecords<T>(entityName: string, options?: QueryOptions): Promise<PaginatedResponse<T>> {
    try {
      this.ensureInitialized();
      
      const queryString = this.buildQueryString(options);
      const response = await this.xrmWebApi.retrieveMultipleRecords(entityName, queryString);
      
      return {
        data: response.entities,
        success: true,
        pagination: {
          page: 1, // Dynamics 365 doesn't provide page numbers directly
          pageSize: response.entities.length,
          totalCount: response.entities.length, // This would need additional logic for accurate count
          hasNext: !!response['@odata.nextLink'],
          hasPrevious: false
        }
      };
    } catch (error) {
      return this.handleErrorPaginated<T>(error);
    }
  }

  async createRecord<T>(entityName: string, data: any): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      const response = await this.xrmWebApi.createRecord(entityName, data);
      
      return {
        data: response,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async updateRecord<T>(entityName: string, id: string, data: any): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      await this.xrmWebApi.updateRecord(entityName, id, data);
      
      return {
        data: null as T,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async deleteRecord(entityName: string, id: string): Promise<ApiResponse<void>> {
    try {
      this.ensureInitialized();
      
      await this.xrmWebApi.deleteRecord(entityName, id);
      
      return {
        data: undefined,
        success: true
      };
    } catch (error) {
      return this.handleError<void>(error);
    }
  }

  async executeFunction<T>(functionName: string, parameters?: any): Promise<ApiResponse<T>> {
    try {
      this.ensureInitialized();
      
      // Build function URL with parameters
      let functionUrl = functionName;
      if (parameters) {
        const paramString = Object.keys(parameters)
          .map(key => `${key}=${encodeURIComponent(parameters[key])}`)
          .join(',');
        functionUrl += `(${paramString})`;
      }
      
      const response = await this.xrmWebApi.online.executeFunction(functionUrl);
      
      return {
        data: response,
        success: true
      };
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  async executeBatch(requests: BatchRequest[]): Promise<BatchResponse> {
    try {
      this.ensureInitialized();
      
      // Dynamics 365 batch operations would need to be implemented
      // This is a simplified version
      const responses: ApiResponse[] = [];
      
      for (const request of requests) {
        try {
          let response: ApiResponse;
          
          switch (request.method) {
            case 'GET':
              response = await this.get(request.url);
              break;
            case 'POST':
              response = await this.post(request.url, request.data);
              break;
            case 'PUT':
              response = await this.put(request.url, request.data);
              break;
            case 'PATCH':
              response = await this.patch(request.url, request.data);
              break;
            case 'DELETE':
              response = await this.delete(request.url);
              break;
            default:
              throw new Error(`Unsupported method: ${request.method}`);
          }
          
          responses.push(response);
        } catch (error) {
          responses.push(this.handleError(error));
        }
      }
      
      return {
        responses,
        success: responses.every(r => r.success)
      };
    } catch (error) {
      logger.error('Batch execution failed:', error);
      return {
        responses: [],
        success: false,
        errors: [error instanceof Error ? error.message : 'Batch execution failed']
      };
    }
  }

  private async waitForXrmContext(timeout: number = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkContext = () => {
        if ((window as any).Xrm && (window as any).Xrm.WebApi) {
          resolve();
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error('Timeout waiting for Dynamics 365 context'));
          return;
        }
        
        setTimeout(checkContext, 100);
      };
      
      checkContext();
    });
  }

  private ensureInitialized(): void {
    if (!this.xrmWebApi) {
      throw new AuthenticationError('Dynamics 365 API client not initialized');
    }
  }

  private buildQueryString(options?: QueryOptions): string {
    if (!options) return '';
    
    const params: string[] = [];
    
    if (options.select) {
      params.push(`$select=${options.select.join(',')}`);
    }
    
    if (options.filter) {
      params.push(`$filter=${encodeURIComponent(options.filter)}`);
    }
    
    if (options.orderBy) {
      params.push(`$orderby=${encodeURIComponent(options.orderBy)}`);
    }
    
    if (options.expand) {
      params.push(`$expand=${options.expand.join(',')}`);
    }
    
    if (options.top) {
      params.push(`$top=${options.top}`);
    }
    
    if (options.skip) {
      params.push(`$skip=${options.skip}`);
    }
    
    return params.length > 0 ? `?${params.join('&')}` : '';
  }

  private extractEntityNameFromUrl(url: string): string {
    // Simple URL parsing - in practice, this would be more sophisticated
    const match = url.match(/\/([^\/\?]+)/);
    return match ? match[1] : 'systemuser';
  }

  private parseEntityUrl(url: string): { entityName: string; id: string } {
    // Simple URL parsing - in practice, this would be more sophisticated
    const match = url.match(/\/([^\/]+)\/([^\/\?]+)/);
    return {
      entityName: match ? match[1] : 'systemuser',
      id: match ? match[2] : ''
    };
  }

  private handleError<T>(error: any): ApiResponse<T> {
    logger.error('Dynamics 365 API error:', error);
    
    let message = 'An error occurred';
    let statusCode = 500;
    
    if (error && error.message) {
      message = error.message;
    }
    
    if (error && error.status) {
      statusCode = error.status;
    }
    
    return {
      data: null as T,
      success: false,
      message,
      statusCode,
      errors: [message]
    };
  }

  private handleErrorPaginated<T>(error: any): PaginatedResponse<T> {
    const baseError = this.handleError<T[]>(error);
    
    return {
      ...baseError,
      data: [],
      pagination: {
        page: 1,
        pageSize: 0,
        totalCount: 0,
        hasNext: false,
        hasPrevious: false
      }
    };
  }
}
