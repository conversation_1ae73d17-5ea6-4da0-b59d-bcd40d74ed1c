{"version": 3, "file": "BrowserCacheManager.d.ts", "sourceRoot": "", "sources": ["../../../src/cache/BrowserCacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAIH,8BAA8B,EAC9B,OAAO,EACP,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,qBAAqB,EACrB,gBAAgB,EAEhB,MAAM,EACN,uBAAuB,EAEvB,WAAW,EAKX,mBAAmB,EACnB,SAAS,EACT,cAAc,EACd,WAAW,EAKX,kBAAkB,EAClB,sBAAsB,EAEtB,YAAY,EAIf,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAK1D,OAAO,EACH,oBAAoB,EACpB,eAAe,EAIlB,MAAM,8BAA8B,CAAC;AAGtC,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAC;AAC7E,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAG1D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAGnD;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,YAAY;IAEjD,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE9C,SAAS,CAAC,cAAc,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,eAAe,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,qBAAqB,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAExD,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;IAEvC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IAEzB,SAAS,CAAC,iBAAiB,CAAC,EAAE,kBAAkB,CAAC;gBAG7C,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,EACnC,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,EACd,sBAAsB,CAAC,EAAE,sBAAsB,EAC/C,iBAAiB,CAAC,EAAE,kBAAkB;IAuB1C;;;OAGG;IACH,SAAS,CAAC,mBAAmB,CACzB,aAAa,EAAE,oBAAoB,GAAG,MAAM,GAC7C,cAAc,CAAC,MAAM,CAAC;IAkBzB;;;OAGG;IACH,SAAS,CAAC,mBAAmB,IAAI,IAAI;IA6CrC;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAoJrB;;;OAGG;IACH,SAAS,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAiBhE;;;OAGG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAInC;;;;OAIG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAoDhE;;;OAGG;IACH,UAAU,CACN,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,EACrB,MAAM,CAAC,EAAE,MAAM,GAChB,aAAa,GAAG,IAAI;IAevB;;;;;OAKG;IACH,sBAAsB,CAClB,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI;IAkBvB;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAQ/D;;;OAGG;IACH,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC;IAa/B;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAwB5D;;;OAGG;IACH,uBAAuB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IA8BjE;;;OAGG;IACG,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKtE;;;OAGG;IACH,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAKtE;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAKvD;;;OAGG;IACH,iBAAiB,CACb,GAAG,EAAE,MAAM,EACX,aAAa,EAAE,MAAM,EACrB,eAAe,GAAE,OAAc,GAChC,IAAI;IAcP,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAqBvE;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAK5D;;;OAGG;IACH,YAAY,IAAI,SAAS;IAgCzB;;;;;OAKG;IACH,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAkB/D;;;;OAIG;IACH,WAAW,CACP,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,cAAc,EACpB,aAAa,EAAE,MAAM,GACtB,IAAI;IA6CP;;;;OAIG;IACH,cAAc,CACV,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,cAAc,EACpB,aAAa,EAAE,MAAM,EACrB,SAAS,GAAE,SAA+B,GAC3C,IAAI;IAgEP;;;OAGG;IACH,oBAAoB,CAChB,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI;IAwBvB;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAUzE;;;OAGG;IACH,wBAAwB,CACpB,cAAc,EAAE,MAAM,EACtB,aAAa,EAAE,MAAM,GACtB,iBAAiB,GAAG,IAAI;IA8B3B;;;OAGG;IACH,wBAAwB,CACpB,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,IAAI;IAoBP;;;OAGG;IACH,yBAAyB,CACrB,eAAe,EAAE,MAAM,EACvB,aAAa,EAAE,MAAM,GACtB,kBAAkB,GAAG,IAAI;IA8B5B;;;OAGG;IACH,yBAAyB,CACrB,YAAY,EAAE,kBAAkB,EAChC,aAAa,EAAE,MAAM,GACtB,IAAI;IAqBP;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAwBhE;;;OAGG;IACH,cAAc,CACV,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,IAAI;IAUP;;;OAGG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI;IA0B/B;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,EACtC,aAAa,EAAE,MAAM,GACtB,IAAI;IASP;;OAEG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAqBjE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAOzC;;;;OAIG;IACH,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,IAAI;IAQpE;;OAEG;IACH,kBAAkB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;IAUtC;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,uBAAuB,GAAG,IAAI;IAKxE;;OAEG;IACH,gBAAgB,CAAC,aAAa,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IA4D3D;;;OAGG;IACH,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAkC1E;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IA2BvE;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,EACjC,aAAa,EAAE,MAAM,GACtB,IAAI;IASP;;;;OAIG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,IAAI;IAsCzE;;;;;;OAMG;IACH,iBAAiB,CACb,QAAQ,EAAE,MAAM,EAChB,KAAK,EAAE,MAAM,EACb,WAAW,CAAC,EAAE,OAAO,GACtB,IAAI;IAiBP;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAI7B;;;;OAIG;IACH,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAUtC;;OAEG;IACH,OAAO,IAAI,MAAM,EAAE;IAInB;;OAEG;IACG,KAAK,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA4BjD;;;;;OAKG;IACG,4BAA4B,CAC9B,iBAAiB,EAAE,kBAAkB,EACrC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAgChB;;;;OAIG;IACH,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAerC;;;OAGG;IACH,oBAAoB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAUjD;;;OAGG;IACH,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAU7C;;;OAGG;IACH,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAU7C;;OAEG;IACH,kBAAkB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAWtD;;;;OAIG;IACH,kBAAkB,CACd,KAAK,EAAE,MAAM,EACb,KAAK,EAAE,MAAM,EACb,iBAAiB,EAAE,MAAM,EACzB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,WAAW,GAAG,IAAI,GAC5B,IAAI;IAqCP;;;OAGG;IACH,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAoCtC;;;OAGG;IACH,mBAAmB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAa9C;;;;OAIG;IACH,6BAA6B,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI;IAkCrE,gBAAgB,CAAC,eAAe,EAAE,8BAA8B,GAAG,IAAI;IAWvE;;OAEG;IACH,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,8BAA8B;IA4C/D;;OAEG;IACH,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;IA0BnD,uBAAuB,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO;IAUzD,wBAAwB,IAAI,MAAM,GAAG,IAAI;IAKzC,wBAAwB,CAAC,UAAU,EAAE,OAAO,GAAG,IAAI;IAoBnD;;;OAGG;IACH,kBAAkB,IAAI,MAAM,GAAG,IAAI;IAgDnC;;OAEG;IACH,wBAAwB,CACpB,eAAe,EAAE,MAAM,EACvB,UAAU,EAAE,mBAAmB,EAC/B,aAAa,EAAE,MAAM,GACtB,MAAM;IAsBT;;;;OAIG;IACG,YAAY,CACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,EACD,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,YAAY,GACnB,OAAO,CAAC,IAAI,CAAC;IA+ChB;;;;;OAKG;IACG,eAAe,CACjB,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,YAAY,GAC5B,OAAO,CAAC,IAAI,CAAC;CA8BnB;AAED,eAAO,MAAM,6BAA6B,aAC5B,MAAM,UACR,MAAM,KACf,mBAeF,CAAC"}