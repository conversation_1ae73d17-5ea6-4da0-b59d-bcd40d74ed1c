/**
 * Basic App Tests
 * 
 * Simple tests for the main app component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@shared/services/theme';
import { ThemeMode } from '@shared/config/deploymentContext';

// Mock the App component since it has complex dependencies
const MockApp: React.FC = () => {
  return (
    <div data-testid="app-container" className="transcript-app">
      <header className="transcript-header">
        <h1 className="transcript-header__title">Transcript and Summary</h1>
      </header>
      <main className="transcript-content" data-testid="app-content">
        <p>App content goes here</p>
      </main>
    </div>
  );
};

describe('Basic App Tests', () => {
  test('renders app with CRM theme', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.CRM}>
        <MockApp />
      </ThemeProvider>
    );

    const appContainer = screen.getByTestId('app-container');
    const title = screen.getByText('Transcript and Summary');
    const content = screen.getByTestId('app-content');

    expect(appContainer).toBeTruthy();
    expect(title).toBeTruthy();
    expect(content).toBeTruthy();
  });

  test('renders app with MFE theme', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.MFE}>
        <MockApp />
      </ThemeProvider>
    );

    const appContainer = screen.getByTestId('app-container');
    const title = screen.getByText('Transcript and Summary');
    const content = screen.getByTestId('app-content');

    expect(appContainer).toBeTruthy();
    expect(title).toBeTruthy();
    expect(content).toBeTruthy();
  });

  test('app has correct CSS classes', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.CRM}>
        <MockApp />
      </ThemeProvider>
    );

    const appContainer = screen.getByTestId('app-container');
    const header = screen.getByRole('banner');
    const title = screen.getByRole('heading', { level: 1 });
    const content = screen.getByTestId('app-content');

    expect(appContainer.className).toContain('transcript-app');
    expect(header.className).toContain('transcript-header');
    expect(title.className).toContain('transcript-header__title');
    expect(content.className).toContain('transcript-content');
  });
});
