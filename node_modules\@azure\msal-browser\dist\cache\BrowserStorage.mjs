/*! @azure/msal-browser v3.30.0 2025-08-05 */
'use strict';
import { createBrowserConfigurationAuthError } from '../error/BrowserConfigurationAuthError.mjs';
import { BrowserCacheLocation } from '../utils/BrowserConstants.mjs';
import { LocalStorage } from './LocalStorage.mjs';
import { SessionStorage } from './SessionStorage.mjs';
import { storageNotSupported } from '../error/BrowserConfigurationAuthErrorCodes.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * @deprecated This class will be removed in a future major version
 */
class BrowserStorage {
    constructor(cacheLocation) {
        if (cacheLocation === BrowserCacheLocation.LocalStorage) {
            this.windowStorage = new LocalStorage();
        }
        else if (cacheLocation === BrowserCacheLocation.SessionStorage) {
            this.windowStorage = new SessionStorage();
        }
        else {
            throw createBrowserConfigurationAuthError(storageNotSupported);
        }
    }
    getItem(key) {
        return this.windowStorage.getItem(key);
    }
    setItem(key, value) {
        this.windowStorage.setItem(key, value);
    }
    removeItem(key) {
        this.windowStorage.removeItem(key);
    }
    getKeys() {
        return Object.keys(this.windowStorage);
    }
    containsKey(key) {
        return this.windowStorage.hasOwnProperty(key);
    }
}

export { BrowserStorage };
//# sourceMappingURL=BrowserStorage.mjs.map
