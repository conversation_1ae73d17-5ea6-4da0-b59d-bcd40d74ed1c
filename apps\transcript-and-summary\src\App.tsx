import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, LoadingSpinner } from '@shared/components';
import { useAuth } from '@shared/services';
import { logger, formatDate } from '@shared/utils';
import TranscriptViewer from './components/TranscriptViewer';
import SummaryPanel from './components/SummaryPanel';

interface Transcript {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  duration: number;
  summary?: string;
}

function App() {
  const [transcripts, setTranscripts] = useState<Transcript[]>([]);
  const [selectedTranscript, setSelectedTranscript] = useState<Transcript | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user, isAuthenticated, login } = useAuth();

  useEffect(() => {
    logger.info('Transcript and Summary app initialized');
    loadTranscripts();
  }, []);

  const loadTranscripts = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTranscripts: Transcript[] = [
        {
          id: '1',
          title: 'Customer Support Call - John Doe',
          content: 'Customer called regarding billing issue. Resolved by updating payment method.',
          createdAt: new Date().toISOString(),
          duration: 180,
          summary: 'Billing issue resolved by updating payment method.',
        },
        {
          id: '2',
          title: 'Sales Call - Jane Smith',
          content: 'Discussed product features and pricing. Customer interested in enterprise plan.',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          duration: 300,
          summary: 'Sales opportunity for enterprise plan.',
        },
      ];
      
      setTranscripts(mockTranscripts);
      logger.info('Transcripts loaded successfully', { count: mockTranscripts.length });
    } catch (error) {
      logger.error('Failed to load transcripts', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTranscriptSelect = (transcript: Transcript) => {
    setSelectedTranscript(transcript);
    logger.info('Transcript selected', { id: transcript.id, title: transcript.title });
  };

  const handleGenerateSummary = async (transcriptId: string) => {
    logger.info('Generating summary for transcript', { transcriptId });
    
    // Simulate AI summary generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const updatedTranscripts = transcripts.map(t => 
      t.id === transcriptId 
        ? { ...t, summary: 'AI-generated summary: Key points and action items from the conversation.' }
        : t
    );
    
    setTranscripts(updatedTranscripts);
    
    if (selectedTranscript?.id === transcriptId) {
      setSelectedTranscript(updatedTranscripts.find(t => t.id === transcriptId) || null);
    }
    
    logger.info('Summary generated successfully', { transcriptId });
  };

  if (!isAuthenticated) {
    return (
      <div className="transcript-app" style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: 'var(--transcript-content-padding)'
      }}>
        <h1 className="transcript-header__title">Transcript and Summary</h1>
        <p style={{ color: 'var(--theme-text-secondary)', marginBottom: 'var(--theme-spacing-lg)' }}>
          Please log in to access the application.
        </p>
        <Button
          onClick={() => login({ email: '<EMAIL>', password: 'password' })}
          variant="primary"
          className="transcript-button transcript-button--primary"
        >
          Demo Login
        </Button>
      </div>
    );
  }

  return (
    <div className="transcript-app" data-testid="app-container">
      {/* Header */}
      <header className="transcript-header">
        <h1 className="transcript-header__title">Transcript and Summary</h1>
        <div className="transcript-header__actions">
          <span style={{ color: 'var(--theme-text-secondary)' }}>
            Welcome, {user?.name || 'User'}
          </span>
          <Button
            variant="secondary"
            size="small"
            className="transcript-button transcript-button--secondary"
          >
            Logout
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="transcript-main">
        {/* Sidebar - Transcript List */}
        <aside style={{
          width: 'var(--transcript-sidebar-width)',
          background: 'var(--theme-bg-secondary)',
          borderRight: '1px solid var(--theme-border-primary)',
          padding: 'var(--transcript-content-padding)'
        }}>
          <div style={{ marginBottom: 'var(--theme-spacing-md)' }}>
            <Button
              onClick={loadTranscripts}
              size="small"
              className="transcript-button transcript-button--secondary"
              style={{ width: '100%' }}
            >
              Refresh Transcripts
            </Button>
          </div>
          
          {isLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 'var(--theme-spacing-xl)' }}>
              <LoadingSpinner text="Loading transcripts..." />
            </div>
          ) : (
            <div>
              <h3 style={{
                marginBottom: 'var(--theme-spacing-md)',
                color: 'var(--theme-text-primary)',
                fontSize: 'var(--transcript-body-size)',
                fontWeight: '600'
              }}>
                Recent Transcripts
              </h3>
              {transcripts.map(transcript => (
                <div
                  key={transcript.id}
                  onClick={() => handleTranscriptSelect(transcript)}
                  className="transcript-panel"
                  style={{
                    padding: 'var(--theme-spacing-md)',
                    marginBottom: 'var(--theme-spacing-sm)',
                    backgroundColor: selectedTranscript?.id === transcript.id
                      ? 'var(--theme-bg-hover)'
                      : 'var(--theme-bg-primary)',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <h4 style={{
                    margin: '0 0 var(--theme-spacing-xs) 0',
                    fontSize: 'var(--transcript-body-size)',
                    fontWeight: '600',
                    color: 'var(--theme-text-primary)'
                  }}>
                    {transcript.title}
                  </h4>
                  <p style={{
                    margin: '0',
                    fontSize: 'var(--transcript-caption-size)',
                    color: 'var(--theme-text-secondary)'
                  }}>
                    {formatDate(transcript.createdAt)} • {Math.floor(transcript.duration / 60)}m {transcript.duration % 60}s
                  </p>
                </div>
              ))}
            </div>
          )}
        </aside>

        {/* Main Content Area */}
        <main className="transcript-content" data-testid="app-content">
          {selectedTranscript ? (
            <>
              <TranscriptViewer
                transcript={selectedTranscript}
                onGenerateSummary={handleGenerateSummary}
              />
              <SummaryPanel transcript={selectedTranscript} />
            </>
          ) : (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--theme-text-secondary)',
              fontSize: 'var(--transcript-body-size)'
            }}>
              <p>Select a transcript to view details</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default App;
