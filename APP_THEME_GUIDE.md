# App-Specific Theme Customization Guide

This guide provides comprehensive instructions for adding app-specific theme customizations that work seamlessly with both CRM and MFE base themes while maintaining consistency and respecting the existing ThemeMode enum structure.

## 🎯 Overview

The theme system allows individual apps to add their own customizations while:
- Respecting both CRM and MFE base theme structures
- Maintaining theme consistency across deployment modes
- Supporting automatic theme detection and switching
- Preserving accessibility and responsive design principles

## 🏗️ Theme Architecture Principles

### 1. CSS Custom Properties First
Always use CSS custom properties (CSS variables) for theme-dependent values:

```css
/* ✅ Correct - Uses CSS custom properties */
.my-component {
  background-color: var(--theme-primary);
  border-radius: var(--theme-border-radius);
  padding: var(--theme-spacing-md);
}

/* ❌ Incorrect - Hardcoded values */
.my-component {
  background-color: #0078d4;
  border-radius: 2px;
  padding: 16px;
}
```

### 2. Theme-Aware Selectors
Use `[data-theme="..."]` selectors to provide theme-specific styling:

```css
/* CRM theme styling */
[data-theme="crm"] .my-component {
  --my-component-height: 32px;
  --my-component-border-radius: 2px;
  --my-component-font-size: 14px;
}

/* MFE theme styling */
[data-theme="mfe"] .my-component {
  --my-component-height: 44px;
  --my-component-border-radius: 16px;
  --my-component-font-size: 16px;
}

/* Common styling using the custom properties */
.my-component {
  height: var(--my-component-height);
  border-radius: var(--my-component-border-radius);
  font-size: var(--my-component-font-size);
}
```

## 📁 File Structure for App Themes

### Recommended Structure
```
apps/my-app/
├── src/
│   ├── styles/
│   │   ├── app-theme.css          # Main app theme file
│   │   ├── components/            # Component-specific themes
│   │   │   ├── header.css
│   │   │   ├── sidebar.css
│   │   │   └── dashboard.css
│   │   └── themes/                # Theme-specific overrides
│   │       ├── crm-overrides.css
│   │       └── mfe-overrides.css
│   ├── components/
│   └── main.tsx                   # Import theme files here
```

### Import Order
```tsx
// apps/my-app/src/main.tsx
import '@shared/styles/index.css';        // Base themes (required)
import './styles/app-theme.css';          // App-specific theme
import './styles/themes/crm-overrides.css'; // Optional CRM overrides
import './styles/themes/mfe-overrides.css'; // Optional MFE overrides
```

## 🎨 Creating App-Specific Themes

### Step 1: Create Base App Theme File

```css
/* apps/my-app/src/styles/app-theme.css */

/* ==========================================================================
   App-Specific Theme Variables
   ========================================================================== */

/* CRM Theme Customizations */
[data-theme="crm"] {
  /* App-specific colors that complement CRM theme */
  --app-accent-color: #106ebe;           /* Darker blue for accents */
  --app-success-color: #107c10;          /* Microsoft green */
  --app-warning-color: #ff8c00;          /* Microsoft orange */
  --app-error-color: #d13438;            /* Microsoft red */
  
  /* App-specific spacing */
  --app-header-height: 48px;
  --app-sidebar-width: 280px;
  --app-content-padding: 16px;
  
  /* App-specific typography */
  --app-heading-font-weight: 600;
  --app-body-line-height: 1.4;
  
  /* App-specific shadows */
  --app-card-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --app-modal-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* MFE Theme Customizations */
[data-theme="mfe"] {
  /* App-specific colors that complement MFE theme */
  --app-accent-color: #7b2cbf;           /* Purple variant for accents */
  --app-success-color: #28a745;          /* Bootstrap green */
  --app-warning-color: #ffc107;          /* Bootstrap yellow */
  --app-error-color: #dc3545;            /* Bootstrap red */
  
  /* App-specific spacing */
  --app-header-height: 64px;             /* Larger for touch */
  --app-sidebar-width: 320px;
  --app-content-padding: 24px;
  
  /* App-specific typography */
  --app-heading-font-weight: 700;
  --app-body-line-height: 1.6;
  
  /* App-specific shadows */
  --app-card-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  --app-modal-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   App-Specific Components
   ========================================================================== */

/* App Header */
.app-header {
  height: var(--app-header-height);
  background: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border-primary);
  padding: 0 var(--app-content-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-header__title {
  font-size: var(--theme-font-size-lg);
  font-weight: var(--app-heading-font-weight);
  color: var(--theme-text-primary);
  margin: 0;
}

.app-header__actions {
  display: flex;
  gap: var(--theme-spacing-sm);
}

/* App Sidebar */
.app-sidebar {
  width: var(--app-sidebar-width);
  background: var(--theme-bg-secondary);
  border-right: 1px solid var(--theme-border-primary);
  padding: var(--theme-spacing-md);
}

.app-sidebar__nav-item {
  display: block;
  padding: var(--theme-spacing-sm) var(--theme-spacing-md);
  color: var(--theme-text-secondary);
  text-decoration: none;
  border-radius: var(--theme-border-radius);
  transition: all 0.2s ease;
}

.app-sidebar__nav-item:hover {
  background: var(--theme-bg-hover);
  color: var(--theme-text-primary);
}

.app-sidebar__nav-item--active {
  background: var(--app-accent-color);
  color: white;
}

/* App Cards */
.app-card {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-border-radius);
  box-shadow: var(--app-card-shadow);
  padding: var(--theme-spacing-lg);
  margin-bottom: var(--theme-spacing-md);
}

.app-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--theme-spacing-md);
  padding-bottom: var(--theme-spacing-sm);
  border-bottom: 1px solid var(--theme-border-secondary);
}

.app-card__title {
  font-size: var(--theme-font-size-md);
  font-weight: var(--app-heading-font-weight);
  color: var(--theme-text-primary);
  margin: 0;
}

.app-card__content {
  line-height: var(--app-body-line-height);
  color: var(--theme-text-secondary);
}

/* Status Indicators */
.app-status {
  display: inline-flex;
  align-items: center;
  padding: var(--theme-spacing-xs) var(--theme-spacing-sm);
  border-radius: var(--theme-border-radius);
  font-size: var(--theme-font-size-sm);
  font-weight: 500;
}

.app-status--success {
  background: var(--app-success-color);
  color: white;
}

.app-status--warning {
  background: var(--app-warning-color);
  color: white;
}

.app-status--error {
  background: var(--app-error-color);
  color: white;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Mobile breakpoint (theme-aware) */
@media (max-width: var(--theme-mobile-breakpoint)) {
  .app-header {
    padding: 0 var(--theme-spacing-sm);
  }
  
  .app-sidebar {
    width: 100%;
    position: fixed;
    top: var(--app-header-height);
    left: -100%;
    height: calc(100vh - var(--app-header-height));
    transition: left 0.3s ease;
    z-index: 1000;
  }
  
  .app-sidebar--open {
    left: 0;
  }
  
  .app-card {
    margin: var(--theme-spacing-sm);
    padding: var(--theme-spacing-md);
  }
}

/* Tablet breakpoint (theme-aware) */
@media (max-width: var(--theme-tablet-breakpoint)) {
  .app-sidebar {
    width: 240px;
  }
  
  .app-card {
    padding: var(--theme-spacing-md);
  }
}
```

### Step 2: Create Component-Specific Theme Files

```css
/* apps/my-app/src/styles/components/dashboard.css */

/* Dashboard-specific theming */
[data-theme="crm"] .dashboard {
  --dashboard-grid-gap: 16px;
  --dashboard-widget-min-height: 200px;
}

[data-theme="mfe"] .dashboard {
  --dashboard-grid-gap: 24px;
  --dashboard-widget-min-height: 240px;
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--dashboard-grid-gap);
  padding: var(--app-content-padding);
}

.dashboard__widget {
  min-height: var(--dashboard-widget-min-height);
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-border-radius);
  padding: var(--theme-spacing-lg);
}

.dashboard__widget-title {
  font-size: var(--theme-font-size-lg);
  font-weight: var(--app-heading-font-weight);
  color: var(--theme-text-primary);
  margin-bottom: var(--theme-spacing-md);
}
```

## 🔧 Advanced Customization Techniques

### 1. Theme-Specific Component Variants

```tsx
// Component with theme-aware styling
import { useThemeStyles, ThemeMode } from '@shared/services/theme';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export const AppButton: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  size = 'md', 
  children 
}) => {
  const { getThemeStyle, getThemeClass } = useThemeStyles();
  
  const buttonStyles = getThemeStyle({
    [ThemeMode.CRM]: {
      height: size === 'sm' ? '28px' : size === 'lg' ? '36px' : '32px',
      borderRadius: '2px',
      fontSize: '14px',
    },
    [ThemeMode.MFE]: {
      height: size === 'sm' ? '36px' : size === 'lg' ? '52px' : '44px',
      borderRadius: '16px',
      fontSize: '16px',
    },
  });
  
  const className = getThemeClass(`app-button app-button--${variant} app-button--${size}`);
  
  return (
    <button className={className} style={buttonStyles}>
      {children}
    </button>
  );
};
```

### 2. Dynamic Theme Properties

```css
/* Dynamic properties based on theme context */
.app-dynamic-component {
  /* Base properties */
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  
  /* Dynamic sizing based on theme */
  padding: var(--theme-spacing-md);
  border-radius: var(--theme-border-radius);
  
  /* Theme-specific animations */
  transition: all var(--theme-transition-duration, 0.2s) ease;
}

[data-theme="crm"] .app-dynamic-component {
  --theme-transition-duration: 0.15s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

[data-theme="mfe"] .app-dynamic-component {
  --theme-transition-duration: 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}
```

## 🧪 Testing App Themes

### 1. Theme Testing Utilities

```tsx
// apps/my-app/src/__tests__/theme-integration.test.tsx
import { renderWithAppContext, testAcrossScenarios } from '@shared/testing';
import { MyAppComponent } from '../components/MyAppComponent';

describe('App Theme Integration', () => {
  testAcrossScenarios('MyAppComponent', () => <MyAppComponent />, [
    'crmWebResource',
    'mfeStandalone',
  ]);
  
  test('applies app-specific styling in CRM theme', () => {
    const { container } = renderWithAppContext(<MyAppComponent />, {
      theme: ThemeMode.CRM,
    });
    
    const appCard = container.querySelector('.app-card');
    expect(appCard).toHaveStyle({
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    });
  });
});
```

### 2. Visual Regression Testing

```tsx
// Example using @storybook/test-runner or similar
import { test, expect } from '@playwright/test';

test('app themes render correctly', async ({ page }) => {
  // Test CRM theme
  await page.goto('/my-app?themeMode=crm');
  await expect(page).toHaveScreenshot('app-crm-theme.png');
  
  // Test MFE theme
  await page.goto('/my-app?themeMode=mfe');
  await expect(page).toHaveScreenshot('app-mfe-theme.png');
});
```

## 📋 Best Practices Checklist

### ✅ Theme Development Checklist

- [ ] Use CSS custom properties for all theme-dependent values
- [ ] Implement theme-aware selectors `[data-theme="..."]`
- [ ] Test in both CRM and MFE themes
- [ ] Verify responsive behavior across themes
- [ ] Check accessibility (color contrast, focus states)
- [ ] Test theme switching functionality
- [ ] Validate with theme testing utilities
- [ ] Document custom properties and their usage

### ✅ Performance Checklist

- [ ] Minimize CSS custom property recalculations
- [ ] Use efficient selectors (avoid deep nesting)
- [ ] Optimize for CSS-in-JS if using styled components
- [ ] Test theme switching performance
- [ ] Verify no layout shifts during theme changes

### ✅ Accessibility Checklist

- [ ] Maintain WCAG AA color contrast ratios in both themes
- [ ] Ensure focus indicators are visible in both themes
- [ ] Test with screen readers in both themes
- [ ] Verify keyboard navigation works consistently
- [ ] Check that theme switching doesn't break accessibility

## 🚀 Quick Start Template

Use this template to quickly set up app-specific theming:

```bash
# 1. Create theme directory structure
mkdir -p apps/my-app/src/styles/{components,themes}

# 2. Create base app theme file
touch apps/my-app/src/styles/app-theme.css

# 3. Add theme imports to main.tsx
echo "import './styles/app-theme.css';" >> apps/my-app/src/main.tsx

# 4. Create component theme files as needed
touch apps/my-app/src/styles/components/{header,sidebar,dashboard}.css
```

## 🔍 Troubleshooting

### Common Issues and Solutions

1. **CSS Variables Not Working**
   - Ensure proper `[data-theme="..."]` selectors
   - Check import order (base themes first)
   - Verify CSS custom property syntax

2. **Theme Switching Not Working**
   - Confirm ThemeProvider is properly configured
   - Check that components use theme hooks correctly
   - Verify CSS selectors target the right theme attributes

3. **Styling Conflicts**
   - Use more specific selectors for app-specific styles
   - Avoid `!important` declarations
   - Check CSS specificity and cascade order

For more detailed troubleshooting, see the main [README.md](./README.md) theme troubleshooting section.
