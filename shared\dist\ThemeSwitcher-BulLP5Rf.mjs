import { g as N, j as e, u as M, c as z } from "./themeContext-DCSGPJOH.mjs";
import { useState as T } from "react";
import { T as t, g as R } from "./index-CIKzu-5v.mjs";
const D = ({
  children: o,
  onClick: c,
  variant: i = "primary",
  size: n = "medium",
  disabled: l = !1,
  type: p = "button",
  className: h = "",
  style: b,
  ...y
}) => {
  const { getThemeClass: a } = N(), s = "theme-button", d = {
    primary: "theme-button-primary",
    secondary: "theme-button-secondary",
    danger: "theme-button-danger"
  }, g = {
    small: "theme-button-sm",
    medium: "theme-button-base",
    large: "theme-button-lg"
  }, C = l ? "disabled" : "", x = [
    a(s),
    a(d[i]),
    a(g[n]),
    C,
    h
  ].filter(Boolean).join(" ");
  return /* @__PURE__ */ e.jsx(
    "button",
    {
      type: p,
      className: x,
      style: b,
      onClick: c,
      disabled: l,
      ...y,
      children: o
    }
  );
}, O = ({
  size: o = "medium",
  color: c = "primary",
  className: i = "",
  text: n
}) => {
  const { getThemeClass: l } = N(), p = {
    small: "theme-spinner-sm",
    medium: "theme-spinner",
    large: "theme-spinner-lg"
  }, h = {
    primary: "theme-spinner-primary",
    secondary: "theme-spinner-secondary",
    white: "theme-spinner-white"
  }, b = [
    l("theme-spinner"),
    l(p[o]),
    l(h[c]),
    i
  ].filter(Boolean).join(" ");
  return /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col items-center justify-center", children: [
    /* @__PURE__ */ e.jsxs(
      "svg",
      {
        className: b,
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        children: [
          /* @__PURE__ */ e.jsx(
            "circle",
            {
              className: "opacity-25",
              cx: "12",
              cy: "12",
              r: "10",
              stroke: "currentColor",
              strokeWidth: "4"
            }
          ),
          /* @__PURE__ */ e.jsx(
            "path",
            {
              className: "opacity-75",
              fill: "currentColor",
              d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            }
          )
        ]
      }
    ),
    n && /* @__PURE__ */ e.jsx("p", { className: `mt-2 text-sm ${h[c]}`, children: n })
  ] });
}, u = {
  [t.CRM]: "Dynamics CRM",
  [t.MFE]: "ZB Champion"
}, v = {
  [t.CRM]: "🏢",
  [t.MFE]: "🎯"
}, j = {
  [t.CRM]: "Dynamics 365 enterprise styling",
  [t.MFE]: "ZB Champion standard theme"
}, Z = ({
  variant: o = "dropdown",
  size: c = "md",
  showLabels: i = !0,
  showIcons: n = !0,
  className: l = "",
  disabled: p = !1,
  hideIfDisabled: h = !0
}) => {
  const { switchTheme: b, isLoading: y, error: a } = M(), s = z(), [d, g] = T(!1), x = R().features.enableThemeSwitching;
  if (!x && h)
    return null;
  const m = p || !x || y, f = async (r) => {
    if (!(m || r === s))
      try {
        await b(r), g(!1);
      } catch (k) {
        console.error("Failed to switch theme:", k);
      }
  }, w = `theme-switcher ${(() => {
    switch (c) {
      case "sm":
        return "theme-switcher-sm";
      case "lg":
        return "theme-switcher-lg";
      default:
        return "theme-switcher-md";
    }
  })()} ${l}`;
  return o === "toggle" ? /* @__PURE__ */ e.jsxs("div", { className: `${w} theme-switcher-toggle`, children: [
    /* @__PURE__ */ e.jsxs(
      "button",
      {
        className: `theme-toggle-button ${m ? "disabled" : ""}`,
        onClick: () => f(s === t.CRM ? t.MFE : t.CRM),
        disabled: m,
        title: `Switch to ${s === t.CRM ? u[t.MFE] : u[t.CRM]}`,
        children: [
          n && /* @__PURE__ */ e.jsx("span", { className: "theme-icon", children: v[s === t.CRM ? t.MFE : t.CRM] }),
          i && /* @__PURE__ */ e.jsx("span", { className: "theme-label", children: s === t.CRM ? "Switch to MFE" : "Switch to CRM" })
        ]
      }
    ),
    a && /* @__PURE__ */ e.jsx("div", { className: "theme-error", title: a, children: "⚠️" })
  ] }) : o === "buttons" ? /* @__PURE__ */ e.jsxs("div", { className: `${w} theme-switcher-buttons`, children: [
    Object.values(t).map((r) => /* @__PURE__ */ e.jsxs(
      "button",
      {
        className: `theme-button ${s === r ? "active" : ""} ${m ? "disabled" : ""}`,
        onClick: () => f(r),
        disabled: m,
        title: j[r],
        children: [
          n && /* @__PURE__ */ e.jsx("span", { className: "theme-icon", children: v[r] }),
          i && /* @__PURE__ */ e.jsx("span", { className: "theme-label", children: u[r] })
        ]
      },
      r
    )),
    a && /* @__PURE__ */ e.jsx("div", { className: "theme-error", title: a, children: "⚠️" })
  ] }) : /* @__PURE__ */ e.jsxs("div", { className: `${w} theme-switcher-dropdown`, children: [
    /* @__PURE__ */ e.jsxs(
      "button",
      {
        className: `theme-dropdown-trigger ${d ? "open" : ""} ${m ? "disabled" : ""}`,
        onClick: () => !m && g(!d),
        disabled: m,
        "aria-expanded": d,
        "aria-haspopup": "listbox",
        children: [
          n && /* @__PURE__ */ e.jsx("span", { className: "theme-icon", children: v[s] }),
          i && /* @__PURE__ */ e.jsx("span", { className: "theme-label", children: u[s] }),
          /* @__PURE__ */ e.jsx("span", { className: "theme-dropdown-arrow", children: "▼" })
        ]
      }
    ),
    d && /* @__PURE__ */ e.jsx("div", { className: "theme-dropdown-menu", children: Object.values(t).map((r) => /* @__PURE__ */ e.jsxs(
      "button",
      {
        className: `theme-dropdown-item ${s === r ? "active" : ""}`,
        onClick: () => f(r),
        role: "option",
        "aria-selected": s === r,
        children: [
          n && /* @__PURE__ */ e.jsx("span", { className: "theme-icon", children: v[r] }),
          /* @__PURE__ */ e.jsxs("div", { className: "theme-info", children: [
            i && /* @__PURE__ */ e.jsx("span", { className: "theme-label", children: u[r] }),
            /* @__PURE__ */ e.jsx("span", { className: "theme-description", children: j[r] })
          ] }),
          s === r && /* @__PURE__ */ e.jsx("span", { className: "theme-check", children: "✓" })
        ]
      },
      r
    )) }),
    a && /* @__PURE__ */ e.jsx("div", { className: "theme-error", title: a, children: "⚠️" }),
    d && /* @__PURE__ */ e.jsx(
      "div",
      {
        className: "theme-dropdown-backdrop",
        onClick: () => g(!1)
      }
    )
  ] });
}, $ = `
.theme-switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.theme-switcher-sm {
  font-size: 0.875rem;
}

.theme-switcher-md {
  font-size: 1rem;
}

.theme-switcher-lg {
  font-size: 1.125rem;
}

/* Toggle variant */
.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-toggle-button:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

/* Buttons variant */
.theme-switcher-buttons {
  display: flex;
  gap: 4px;
  padding: 2px;
  background-color: var(--theme-bg-tertiary);
  border-radius: var(--theme-radius-lg);
}

.theme-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--theme-radius-base);
  background-color: transparent;
  color: var(--theme-text-secondary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-button:hover:not(.disabled) {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.theme-button.active {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

/* Dropdown variant */
.theme-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-dropdown-trigger:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

.theme-dropdown-arrow {
  font-size: 0.75em;
  transition: transform var(--theme-transition-base);
}

.theme-dropdown-trigger.open .theme-dropdown-arrow {
  transform: rotate(180deg);
}

.theme-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-lg);
  box-shadow: var(--theme-shadow-lg);
  z-index: var(--theme-z-dropdown);
  overflow: hidden;
}

.theme-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background-color: transparent;
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  text-align: left;
}

.theme-dropdown-item:hover {
  background-color: var(--theme-bg-secondary);
}

.theme-dropdown-item.active {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.theme-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.theme-description {
  font-size: 0.875em;
  color: var(--theme-text-secondary);
}

.theme-check {
  color: var(--theme-primary);
  font-weight: bold;
}

.theme-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: calc(var(--theme-z-dropdown) - 1);
}

/* Disabled state */
.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* Error indicator */
.theme-error {
  color: var(--theme-error);
  font-size: 1.2em;
  cursor: help;
}
`;
if (typeof document < "u") {
  const o = document.createElement("style");
  o.textContent = $, document.head.appendChild(o);
}
export {
  D as B,
  O as L,
  Z as T
};
