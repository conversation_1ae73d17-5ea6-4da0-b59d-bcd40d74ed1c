/* Contact Timeline Control Styles */
.contact-timeline {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    padding: 16px;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Loading State */
.timeline-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.timeline-error {
    padding: 20px;
    text-align: center;
    color: #d13438;
    background-color: #fdf2f2;
    border: 1px solid #fecaca;
    border-radius: 4px;
}

.timeline-error button {
    margin-top: 12px;
    padding: 8px 16px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.timeline-error button:hover {
    background-color: #106ebe;
}

/* Filters */
.timeline-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e1e5e9;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #323130;
    margin-bottom: 4px;
}

.filter-group select,
.filter-group input {
    padding: 6px 8px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    font-size: 14px;
    min-width: 120px;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 1px #0078d4;
}

/* Timeline Items */
.timeline-items {
    position: relative;
}

.timeline-items::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e1e5e9;
}

.timeline-item {
    position: relative;
    display: flex;
    margin-bottom: 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 12px;
    border-radius: 4px;
}

.timeline-item:hover {
    background-color: #f8f9fa;
}

.timeline-icon {
    position: relative;
    width: 40px;
    height: 40px;
    background-color: #ffffff;
    border: 2px solid #0078d4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    z-index: 1;
    font-size: 16px;
}

.timeline-content {
    flex: 1;
    min-width: 0;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.timeline-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #323130;
    line-height: 1.3;
}

.timeline-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    text-transform: capitalize;
    white-space: nowrap;
}

.timeline-description {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #605e5c;
    line-height: 1.4;
}

.timeline-date {
    font-size: 12px;
    color: #8a8886;
    font-weight: 400;
}

/* No Items State */
.no-items {
    text-align: center;
    padding: 40px 20px;
    color: #8a8886;
}

.no-items p {
    margin: 0;
    font-size: 14px;
}

/* Footer */
.timeline-footer {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e1e5e9;
    text-align: center;
}

.timeline-footer button {
    padding: 8px 16px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.timeline-footer button:hover {
    background-color: #106ebe;
}

.timeline-footer button:active {
    background-color: #005a9e;
}

/* Responsive Design */
@media (max-width: 600px) {
    .timeline-filters {
        flex-direction: column;
        gap: 12px;
    }
    
    .timeline-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .timeline-status {
        align-self: flex-start;
    }
}
