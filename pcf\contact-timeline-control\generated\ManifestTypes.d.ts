/*
*This is auto generated from the ControlManifest.Input.xml file
*/

// Define IInputs and IOutputs Type. They should match with ControlManifest.
export interface IInputs {
    contactId: ComponentFramework.PropertyTypes.StringProperty;
    timelineData: ComponentFramework.PropertyTypes.Property;
    maxItems: ComponentFramework.PropertyTypes.WholeNumberProperty;
    showFilters: ComponentFramework.PropertyTypes.TwoOptionsProperty;
}
export interface IOutputs {
}
