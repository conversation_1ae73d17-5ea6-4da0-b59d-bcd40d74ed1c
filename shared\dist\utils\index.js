"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const G=require("../logger-DOG_vMcL.js");function F(t){"@babel/helpers - typeof";return F=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},F(t)}function O(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function v(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function h(t){v(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||F(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function he(t,e){v(2,arguments);var r=h(t).getTime(),a=O(e);return new Date(r+a)}var ve={};function k(){return ve}function Q(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function $(t,e){v(2,arguments);var r=h(t),a=h(e),n=r.getTime()-a.getTime();return n<0?-1:n>0?1:n}var ee=6e4,te=36e5;function ge(t){return v(1,arguments),t instanceof Date||F(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function _(t){if(v(1,arguments),!ge(t)&&typeof t!="number")return!1;var e=h(t);return!isNaN(Number(e))}function we(t,e){v(2,arguments);var r=h(t),a=h(e),n=r.getFullYear()-a.getFullYear(),i=r.getMonth()-a.getMonth();return n*12+i}function ye(t,e){return v(2,arguments),h(t).getTime()-h(e).getTime()}var be={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(e){return e<0?Math.ceil(e):Math.floor(e)}},pe="trunc";function Te(t){return be[pe]}function Me(t){v(1,arguments);var e=h(t);return e.setHours(23,59,59,999),e}function De(t){v(1,arguments);var e=h(t),r=e.getMonth();return e.setFullYear(e.getFullYear(),r+1,0),e.setHours(23,59,59,999),e}function Oe(t){v(1,arguments);var e=h(t);return Me(e).getTime()===De(e).getTime()}function Ce(t,e){v(2,arguments);var r=h(t),a=h(e),n=$(r,a),i=Math.abs(we(r,a)),o;if(i<1)o=0;else{r.getMonth()===1&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-n*i);var s=$(r,a)===-n;Oe(h(t))&&i===1&&$(t,a)===1&&(s=!1),o=n*(i-Number(s))}return o===0?0:o}function Se(t,e,r){v(2,arguments);var a=ye(t,e)/1e3;return Te()(a)}function Pe(t,e){v(2,arguments);var r=O(e);return he(t,-r)}var xe=864e5;function Ue(t){v(1,arguments);var e=h(t),r=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var a=e.getTime(),n=r-a;return Math.floor(n/xe)+1}function L(t){v(1,arguments);var e=1,r=h(t),a=r.getUTCDay(),n=(a<e?7:0)+a-e;return r.setUTCDate(r.getUTCDate()-n),r.setUTCHours(0,0,0,0),r}function re(t){v(1,arguments);var e=h(t),r=e.getUTCFullYear(),a=new Date(0);a.setUTCFullYear(r+1,0,4),a.setUTCHours(0,0,0,0);var n=L(a),i=new Date(0);i.setUTCFullYear(r,0,4),i.setUTCHours(0,0,0,0);var o=L(i);return e.getTime()>=n.getTime()?r+1:e.getTime()>=o.getTime()?r:r-1}function Ne(t){v(1,arguments);var e=re(t),r=new Date(0);r.setUTCFullYear(e,0,4),r.setUTCHours(0,0,0,0);var a=L(r);return a}var Ee=6048e5;function We(t){v(1,arguments);var e=h(t),r=L(e).getTime()-Ne(e).getTime();return Math.round(r/Ee)+1}function q(t,e){var r,a,n,i,o,s,u,d;v(1,arguments);var f=k(),l=O((r=(a=(n=(i=e==null?void 0:e.weekStartsOn)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&n!==void 0?n:f.weekStartsOn)!==null&&a!==void 0?a:(u=f.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.weekStartsOn)!==null&&r!==void 0?r:0);if(!(l>=0&&l<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var w=h(t),c=w.getUTCDay(),g=(c<l?7:0)+c-l;return w.setUTCDate(w.getUTCDate()-g),w.setUTCHours(0,0,0,0),w}function ae(t,e){var r,a,n,i,o,s,u,d;v(1,arguments);var f=h(t),l=f.getUTCFullYear(),w=k(),c=O((r=(a=(n=(i=e==null?void 0:e.firstWeekContainsDate)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&n!==void 0?n:w.firstWeekContainsDate)!==null&&a!==void 0?a:(u=w.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&r!==void 0?r:1);if(!(c>=1&&c<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var g=new Date(0);g.setUTCFullYear(l+1,0,c),g.setUTCHours(0,0,0,0);var C=q(g,e),b=new Date(0);b.setUTCFullYear(l,0,c),b.setUTCHours(0,0,0,0);var U=q(b,e);return f.getTime()>=C.getTime()?l+1:f.getTime()>=U.getTime()?l:l-1}function ke(t,e){var r,a,n,i,o,s,u,d;v(1,arguments);var f=k(),l=O((r=(a=(n=(i=e==null?void 0:e.firstWeekContainsDate)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&n!==void 0?n:f.firstWeekContainsDate)!==null&&a!==void 0?a:(u=f.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&r!==void 0?r:1),w=ae(t,e),c=new Date(0);c.setUTCFullYear(w,0,l),c.setUTCHours(0,0,0,0);var g=q(c,e);return g}var _e=6048e5;function Ye(t,e){v(1,arguments);var r=h(t),a=q(r,e).getTime()-ke(r,e).getTime();return Math.round(a/_e)+1}function m(t,e){for(var r=t<0?"-":"",a=Math.abs(t).toString();a.length<e;)a="0"+a;return r+a}var D={y:function(e,r){var a=e.getUTCFullYear(),n=a>0?a:1-a;return m(r==="yy"?n%100:n,r.length)},M:function(e,r){var a=e.getUTCMonth();return r==="M"?String(a+1):m(a+1,2)},d:function(e,r){return m(e.getUTCDate(),r.length)},a:function(e,r){var a=e.getUTCHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return a.toUpperCase();case"aaa":return a;case"aaaaa":return a[0];case"aaaa":default:return a==="am"?"a.m.":"p.m."}},h:function(e,r){return m(e.getUTCHours()%12||12,r.length)},H:function(e,r){return m(e.getUTCHours(),r.length)},m:function(e,r){return m(e.getUTCMinutes(),r.length)},s:function(e,r){return m(e.getUTCSeconds(),r.length)},S:function(e,r){var a=r.length,n=e.getUTCMilliseconds(),i=Math.floor(n*Math.pow(10,a-3));return m(i,r.length)}},x={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Ie={G:function(e,r,a){var n=e.getUTCFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return a.era(n,{width:"abbreviated"});case"GGGGG":return a.era(n,{width:"narrow"});case"GGGG":default:return a.era(n,{width:"wide"})}},y:function(e,r,a){if(r==="yo"){var n=e.getUTCFullYear(),i=n>0?n:1-n;return a.ordinalNumber(i,{unit:"year"})}return D.y(e,r)},Y:function(e,r,a,n){var i=ae(e,n),o=i>0?i:1-i;if(r==="YY"){var s=o%100;return m(s,2)}return r==="Yo"?a.ordinalNumber(o,{unit:"year"}):m(o,r.length)},R:function(e,r){var a=re(e);return m(a,r.length)},u:function(e,r){var a=e.getUTCFullYear();return m(a,r.length)},Q:function(e,r,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(r){case"Q":return String(n);case"QQ":return m(n,2);case"Qo":return a.ordinalNumber(n,{unit:"quarter"});case"QQQ":return a.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return a.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,r,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(r){case"q":return String(n);case"qq":return m(n,2);case"qo":return a.ordinalNumber(n,{unit:"quarter"});case"qqq":return a.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return a.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,r,a){var n=e.getUTCMonth();switch(r){case"M":case"MM":return D.M(e,r);case"Mo":return a.ordinalNumber(n+1,{unit:"month"});case"MMM":return a.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return a.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(n,{width:"wide",context:"formatting"})}},L:function(e,r,a){var n=e.getUTCMonth();switch(r){case"L":return String(n+1);case"LL":return m(n+1,2);case"Lo":return a.ordinalNumber(n+1,{unit:"month"});case"LLL":return a.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return a.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(n,{width:"wide",context:"standalone"})}},w:function(e,r,a,n){var i=Ye(e,n);return r==="wo"?a.ordinalNumber(i,{unit:"week"}):m(i,r.length)},I:function(e,r,a){var n=We(e);return r==="Io"?a.ordinalNumber(n,{unit:"week"}):m(n,r.length)},d:function(e,r,a){return r==="do"?a.ordinalNumber(e.getUTCDate(),{unit:"date"}):D.d(e,r)},D:function(e,r,a){var n=Ue(e);return r==="Do"?a.ordinalNumber(n,{unit:"dayOfYear"}):m(n,r.length)},E:function(e,r,a){var n=e.getUTCDay();switch(r){case"E":case"EE":case"EEE":return a.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return a.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(n,{width:"short",context:"formatting"});case"EEEE":default:return a.day(n,{width:"wide",context:"formatting"})}},e:function(e,r,a,n){var i=e.getUTCDay(),o=(i-n.weekStartsOn+8)%7||7;switch(r){case"e":return String(o);case"ee":return m(o,2);case"eo":return a.ordinalNumber(o,{unit:"day"});case"eee":return a.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return a.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(i,{width:"short",context:"formatting"});case"eeee":default:return a.day(i,{width:"wide",context:"formatting"})}},c:function(e,r,a,n){var i=e.getUTCDay(),o=(i-n.weekStartsOn+8)%7||7;switch(r){case"c":return String(o);case"cc":return m(o,r.length);case"co":return a.ordinalNumber(o,{unit:"day"});case"ccc":return a.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return a.day(i,{width:"narrow",context:"standalone"});case"cccccc":return a.day(i,{width:"short",context:"standalone"});case"cccc":default:return a.day(i,{width:"wide",context:"standalone"})}},i:function(e,r,a){var n=e.getUTCDay(),i=n===0?7:n;switch(r){case"i":return String(i);case"ii":return m(i,r.length);case"io":return a.ordinalNumber(i,{unit:"day"});case"iii":return a.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return a.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return a.day(n,{width:"short",context:"formatting"});case"iiii":default:return a.day(n,{width:"wide",context:"formatting"})}},a:function(e,r,a){var n=e.getUTCHours(),i=n/12>=1?"pm":"am";switch(r){case"a":case"aa":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(e,r,a){var n=e.getUTCHours(),i;switch(n===12?i=x.noon:n===0?i=x.midnight:i=n/12>=1?"pm":"am",r){case"b":case"bb":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(e,r,a){var n=e.getUTCHours(),i;switch(n>=17?i=x.evening:n>=12?i=x.afternoon:n>=4?i=x.morning:i=x.night,r){case"B":case"BB":case"BBB":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(e,r,a){if(r==="ho"){var n=e.getUTCHours()%12;return n===0&&(n=12),a.ordinalNumber(n,{unit:"hour"})}return D.h(e,r)},H:function(e,r,a){return r==="Ho"?a.ordinalNumber(e.getUTCHours(),{unit:"hour"}):D.H(e,r)},K:function(e,r,a){var n=e.getUTCHours()%12;return r==="Ko"?a.ordinalNumber(n,{unit:"hour"}):m(n,r.length)},k:function(e,r,a){var n=e.getUTCHours();return n===0&&(n=24),r==="ko"?a.ordinalNumber(n,{unit:"hour"}):m(n,r.length)},m:function(e,r,a){return r==="mo"?a.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):D.m(e,r)},s:function(e,r,a){return r==="so"?a.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):D.s(e,r)},S:function(e,r){return D.S(e,r)},X:function(e,r,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();if(o===0)return"Z";switch(r){case"X":return z(o);case"XXXX":case"XX":return S(o);case"XXXXX":case"XXX":default:return S(o,":")}},x:function(e,r,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(r){case"x":return z(o);case"xxxx":case"xx":return S(o);case"xxxxx":case"xxx":default:return S(o,":")}},O:function(e,r,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+B(o,":");case"OOOO":default:return"GMT"+S(o,":")}},z:function(e,r,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+B(o,":");case"zzzz":default:return"GMT"+S(o,":")}},t:function(e,r,a,n){var i=n._originalDate||e,o=Math.floor(i.getTime()/1e3);return m(o,r.length)},T:function(e,r,a,n){var i=n._originalDate||e,o=i.getTime();return m(o,r.length)}};function B(t,e){var r=t>0?"-":"+",a=Math.abs(t),n=Math.floor(a/60),i=a%60;if(i===0)return r+String(n);var o=e;return r+String(n)+o+m(i,2)}function z(t,e){if(t%60===0){var r=t>0?"-":"+";return r+m(Math.abs(t)/60,2)}return S(t,e)}function S(t,e){var r=e||"",a=t>0?"-":"+",n=Math.abs(t),i=m(Math.floor(n/60),2),o=m(n%60,2);return a+i+r+o}var J=function(e,r){switch(e){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},ne=function(e,r){switch(e){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},Re=function(e,r){var a=e.match(/(P+)(p+)?/)||[],n=a[1],i=a[2];if(!i)return J(e,r);var o;switch(n){case"P":o=r.dateTime({width:"short"});break;case"PP":o=r.dateTime({width:"medium"});break;case"PPP":o=r.dateTime({width:"long"});break;case"PPPP":default:o=r.dateTime({width:"full"});break}return o.replace("{{date}}",J(n,r)).replace("{{time}}",ne(i,r))},$e={p:ne,P:Re},Fe=["D","DD"],Le=["YY","YYYY"];function qe(t){return Fe.indexOf(t)!==-1}function He(t){return Le.indexOf(t)!==-1}function Z(t,e,r){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Xe={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ae=function(e,r,a){var n,i=Xe[e];return typeof i=="string"?n=i:r===1?n=i.one:n=i.other.replace("{{count}}",r.toString()),a!=null&&a.addSuffix?a.comparison&&a.comparison>0?"in "+n:n+" ago":n};function A(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=e.width?String(e.width):t.defaultWidth,a=t.formats[r]||t.formats[t.defaultWidth];return a}}var je={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Ve={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Qe={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ge={date:A({formats:je,defaultWidth:"full"}),time:A({formats:Ve,defaultWidth:"full"}),dateTime:A({formats:Qe,defaultWidth:"full"})},Be={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},ze=function(e,r,a,n){return Be[e]};function N(t){return function(e,r){var a=r!=null&&r.context?String(r.context):"standalone",n;if(a==="formatting"&&t.formattingValues){var i=t.defaultFormattingWidth||t.defaultWidth,o=r!=null&&r.width?String(r.width):i;n=t.formattingValues[o]||t.formattingValues[i]}else{var s=t.defaultWidth,u=r!=null&&r.width?String(r.width):t.defaultWidth;n=t.values[u]||t.values[s]}var d=t.argumentCallback?t.argumentCallback(e):e;return n[d]}}var Je={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Ze={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Ke={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},et={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},tt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},rt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},at=function(e,r){var a=Number(e),n=a%100;if(n>20||n<10)switch(n%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},nt={ordinalNumber:at,era:N({values:Je,defaultWidth:"wide"}),quarter:N({values:Ze,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:N({values:Ke,defaultWidth:"wide"}),day:N({values:et,defaultWidth:"wide"}),dayPeriod:N({values:tt,defaultWidth:"wide",formattingValues:rt,defaultFormattingWidth:"wide"})};function E(t){return function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.width,n=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],i=e.match(n);if(!i)return null;var o=i[0],s=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],u=Array.isArray(s)?ot(s,function(l){return l.test(o)}):it(s,function(l){return l.test(o)}),d;d=t.valueCallback?t.valueCallback(u):u,d=r.valueCallback?r.valueCallback(d):d;var f=e.slice(o.length);return{value:d,rest:f}}}function it(t,e){for(var r in t)if(t.hasOwnProperty(r)&&e(t[r]))return r}function ot(t,e){for(var r=0;r<t.length;r++)if(e(t[r]))return r}function ut(t){return function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=e.match(t.matchPattern);if(!a)return null;var n=a[0],i=e.match(t.parsePattern);if(!i)return null;var o=t.valueCallback?t.valueCallback(i[0]):i[0];o=r.valueCallback?r.valueCallback(o):o;var s=e.slice(n.length);return{value:o,rest:s}}}var st=/^(\d+)(th|st|nd|rd)?/i,dt=/\d+/i,lt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},ct={any:[/^b/i,/^(a|c)/i]},ft={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},mt={any:[/1/i,/2/i,/3/i,/4/i]},ht={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},vt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},gt={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},wt={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},yt={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},bt={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},pt={ordinalNumber:ut({matchPattern:st,parsePattern:dt,valueCallback:function(e){return parseInt(e,10)}}),era:E({matchPatterns:lt,defaultMatchWidth:"wide",parsePatterns:ct,defaultParseWidth:"any"}),quarter:E({matchPatterns:ft,defaultMatchWidth:"wide",parsePatterns:mt,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:E({matchPatterns:ht,defaultMatchWidth:"wide",parsePatterns:vt,defaultParseWidth:"any"}),day:E({matchPatterns:gt,defaultMatchWidth:"wide",parsePatterns:wt,defaultParseWidth:"any"}),dayPeriod:E({matchPatterns:yt,defaultMatchWidth:"any",parsePatterns:bt,defaultParseWidth:"any"})},ie={code:"en-US",formatDistance:Ae,formatLong:Ge,formatRelative:ze,localize:nt,match:pt,options:{weekStartsOn:0,firstWeekContainsDate:1}},Tt=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Mt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Dt=/^'([^]*?)'?$/,Ot=/''/g,Ct=/[a-zA-Z]/;function St(t,e,r){var a,n,i,o,s,u,d,f,l,w,c,g,C,b;v(2,arguments);var U=String(e),T=k(),p=(a=(n=void 0)!==null&&n!==void 0?n:T.locale)!==null&&a!==void 0?a:ie,H=O((i=(o=(s=(u=void 0)!==null&&u!==void 0?u:void 0)!==null&&s!==void 0?s:T.firstWeekContainsDate)!==null&&o!==void 0?o:(d=T.locale)===null||d===void 0||(f=d.options)===null||f===void 0?void 0:f.firstWeekContainsDate)!==null&&i!==void 0?i:1);if(!(H>=1&&H<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var X=O((l=(w=(c=(g=void 0)!==null&&g!==void 0?g:void 0)!==null&&c!==void 0?c:T.weekStartsOn)!==null&&w!==void 0?w:(C=T.locale)===null||C===void 0||(b=C.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&l!==void 0?l:0);if(!(X>=0&&X<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!p.localize)throw new RangeError("locale must contain localize property");if(!p.formatLong)throw new RangeError("locale must contain formatLong property");var Y=h(t);if(!_(Y))throw new RangeError("Invalid time value");var le=Q(Y),ce=Pe(Y,le),fe={firstWeekContainsDate:H,weekStartsOn:X,locale:p,_originalDate:Y},me=U.match(Mt).map(function(y){var M=y[0];if(M==="p"||M==="P"){var I=$e[M];return I(y,p.formatLong)}return y}).join("").match(Tt).map(function(y){if(y==="''")return"'";var M=y[0];if(M==="'")return Pt(y);var I=Ie[M];if(I)return He(y)&&Z(y,e,String(t)),qe(y)&&Z(y,e,String(t)),I(ce,y,p.localize,fe);if(M.match(Ct))throw new RangeError("Format string contains an unescaped latin alphabet character `"+M+"`");return y}).join("");return me}function Pt(t){var e=t.match(Dt);return e?e[1].replace(Ot,"'"):t}function oe(t,e){if(t==null)throw new TypeError("assign requires that input parameter not be null or undefined");for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}function xt(t){return oe({},t)}var K=1440,Ut=2520,j=43200,Nt=86400;function Et(t,e,r){var a,n;v(2,arguments);var i=k(),o=(a=(n=r==null?void 0:r.locale)!==null&&n!==void 0?n:i.locale)!==null&&a!==void 0?a:ie;if(!o.formatDistance)throw new RangeError("locale must contain formatDistance property");var s=$(t,e);if(isNaN(s))throw new RangeError("Invalid time value");var u=oe(xt(r),{addSuffix:!!(r!=null&&r.addSuffix),comparison:s}),d,f;s>0?(d=h(e),f=h(t)):(d=h(t),f=h(e));var l=Se(f,d),w=(Q(f)-Q(d))/1e3,c=Math.round((l-w)/60),g;if(c<2)return r!=null&&r.includeSeconds?l<5?o.formatDistance("lessThanXSeconds",5,u):l<10?o.formatDistance("lessThanXSeconds",10,u):l<20?o.formatDistance("lessThanXSeconds",20,u):l<40?o.formatDistance("halfAMinute",0,u):l<60?o.formatDistance("lessThanXMinutes",1,u):o.formatDistance("xMinutes",1,u):c===0?o.formatDistance("lessThanXMinutes",1,u):o.formatDistance("xMinutes",c,u);if(c<45)return o.formatDistance("xMinutes",c,u);if(c<90)return o.formatDistance("aboutXHours",1,u);if(c<K){var C=Math.round(c/60);return o.formatDistance("aboutXHours",C,u)}else{if(c<Ut)return o.formatDistance("xDays",1,u);if(c<j){var b=Math.round(c/K);return o.formatDistance("xDays",b,u)}else if(c<Nt)return g=Math.round(c/j),o.formatDistance("aboutXMonths",g,u)}if(g=Ce(f,d),g<12){var U=Math.round(c/j);return o.formatDistance("xMonths",U,u)}else{var T=g%12,p=Math.floor(g/12);return T<3?o.formatDistance("aboutXYears",p,u):T<9?o.formatDistance("overXYears",p,u):o.formatDistance("almostXYears",p+1,u)}}function Wt(t,e){return v(1,arguments),Et(t,Date.now(),e)}function kt(t,e){var r;v(1,arguments);var a=O((r=void 0)!==null&&r!==void 0?r:2);if(a!==2&&a!==1&&a!==0)throw new RangeError("additionalDigits must be 0, 1 or 2");if(!(typeof t=="string"||Object.prototype.toString.call(t)==="[object String]"))return new Date(NaN);var n=Rt(t),i;if(n.date){var o=$t(n.date,a);i=Ft(o.restDateString,o.year)}if(!i||isNaN(i.getTime()))return new Date(NaN);var s=i.getTime(),u=0,d;if(n.time&&(u=Lt(n.time),isNaN(u)))return new Date(NaN);if(n.timezone){if(d=qt(n.timezone),isNaN(d))return new Date(NaN)}else{var f=new Date(s+u),l=new Date(0);return l.setFullYear(f.getUTCFullYear(),f.getUTCMonth(),f.getUTCDate()),l.setHours(f.getUTCHours(),f.getUTCMinutes(),f.getUTCSeconds(),f.getUTCMilliseconds()),l}return new Date(s+u+d)}var R={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},_t=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Yt=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,It=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Rt(t){var e={},r=t.split(R.dateTimeDelimiter),a;if(r.length>2)return e;if(/:/.test(r[0])?a=r[0]:(e.date=r[0],a=r[1],R.timeZoneDelimiter.test(e.date)&&(e.date=t.split(R.timeZoneDelimiter)[0],a=t.substr(e.date.length,t.length))),a){var n=R.timezone.exec(a);n?(e.time=a.replace(n[1],""),e.timezone=n[1]):e.time=a}return e}function $t(t,e){var r=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),a=t.match(r);if(!a)return{year:NaN,restDateString:""};var n=a[1]?parseInt(a[1]):null,i=a[2]?parseInt(a[2]):null;return{year:i===null?n:i*100,restDateString:t.slice((a[1]||a[2]).length)}}function Ft(t,e){if(e===null)return new Date(NaN);var r=t.match(_t);if(!r)return new Date(NaN);var a=!!r[4],n=W(r[1]),i=W(r[2])-1,o=W(r[3]),s=W(r[4]),u=W(r[5])-1;if(a)return Vt(e,s,u)?Ht(e,s,u):new Date(NaN);var d=new Date(0);return!At(e,i,o)||!jt(e,n)?new Date(NaN):(d.setUTCFullYear(e,i,Math.max(n,o)),d)}function W(t){return t?parseInt(t):1}function Lt(t){var e=t.match(Yt);if(!e)return NaN;var r=V(e[1]),a=V(e[2]),n=V(e[3]);return Qt(r,a,n)?r*te+a*ee+n*1e3:NaN}function V(t){return t&&parseFloat(t.replace(",","."))||0}function qt(t){if(t==="Z")return 0;var e=t.match(It);if(!e)return 0;var r=e[1]==="+"?-1:1,a=parseInt(e[2]),n=e[3]&&parseInt(e[3])||0;return Gt(a,n)?r*(a*te+n*ee):NaN}function Ht(t,e,r){var a=new Date(0);a.setUTCFullYear(t,0,4);var n=a.getUTCDay()||7,i=(e-1)*7+r+1-n;return a.setUTCDate(a.getUTCDate()+i),a}var Xt=[31,null,31,30,31,30,31,31,30,31,30,31];function ue(t){return t%400===0||t%4===0&&t%100!==0}function At(t,e,r){return e>=0&&e<=11&&r>=1&&r<=(Xt[e]||(ue(t)?29:28))}function jt(t,e){return e>=1&&e<=(ue(t)?366:365)}function Vt(t,e,r){return e>=1&&e<=53&&r>=0&&r<=6}function Qt(t,e,r){return t===24?e===0&&r===0:r>=0&&r<60&&e>=0&&e<60&&t>=0&&t<25}function Gt(t,e){return e>=0&&e<=59}const se=(t,e="MMM dd, yyyy")=>{try{const r=P(t);return _(r)?St(r,e):"Invalid Date"}catch(r){return console.error("Error formatting date:",r),"Invalid Date"}},Bt=(t,e="MMM dd, yyyy h:mm a")=>se(t,e),zt=t=>{try{const e=P(t);return _(e)?Wt(e,{addSuffix:!0}):"Invalid Date"}catch(e){return console.error("Error formatting relative time:",e),"Invalid Date"}},Jt=t=>{try{const e=P(t);if(!_(e))throw new Error("Invalid date");return e.toISOString()}catch(e){throw console.error("Error formatting date for API:",e),e}},P=t=>{if(t instanceof Date)return t;if(typeof t=="string")return t.includes("T")||t.includes("Z")?kt(t):new Date(t);if(typeof t=="number")return new Date(t);throw new Error("Invalid date input")},Zt=t=>{try{const e=P(t);return _(e)}catch{return!1}},Kt=t=>{const e=P(t);return e.setHours(0,0,0,0),e},er=t=>{const e=P(t);return e.setHours(23,59,59,999),e},tr={SHORT:"MM/dd/yyyy",MEDIUM:"MMM dd, yyyy",LONG:"MMMM dd, yyyy",ISO:"yyyy-MM-dd'T'HH:mm:ss.SSSxxx",TIME_12:"h:mm a",TIME_24:"HH:mm",DATETIME_SHORT:"MM/dd/yyyy h:mm a",DATETIME_MEDIUM:"MMM dd, yyyy h:mm a",DATETIME_LONG:"MMMM dd, yyyy h:mm a"},rr=t=>{const e=[];return t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)||e.push("Please enter a valid email address"):e.push("Email is required"),{isValid:e.length===0,errors:e}},ar=t=>{const e=[];return t?(t.length<8&&e.push("Password must be at least 8 characters long"),/(?=.*[a-z])/.test(t)||e.push("Password must contain at least one lowercase letter"),/(?=.*[A-Z])/.test(t)||e.push("Password must contain at least one uppercase letter"),/(?=.*\d)/.test(t)||e.push("Password must contain at least one number"),/(?=.*[@$!%*?&])/.test(t)||e.push("Password must contain at least one special character (@$!%*?&)")):e.push("Password is required"),{isValid:e.length===0,errors:e}},nr=t=>{const e=[];return t?t.replace(/\D/g,"").length!==10&&e.push("Phone number must be 10 digits"):e.push("Phone number is required"),{isValid:e.length===0,errors:e}},ir=(t,e)=>{const r=[];return(t==null||t==="")&&r.push(`${e} is required`),{isValid:r.length===0,errors:r}},or=(t,e,r,a)=>{const n=[];return t.length<e&&n.push(`${a} must be at least ${e} characters long`),t.length>r&&n.push(`${a} must be no more than ${r} characters long`),{isValid:n.length===0,errors:n}},ur=(t,e,r,a)=>{const n=[];return isNaN(t)?n.push(`${a} must be a valid number`):(t<e&&n.push(`${a} must be at least ${e}`),t>r&&n.push(`${a} must be no more than ${r}`)),{isValid:n.length===0,errors:n}},sr=t=>{const e=[];if(!t)e.push("URL is required");else try{new URL(t)}catch{e.push("Please enter a valid URL")}return{isValid:e.length===0,errors:e}},de=(...t)=>{const e=t.flatMap(r=>r.errors);return{isValid:e.length===0,errors:e}},dr=(t,e)=>{const r=Object.keys(e).map(a=>{const n=e[a];return n(t[a])});return de(...r)};exports.LogLevel=G.LogLevel;exports.Logger=G.Logger;exports.logger=G.logger;exports.DATE_FORMATS=tr;exports.combineValidationResults=de;exports.formatDate=se;exports.formatDateForApi=Jt;exports.formatDateTime=Bt;exports.formatRelativeTime=zt;exports.getEndOfDay=er;exports.getStartOfDay=Kt;exports.isValidDate=Zt;exports.parseDate=P;exports.validateEmail=rr;exports.validateLength=or;exports.validateNumberRange=ur;exports.validateObject=dr;exports.validatePassword=ar;exports.validatePhoneNumber=nr;exports.validateRequired=ir;exports.validateUrl=sr;
