{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "allowImportingTsExtensions": false, "composite": true}, "include": ["components/**/*", "services/**/*", "utils/**/*", "config/**/*", "index.ts", "vite-env.d.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}