/**
 * Comprehensive Render Utilities
 * 
 * Combines theme and auth testing utilities for complete app testing
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ReactElement } from 'react';
import { ThemeProvider, ThemeMode } from '../services/theme';
import { DeploymentMode } from '../config/deploymentContext';
import { mockAuthContext, MockUser, authStates } from './auth-test-utils';
import { mockThemeDetection } from './theme-test-utils';

export interface AppRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Theme options
  theme?: ThemeMode;
  enableAutoDetection?: boolean;
  enablePersistence?: boolean;
  themeStorageKey?: string;
  
  // Auth options
  deploymentMode?: DeploymentMode;
  isAuthenticated?: boolean;
  user?: MockUser | null;
  mockAuthMethods?: boolean;
  
  // Additional providers
  additionalWrappers?: React.ComponentType<{ children: React.ReactNode }>[];
}

/**
 * Render component with full app context (theme + auth + other providers)
 */
export function renderWithAppContext(
  ui: ReactElement,
  options: AppRenderOptions = {}
): RenderResult {
  const {
    theme = ThemeMode.CRM,
    enableAutoDetection = false,
    enablePersistence = false,
    themeStorageKey = 'test-theme',
    deploymentMode = DeploymentMode.STANDALONE_MFE,
    isAuthenticated = true,
    user = authStates.authenticatedUser.user,
    mockAuthMethods = true,
    additionalWrappers = [],
    ...renderOptions
  } = options;

  // Setup mocks
  if (mockAuthMethods) {
    mockAuthContext({ isAuthenticated, user, deploymentMode });
  }
  mockThemeDetection(theme);

  // Create wrapper with all providers
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    let wrappedChildren = (
      <ThemeProvider
        defaultTheme={theme}
        enableAutoDetection={enableAutoDetection}
        enablePersistence={enablePersistence}
        storageKey={themeStorageKey}
      >
        {children}
      </ThemeProvider>
    );

    // Apply additional wrappers
    additionalWrappers.forEach((WrapperComponent) => {
      wrappedChildren = <WrapperComponent>{wrappedChildren}</WrapperComponent>;
    });

    return <div data-testid="app-wrapper">{wrappedChildren}</div>;
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

/**
 * Test scenarios for different app configurations
 */
export const testScenarios = {
  // CRM deployment scenarios
  crmWebResource: {
    theme: ThemeMode.CRM,
    deploymentMode: DeploymentMode.WEB_RESOURCE,
    isAuthenticated: true,
    user: authStates.authenticatedSystemUser.user,
  },
  
  crmEmbeddedSpa: {
    theme: ThemeMode.CRM,
    deploymentMode: DeploymentMode.EMBEDDED_SPA,
    isAuthenticated: true,
    user: authStates.authenticatedUser.user,
  },

  // MFE deployment scenarios
  mfeStandalone: {
    theme: ThemeMode.MFE,
    deploymentMode: DeploymentMode.STANDALONE_MFE,
    isAuthenticated: true,
    user: authStates.authenticatedUser.user,
  },

  mfeUnauthenticated: {
    theme: ThemeMode.MFE,
    deploymentMode: DeploymentMode.STANDALONE_MFE,
    isAuthenticated: false,
    user: null,
  },

  // Admin scenarios
  crmAdmin: {
    theme: ThemeMode.CRM,
    deploymentMode: DeploymentMode.WEB_RESOURCE,
    isAuthenticated: true,
    user: authStates.authenticatedAdmin.user,
  },

  mfeAdmin: {
    theme: ThemeMode.MFE,
    deploymentMode: DeploymentMode.STANDALONE_MFE,
    isAuthenticated: true,
    user: authStates.authenticatedAdmin.user,
  },
};

/**
 * Test component across multiple scenarios
 */
export function testAcrossScenarios(
  testName: string,
  componentFactory: (scenario: typeof testScenarios[keyof typeof testScenarios]) => ReactElement,
  scenarios: (keyof typeof testScenarios)[] = Object.keys(testScenarios) as (keyof typeof testScenarios)[]
): void {
  describe(testName, () => {
    scenarios.forEach((scenarioKey) => {
      const scenario = testScenarios[scenarioKey];
      test(`in ${scenarioKey} scenario`, () => {
        const { container } = renderWithAppContext(
          componentFactory(scenario),
          scenario
        );

        // Basic assertions that should work across all scenarios
        expect(container.firstChild).toBeInTheDocument();
        expect(document.documentElement).toHaveAttribute('data-theme', scenario.theme);
      });
    });
  });
}

/**
 * Create test suite for a component with theme and auth variations
 */
export function createComponentTestSuite(
  componentName: string,
  componentFactory: (props?: any) => ReactElement,
  customTests?: {
    themeTests?: (theme: ThemeMode) => void;
    authTests?: (authState: typeof authStates[keyof typeof authStates]) => void;
    scenarioTests?: (scenario: typeof testScenarios[keyof typeof testScenarios]) => void;
  }
) {
  describe(componentName, () => {
    describe('Theme Variations', () => {
      Object.values(ThemeMode).forEach((theme) => {
        describe(`${theme} theme`, () => {
          test('renders correctly', () => {
            const { container } = renderWithAppContext(componentFactory(), { theme });
            expect(container.firstChild).toBeInTheDocument();
            expect(document.documentElement).toHaveAttribute('data-theme', theme);
          });

          if (customTests?.themeTests) {
            customTests.themeTests(theme);
          }
        });
      });
    });

    describe('Authentication Variations', () => {
      Object.entries(authStates).forEach(([stateName, authState]) => {
        describe(`${stateName} state`, () => {
          test('renders correctly', () => {
            const { container } = renderWithAppContext(componentFactory(), authState);
            expect(container.firstChild).toBeInTheDocument();
          });

          if (customTests?.authTests) {
            customTests.authTests(authState);
          }
        });
      });
    });

    describe('Deployment Scenarios', () => {
      Object.entries(testScenarios).forEach(([scenarioName, scenario]) => {
        describe(`${scenarioName} scenario`, () => {
          test('renders correctly', () => {
            const { container } = renderWithAppContext(componentFactory(), scenario);
            expect(container.firstChild).toBeInTheDocument();
          });

          if (customTests?.scenarioTests) {
            customTests.scenarioTests(scenario);
          }
        });
      });
    });
  });
}

/**
 * Utility to test responsive behavior across themes
 */
export function testResponsiveBehavior(
  componentFactory: () => ReactElement,
  breakpointTests: {
    mobile?: () => void;
    tablet?: () => void;
    desktop?: () => void;
  }
) {
  describe('Responsive Behavior', () => {
    Object.values(ThemeMode).forEach((theme) => {
      describe(`${theme} theme`, () => {
        ['mobile', 'tablet', 'desktop'].forEach((breakpoint) => {
          if (breakpointTests[breakpoint as keyof typeof breakpointTests]) {
            test(`${breakpoint} breakpoint`, () => {
              // Mock viewport size
              const breakpoints = {
                mobile: theme === ThemeMode.MFE ? 839 : 767,
                tablet: theme === ThemeMode.MFE ? 1199 : 1023,
                desktop: 1920,
              };

              Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: breakpoints[breakpoint as keyof typeof breakpoints],
              });

              renderWithAppContext(componentFactory(), { theme });
              breakpointTests[breakpoint as keyof typeof breakpointTests]!();
            });
          }
        });
      });
    });
  });
}
