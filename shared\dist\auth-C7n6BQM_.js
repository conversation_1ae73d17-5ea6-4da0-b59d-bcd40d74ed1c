"use strict";var gt=Object.defineProperty;var yt=(r,e,t)=>e in r?gt(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var S=(r,e,t)=>yt(r,typeof e!="symbol"?e+"":e,t);const y=require("./logger-B08GnSxL.js"),L=require("react");function Ve(r,e){return function(){return r.apply(e,arguments)}}const{toString:wt}=Object.prototype,{getPrototypeOf:Ce}=Object,{iterator:ie,toStringTag:He}=Symbol,oe=(r=>e=>{const t=wt.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),I=r=>(r=r.toLowerCase(),e=>oe(e)===r),ae=r=>e=>typeof e===r,{isArray:q}=Array,W=ae("undefined");function J(r){return r!==null&&!W(r)&&r.constructor!==null&&!W(r.constructor)&&x(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}const We=I("ArrayBuffer");function Et(r){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(r):e=r&&r.buffer&&We(r.buffer),e}const At=ae("string"),x=ae("function"),Je=ae("number"),X=r=>r!==null&&typeof r=="object",bt=r=>r===!0||r===!1,Z=r=>{if(oe(r)!=="object")return!1;const e=Ce(r);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(He in r)&&!(ie in r)},St=r=>{if(!X(r)||J(r))return!1;try{return Object.keys(r).length===0&&Object.getPrototypeOf(r)===Object.prototype}catch{return!1}},Rt=I("Date"),Tt=I("File"),Ct=I("Blob"),Ot=I("FileList"),xt=r=>X(r)&&x(r.pipe),kt=r=>{let e;return r&&(typeof FormData=="function"&&r instanceof FormData||x(r.append)&&((e=oe(r))==="formdata"||e==="object"&&x(r.toString)&&r.toString()==="[object FormData]"))},Lt=I("URLSearchParams"),[Ut,It,Nt,_t]=["ReadableStream","Request","Response","Headers"].map(I),Pt=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function G(r,e,{allOwnKeys:t=!1}={}){if(r===null||typeof r>"u")return;let n,s;if(typeof r!="object"&&(r=[r]),q(r))for(n=0,s=r.length;n<s;n++)e.call(null,r[n],n,r);else{if(J(r))return;const i=t?Object.getOwnPropertyNames(r):Object.keys(r),o=i.length;let c;for(n=0;n<o;n++)c=i[n],e.call(null,r[c],c,r)}}function Xe(r,e){if(J(r))return null;e=e.toLowerCase();const t=Object.keys(r);let n=t.length,s;for(;n-- >0;)if(s=t[n],e===s.toLowerCase())return s;return null}const M=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ge=r=>!W(r)&&r!==M;function ye(){const{caseless:r}=Ge(this)&&this||{},e={},t=(n,s)=>{const i=r&&Xe(e,s)||s;Z(e[i])&&Z(n)?e[i]=ye(e[i],n):Z(n)?e[i]=ye({},n):q(n)?e[i]=n.slice():e[i]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&G(arguments[n],t);return e}const Dt=(r,e,t,{allOwnKeys:n}={})=>(G(e,(s,i)=>{t&&x(s)?r[i]=Ve(s,t):r[i]=s},{allOwnKeys:n}),r),Ft=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),vt=(r,e,t,n)=>{r.prototype=Object.create(e.prototype,n),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:e.prototype}),t&&Object.assign(r.prototype,t)},Bt=(r,e,t,n)=>{let s,i,o;const c={};if(e=e||{},r==null)return e;do{for(s=Object.getOwnPropertyNames(r),i=s.length;i-- >0;)o=s[i],(!n||n(o,r,e))&&!c[o]&&(e[o]=r[o],c[o]=!0);r=t!==!1&&Ce(r)}while(r&&(!t||t(r,e))&&r!==Object.prototype);return e},Mt=(r,e,t)=>{r=String(r),(t===void 0||t>r.length)&&(t=r.length),t-=e.length;const n=r.indexOf(e,t);return n!==-1&&n===t},$t=r=>{if(!r)return null;if(q(r))return r;let e=r.length;if(!Je(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=r[e];return t},zt=(r=>e=>r&&e instanceof r)(typeof Uint8Array<"u"&&Ce(Uint8Array)),jt=(r,e)=>{const n=(r&&r[ie]).call(r);let s;for(;(s=n.next())&&!s.done;){const i=s.value;e.call(r,i[0],i[1])}},qt=(r,e)=>{let t;const n=[];for(;(t=r.exec(e))!==null;)n.push(t);return n},Vt=I("HTMLFormElement"),Ht=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,n,s){return n.toUpperCase()+s}),Ie=(({hasOwnProperty:r})=>(e,t)=>r.call(e,t))(Object.prototype),Wt=I("RegExp"),Ke=(r,e)=>{const t=Object.getOwnPropertyDescriptors(r),n={};G(t,(s,i)=>{let o;(o=e(s,i,r))!==!1&&(n[i]=o||s)}),Object.defineProperties(r,n)},Jt=r=>{Ke(r,(e,t)=>{if(x(r)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const n=r[t];if(x(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},Xt=(r,e)=>{const t={},n=s=>{s.forEach(i=>{t[i]=!0})};return q(r)?n(r):n(String(r).split(e)),t},Gt=()=>{},Kt=(r,e)=>r!=null&&Number.isFinite(r=+r)?r:e;function Qt(r){return!!(r&&x(r.append)&&r[He]==="FormData"&&r[ie])}const Yt=r=>{const e=new Array(10),t=(n,s)=>{if(X(n)){if(e.indexOf(n)>=0)return;if(J(n))return n;if(!("toJSON"in n)){e[s]=n;const i=q(n)?[]:{};return G(n,(o,c)=>{const d=t(o,s+1);!W(d)&&(i[c]=d)}),e[s]=void 0,i}}return n};return t(r,0)},Zt=I("AsyncFunction"),er=r=>r&&(X(r)||x(r))&&x(r.then)&&x(r.catch),Qe=((r,e)=>r?setImmediate:e?((t,n)=>(M.addEventListener("message",({source:s,data:i})=>{s===M&&i===t&&n.length&&n.shift()()},!1),s=>{n.push(s),M.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",x(M.postMessage)),tr=typeof queueMicrotask<"u"?queueMicrotask.bind(M):typeof process<"u"&&process.nextTick||Qe,rr=r=>r!=null&&x(r[ie]),a={isArray:q,isArrayBuffer:We,isBuffer:J,isFormData:kt,isArrayBufferView:Et,isString:At,isNumber:Je,isBoolean:bt,isObject:X,isPlainObject:Z,isEmptyObject:St,isReadableStream:Ut,isRequest:It,isResponse:Nt,isHeaders:_t,isUndefined:W,isDate:Rt,isFile:Tt,isBlob:Ct,isRegExp:Wt,isFunction:x,isStream:xt,isURLSearchParams:Lt,isTypedArray:zt,isFileList:Ot,forEach:G,merge:ye,extend:Dt,trim:Pt,stripBOM:Ft,inherits:vt,toFlatObject:Bt,kindOf:oe,kindOfTest:I,endsWith:Mt,toArray:$t,forEachEntry:jt,matchAll:qt,isHTMLForm:Vt,hasOwnProperty:Ie,hasOwnProp:Ie,reduceDescriptors:Ke,freezeMethods:Jt,toObjectSet:Xt,toCamelCase:Ht,noop:Gt,toFiniteNumber:Kt,findKey:Xe,global:M,isContextDefined:Ge,isSpecCompliantForm:Qt,toJSONObject:Yt,isAsyncFn:Zt,isThenable:er,setImmediate:Qe,asap:tr,isIterable:rr};function g(r,e,t,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(g,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ye=g.prototype,Ze={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{Ze[r]={value:r}});Object.defineProperties(g,Ze);Object.defineProperty(Ye,"isAxiosError",{value:!0});g.from=(r,e,t,n,s,i)=>{const o=Object.create(Ye);return a.toFlatObject(r,o,function(d){return d!==Error.prototype},c=>c!=="isAxiosError"),g.call(o,r.message,e,t,n,s),o.cause=r,o.name=r.name,i&&Object.assign(o,i),o};const nr=null;function we(r){return a.isPlainObject(r)||a.isArray(r)}function et(r){return a.endsWith(r,"[]")?r.slice(0,-2):r}function Ne(r,e,t){return r?r.concat(e).map(function(s,i){return s=et(s),!t&&i?"["+s+"]":s}).join(t?".":""):e}function sr(r){return a.isArray(r)&&!r.some(we)}const ir=a.toFlatObject(a,{},null,function(e){return/^is[A-Z]/.test(e)});function ce(r,e,t){if(!a.isObject(r))throw new TypeError("target must be an object");e=e||new FormData,t=a.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,p){return!a.isUndefined(p[m])});const n=t.metaTokens,s=t.visitor||l,i=t.dots,o=t.indexes,d=(t.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(e);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if(a.isDate(h))return h.toISOString();if(a.isBoolean(h))return h.toString();if(!d&&a.isBlob(h))throw new g("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(h)||a.isTypedArray(h)?d&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function l(h,m,p){let A=h;if(h&&!p&&typeof h=="object"){if(a.endsWith(m,"{}"))m=n?m:m.slice(0,-2),h=JSON.stringify(h);else if(a.isArray(h)&&sr(h)||(a.isFileList(h)||a.endsWith(m,"[]"))&&(A=a.toArray(h)))return m=et(m),A.forEach(function(T,_){!(a.isUndefined(T)||T===null)&&e.append(o===!0?Ne([m],_,i):o===null?m:m+"[]",u(T))}),!1}return we(h)?!0:(e.append(Ne(p,m,i),u(h)),!1)}const f=[],w=Object.assign(ir,{defaultVisitor:l,convertValue:u,isVisitable:we});function E(h,m){if(!a.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+m.join("."));f.push(h),a.forEach(h,function(A,R){(!(a.isUndefined(A)||A===null)&&s.call(e,A,a.isString(R)?R.trim():R,m,w))===!0&&E(A,m?m.concat(R):[R])}),f.pop()}}if(!a.isObject(r))throw new TypeError("data must be an object");return E(r),e}function _e(r){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function Oe(r,e){this._pairs=[],r&&ce(r,this,e)}const tt=Oe.prototype;tt.append=function(e,t){this._pairs.push([e,t])};tt.toString=function(e){const t=e?function(n){return e.call(this,n,_e)}:_e;return this._pairs.map(function(s){return t(s[0])+"="+t(s[1])},"").join("&")};function or(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rt(r,e,t){if(!e)return r;const n=t&&t.encode||or;a.isFunction(t)&&(t={serialize:t});const s=t&&t.serialize;let i;if(s?i=s(e,t):i=a.isURLSearchParams(e)?e.toString():new Oe(e,t).toString(n),i){const o=r.indexOf("#");o!==-1&&(r=r.slice(0,o)),r+=(r.indexOf("?")===-1?"?":"&")+i}return r}class Pe{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){a.forEach(this.handlers,function(n){n!==null&&e(n)})}}const nt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ar=typeof URLSearchParams<"u"?URLSearchParams:Oe,cr=typeof FormData<"u"?FormData:null,ur=typeof Blob<"u"?Blob:null,lr={isBrowser:!0,classes:{URLSearchParams:ar,FormData:cr,Blob:ur},protocols:["http","https","file","blob","url","data"]},xe=typeof window<"u"&&typeof document<"u",Ee=typeof navigator=="object"&&navigator||void 0,hr=xe&&(!Ee||["ReactNative","NativeScript","NS"].indexOf(Ee.product)<0),dr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",fr=xe&&window.location.href||"http://localhost",pr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:xe,hasStandardBrowserEnv:hr,hasStandardBrowserWebWorkerEnv:dr,navigator:Ee,origin:fr},Symbol.toStringTag,{value:"Module"})),C={...pr,...lr};function mr(r,e){return ce(r,new C.classes.URLSearchParams,{visitor:function(t,n,s,i){return C.isNode&&a.isBuffer(t)?(this.append(n,t.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...e})}function gr(r){return a.matchAll(/\w+|\[(\w*)]/g,r).map(e=>e[0]==="[]"?"":e[1]||e[0])}function yr(r){const e={},t=Object.keys(r);let n;const s=t.length;let i;for(n=0;n<s;n++)i=t[n],e[i]=r[i];return e}function st(r){function e(t,n,s,i){let o=t[i++];if(o==="__proto__")return!0;const c=Number.isFinite(+o),d=i>=t.length;return o=!o&&a.isArray(s)?s.length:o,d?(a.hasOwnProp(s,o)?s[o]=[s[o],n]:s[o]=n,!c):((!s[o]||!a.isObject(s[o]))&&(s[o]=[]),e(t,n,s[o],i)&&a.isArray(s[o])&&(s[o]=yr(s[o])),!c)}if(a.isFormData(r)&&a.isFunction(r.entries)){const t={};return a.forEachEntry(r,(n,s)=>{e(gr(n),s,t,0)}),t}return null}function wr(r,e,t){if(a.isString(r))try{return(e||JSON.parse)(r),a.trim(r)}catch(n){if(n.name!=="SyntaxError")throw n}return(t||JSON.stringify)(r)}const K={transitional:nt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",s=n.indexOf("application/json")>-1,i=a.isObject(e);if(i&&a.isHTMLForm(e)&&(e=new FormData(e)),a.isFormData(e))return s?JSON.stringify(st(e)):e;if(a.isArrayBuffer(e)||a.isBuffer(e)||a.isStream(e)||a.isFile(e)||a.isBlob(e)||a.isReadableStream(e))return e;if(a.isArrayBufferView(e))return e.buffer;if(a.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return mr(e,this.formSerializer).toString();if((c=a.isFileList(e))||n.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return ce(c?{"files[]":e}:e,d&&new d,this.formSerializer)}}return i||s?(t.setContentType("application/json",!1),wr(e)):e}],transformResponse:[function(e){const t=this.transitional||K.transitional,n=t&&t.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(e)||a.isReadableStream(e))return e;if(e&&a.isString(e)&&(n&&!this.responseType||s)){const o=!(t&&t.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(c){if(o)throw c.name==="SyntaxError"?g.from(c,g.ERR_BAD_RESPONSE,this,null,this.response):c}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:C.classes.FormData,Blob:C.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],r=>{K.headers[r]={}});const Er=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ar=r=>{const e={};let t,n,s;return r&&r.split(`
`).forEach(function(o){s=o.indexOf(":"),t=o.substring(0,s).trim().toLowerCase(),n=o.substring(s+1).trim(),!(!t||e[t]&&Er[t])&&(t==="set-cookie"?e[t]?e[t].push(n):e[t]=[n]:e[t]=e[t]?e[t]+", "+n:n)}),e},De=Symbol("internals");function H(r){return r&&String(r).trim().toLowerCase()}function ee(r){return r===!1||r==null?r:a.isArray(r)?r.map(ee):String(r)}function br(r){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=t.exec(r);)e[n[1]]=n[2];return e}const Sr=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function de(r,e,t,n,s){if(a.isFunction(n))return n.call(this,e,t);if(s&&(e=t),!!a.isString(e)){if(a.isString(n))return e.indexOf(n)!==-1;if(a.isRegExp(n))return n.test(e)}}function Rr(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function Tr(r,e){const t=a.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(r,n+t,{value:function(s,i,o){return this[n].call(this,e,s,i,o)},configurable:!0})})}let k=class{constructor(e){e&&this.set(e)}set(e,t,n){const s=this;function i(c,d,u){const l=H(d);if(!l)throw new Error("header name must be a non-empty string");const f=a.findKey(s,l);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||d]=ee(c))}const o=(c,d)=>a.forEach(c,(u,l)=>i(u,l,d));if(a.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(a.isString(e)&&(e=e.trim())&&!Sr(e))o(Ar(e),t);else if(a.isObject(e)&&a.isIterable(e)){let c={},d,u;for(const l of e){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[u=l[0]]=(d=c[u])?a.isArray(d)?[...d,l[1]]:[d,l[1]]:l[1]}o(c,t)}else e!=null&&i(t,e,n);return this}get(e,t){if(e=H(e),e){const n=a.findKey(this,e);if(n){const s=this[n];if(!t)return s;if(t===!0)return br(s);if(a.isFunction(t))return t.call(this,s,n);if(a.isRegExp(t))return t.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=H(e),e){const n=a.findKey(this,e);return!!(n&&this[n]!==void 0&&(!t||de(this,this[n],n,t)))}return!1}delete(e,t){const n=this;let s=!1;function i(o){if(o=H(o),o){const c=a.findKey(n,o);c&&(!t||de(n,n[c],c,t))&&(delete n[c],s=!0)}}return a.isArray(e)?e.forEach(i):i(e),s}clear(e){const t=Object.keys(this);let n=t.length,s=!1;for(;n--;){const i=t[n];(!e||de(this,this[i],i,e,!0))&&(delete this[i],s=!0)}return s}normalize(e){const t=this,n={};return a.forEach(this,(s,i)=>{const o=a.findKey(n,i);if(o){t[o]=ee(s),delete t[i];return}const c=e?Rr(i):String(i).trim();c!==i&&delete t[i],t[c]=ee(s),n[c]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return a.forEach(this,(n,s)=>{n!=null&&n!==!1&&(t[s]=e&&a.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(s=>n.set(s)),n}static accessor(e){const n=(this[De]=this[De]={accessors:{}}).accessors,s=this.prototype;function i(o){const c=H(o);n[c]||(Tr(s,o),n[c]=!0)}return a.isArray(e)?e.forEach(i):i(e),this}};k.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(k.prototype,({value:r},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>r,set(n){this[t]=n}}});a.freezeMethods(k);function fe(r,e){const t=this||K,n=e||t,s=k.from(n.headers);let i=n.data;return a.forEach(r,function(c){i=c.call(t,i,s.normalize(),e?e.status:void 0)}),s.normalize(),i}function it(r){return!!(r&&r.__CANCEL__)}function V(r,e,t){g.call(this,r??"canceled",g.ERR_CANCELED,e,t),this.name="CanceledError"}a.inherits(V,g,{__CANCEL__:!0});function ot(r,e,t){const n=t.config.validateStatus;!t.status||!n||n(t.status)?r(t):e(new g("Request failed with status code "+t.status,[g.ERR_BAD_REQUEST,g.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function Cr(r){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return e&&e[1]||""}function Or(r,e){r=r||10;const t=new Array(r),n=new Array(r);let s=0,i=0,o;return e=e!==void 0?e:1e3,function(d){const u=Date.now(),l=n[i];o||(o=u),t[s]=d,n[s]=u;let f=i,w=0;for(;f!==s;)w+=t[f++],f=f%r;if(s=(s+1)%r,s===i&&(i=(i+1)%r),u-o<e)return;const E=l&&u-l;return E?Math.round(w*1e3/E):void 0}}function xr(r,e){let t=0,n=1e3/e,s,i;const o=(u,l=Date.now())=>{t=l,s=null,i&&(clearTimeout(i),i=null),r(...u)};return[(...u)=>{const l=Date.now(),f=l-t;f>=n?o(u,l):(s=u,i||(i=setTimeout(()=>{i=null,o(s)},n-f)))},()=>s&&o(s)]}const re=(r,e,t=3)=>{let n=0;const s=Or(50,250);return xr(i=>{const o=i.loaded,c=i.lengthComputable?i.total:void 0,d=o-n,u=s(d),l=o<=c;n=o;const f={loaded:o,total:c,progress:c?o/c:void 0,bytes:d,rate:u||void 0,estimated:u&&c&&l?(c-o)/u:void 0,event:i,lengthComputable:c!=null,[e?"download":"upload"]:!0};r(f)},t)},Fe=(r,e)=>{const t=r!=null;return[n=>e[0]({lengthComputable:t,total:r,loaded:n}),e[1]]},ve=r=>(...e)=>a.asap(()=>r(...e)),kr=C.hasStandardBrowserEnv?((r,e)=>t=>(t=new URL(t,C.origin),r.protocol===t.protocol&&r.host===t.host&&(e||r.port===t.port)))(new URL(C.origin),C.navigator&&/(msie|trident)/i.test(C.navigator.userAgent)):()=>!0,Lr=C.hasStandardBrowserEnv?{write(r,e,t,n,s,i){const o=[r+"="+encodeURIComponent(e)];a.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),a.isString(n)&&o.push("path="+n),a.isString(s)&&o.push("domain="+s),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(r){const e=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ur(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function Ir(r,e){return e?r.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):r}function at(r,e,t){let n=!Ur(e);return r&&(n||t==!1)?Ir(r,e):e}const Be=r=>r instanceof k?{...r}:r;function z(r,e){e=e||{};const t={};function n(u,l,f,w){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:w},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function s(u,l,f,w){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u,f,w)}else return n(u,l,f,w)}function i(u,l){if(!a.isUndefined(l))return n(void 0,l)}function o(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u)}else return n(void 0,l)}function c(u,l,f){if(f in e)return n(u,l);if(f in r)return n(void 0,u)}const d={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:c,headers:(u,l,f)=>s(Be(u),Be(l),f,!0)};return a.forEach(Object.keys({...r,...e}),function(l){const f=d[l]||s,w=f(r[l],e[l],l);a.isUndefined(w)&&f!==c||(t[l]=w)}),t}const ct=r=>{const e=z({},r);let{data:t,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:i,headers:o,auth:c}=e;e.headers=o=k.from(o),e.url=rt(at(e.baseURL,e.url,e.allowAbsoluteUrls),r.params,r.paramsSerializer),c&&o.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let d;if(a.isFormData(t)){if(C.hasStandardBrowserEnv||C.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((d=o.getContentType())!==!1){const[u,...l]=d?d.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...l].join("; "))}}if(C.hasStandardBrowserEnv&&(n&&a.isFunction(n)&&(n=n(e)),n||n!==!1&&kr(e.url))){const u=s&&i&&Lr.read(i);u&&o.set(s,u)}return e},Nr=typeof XMLHttpRequest<"u",_r=Nr&&function(r){return new Promise(function(t,n){const s=ct(r);let i=s.data;const o=k.from(s.headers).normalize();let{responseType:c,onUploadProgress:d,onDownloadProgress:u}=s,l,f,w,E,h;function m(){E&&E(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function A(){if(!p)return;const T=k.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),O={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:T,config:r,request:p};ot(function(v){t(v),m()},function(v){n(v),m()},O),p=null}"onloadend"in p?p.onloadend=A:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(A)},p.onabort=function(){p&&(n(new g("Request aborted",g.ECONNABORTED,r,p)),p=null)},p.onerror=function(){n(new g("Network Error",g.ERR_NETWORK,r,p)),p=null},p.ontimeout=function(){let _=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const O=s.transitional||nt;s.timeoutErrorMessage&&(_=s.timeoutErrorMessage),n(new g(_,O.clarifyTimeoutError?g.ETIMEDOUT:g.ECONNABORTED,r,p)),p=null},i===void 0&&o.setContentType(null),"setRequestHeader"in p&&a.forEach(o.toJSON(),function(_,O){p.setRequestHeader(O,_)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),u&&([w,h]=re(u,!0),p.addEventListener("progress",w)),d&&p.upload&&([f,E]=re(d),p.upload.addEventListener("progress",f),p.upload.addEventListener("loadend",E)),(s.cancelToken||s.signal)&&(l=T=>{p&&(n(!T||T.type?new V(null,r,p):T),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const R=Cr(s.url);if(R&&C.protocols.indexOf(R)===-1){n(new g("Unsupported protocol "+R+":",g.ERR_BAD_REQUEST,r));return}p.send(i||null)})},Pr=(r,e)=>{const{length:t}=r=r?r.filter(Boolean):[];if(e||t){let n=new AbortController,s;const i=function(u){if(!s){s=!0,c();const l=u instanceof Error?u:this.reason;n.abort(l instanceof g?l:new V(l instanceof Error?l.message:l))}};let o=e&&setTimeout(()=>{o=null,i(new g(`timeout ${e} of ms exceeded`,g.ETIMEDOUT))},e);const c=()=>{r&&(o&&clearTimeout(o),o=null,r.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),r=null)};r.forEach(u=>u.addEventListener("abort",i));const{signal:d}=n;return d.unsubscribe=()=>a.asap(c),d}},Dr=function*(r,e){let t=r.byteLength;if(t<e){yield r;return}let n=0,s;for(;n<t;)s=n+e,yield r.slice(n,s),n=s},Fr=async function*(r,e){for await(const t of vr(r))yield*Dr(t,e)},vr=async function*(r){if(r[Symbol.asyncIterator]){yield*r;return}const e=r.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},Me=(r,e,t,n)=>{const s=Fr(r,e);let i=0,o,c=d=>{o||(o=!0,n&&n(d))};return new ReadableStream({async pull(d){try{const{done:u,value:l}=await s.next();if(u){c(),d.close();return}let f=l.byteLength;if(t){let w=i+=f;t(w)}d.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(d){return c(d),s.return()}},{highWaterMark:2})},ue=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ut=ue&&typeof ReadableStream=="function",Br=ue&&(typeof TextEncoder=="function"?(r=>e=>r.encode(e))(new TextEncoder):async r=>new Uint8Array(await new Response(r).arrayBuffer())),lt=(r,...e)=>{try{return!!r(...e)}catch{return!1}},Mr=ut&&lt(()=>{let r=!1;const e=new Request(C.origin,{body:new ReadableStream,method:"POST",get duplex(){return r=!0,"half"}}).headers.has("Content-Type");return r&&!e}),$e=64*1024,Ae=ut&&lt(()=>a.isReadableStream(new Response("").body)),ne={stream:Ae&&(r=>r.body)};ue&&(r=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ne[e]&&(ne[e]=a.isFunction(r[e])?t=>t[e]():(t,n)=>{throw new g(`Response type '${e}' is not supported`,g.ERR_NOT_SUPPORT,n)})})})(new Response);const $r=async r=>{if(r==null)return 0;if(a.isBlob(r))return r.size;if(a.isSpecCompliantForm(r))return(await new Request(C.origin,{method:"POST",body:r}).arrayBuffer()).byteLength;if(a.isArrayBufferView(r)||a.isArrayBuffer(r))return r.byteLength;if(a.isURLSearchParams(r)&&(r=r+""),a.isString(r))return(await Br(r)).byteLength},zr=async(r,e)=>{const t=a.toFiniteNumber(r.getContentLength());return t??$r(e)},jr=ue&&(async r=>{let{url:e,method:t,data:n,signal:s,cancelToken:i,timeout:o,onDownloadProgress:c,onUploadProgress:d,responseType:u,headers:l,withCredentials:f="same-origin",fetchOptions:w}=ct(r);u=u?(u+"").toLowerCase():"text";let E=Pr([s,i&&i.toAbortSignal()],o),h;const m=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let p;try{if(d&&Mr&&t!=="get"&&t!=="head"&&(p=await zr(l,n))!==0){let O=new Request(e,{method:"POST",body:n,duplex:"half"}),F;if(a.isFormData(n)&&(F=O.headers.get("content-type"))&&l.setContentType(F),O.body){const[v,Y]=Fe(p,re(ve(d)));n=Me(O.body,$e,v,Y)}}a.isString(f)||(f=f?"include":"omit");const A="credentials"in Request.prototype;h=new Request(e,{...w,signal:E,method:t.toUpperCase(),headers:l.normalize().toJSON(),body:n,duplex:"half",credentials:A?f:void 0});let R=await fetch(h,w);const T=Ae&&(u==="stream"||u==="response");if(Ae&&(c||T&&m)){const O={};["status","statusText","headers"].forEach(Ue=>{O[Ue]=R[Ue]});const F=a.toFiniteNumber(R.headers.get("content-length")),[v,Y]=c&&Fe(F,re(ve(c),!0))||[];R=new Response(Me(R.body,$e,v,()=>{Y&&Y(),m&&m()}),O)}u=u||"text";let _=await ne[a.findKey(ne,u)||"text"](R,r);return!T&&m&&m(),await new Promise((O,F)=>{ot(O,F,{data:_,headers:k.from(R.headers),status:R.status,statusText:R.statusText,config:r,request:h})})}catch(A){throw m&&m(),A&&A.name==="TypeError"&&/Load failed|fetch/i.test(A.message)?Object.assign(new g("Network Error",g.ERR_NETWORK,r,h),{cause:A.cause||A}):g.from(A,A&&A.code,r,h)}}),be={http:nr,xhr:_r,fetch:jr};a.forEach(be,(r,e)=>{if(r){try{Object.defineProperty(r,"name",{value:e})}catch{}Object.defineProperty(r,"adapterName",{value:e})}});const ze=r=>`- ${r}`,qr=r=>a.isFunction(r)||r===null||r===!1,ht={getAdapter:r=>{r=a.isArray(r)?r:[r];const{length:e}=r;let t,n;const s={};for(let i=0;i<e;i++){t=r[i];let o;if(n=t,!qr(t)&&(n=be[(o=String(t)).toLowerCase()],n===void 0))throw new g(`Unknown adapter '${o}'`);if(n)break;s[o||"#"+i]=n}if(!n){const i=Object.entries(s).map(([c,d])=>`adapter ${c} `+(d===!1?"is not supported by the environment":"is not available in the build"));let o=e?i.length>1?`since :
`+i.map(ze).join(`
`):" "+ze(i[0]):"as no adapter specified";throw new g("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return n},adapters:be};function pe(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new V(null,r)}function je(r){return pe(r),r.headers=k.from(r.headers),r.data=fe.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),ht.getAdapter(r.adapter||K.adapter)(r).then(function(n){return pe(r),n.data=fe.call(r,r.transformResponse,n),n.headers=k.from(n.headers),n},function(n){return it(n)||(pe(r),n&&n.response&&(n.response.data=fe.call(r,r.transformResponse,n.response),n.response.headers=k.from(n.response.headers))),Promise.reject(n)})}const dt="1.11.0",le={};["object","boolean","number","function","string","symbol"].forEach((r,e)=>{le[r]=function(n){return typeof n===r||"a"+(e<1?"n ":" ")+r}});const qe={};le.transitional=function(e,t,n){function s(i,o){return"[Axios v"+dt+"] Transitional option '"+i+"'"+o+(n?". "+n:"")}return(i,o,c)=>{if(e===!1)throw new g(s(o," has been removed"+(t?" in "+t:"")),g.ERR_DEPRECATED);return t&&!qe[o]&&(qe[o]=!0,console.warn(s(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(i,o,c):!0}};le.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function Vr(r,e,t){if(typeof r!="object")throw new g("options must be an object",g.ERR_BAD_OPTION_VALUE);const n=Object.keys(r);let s=n.length;for(;s-- >0;){const i=n[s],o=e[i];if(o){const c=r[i],d=c===void 0||o(c,i,r);if(d!==!0)throw new g("option "+i+" must be "+d,g.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new g("Unknown option "+i,g.ERR_BAD_OPTION)}}const te={assertOptions:Vr,validators:le},N=te.validators;let $=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Pe,response:new Pe}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const i=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?i&&!String(n.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+i):n.stack=i}catch{}}throw n}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=z(this.defaults,t);const{transitional:n,paramsSerializer:s,headers:i}=t;n!==void 0&&te.assertOptions(n,{silentJSONParsing:N.transitional(N.boolean),forcedJSONParsing:N.transitional(N.boolean),clarifyTimeoutError:N.transitional(N.boolean)},!1),s!=null&&(a.isFunction(s)?t.paramsSerializer={serialize:s}:te.assertOptions(s,{encode:N.function,serialize:N.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),te.assertOptions(t,{baseUrl:N.spelling("baseURL"),withXsrfToken:N.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&a.merge(i.common,i[t.method]);i&&a.forEach(["delete","get","head","post","put","patch","common"],h=>{delete i[h]}),t.headers=k.concat(o,i);const c=[];let d=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(t)===!1||(d=d&&m.synchronous,c.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let l,f=0,w;if(!d){const h=[je.bind(this),void 0];for(h.unshift(...c),h.push(...u),w=h.length,l=Promise.resolve(t);f<w;)l=l.then(h[f++],h[f++]);return l}w=c.length;let E=t;for(f=0;f<w;){const h=c[f++],m=c[f++];try{E=h(E)}catch(p){m.call(this,p);break}}try{l=je.call(this,E)}catch(h){return Promise.reject(h)}for(f=0,w=u.length;f<w;)l=l.then(u[f++],u[f++]);return l}getUri(e){e=z(this.defaults,e);const t=at(e.baseURL,e.url,e.allowAbsoluteUrls);return rt(t,e.params,e.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(e){$.prototype[e]=function(t,n){return this.request(z(n||{},{method:e,url:t,data:(n||{}).data}))}});a.forEach(["post","put","patch"],function(e){function t(n){return function(i,o,c){return this.request(z(c||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}$.prototype[e]=t(),$.prototype[e+"Form"]=t(!0)});let Hr=class ft{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(i){t=i});const n=this;this.promise.then(s=>{if(!n._listeners)return;let i=n._listeners.length;for(;i-- >0;)n._listeners[i](s);n._listeners=null}),this.promise.then=s=>{let i;const o=new Promise(c=>{n.subscribe(c),i=c}).then(s);return o.cancel=function(){n.unsubscribe(i)},o},e(function(i,o,c){n.reason||(n.reason=new V(i,o,c),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=n=>{e.abort(n)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new ft(function(s){e=s}),cancel:e}}};function Wr(r){return function(t){return r.apply(null,t)}}function Jr(r){return a.isObject(r)&&r.isAxiosError===!0}const Se={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Se).forEach(([r,e])=>{Se[e]=r});function pt(r){const e=new $(r),t=Ve($.prototype.request,e);return a.extend(t,$.prototype,e,{allOwnKeys:!0}),a.extend(t,e,null,{allOwnKeys:!0}),t.create=function(s){return pt(z(r,s))},t}const b=pt(K);b.Axios=$;b.CanceledError=V;b.CancelToken=Hr;b.isCancel=it;b.VERSION=dt;b.toFormData=ce;b.AxiosError=g;b.Cancel=b.CanceledError;b.all=function(e){return Promise.all(e)};b.spread=Wr;b.isAxiosError=Jr;b.mergeConfig=z;b.AxiosHeaders=k;b.formToJSON=r=>st(a.isHTMLForm(r)?new FormData(r):r);b.getAdapter=ht.getAdapter;b.HttpStatusCode=Se;b.default=b;const{Axios:un,AxiosError:ln,CanceledError:hn,isCancel:dn,CancelToken:fn,VERSION:pn,all:mn,Cancel:gn,isAxiosError:yn,spread:wn,toFormData:En,AxiosHeaders:An,HttpStatusCode:bn,formToJSON:Sn,getAdapter:Rn,mergeConfig:Tn}=b;class ke extends Error{constructor(t,n,s){super(t);S(this,"statusCode");S(this,"response");this.name="ApiError",this.statusCode=n,this.response=s}}class mt extends ke{constructor(e="Authentication failed"){super(e,401),this.name="AuthenticationError"}}class Xr extends ke{constructor(e="Network error"){super(e,0),this.name="NetworkError"}}var U=(r=>(r.LOGIN_START="login_start",r.LOGIN_SUCCESS="login_success",r.LOGIN_FAILURE="login_failure",r.LOGOUT="logout",r.TOKEN_REFRESH="token_refresh",r.TOKEN_EXPIRED="token_expired",r.AUTH_ERROR="auth_error",r))(U||{});class Gr{constructor(){S(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});S(this,"listeners",[]);S(this,"d365Context",null)}async initialize(){this.setLoading(!0);try{await this.waitForDynamicsContext();const e=await this.getDynamicsUser();if(e)this.authState={user:e,isAuthenticated:!0,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGIN_SUCCESS,{user:e}),y.logger.info("Dynamics 365 authentication initialized successfully");else throw new Error("Failed to get user information from Dynamics 365")}catch(e){const t=e instanceof Error?e.message:"Unknown authentication error";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:t,token:null},this.emitAuthEvent(U.AUTH_ERROR,{error:t}),y.logger.error("Dynamics 365 authentication initialization failed:",e)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(e){try{return this.authState.isAuthenticated&&this.authState.user?{success:!0,user:this.authState.user,token:this.authState.token||void 0}:(await this.initialize(),{success:this.authState.isAuthenticated,user:this.authState.user||void 0,error:this.authState.error||void 0})}catch(t){return{success:!1,error:t instanceof Error?t.message:"Login failed"}}}async logout(){this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGOUT),this.notifyListeners(),y.logger.info("Dynamics 365 authentication state cleared")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{return await this.getDynamicsUser()}catch(e){return y.logger.error("Failed to get current user:",e),null}}async getAccessToken(){return null}async refreshToken(){return this.authState.isAuthenticated}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(e){return this.listeners.push(e),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}async waitForDynamicsContext(e=1e4){return new Promise((t,n)=>{const s=Date.now(),i=()=>{if(this.isDynamicsContextAvailable()){t();return}if(Date.now()-s>e){n(new Error("Timeout waiting for Dynamics 365 context"));return}setTimeout(i,100)};i()})}isDynamicsContextAvailable(){try{return!!window.Xrm&&!!window.Xrm.WebApi}catch{return!1}}async getDynamicsUser(){try{const e=window.Xrm;if(!e||!e.WebApi)throw new Error("Dynamics 365 context not available");const t=await e.WebApi.retrieveRecord("systemuser",e.Utility.getGlobalContext().getUserId(),"?$select=systemuserid,fullname,internalemailaddress"),n=await e.WebApi.retrieveMultipleRecords("role","?$filter=_parentroleid_value eq null&$select=name"),s=e.Utility.getGlobalContext().getOrganizationSettings();return this.d365Context={userId:t.systemuserid,userRoles:n.entities.map(i=>i.name),organizationId:s.organizationId,organizationName:s.uniqueName,serverUrl:e.Utility.getGlobalContext().getClientUrl(),version:e.Utility.getGlobalContext().getVersion()},{id:t.systemuserid,email:t.internalemailaddress,name:t.fullname,roles:this.d365Context.userRoles,organizationId:this.d365Context.organizationId}}catch(e){throw y.logger.error("Failed to get Dynamics 365 user information:",e),e}}setLoading(e){this.authState.isLoading=e,this.notifyListeners()}notifyListeners(){this.listeners.forEach(e=>{try{e(this.getAuthState())}catch(t){y.logger.error("Error in auth state listener:",t)}})}emitAuthEvent(e,t){const n={event:e,timestamp:new Date,...t};y.logger.debug("Auth event emitted:",n)}getDynamics365Context(){return this.d365Context}}class Kr{constructor(e){S(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});S(this,"listeners",[]);S(this,"msalInstance",null);S(this,"config");S(this,"tokenRefreshTimer",null);this.config=e}async initialize(){this.setLoading(!0);try{await this.initializeMSAL(),await this.checkExistingAuth(),y.logger.info("MSAL authentication initialized successfully")}catch(e){const t=e instanceof Error?e.message:"MSAL initialization failed";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:t,token:null},this.emitAuthEvent(U.AUTH_ERROR,{error:t}),y.logger.error("MSAL authentication initialization failed:",e)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(e){this.setLoading(!0),this.emitAuthEvent(U.LOGIN_START);try{if(!this.msalInstance)throw new Error("MSAL not initialized");const t={scopes:this.config.scopes,prompt:e!=null&&e.interactive?"select_account":void 0};let n;try{n=await this.msalInstance.acquireTokenSilent(t)}catch{n=await this.msalInstance.loginPopup(t)}const s=await this.processAuthResult(n);if(s)return this.authState={user:s,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(n)},this.startTokenRefreshTimer(),this.emitAuthEvent(U.LOGIN_SUCCESS,{user:s}),{success:!0,user:s,token:this.authState.token||void 0};throw new Error("Failed to process authentication result")}catch(t){const n=t instanceof Error?t.message:"Login failed";return this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:n,token:null},this.emitAuthEvent(U.LOGIN_FAILURE,{error:n}),{success:!1,error:n}}finally{this.notifyListeners()}}async logout(){try{this.tokenRefreshTimer&&(clearTimeout(this.tokenRefreshTimer),this.tokenRefreshTimer=null),this.msalInstance&&await this.msalInstance.logout({postLogoutRedirectUri:this.config.redirectUri})}catch(e){y.logger.error("Logout error:",e)}this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGOUT),this.notifyListeners(),y.logger.info("User logged out successfully")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{if(!this.msalInstance)return null;const e=this.msalInstance.getActiveAccount();return e?this.createUserFromAccount(e):null}catch(e){return y.logger.error("Failed to get current user:",e),null}}async getAccessToken(){try{if(!this.msalInstance)return null;if(this.authState.token&&this.authState.token.expiresAt>new Date)return this.authState.token.accessToken;const e={scopes:this.config.scopes,account:this.msalInstance.getActiveAccount()},t=await this.msalInstance.acquireTokenSilent(e);return t?(this.authState.token=this.createTokenFromAuthResult(t),this.notifyListeners(),t.accessToken):null}catch(e){return y.logger.error("Failed to get access token:",e),this.emitAuthEvent(U.TOKEN_EXPIRED),null}}async refreshToken(){try{return await this.getAccessToken()?(this.emitAuthEvent(U.TOKEN_REFRESH),!0):!1}catch(e){return y.logger.error("Token refresh failed:",e),!1}}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(e){return this.listeners.push(e),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}async initializeMSAL(){try{const{PublicClientApplication:e}=await Promise.resolve().then(()=>require("./index-DAOl4Ulu.js")),t={auth:{clientId:this.config.clientId,authority:this.config.authority,redirectUri:this.config.redirectUri},cache:{cacheLocation:this.config.cacheLocation||"localStorage",storeAuthStateInCookie:!1}};this.msalInstance=new e(t),await this.msalInstance.initialize()}catch(e){if(e instanceof Error&&e.message.includes("Failed to resolve module"))y.logger.warn("MSAL library not available, using mock implementation"),this.msalInstance=this.createMockMSALInstance();else throw e}}async checkExistingAuth(){if(this.msalInstance)try{const e=this.msalInstance.getAllAccounts();if(e.length>0){this.msalInstance.setActiveAccount(e[0]);const t={scopes:this.config.scopes,account:e[0]},n=await this.msalInstance.acquireTokenSilent(t);if(n){const s=this.createUserFromAccount(e[0]);this.authState={user:s,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(n)},this.startTokenRefreshTimer()}}}catch(e){y.logger.debug("No existing valid authentication found:",e)}}async processAuthResult(e){var t;return!e||!e.account?null:((t=this.msalInstance)==null||t.setActiveAccount(e.account),this.createUserFromAccount(e.account))}createUserFromAccount(e){return{id:e.homeAccountId||e.localAccountId,email:e.username,name:e.name||e.username,roles:[],tenantId:e.tenantId}}createTokenFromAuthResult(e){return{accessToken:e.accessToken,expiresAt:new Date(e.expiresOn),scopes:e.scopes||this.config.scopes}}startTokenRefreshTimer(){if(this.tokenRefreshTimer&&clearTimeout(this.tokenRefreshTimer),this.authState.token){const e=this.authState.token.expiresAt.getTime()-Date.now()-3e5;e>0&&(this.tokenRefreshTimer=setTimeout(()=>{this.refreshToken()},e))}}setLoading(e){this.authState.isLoading=e,this.notifyListeners()}notifyListeners(){this.listeners.forEach(e=>{try{e(this.getAuthState())}catch(t){y.logger.error("Error in auth state listener:",t)}})}emitAuthEvent(e,t){const n={event:e,timestamp:new Date,...t};y.logger.debug("Auth event emitted:",n)}createMockMSALInstance(){return{initialize:async()=>{},loginPopup:async()=>({account:{username:"<EMAIL>",name:"Mock User"},accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),loginRedirect:async()=>{},logout:async()=>{},acquireTokenSilent:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),acquireTokenPopup:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),getAllAccounts:()=>[],getActiveAccount:()=>null,setActiveAccount:()=>{}}}}const me={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1};var j=(r=>(r.WEB_RESOURCE="web_resource",r.STANDALONE="standalone",r))(j||{});const B=class B{constructor(){S(this,"_detectedMode",null);S(this,"_config",null)}static getInstance(){return B._instance||(B._instance=new B),B._instance}detectDeploymentMode(){if(this._detectedMode)return this._detectedMode;const e=this.isDynamics365Environment(),t=this.getConfigurationOverride();return t?this._detectedMode=t:this._detectedMode=e?"web_resource":"standalone",this._detectedMode}isDynamics365Environment(){try{if(typeof window<"u"){if(window.Xrm&&window.Xrm.WebApi)return!0;if(window.parent&&window.parent!==window)try{if(window.parent.Xrm)return!0}catch{}const e=window.location.href;if(e.includes(".dynamics.com")||e.includes(".crm.dynamics.com")||e.includes("orgname.crm"))return!0;const t=new URLSearchParams(window.location.search);if(t.has("orgname")||t.has("appid"))return!0}return!1}catch(e){return console.warn("Error detecting Dynamics 365 environment:",e),!1}}getConfigurationOverride(){try{const e=this.getEnvironmentVariable("VITE_DEPLOYMENT_MODE");if(e)return e;if(typeof window<"u"){const n=new URLSearchParams(window.location.search).get("deploymentMode");if(n&&Object.values(j).includes(n))return n}return null}catch(e){return console.warn("Error getting configuration override:",e),null}}getEnvironmentVariable(e){try{return(me==null?void 0:me[e])||null}catch{return null}}getDeploymentConfig(){if(this._config)return this._config;const e=this.detectDeploymentMode();return this._config=this.createConfigForMode(e),this._config}createConfigForMode(e){const t={mode:e,features:{enableLogging:this.getEnvironmentVariable("VITE_ENABLE_LOGGING")==="true"||e==="standalone",enableOfflineMode:this.getEnvironmentVariable("VITE_ENABLE_OFFLINE")==="true"||!1,enableTelemetry:this.getEnvironmentVariable("VITE_ENABLE_TELEMETRY")==="true"||!1}};return e==="web_resource"?{...t,apiBaseUrl:"",authMethod:"dynamics365",dynamicsConfig:{serverUrl:this.getEnvironmentVariable("VITE_DYNAMICS_SERVER_URL")||"",version:this.getEnvironmentVariable("VITE_DYNAMICS_API_VERSION")||"9.2"}}:{...t,apiBaseUrl:this.getEnvironmentVariable("VITE_API_BASE_URL")||"https://your-org.api.crm.dynamics.com/api/data/v9.2",authMethod:"msal",msalConfig:{clientId:this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID")||"",authority:this.getEnvironmentVariable("VITE_MSAL_AUTHORITY")||"https://login.microsoftonline.com/common",redirectUri:this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI")||window.location.origin,scopes:(this.getEnvironmentVariable("VITE_MSAL_SCOPES")||"https://your-org.crm.dynamics.com/.default").split(",")}}}forceDeploymentMode(e){this._detectedMode=e,this._config=null}reset(){this._detectedMode=null,this._config=null}};S(B,"_instance");let se=B;function Q(){return se.getInstance().getDeploymentConfig()}function Qr(){return Q().mode==="web_resource"}function Yr(){return Q().mode==="standalone"}const P=class P{static getInstance(){return P._instance||(P._instance=P.createAuthService()),P._instance}static createAuthService(){const e=Q();switch(y.logger.info(`Creating authentication service for ${e.mode} deployment mode`),e.mode){case j.WEB_RESOURCE:return new Gr;case j.STANDALONE:if(!e.msalConfig)throw new Error("MSAL configuration is required for standalone deployment mode");return new Kr(e.msalConfig);default:throw new Error(`Unsupported deployment mode: ${e.mode}`)}}static reset(){P._instance=null}static forceInstance(e){P._instance=e}};S(P,"_instance",null);let Re=P;function he(){return Re.getInstance()}const ge={};class Le{constructor(e){S(this,"client");S(this,"authService",he());this.client=b.create({baseURL:e.baseURL,timeout:e.timeout||3e4,headers:{"Content-Type":"application/json","OData-MaxVersion":"4.0","OData-Version":"4.0",Accept:"application/json",Prefer:"return=representation",...e.headers}}),this.setupInterceptors()}async initialize(){try{await this.authService.initialize(),y.logger.info("External API client initialized successfully")}catch(e){throw y.logger.error("Failed to initialize external API client:",e),e}}setupInterceptors(){this.client.interceptors.request.use(async e=>{var n;y.logger.info(`API Request: ${(n=e.method)==null?void 0:n.toUpperCase()} ${e.url}`);const t=await this.getAuthToken();return t&&(e.headers=e.headers||{},e.headers.Authorization=`Bearer ${t}`),e},e=>(y.logger.error("API Request Error:",e),Promise.reject(this.createApiError(e)))),this.client.interceptors.response.use(e=>(y.logger.info(`API Response: ${e.status} ${e.config.url}`),e),async e=>{var t;if(y.logger.error("API Response Error:",e),((t=e.response)==null?void 0:t.status)===401&&await this.handleUnauthorized()&&e.config){const s=await this.getAuthToken();if(s)return e.config.headers.Authorization=`Bearer ${s}`,this.client.request(e.config)}return Promise.reject(this.createApiError(e))})}async getAuthToken(){try{return await this.authService.getAccessToken()}catch(e){return y.logger.error("Failed to get auth token:",e),null}}async handleUnauthorized(){try{return await this.authService.refreshToken()?(y.logger.info("Token refreshed successfully"),!0):(y.logger.warn("Token refresh failed, triggering re-authentication"),await this.authService.login({interactive:!0}),!0)}catch(e){return y.logger.error("Failed to handle unauthorized access:",e),!1}}async get(e,t){try{const n=this.convertConfig(t),s=await this.client.get(e,n);return{data:s.data,success:!0,statusCode:s.status}}catch(n){return this.handleError(n)}}async post(e,t,n){try{const s=this.convertConfig(n),i=await this.client.post(e,t,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async put(e,t,n){try{const s=this.convertConfig(n),i=await this.client.put(e,t,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async patch(e,t,n){try{const s=this.convertConfig(n),i=await this.client.patch(e,t,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async delete(e,t){try{const n=this.convertConfig(t),s=await this.client.delete(e,n);return{data:s.data,success:!0,statusCode:s.status}}catch(n){return this.handleError(n)}}async retrieveRecord(e,t,n){const s=this.buildQueryString(n),i=`${e}(${t})${s}`;return this.get(i)}async retrieveMultipleRecords(e,t){try{const n=this.buildQueryString(t),s=`${e}${n}`,i=await this.get(s);return i.success&&i.data?{data:i.data.value,success:!0,statusCode:i.statusCode,pagination:{page:Math.floor(((t==null?void 0:t.skip)||0)/((t==null?void 0:t.top)||50))+1,pageSize:i.data.value.length,totalCount:i.data["@odata.count"]||i.data.value.length,hasNext:!!i.data["@odata.nextLink"],hasPrevious:((t==null?void 0:t.skip)||0)>0}}:this.handleErrorPaginated(new Error("Invalid response format"))}catch(n){return this.handleErrorPaginated(n)}}async createRecord(e,t){return this.post(e,t)}async updateRecord(e,t,n){const s=`${e}(${t})`;return this.patch(s,n)}async deleteRecord(e,t){const n=`${e}(${t})`;return this.delete(n)}async executeFunction(e,t){let n=e;if(t){const s=Object.keys(t).map(i=>`${i}=${encodeURIComponent(t[i])}`).join(",");n+=`(${s})`}return this.get(n)}async executeBatch(e){try{const t=this.generateBatchId(),n=this.generateChangesetId(),s=this.buildBatchBody(e,t,n),i=await this.client.post("$batch",s,{headers:{"Content-Type":`multipart/mixed; boundary=batch_${t}`}}),o=this.parseBatchResponse(i.data);return{responses:o,success:o.every(c=>c.success)}}catch(t){return y.logger.error("Batch execution failed:",t),{responses:[],success:!1,errors:[t instanceof Error?t.message:"Batch execution failed"]}}}convertConfig(e){return e?{headers:e.headers,timeout:e.timeout}:{}}buildQueryString(e){if(!e)return"";const t=[];return e.select&&t.push(`$select=${e.select.join(",")}`),e.filter&&t.push(`$filter=${encodeURIComponent(e.filter)}`),e.orderBy&&t.push(`$orderby=${encodeURIComponent(e.orderBy)}`),e.expand&&t.push(`$expand=${e.expand.join(",")}`),e.top&&t.push(`$top=${e.top}`),e.skip&&t.push(`$skip=${e.skip}`),t.length>0?`?${t.join("&")}`:""}generateBatchId(){return Math.random().toString(36).substring(2,15)}generateChangesetId(){return Math.random().toString(36).substring(2,15)}buildBatchBody(e,t,n){return e.map(s=>`--batch_${t}
Content-Type: application/http

${s.method} ${s.url} HTTP/1.1

`).join("")}parseBatchResponse(e){return[]}createApiError(e){var t,n,s,i,o,c;return e.code==="ECONNABORTED"||e.code==="ENOTFOUND"?new Xr(e.message):((t=e.response)==null?void 0:t.status)===401?new mt(((n=e.response.data)==null?void 0:n.message)||"Authentication failed"):new ke(((i=(s=e.response)==null?void 0:s.data)==null?void 0:i.message)||e.message||"An error occurred",((o=e.response)==null?void 0:o.status)||500,(c=e.response)==null?void 0:c.data)}handleError(e){const t=this.createApiError(e);return{data:null,success:!1,message:t.message,statusCode:t.statusCode,errors:[t.message]}}handleErrorPaginated(e){return{...this.handleError(e),data:[],pagination:{page:1,pageSize:0,totalCount:0,hasNext:!1,hasPrevious:!1}}}}const Zr=()=>(ge==null?void 0:ge.VITE_API_BASE_URL)||"http://localhost:3001/api";new Le({baseURL:Zr()});class en{constructor(){S(this,"xrmWebApi",null)}async initialize(){try{if(await this.waitForXrmContext(),this.xrmWebApi=window.Xrm.WebApi,!this.xrmWebApi)throw new Error("Xrm.WebApi is not available");y.logger.info("Dynamics 365 API client initialized successfully")}catch(e){throw y.logger.error("Failed to initialize Dynamics 365 API client:",e),e}}async get(e,t){try{return this.ensureInitialized(),{data:await this.xrmWebApi.retrieveRecord("systemuser",e),success:!0}}catch(n){return this.handleError(n)}}async post(e,t,n){try{this.ensureInitialized();const s=this.extractEntityNameFromUrl(e);return{data:await this.xrmWebApi.createRecord(s,t),success:!0}}catch(s){return this.handleError(s)}}async put(e,t,n){try{this.ensureInitialized();const{entityName:s,id:i}=this.parseEntityUrl(e);return await this.xrmWebApi.updateRecord(s,i,t),{data:null,success:!0}}catch(s){return this.handleError(s)}}async patch(e,t,n){return this.put(e,t,n)}async delete(e,t){try{this.ensureInitialized();const{entityName:n,id:s}=this.parseEntityUrl(e);return await this.xrmWebApi.deleteRecord(n,s),{data:null,success:!0}}catch(n){return this.handleError(n)}}async retrieveRecord(e,t,n){try{this.ensureInitialized();const s=this.buildQueryString(n);return{data:await this.xrmWebApi.retrieveRecord(e,t,s),success:!0}}catch(s){return this.handleError(s)}}async retrieveMultipleRecords(e,t){try{this.ensureInitialized();const n=this.buildQueryString(t),s=await this.xrmWebApi.retrieveMultipleRecords(e,n);return{data:s.entities,success:!0,pagination:{page:1,pageSize:s.entities.length,totalCount:s.entities.length,hasNext:!!s["@odata.nextLink"],hasPrevious:!1}}}catch(n){return this.handleErrorPaginated(n)}}async createRecord(e,t){try{return this.ensureInitialized(),{data:await this.xrmWebApi.createRecord(e,t),success:!0}}catch(n){return this.handleError(n)}}async updateRecord(e,t,n){try{return this.ensureInitialized(),await this.xrmWebApi.updateRecord(e,t,n),{data:null,success:!0}}catch(s){return this.handleError(s)}}async deleteRecord(e,t){try{return this.ensureInitialized(),await this.xrmWebApi.deleteRecord(e,t),{data:void 0,success:!0}}catch(n){return this.handleError(n)}}async executeFunction(e,t){try{this.ensureInitialized();let n=e;if(t){const i=Object.keys(t).map(o=>`${o}=${encodeURIComponent(t[o])}`).join(",");n+=`(${i})`}return{data:await this.xrmWebApi.online.executeFunction(n),success:!0}}catch(n){return this.handleError(n)}}async executeBatch(e){try{this.ensureInitialized();const t=[];for(const n of e)try{let s;switch(n.method){case"GET":s=await this.get(n.url);break;case"POST":s=await this.post(n.url,n.data);break;case"PUT":s=await this.put(n.url,n.data);break;case"PATCH":s=await this.patch(n.url,n.data);break;case"DELETE":s=await this.delete(n.url);break;default:throw new Error(`Unsupported method: ${n.method}`)}t.push(s)}catch(s){t.push(this.handleError(s))}return{responses:t,success:t.every(n=>n.success)}}catch(t){return y.logger.error("Batch execution failed:",t),{responses:[],success:!1,errors:[t instanceof Error?t.message:"Batch execution failed"]}}}async waitForXrmContext(e=1e4){return new Promise((t,n)=>{const s=Date.now(),i=()=>{if(window.Xrm&&window.Xrm.WebApi){t();return}if(Date.now()-s>e){n(new Error("Timeout waiting for Dynamics 365 context"));return}setTimeout(i,100)};i()})}ensureInitialized(){if(!this.xrmWebApi)throw new mt("Dynamics 365 API client not initialized")}buildQueryString(e){if(!e)return"";const t=[];return e.select&&t.push(`$select=${e.select.join(",")}`),e.filter&&t.push(`$filter=${encodeURIComponent(e.filter)}`),e.orderBy&&t.push(`$orderby=${encodeURIComponent(e.orderBy)}`),e.expand&&t.push(`$expand=${e.expand.join(",")}`),e.top&&t.push(`$top=${e.top}`),e.skip&&t.push(`$skip=${e.skip}`),t.length>0?`?${t.join("&")}`:""}extractEntityNameFromUrl(e){const t=e.match(/\/([^\/\?]+)/);return t?t[1]:"systemuser"}parseEntityUrl(e){const t=e.match(/\/([^\/]+)\/([^\/\?]+)/);return{entityName:t?t[1]:"systemuser",id:t?t[2]:""}}handleError(e){y.logger.error("Dynamics 365 API error:",e);let t="An error occurred",n=500;return e&&e.message&&(t=e.message),e&&e.status&&(n=e.status),{data:null,success:!1,message:t,statusCode:n,errors:[t]}}handleErrorPaginated(e){return{...this.handleError(e),data:[],pagination:{page:1,pageSize:0,totalCount:0,hasNext:!1,hasPrevious:!1}}}}const D=class D{static async getInstance(){return D._instance||(D._instance=await D.createApiClient()),D._instance}static async createApiClient(){const e=Q();y.logger.info(`Creating API client for ${e.mode} deployment mode`);let t;switch(e.mode){case j.WEB_RESOURCE:t=new en;break;case j.STANDALONE:t=new Le({baseURL:e.apiBaseUrl,timeout:3e4,headers:{"OData-MaxVersion":"4.0","OData-Version":"4.0",Accept:"application/json",Prefer:"return=representation"}});break;default:throw new Error(`Unsupported deployment mode: ${e.mode}`)}return await t.initialize(),t}static reset(){D._instance=null}static forceInstance(e){D._instance=e}};S(D,"_instance",null);let Te=D;async function tn(){return Te.getInstance()}class rn{constructor(){S(this,"authService",he())}async initialize(){return this.authService.initialize()}getAuthState(){return this.authService.getAuthState()}async login(e){return await this.authService.login(e)}async logout(){return this.authService.logout()}async refreshToken(){return this.authService.refreshToken()}async getCurrentUser(){return this.authService.getCurrentUser()}async getAccessToken(){return this.authService.getAccessToken()}isAuthenticated(){return this.authService.isAuthenticated()}onAuthStateChanged(e){return this.authService.onAuthStateChanged(e)}}const nn=new rn,sn=()=>{const[r,e]=L.useState({user:null,isAuthenticated:!1,isLoading:!0,error:null,token:null}),t=he();L.useEffect(()=>{const E=(async()=>{try{await t.initialize();const h=t.onAuthStateChanged(m=>{e(m)});return e(t.getAuthState()),h}catch(h){y.logger.error("Auth initialization error:",h),e(m=>({...m,error:h instanceof Error?h.message:"Authentication initialization failed",isLoading:!1}))}})();return()=>{E.then(h=>{h&&h()})}},[t]);const n=L.useCallback(async w=>await t.login(w),[t]),s=L.useCallback(async()=>{await t.logout()},[t]),i=L.useCallback(async()=>await t.refreshToken(),[t]),o=L.useCallback(async()=>await t.getCurrentUser(),[t]),c=L.useCallback(async()=>await t.getAccessToken(),[t]),d=L.useCallback(()=>t.isAuthenticated(),[t]),u=L.useCallback(w=>t.onAuthStateChanged(w),[t]),l=L.useCallback(()=>t.getAuthState(),[t]),f=L.useCallback(async()=>t.initialize(),[t]);return{...r,login:n,logout:s,refreshToken:i,getCurrentUser:o,getAccessToken:c,checkAuthenticated:d,onAuthStateChanged:u,getAuthState:l,initialize:f}};exports.DeploymentContextDetector=se;exports.DeploymentMode=j;exports.ExternalApiClient=Le;exports.authService=nn;exports.getApiClient=tn;exports.getAuthService=he;exports.getDeploymentConfig=Q;exports.isStandaloneMode=Yr;exports.isWebResourceMode=Qr;exports.useAuth=sn;
