{"version": 3, "file": "AccountEntity.mjs", "sources": ["../../../src/cache/entities/AccountEntity.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.invalidCacheEnvironment"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAuBH;;;;;;;;;;;;;;;;;;;;;;AAsBG;MACU,aAAa,CAAA;AAiBtB;;AAEG;IACH,iBAAiB,GAAA;QACb,MAAM,SAAS,GAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACvE;AAED;;AAEG;IACH,kBAAkB,GAAA;QACd,OAAO,aAAa,CAAC,uBAAuB,CAAC;YACzC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;AACtC,SAAA,CAAC,CAAC;KACN;AAED;;AAEG;IACH,cAAc,GAAA;QACV,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;;AAEjC,YAAA,cAAc,EAAE,IAAI,GAAG,CACnB,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,aAAa,KAAI;AAC9C,gBAAA,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AACnD,aAAC,CAAC,CACL;SACJ,CAAC;KACL;AAED;;AAEG;IACH,cAAc,GAAA;AACV,QAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;KAC/B;AAED;;;AAGG;IACH,OAAO,uBAAuB,CAAC,gBAA6B,EAAA;AACxD,QAAA,MAAM,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,QAAA,MAAM,UAAU,GAAG;AACf,YAAA,gBAAgB,CAAC,aAAa;YAC9B,gBAAgB,CAAC,WAAW,IAAI,EAAE;AAClC,YAAA,YAAY,IAAI,gBAAgB,CAAC,QAAQ,IAAI,EAAE;SAClD,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACxE;AAED;;;AAGG;AACH,IAAA,OAAO,aAAa,CAChB,cASC,EACD,SAAoB,EACpB,YAAwC,EAAA;AAExC,QAAA,MAAM,OAAO,GAAkB,IAAI,aAAa,EAAE,CAAC;AAEnD,QAAA,IAAI,SAAS,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;AAChD,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,iBAAiB,CAAC;AAC9D,SAAA;AAAM,aAAA,IAAI,SAAS,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;AACpD,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;AAC/D,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,oBAAoB,CAAC;AACjE,SAAA;AAED,QAAA,IAAI,UAAkC,CAAC;AAEvC,QAAA,IAAI,cAAc,CAAC,UAAU,IAAI,YAAY,EAAE;YAC3C,UAAU,GAAG,eAAe,CACxB,cAAc,CAAC,UAAU,EACzB,YAAY,CACf,CAAC;AACL,SAAA;AAED,QAAA,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC/C,QAAA,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;AACrD,QAAA,OAAO,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;AAEzD,QAAA,MAAM,GAAG,GACL,cAAc,CAAC,WAAW;AAC1B,aAAC,SAAS,IAAI,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC,GAAG,EAAE;AACN,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;;AAE1B,QAAA,OAAO,CAAC,KAAK;AACT,YAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,4BAA4B,CAAC,cAAc,CAAC,aAAa,CAAC;AAC1D,gBAAA,EAAE,CAAC;;AAGP,QAAA,OAAO,CAAC,cAAc;AAClB,YAAA,UAAU,EAAE,GAAG;gBACf,cAAc,CAAC,aAAa,EAAE,GAAG;gBACjC,cAAc,CAAC,aAAa,EAAE,GAAG;AACjC,gBAAA,EAAE,CAAC;AAEP;;;;AAIG;AACH,QAAA,MAAM,iBAAiB,GACnB,cAAc,CAAC,aAAa,EAAE,kBAAkB;AAChD,YAAA,cAAc,CAAC,aAAa,EAAE,GAAG,CAAC;AACtC,QAAA,MAAM,KAAK,GAAG,cAAc,CAAC,aAAa,EAAE,MAAM;cAC5C,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;cACtC,IAAI,CAAC;QAEX,OAAO,CAAC,QAAQ,GAAG,iBAAiB,IAAI,KAAK,IAAI,EAAE,CAAC;QACpD,OAAO,CAAC,IAAI,GAAG,cAAc,CAAC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC;AAExD,QAAA,OAAO,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,QAAA,OAAO,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;QAEjD,IAAI,cAAc,CAAC,cAAc,EAAE;AAC/B,YAAA,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;AAC1D,SAAA;AAAM,aAAA;YACH,MAAM,aAAa,GAAG,kBAAkB,CACpC,cAAc,CAAC,aAAa,EAC5B,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,KAAK,EACb,cAAc,CAAC,aAAa,CAC/B,CAAC;AACF,YAAA,OAAO,CAAC,cAAc,GAAG,CAAC,aAAa,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;AAED;;;;;;AAMG;AACH,IAAA,OAAO,qBAAqB,CACxB,WAAwB,EACxB,kBAA2B,EAC3B,WAAoB,EAAA;AAEpB,QAAA,MAAM,OAAO,GAAkB,IAAI,aAAa,EAAE,CAAC;AAEnD,QAAA,OAAO,CAAC,aAAa;AACjB,YAAA,WAAW,CAAC,aAAa,IAAI,gBAAgB,CAAC,oBAAoB,CAAC;AACvE,QAAA,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;AAClD,QAAA,OAAO,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACpD,QAAA,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;AAEtD,QAAA,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAA,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;AAE9C,QAAA,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AACxC,QAAA,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAEhC,QAAA,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAChD,QAAA,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;;AAElC,QAAA,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,IAAI,CAC/B,WAAW,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,CAC7C,CAAC;AAEF,QAAA,OAAO,OAAO,CAAC;KAClB;AAED;;;;AAIG;IACH,OAAO,qBAAqB,CACxB,gBAAwB,EACxB,QAAuB,EACvB,MAAc,EACd,SAAkB,EAClB,aAA2B,EAAA;;AAG3B,QAAA,IACI,EACI,QAAQ,KAAK,aAAa,CAAC,IAAI;AAC/B,YAAA,QAAQ,KAAK,aAAa,CAAC,IAAI,CAClC,EACH;;AAEE,YAAA,IAAI,gBAAgB,EAAE;gBAClB,IAAI;oBACA,MAAM,UAAU,GAAG,eAAe,CAC9B,gBAAgB,EAChB,SAAS,CAAC,YAAY,CACzB,CAAC;AACF,oBAAA,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE;wBACnC,OAAO,CAAA,EAAG,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,IAAI,CAAA,CAAE,CAAC;AACjD,qBAAA;AACJ,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;AACjB,aAAA;AACD,YAAA,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAChD,SAAA;;AAGD,QAAA,OAAO,aAAa,EAAE,GAAG,IAAI,EAAE,CAAC;KACnC;AAED;;;AAGG;IACH,OAAO,eAAe,CAAC,MAAc,EAAA;QACjC,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,YAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9B,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,EACxC;KACL;AAED;;;;;AAKG;AACH,IAAA,OAAO,kBAAkB,CACrB,QAA4B,EAC5B,QAA4B,EAC5B,aAAuB,EAAA;AAEvB,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACxB,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,MAAM,cAAc,IAAI,QAAQ,CAAC,aAAa;AAC1C,gBAAA,EAAE,CAAgB,CAAC;AACvB,YAAA,MAAM,cAAc,IAAI,QAAQ,CAAC,aAAa;AAC1C,gBAAA,EAAE,CAAgB,CAAC;;YAGvB,WAAW;AACP,gBAAA,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,GAAG;AACzC,oBAAA,cAAc,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC;AACrD,SAAA;AAED,QAAA,QACI,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa;AACjD,YAAA,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,cAAc;AACnD,YAAA,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;AACvC,YAAA,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;AACvC,YAAA,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW;AAC7C,YAAA,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC,eAAe;AACrD,YAAA,WAAW,EACb;KACL;AACJ;;;;"}