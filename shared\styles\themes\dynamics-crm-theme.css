/**
 * Dynamics CRM Theme - Enhanced Dynamics 365 Branded Styling
 * 
 * This theme is used for web resource and embedded SPA deployments
 * that should maintain Dynamics 365 visual consistency
 */

:root[data-theme="crm"] {
  /* Primary Colors - Dynamics 365 Blue */
  --theme-primary: #0078d4;
  --theme-primary-hover: #106ebe;
  --theme-primary-active: #005a9e;
  --theme-primary-light: #deecf9;
  --theme-primary-dark: #004578;

  /* Secondary Colors - Dynamics 365 Gray */
  --theme-secondary: #8a8886;
  --theme-secondary-hover: #605e5c;
  --theme-secondary-active: #484644;
  --theme-secondary-light: #f3f2f1;
  --theme-secondary-dark: #323130;

  /* Background Colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #faf9f8;
  --theme-bg-tertiary: #f3f2f1;
  --theme-bg-quaternary: #edebe9;
  --theme-bg-overlay: rgba(255, 255, 255, 0.9);

  /* Text Colors */
  --theme-text-primary: #323130;
  --theme-text-secondary: #605e5c;
  --theme-text-tertiary: #8a8886;
  --theme-text-disabled: #a19f9d;
  --theme-text-inverse: #ffffff;

  /* Border Colors */
  --theme-border-primary: #8a8886;
  --theme-border-secondary: #c8c6c4;
  --theme-border-tertiary: #edebe9;
  --theme-border-focus: #0078d4;
  --theme-border-error: #d13438;
  --theme-border-success: #107c10;
  --theme-border-warning: #ff8c00;

  /* Status Colors */
  --theme-success: #107c10;
  --theme-success-light: #dff6dd;
  --theme-error: #d13438;
  --theme-error-light: #fdf2f2;
  --theme-warning: #ff8c00;
  --theme-warning-light: #fff4ce;
  --theme-info: #0078d4;
  --theme-info-light: #deecf9;

  /* Typography */
  --theme-font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;
  --theme-font-size-xs: 0.75rem;    /* 12px */
  --theme-font-size-sm: 0.875rem;   /* 14px */
  --theme-font-size-base: 1rem;     /* 16px */
  --theme-font-size-lg: 1.125rem;   /* 18px */
  --theme-font-size-xl: 1.25rem;    /* 20px */
  --theme-font-size-2xl: 1.5rem;    /* 24px */
  --theme-font-size-3xl: 1.875rem;  /* 30px */

  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-semibold: 600;
  --theme-font-weight-bold: 700;

  --theme-line-height-tight: 1.25;
  --theme-line-height-normal: 1.5;
  --theme-line-height-relaxed: 1.75;

  /* Spacing - Dynamics 365 standard spacing */
  --theme-spacing-xs: 4px;
  --theme-spacing-sm: 8px;
  --theme-spacing-md: 16px;
  --theme-spacing-lg: 24px;
  --theme-spacing-xl: 32px;
  --theme-spacing-2xl: 48px;
  --theme-spacing-3xl: 64px;

  /* Border Radius - Conservative for enterprise */
  --theme-radius-none: 0;
  --theme-radius-sm: 2px;
  --theme-radius-base: 4px;
  --theme-radius-lg: 6px;
  --theme-radius-xl: 8px;
  --theme-radius-full: 9999px;

  /* Shadows - Subtle enterprise shadows */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --theme-shadow-base: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Z-Index */
  --theme-z-dropdown: 1000;
  --theme-z-sticky: 1020;
  --theme-z-fixed: 1030;
  --theme-z-modal-backdrop: 1040;
  --theme-z-modal: 1050;
  --theme-z-popover: 1060;
  --theme-z-tooltip: 1070;

  /* Component Specific - Dynamics 365 standards */
  --theme-header-height: 48px;
  --theme-sidebar-width: 280px;
  --theme-sidebar-collapsed-width: 64px;
  --theme-content-max-width: 1200px;

  /* Form Controls - Dynamics 365 style */
  --theme-input-height: 32px;
  --theme-input-padding: 8px 12px;
  --theme-input-border-width: 1px;
  --theme-input-focus-ring: 0 0 0 2px rgba(0, 120, 212, 0.2);

  /* Buttons - Dynamics 365 style */
  --theme-button-height-sm: 24px;
  --theme-button-height-base: 32px;
  --theme-button-height-lg: 40px;
  --theme-button-padding-sm: 4px 8px;
  --theme-button-padding-base: 8px 16px;
  --theme-button-padding-lg: 12px 24px;

  /* Transitions */
  --theme-transition-fast: 150ms ease-in-out;
  --theme-transition-base: 200ms ease-in-out;
  --theme-transition-slow: 300ms ease-in-out;

  /* CRM Specific Variables */
  --crm-header-bg: var(--theme-primary);
  --crm-sidebar-bg: var(--theme-bg-tertiary);
  --crm-card-bg: var(--theme-bg-primary);
  --crm-border-radius: var(--theme-radius-sm);
  --crm-shadow: var(--theme-shadow-base);
  --crm-spacing-xs: var(--theme-spacing-xs);
  --crm-spacing-sm: var(--theme-spacing-sm);
  --crm-spacing-md: var(--theme-spacing-md);
  --crm-spacing-lg: var(--theme-spacing-lg);
  --crm-spacing-xl: var(--theme-spacing-xl);
}

/* CRM Theme Body Styles */
body[data-theme="crm"] {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  line-height: var(--theme-line-height-normal);
  color: var(--theme-text-primary);
  background-color: var(--theme-bg-secondary);
  margin: 0;
  padding: 0;
}

/* CRM Theme Component Overrides */
[data-theme="crm"] .theme-button-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: var(--theme-text-inverse);
  border-radius: var(--theme-radius-sm);
}

[data-theme="crm"] .theme-button-primary:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

[data-theme="crm"] .theme-button-secondary {
  background-color: transparent;
  border-color: var(--theme-border-primary);
  color: var(--theme-text-primary);
  border-radius: var(--theme-radius-sm);
}

[data-theme="crm"] .theme-button-secondary:hover {
  background-color: var(--theme-bg-tertiary);
  border-color: var(--theme-border-secondary);
}

[data-theme="crm"] .theme-card {
  background-color: var(--crm-card-bg);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--crm-border-radius);
  box-shadow: var(--crm-shadow);
}

[data-theme="crm"] .theme-header {
  background-color: var(--crm-header-bg);
  color: var(--theme-text-inverse);
  height: var(--theme-header-height);
}

[data-theme="crm"] .theme-sidebar {
  background-color: var(--crm-sidebar-bg);
  width: var(--theme-sidebar-width);
  border-right: 1px solid var(--theme-border-tertiary);
}

/* CRM Theme Form Controls */
[data-theme="crm"] .theme-input {
  height: var(--theme-input-height);
  padding: var(--theme-input-padding);
  border: var(--theme-input-border-width) solid var(--theme-border-primary);
  border-radius: var(--theme-radius-sm);
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

[data-theme="crm"] .theme-input:focus {
  outline: none;
  border-color: var(--theme-border-focus);
  box-shadow: var(--theme-input-focus-ring);
}

/* CRM Theme Loading Spinner */
[data-theme="crm"] .theme-spinner {
  border-color: var(--theme-border-tertiary);
  border-top-color: var(--theme-primary);
}

/* CRM Theme Utilities */
[data-theme="crm"] .theme-text-primary { color: var(--theme-text-primary); }
[data-theme="crm"] .theme-text-secondary { color: var(--theme-text-secondary); }
[data-theme="crm"] .theme-text-success { color: var(--theme-success); }
[data-theme="crm"] .theme-text-error { color: var(--theme-error); }
[data-theme="crm"] .theme-text-warning { color: var(--theme-warning); }

[data-theme="crm"] .theme-bg-primary { background-color: var(--theme-bg-primary); }
[data-theme="crm"] .theme-bg-secondary { background-color: var(--theme-bg-secondary); }
[data-theme="crm"] .theme-bg-success { background-color: var(--theme-success-light); }
[data-theme="crm"] .theme-bg-error { background-color: var(--theme-error-light); }
[data-theme="crm"] .theme-bg-warning { background-color: var(--theme-warning-light); }

/* Dynamics 365 specific enhancements */
[data-theme="crm"] .dynamics-command-bar {
  background-color: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border-tertiary);
  padding: var(--theme-spacing-sm) var(--theme-spacing-md);
}

[data-theme="crm"] .dynamics-form-section {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--theme-radius-sm);
  margin-bottom: var(--theme-spacing-md);
}

[data-theme="crm"] .dynamics-grid-container {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--theme-radius-sm);
}
