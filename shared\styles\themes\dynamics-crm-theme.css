/**
 * Dynamics CRM Theme - Enhanced Dynamics 365 Branded Styling
 *
 * This theme is used for web resource and embedded SPA deployments
 * that should maintain Dynamics 365 visual consistency.
 * Uses the enhanced base theme structure with Dynamics 365 colors.
 */

:root[data-theme="crm"] {
  /* === BRAND COLORS === */
  /* Primary Colors - Dynamics 365 Blue */
  --theme-primary: #0078d4;
  --theme-primary-hover: #106ebe;
  --theme-primary-active: #005a9e;
  --theme-primary-light: #deecf9;
  --theme-primary-dark: #004578;

  /* Secondary Colors - Dynamics 365 Gray */
  --theme-secondary: #8a8886;
  --theme-secondary-hover: #605e5c;
  --theme-secondary-active: #484644;
  --theme-secondary-light: #f3f2f1;
  --theme-secondary-dark: #323130;

  /* Background Colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #faf9f8;
  --theme-bg-tertiary: #f3f2f1;
  --theme-bg-quaternary: #edebe9;
  --theme-bg-overlay: rgba(255, 255, 255, 0.9);

  /* Text Colors */
  --theme-text-primary: #323130;
  --theme-text-secondary: #605e5c;
  --theme-text-tertiary: #8a8886;
  --theme-text-disabled: #a19f9d;
  --theme-text-inverse: #ffffff;

  /* Border Colors */
  --theme-border-primary: #8a8886;
  --theme-border-secondary: #c8c6c4;
  --theme-border-tertiary: #edebe9;
  --theme-border-focus: #0078d4;
  --theme-border-error: #d13438;
  --theme-border-success: #107c10;
  --theme-border-warning: #ff8c00;

  /* Status Colors */
  --theme-success: #107c10;
  --theme-success-light: #dff6dd;
  --theme-error: #d13438;
  --theme-error-light: #fdf2f2;
  --theme-warning: #ff8c00;
  --theme-warning-light: #fff4ce;
  --theme-info: #0078d4;
  --theme-info-light: #deecf9;

  /* === TYPOGRAPHY === */
  /* Font families - Dynamics 365 standard */
  --theme-font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;
  --theme-font-family-heading: var(--theme-font-family);

  /* Font sizes - inherit from base theme */
  --theme-font-size-xs: var(--zb-font-size-xs);
  --theme-font-size-sm: var(--zb-font-size-sm);
  --theme-font-size-base: var(--zb-font-size-base);
  --theme-font-size-lg: var(--zb-font-size-lg);
  --theme-font-size-xl: var(--zb-font-size-xl);
  --theme-font-size-2xl: var(--zb-font-size-2xl);
  --theme-font-size-3xl: var(--zb-font-size-3xl);

  /* Font weights - inherit from base theme */
  --theme-font-weight-normal: var(--zb-font-weight-normal);
  --theme-font-weight-medium: var(--zb-font-weight-medium);
  --theme-font-weight-semibold: var(--zb-font-weight-semibold);
  --theme-font-weight-bold: var(--zb-font-weight-bold);

  /* Line heights - inherit from base theme */
  --theme-line-height-tight: var(--zb-line-height-tight);
  --theme-line-height-normal: var(--zb-line-height-normal);
  --theme-line-height-relaxed: var(--zb-line-height-relaxed);

  /* === SPACING === */
  /* Inherit spacing from base theme */
  --theme-spacing-xs: var(--zb-spacing-xs);
  --theme-spacing-sm: var(--zb-spacing-sm);
  --theme-spacing-md: var(--zb-spacing-md);
  --theme-spacing-lg: var(--zb-spacing-lg);
  --theme-spacing-xl: var(--zb-spacing-xl);
  --theme-spacing-2xl: var(--zb-spacing-2xl);
  --theme-spacing-3xl: var(--zb-spacing-3xl);

  /* === BORDER RADIUS === */
  /* Conservative enterprise radius - slightly reduced from base */
  --theme-radius-none: 0;
  --theme-radius-sm: 2px;
  --theme-radius-base: 4px;
  --theme-radius-lg: 6px;
  --theme-radius-xl: 8px;
  --theme-radius-full: 9999px;

  /* === SHADOWS === */
  /* Subtle enterprise shadows - inherit from base */
  --theme-shadow-sm: var(--zb-shadow-sm);
  --theme-shadow-base: var(--zb-shadow-base);
  --theme-shadow-md: var(--zb-shadow-md);
  --theme-shadow-lg: var(--zb-shadow-lg);
  --theme-shadow-xl: var(--zb-shadow-xl);

  /* Z-Index */
  --theme-z-dropdown: 1000;
  --theme-z-sticky: 1020;
  --theme-z-fixed: 1030;
  --theme-z-modal-backdrop: 1040;
  --theme-z-modal: 1050;
  --theme-z-popover: 1060;
  --theme-z-tooltip: 1070;

  /* Component Specific - Dynamics 365 standards */
  --theme-header-height: 48px;
  --theme-sidebar-width: 280px;
  --theme-sidebar-collapsed-width: 64px;
  --theme-content-max-width: 1200px;

  /* Form Controls - Dynamics 365 style */
  --theme-input-height: 32px;
  --theme-input-padding: 8px 12px;
  --theme-input-border-width: 1px;
  --theme-input-focus-ring: 0 0 0 2px rgba(0, 120, 212, 0.2);

  /* Buttons - Dynamics 365 style */
  --theme-button-height-sm: 24px;
  --theme-button-height-base: 32px;
  --theme-button-height-lg: 40px;
  --theme-button-padding-sm: 4px 8px;
  --theme-button-padding-base: 8px 16px;
  --theme-button-padding-lg: 12px 24px;

  /* Transitions */
  --theme-transition-fast: 150ms ease-in-out;
  --theme-transition-base: 200ms ease-in-out;
  --theme-transition-slow: 300ms ease-in-out;

  /* CRM Specific Variables */
  --crm-header-bg: var(--theme-primary);
  --crm-sidebar-bg: var(--theme-bg-tertiary);
  --crm-card-bg: var(--theme-bg-primary);
  --crm-border-radius: var(--theme-radius-sm);
  --crm-shadow: var(--theme-shadow-base);
  --crm-spacing-xs: var(--theme-spacing-xs);
  --crm-spacing-sm: var(--theme-spacing-sm);
  --crm-spacing-md: var(--theme-spacing-md);
  --crm-spacing-lg: var(--theme-spacing-lg);
  --crm-spacing-xl: var(--theme-spacing-xl);
}

/* CRM Theme Body Styles */
body[data-theme="crm"] {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  line-height: var(--theme-line-height-normal);
  color: var(--theme-text-primary);
  background-color: var(--theme-bg-secondary);
  margin: 0;
  padding: 0;
}

/* === CRM THEME COMPONENT OVERRIDES === */

/* Button System - CRM Theme */
[data-theme="crm"] .zb-button,
[data-theme="crm"] .theme-button {
  /* Inherit structural styles from base theme */
  height: var(--zb-button-height);
  padding: var(--zb-button-padding-y) var(--zb-button-padding-x);
  border-radius: var(--theme-radius-lg); /* Slightly more conservative than base */
  font-size: var(--zb-button-font-size);
  line-height: var(--zb-button-line-height);
  min-width: var(--zb-button-min-width);
  font-weight: var(--theme-font-weight-medium);
  transition: all var(--zb-transition-base);
}

[data-theme="crm"] .zb-button-primary,
[data-theme="crm"] .theme-button-primary {
  background-color: var(--theme-primary);
  border: var(--zb-border-width-thin) solid var(--theme-primary);
  color: var(--theme-text-inverse);
}

[data-theme="crm"] .zb-button-primary:hover,
[data-theme="crm"] .theme-button-primary:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

[data-theme="crm"] .zb-button-primary:disabled,
[data-theme="crm"] .theme-button-primary:disabled {
  background-color: var(--theme-secondary-light);
  border-color: var(--theme-secondary-light);
  color: var(--theme-text-disabled);
}

[data-theme="crm"] .zb-button-secondary,
[data-theme="crm"] .theme-button-secondary {
  background-color: var(--theme-bg-primary);
  border: var(--zb-border-width-base) solid var(--theme-primary);
  color: var(--theme-primary);
  padding: var(--zb-button-padding-y-secondary) var(--zb-button-padding-x);
}

[data-theme="crm"] .zb-button-secondary:hover,
[data-theme="crm"] .theme-button-secondary:hover {
  background-color: var(--theme-primary-light);
  border-color: var(--theme-primary-hover);
  color: var(--theme-primary-hover);
}

[data-theme="crm"] .zb-button-secondary:disabled,
[data-theme="crm"] .theme-button-secondary:disabled {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-secondary-light);
  color: var(--theme-text-disabled);
}

/* Mobile button adjustments */
@media (max-width: 840px) {
  [data-theme="crm"] .zb-button,
  [data-theme="crm"] .theme-button {
    font-size: var(--zb-button-font-size-mobile);
    line-height: var(--zb-button-line-height-mobile);
  }

  [data-theme="crm"] .zb-button-secondary,
  [data-theme="crm"] .theme-button-secondary {
    padding: var(--zb-button-padding-y-secondary-mobile) var(--zb-button-padding-x);
  }
}

[data-theme="crm"] .theme-card {
  background-color: var(--crm-card-bg);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--crm-border-radius);
  box-shadow: var(--crm-shadow);
}

[data-theme="crm"] .theme-header {
  background-color: var(--crm-header-bg);
  color: var(--theme-text-inverse);
  height: var(--theme-header-height);
}

[data-theme="crm"] .theme-sidebar {
  background-color: var(--crm-sidebar-bg);
  width: var(--theme-sidebar-width);
  border-right: 1px solid var(--theme-border-tertiary);
}

/* Input System - CRM Theme */
[data-theme="crm"] .zb-input,
[data-theme="crm"] .theme-input {
  /* Inherit structural styles from base theme */
  height: var(--zb-input-height);
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
  border: var(--zb-input-border-width) solid var(--theme-border-primary);
  border-radius: var(--zb-input-border-radius);
  font-family: var(--theme-font-family);
  font-size: var(--zb-input-font-size);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  transition: all var(--zb-transition-base);
}

[data-theme="crm"] .zb-input:hover,
[data-theme="crm"] .theme-input:hover {
  border-color: var(--theme-primary);
}

[data-theme="crm"] .zb-input:focus,
[data-theme="crm"] .theme-input:focus {
  outline: none;
  border-color: var(--theme-border-primary);
  box-shadow: 0 0 0 var(--zb-focus-ring-offset) var(--theme-bg-primary),
              0 0 0 var(--zb-focus-ring-width) var(--theme-primary);
}

[data-theme="crm"] .zb-input:disabled,
[data-theme="crm"] .theme-input:disabled {
  border-color: var(--theme-border-tertiary);
  background-color: var(--theme-bg-tertiary);
  color: var(--theme-text-disabled);
  cursor: not-allowed;
}

/* Mobile input adjustments */
@media (max-width: 840px) {
  [data-theme="crm"] .zb-input,
  [data-theme="crm"] .theme-input {
    height: var(--zb-input-height-mobile);
    font-size: var(--zb-input-font-size-mobile);
  }
}

/* CRM Theme Loading Spinner */
[data-theme="crm"] .theme-spinner {
  border-color: var(--theme-border-tertiary);
  border-top-color: var(--theme-primary);
}

/* CRM Theme Utilities */
[data-theme="crm"] .theme-text-primary { color: var(--theme-text-primary); }
[data-theme="crm"] .theme-text-secondary { color: var(--theme-text-secondary); }
[data-theme="crm"] .theme-text-success { color: var(--theme-success); }
[data-theme="crm"] .theme-text-error { color: var(--theme-error); }
[data-theme="crm"] .theme-text-warning { color: var(--theme-warning); }

[data-theme="crm"] .theme-bg-primary { background-color: var(--theme-bg-primary); }
[data-theme="crm"] .theme-bg-secondary { background-color: var(--theme-bg-secondary); }
[data-theme="crm"] .theme-bg-success { background-color: var(--theme-success-light); }
[data-theme="crm"] .theme-bg-error { background-color: var(--theme-error-light); }
[data-theme="crm"] .theme-bg-warning { background-color: var(--theme-warning-light); }

/* Dynamics 365 specific enhancements */
[data-theme="crm"] .dynamics-command-bar {
  background-color: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border-tertiary);
  padding: var(--theme-spacing-sm) var(--theme-spacing-md);
}

[data-theme="crm"] .dynamics-form-section {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--theme-radius-sm);
  margin-bottom: var(--theme-spacing-md);
}

[data-theme="crm"] .dynamics-grid-container {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-tertiary);
  border-radius: var(--theme-radius-sm);
}
