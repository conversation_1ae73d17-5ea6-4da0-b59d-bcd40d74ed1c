{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES5", "lib": ["ES6", "DOM", "ES2016", "ES2017"], "module": "CommonJS", "moduleResolution": "node", "jsx": "react", "declaration": true, "outDir": "./out", "strict": true, "noImplicitAny": false, "strictNullChecks": false, "noImplicitThis": true, "alwaysStrict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "experimentalDecorators": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@pcf/shared/*": ["../shared/*"]}}, "include": ["./**/*.ts", "./**/*.tsx"], "exclude": ["node_modules", "out"]}