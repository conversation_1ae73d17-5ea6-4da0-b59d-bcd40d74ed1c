/**
 * Authentication Testing Utilities
 * 
 * Utilities for testing authentication switching and auth-aware components
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ReactElement } from 'react';
import { DeploymentMode } from '../config/deploymentContext';

export interface MockUser {
  id: string;
  name: string;
  email: string;
  roles?: string[];
}

export interface AuthRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  deploymentMode?: DeploymentMode;
  isAuthenticated?: boolean;
  user?: MockUser | null;
  mockAuthMethods?: boolean;
}

/**
 * Mock authentication context for testing
 */
export function mockAuthContext(options: {
  isAuthenticated?: boolean;
  user?: MockUser | null;
  deploymentMode?: DeploymentMode;
}) {
  const {
    isAuthenticated = false,
    user = null,
    deploymentMode = DeploymentMode.STANDALONE_MFE,
  } = options;

  // Mock environment for deployment mode
  process.env.VITE_DEPLOYMENT_MODE = deploymentMode;

  // Mock auth service based on deployment mode
  if (deploymentMode === DeploymentMode.WEB_RESOURCE || deploymentMode === DeploymentMode.EMBEDDED_SPA) {
    // Mock Dynamics 365 Xrm context
    global.Xrm = {
      Utility: {
        getGlobalContext: jest.fn().mockReturnValue({
          userSettings: {
            userId: user?.id || 'test-user-id',
            userName: user?.name || 'Test User',
          },
          getClientUrl: jest.fn().mockReturnValue('https://test.crm.dynamics.com'),
          getVersion: jest.fn().mockReturnValue('9.2'),
        }),
      },
      WebApi: {
        retrieveMultipleRecords: jest.fn(),
        retrieveRecord: jest.fn(),
        createRecord: jest.fn(),
        updateRecord: jest.fn(),
        deleteRecord: jest.fn(),
      },
    };
  } else {
    // Mock MSAL for standalone mode
    const mockMsalInstance = {
      initialize: jest.fn().mockResolvedValue(undefined),
      acquireTokenSilent: jest.fn().mockResolvedValue({
        accessToken: 'mock-access-token',
        account: {
          homeAccountId: user?.id || 'test-account-id',
          name: user?.name || 'Test User',
          username: user?.email || '<EMAIL>',
        },
      }),
      acquireTokenPopup: jest.fn(),
      loginPopup: jest.fn(),
      logout: jest.fn(),
      getAllAccounts: jest.fn().mockReturnValue(
        isAuthenticated && user ? [{ 
          homeAccountId: user.id,
          name: user.name,
          username: user.email,
        }] : []
      ),
    };

    // Mock MSAL module
    jest.mock('@azure/msal-browser', () => ({
      PublicClientApplication: jest.fn().mockImplementation(() => mockMsalInstance),
      InteractionRequiredAuthError: class MockInteractionRequiredAuthError extends Error {},
    }));
  }

  return {
    isAuthenticated,
    user,
    deploymentMode,
  };
}

/**
 * Render component with authentication context
 */
export function renderWithAuth(
  ui: ReactElement,
  options: AuthRenderOptions = {}
): RenderResult {
  const {
    deploymentMode = DeploymentMode.STANDALONE_MFE,
    isAuthenticated = true,
    user = {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
    },
    mockAuthMethods = true,
    ...renderOptions
  } = options;

  if (mockAuthMethods) {
    mockAuthContext({ isAuthenticated, user, deploymentMode });
  }

  // Note: You'll need to create an AuthProvider wrapper similar to ThemeProvider
  // This is a placeholder for the actual auth provider implementation
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    // Mock auth provider context
    return <div data-testid="auth-wrapper">{children}</div>;
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

/**
 * Test component in different deployment modes
 */
export function testInDeploymentModes(
  testName: string,
  testFn: (deploymentMode: DeploymentMode) => void
): void {
  describe(testName, () => {
    test('in web resource mode', () => {
      testFn(DeploymentMode.WEB_RESOURCE);
    });

    test('in embedded SPA mode', () => {
      testFn(DeploymentMode.EMBEDDED_SPA);
    });

    test('in standalone MFE mode', () => {
      testFn(DeploymentMode.STANDALONE_MFE);
    });
  });
}

/**
 * Mock different authentication states
 */
export const authStates = {
  unauthenticated: {
    isAuthenticated: false,
    user: null,
  },
  authenticatedUser: {
    isAuthenticated: true,
    user: {
      id: 'user-123',
      name: 'John Doe',
      email: '<EMAIL>',
      roles: ['user'],
    },
  },
  authenticatedAdmin: {
    isAuthenticated: true,
    user: {
      id: 'admin-456',
      name: 'Jane Admin',
      email: '<EMAIL>',
      roles: ['admin', 'user'],
    },
  },
  authenticatedSystemUser: {
    isAuthenticated: true,
    user: {
      id: 'system-789',
      name: 'System User',
      email: '<EMAIL>',
      roles: ['system'],
    },
  },
};

/**
 * Mock API responses for different deployment modes
 */
export function mockApiResponses(deploymentMode: DeploymentMode) {
  if (deploymentMode === DeploymentMode.WEB_RESOURCE || deploymentMode === DeploymentMode.EMBEDDED_SPA) {
    // Mock Xrm.WebApi responses
    if (global.Xrm?.WebApi) {
      global.Xrm.WebApi.retrieveMultipleRecords = jest.fn().mockResolvedValue({
        entities: [],
        '@odata.count': 0,
      });
      
      global.Xrm.WebApi.retrieveRecord = jest.fn().mockResolvedValue({
        id: 'test-record-id',
        name: 'Test Record',
      });
      
      global.Xrm.WebApi.createRecord = jest.fn().mockResolvedValue({
        id: 'new-record-id',
      });
      
      global.Xrm.WebApi.updateRecord = jest.fn().mockResolvedValue({});
      global.Xrm.WebApi.deleteRecord = jest.fn().mockResolvedValue({});
    }
  } else {
    // Mock fetch for external API calls
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url.includes('/api/')) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            value: [],
            '@odata.count': 0,
          }),
        });
      }
      return Promise.reject(new Error('Unmocked fetch call'));
    });
  }
}

/**
 * Authentication test helpers
 */
export const authTestHelpers = {
  /**
   * Setup authentication for specific deployment mode
   */
  setupAuth: (deploymentMode: DeploymentMode, authState = authStates.authenticatedUser) => {
    mockAuthContext({ ...authState, deploymentMode });
    mockApiResponses(deploymentMode);
  },

  /**
   * Verify authentication state
   */
  verifyAuthState: (isAuthenticated: boolean, user?: MockUser | null) => {
    // Add assertions based on your auth implementation
    if (isAuthenticated) {
      expect(user).toBeTruthy();
      expect(user?.id).toBeDefined();
      expect(user?.name).toBeDefined();
    } else {
      expect(user).toBeNull();
    }
  },

  /**
   * Test authentication flow
   */
  testAuthFlow: async (deploymentMode: DeploymentMode) => {
    // Setup unauthenticated state
    mockAuthContext({ 
      isAuthenticated: false, 
      user: null, 
      deploymentMode 
    });

    // Simulate login
    const mockUser = authStates.authenticatedUser.user;
    mockAuthContext({ 
      isAuthenticated: true, 
      user: mockUser, 
      deploymentMode 
    });

    // Verify authenticated state
    authTestHelpers.verifyAuthState(true, mockUser);
  },

  /**
   * Mock role-based access
   */
  mockRoleAccess: (user: MockUser, requiredRoles: string[]) => {
    const hasAccess = requiredRoles.some(role => user.roles?.includes(role));
    return hasAccess;
  },
};
