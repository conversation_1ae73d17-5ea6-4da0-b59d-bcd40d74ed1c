import { AxiosRequestConfig } from 'axios';

export interface ApiClientConfig {
    baseURL: string;
    timeout?: number;
    headers?: Record<string, string>;
}
export interface ApiResponse<T = any> {
    data: T;
    success: boolean;
    message?: string;
    errors?: string[];
}
export declare class ApiClient {
    private client;
    constructor(config: ApiClientConfig);
    private setupInterceptors;
    private getAuthToken;
    private handleUnauthorized;
    get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>>;
    post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>>;
    put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>>;
    delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>>;
    private handleError;
}
export declare const apiClient: ApiClient;
//# sourceMappingURL=apiClient.d.ts.map