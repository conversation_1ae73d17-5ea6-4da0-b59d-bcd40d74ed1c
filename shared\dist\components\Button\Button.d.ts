import { default as React } from 'react';

export interface ButtonProps {
    /**
     * Button contents
     */
    children: React.ReactNode;
    /**
     * Optional click handler
     */
    onClick?: () => void;
    /**
     * Button variant
     */
    variant?: 'primary' | 'secondary' | 'danger';
    /**
     * Button size
     */
    size?: 'small' | 'medium' | 'large';
    /**
     * Is the button disabled?
     */
    disabled?: boolean;
    /**
     * Button type
     */
    type?: 'button' | 'submit' | 'reset';
    /**
     * Additional CSS classes
     */
    className?: string;
    /**
     * Inline styles
     */
    style?: React.CSSProperties;
}
/**
 * Primary UI component for user interaction
 */
export declare const Button: React.FC<ButtonProps>;
//# sourceMappingURL=Button.d.ts.map