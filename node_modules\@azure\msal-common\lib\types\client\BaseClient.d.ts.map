{"version": 3, "file": "BaseClient.d.ts", "sourceRoot": "", "sources": ["../../../src/client/BaseClient.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,mBAAmB,EAEnB,yBAAyB,EAC5B,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACH,cAAc,EACd,qBAAqB,EACxB,MAAM,8BAA8B,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,gCAAgC,EAAE,MAAM,iDAAiD,CAAC;AACnG,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,sBAAsB,EAAE,MAAM,+CAA+C,CAAC;AACvF,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAEpE,OAAO,EAAE,aAAa,EAAqB,MAAM,6BAA6B,CAAC;AAE/E,OAAO,EAAE,kBAAkB,EAAE,MAAM,gDAAgD,CAAC;AAEpF,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAYhE;;;GAGG;AACH,8BAAsB,UAAU;IAErB,MAAM,EAAE,MAAM,CAAC;IAGtB,SAAS,CAAC,MAAM,EAAE,yBAAyB,CAAC;IAG5C,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC;IAG/B,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC;IAGrC,SAAS,CAAC,aAAa,EAAE,cAAc,CAAC;IAGxC,SAAS,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,IAAI,CAAC;IAGzD,SAAS,EAAE,SAAS,CAAC;IAG5B,SAAS,CAAC,iBAAiB,CAAC,EAAE,kBAAkB,CAAC;IAEjD,SAAS,aACL,aAAa,EAAE,mBAAmB,EAClC,iBAAiB,CAAC,EAAE,kBAAkB;IA2B1C;;OAEG;IACH,SAAS,CAAC,yBAAyB,CAC/B,OAAO,CAAC,EAAE,aAAa,GACxB,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IA8BzB;;;;;;OAMG;cACa,0BAA0B,CACtC,aAAa,EAAE,MAAM,EACrB,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAC/B,UAAU,EAAE,iBAAiB,EAC7B,aAAa,EAAE,MAAM,EACrB,WAAW,CAAC,EAAE,MAAM,GACrB,OAAO,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC;IA4B7D;;;;;;OAMG;IACG,eAAe,CAAC,CAAC,SAAS,gCAAgC,EAC5D,UAAU,EAAE,iBAAiB,EAC7B,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,qBAAqB,EAC9B,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAwE9B;;;OAGG;IACG,eAAe,CACjB,qBAAqB,EAAE,MAAM,EAC7B,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAkBhB;;;OAGG;IACH,0BAA0B,CAAC,OAAO,EAAE,eAAe,GAAG,MAAM;CAuB/D"}