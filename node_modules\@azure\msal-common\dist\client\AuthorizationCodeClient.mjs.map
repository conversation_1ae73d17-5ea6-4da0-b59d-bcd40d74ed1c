{"version": 3, "file": "AuthorizationCodeClient.mjs", "sources": ["../../src/client/AuthorizationCodeClient.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.requestCannotBeMade", "TimeUtils.nowSeconds", "ClientAuthErrorCodes.authorizationCodeMissingFromServerResponse", "ClientConfigurationErrorCodes.logoutRequestEmpty", "AADServerParamKeys.CLIENT_ID", "ClientConfigurationErrorCodes.missingSshJwk", "AADServerParamKeys.RETURN_SPA_CODE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAoDH;;;AAGG;AACG,MAAO,uBAAwB,SAAQ,UAAU,CAAA;IAKnD,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;AAEtC,QAAA,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;;QAPlC,IAAkB,CAAA,kBAAA,GAAY,IAAI,CAAC;AAQzC,QAAA,IAAI,CAAC,iBAAiB;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC;KAC5E;AAED;;;;;;;;;AASG;IACH,MAAM,cAAc,CAChB,OAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,cAAc,EAChC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5C,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,CAAC;AAEX,QAAA,OAAO,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,CAAC,qBAAqB,EACpC,WAAW,CACd,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,MAAM,YAAY,CACd,OAAuC,EACvC,eAA0C,EAAA;AAE1C,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,sBAAsB,EACxC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACf,YAAA,MAAM,qBAAqB,CACvBA,mBAAwC,CAC3C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,YAAY,GAAGC,UAAoB,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;;QAG3B,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;AAElE,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAErD,OAAO,WAAW,CACd,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/D,iBAAiB,CAAC,yBAAyB,EAC3C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,eAAe,EACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACZ,CAAC;KACL;AAED;;;;AAIG;IACH,sBAAsB,CAClB,YAA6C,EAC7C,WAAmB,EAAA;;AAGnB,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,IAAI,CACP,CAAC;;AAGF,QAAA,eAAe,CAAC,uCAAuC,CACnD,YAAY,EACZ,WAAW,CACd,CAAC;;AAGF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACpB,YAAA,MAAM,qBAAqB,CACvBC,0CAA+D,CAClE,CAAC;AACL,SAAA;AAED,QAAA,OAAO,YAAwC,CAAC;KACnD;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,aAAsC,EAAA;;QAE/C,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,8BAA8B,CAChCC,kBAAgD,CACnD,CAAC;AACL,SAAA;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;;AAGnE,QAAA,OAAO,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,CAAC,kBAAkB,EACjC,WAAW,CACd,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,SAAoB,EACpB,OAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,6BAA6B,EAC/C,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,CAAC;QAEX,IAAI,aAAa,GAA8B,SAAS,CAAC;QACzD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,IAAI;AACA,gBAAA,MAAM,UAAU,GAAG,eAAe,CAC9B,OAAO,CAAC,UAAU,EAClB,IAAI,CAAC,WAAW,CAAC,YAAY,CAChC,CAAC;AACF,gBAAA,aAAa,GAAG;AACZ,oBAAA,UAAU,EAAE,CAAA,EAAG,UAAU,CAAC,GAAG,CAAA,EAAG,UAAU,CAAC,qBAAqB,CAAA,EAAG,UAAU,CAAC,IAAI,CAAE,CAAA;oBACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iBAC1C,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8CAA8C,GAAG,CAAC,CACrD,CAAC;AACL,aAAA;AACJ,SAAA;AACD,QAAA,MAAM,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAClE,aAAa,IAAI,OAAO,CAAC,aAAa,CACzC,CAAC;AAEF,QAAA,MAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EACJ,OAAO,CAAC,mBAAmB,EAAE,QAAQ;AACrC,gBAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YACpC,SAAS,EAAE,SAAS,CAAC,kBAAkB;YACvC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;QAEF,OAAO,WAAW,CACd,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1C,iBAAiB,CAAC,iDAAiD,EACnE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,iBAAiB,CAAC,iDAAiD,CACtE,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,gCAAgC,EAClD,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,CAChD,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,QAAA,gBAAgB,CAAC,WAAW,CACxB,OAAO,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,mBAAmB,GAAGC,SAA4B,CAAC;AAC3D,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACvC,CAAC;AAEF;;;AAGG;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;;AAE1B,YAAA,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;;AAEH,YAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,SAAA;;AAGD,QAAA,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,MAAM,EACd,IAAI,EACJ,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,QAAA,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;QAGpD,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzD,gBAAgB,CAAC,uBAAuB,CACpC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;QACF,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjE,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,SAAA;;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE;AACtB,YAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC5C,gBAAgB,CAAC,eAAe,CAC5B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAElD,gBAAgB,CAAC,kBAAkB,CAC/B,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;AACF,YAAA,gBAAgB,CAAC,sBAAsB,CACnC,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;AAED,QAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAClE,gBAAgB,CAAC,aAAa,EAAE,CAAC;AAEjC,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;AAC3D,YAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC3C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D,aAAA;;AAGD,YAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;YAClE,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,gBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,aAAA;AACJ,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;QAED,IAAI,OAAO,GAA8B,SAAS,CAAC;QACnD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,IAAI;AACA,gBAAA,MAAM,UAAU,GAAG,eAAe,CAC9B,OAAO,CAAC,UAAU,EAClB,IAAI,CAAC,WAAW,CAAC,YAAY,CAChC,CAAC;AACF,gBAAA,OAAO,GAAG;AACN,oBAAA,UAAU,EAAE,CAAA,EAAG,UAAU,CAAC,GAAG,CAAA,EAAG,UAAU,CAAC,qBAAqB,CAAA,EAAG,UAAU,CAAC,IAAI,CAAE,CAAA;oBACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iBAC1C,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8CAA8C,GAAG,CAAC,CACrD,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC;AACnC,SAAA;;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,EAAE;YAC3D,QAAQ,OAAO,CAAC,IAAI;gBAChB,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,UAAU,CACrB,CAAC;AACF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD;AAC9C,4BAAA,CAAC,CACR,CAAC;AACL,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;AACtB,oBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC/C,MAAM;AACb,aAAA;AACJ,SAAA;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,gBAAgB,CAAC,mBAAmB,CAAC;AACjC,gBAAA,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAChD,gBAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW;AACzD,aAAA,CAAC,CAAC;AACN,SAAA;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;AAC7B,YAAA,gBAAgB,CAAC,uBAAuB,CACpC,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;;QAGD,IACI,OAAO,CAAC,0BAA0B;aACjC,CAAC,OAAO,CAAC,mBAAmB;gBACzB,CAAC,OAAO,CAAC,mBAAmB,CACxBC,eAAkC,CACrC,CAAC,EACR;YACE,gBAAgB,CAAC,uBAAuB,CAAC;AACrC,gBAAA,CAACA,eAAkC,GAAG,GAAG;AAC5C,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C;AAED;;;AAGG;IACK,MAAM,4BAA4B,CACtC,OAAsC,EAAA;;AAGtC,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;QAEhD,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,2BAA2B,EAC7C,aAAa,CAChB,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,CAChD,aAAa,EACb,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,QAAA,gBAAgB,CAAC,WAAW,CACxB,OAAO,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,oBAAoB,GAAGF,SAA4B,CAAC;AAC5D,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACvC,CAAC;AAEF,QAAA,MAAM,aAAa,GAAG;AAClB,YAAA,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;AACzB,YAAA,IAAI,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;SAC1C,CAAC;QACF,gBAAgB,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;;AAGxE,QAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAErD,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;;AAGjD,QAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;;QAGvD,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;;QAGvC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAClC,gBAAgB,CAAC,uBAAuB,CACpC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACL,SAAA;;QAGD,gBAAgB,CAAC,aAAa,EAAE,CAAC;AAEjC,QAAA,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,mBAAmB,EAAE;YACtD,gBAAgB,CAAC,sBAAsB,CACnC,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,SAAA;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;AACpB,YAAA,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtD,SAAA;;AAGD,QAAA,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,cAAc,EAAE;;YAE/C,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;;AAEpD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uEAAuE,CAC1E,CAAC;AACF,gBAAA,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACxC,aAAA;iBAAM,IAAI,OAAO,CAAC,OAAO,EAAE;gBACxB,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC3D,IAAI,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAC7C,OAAO,CAAC,OAAO,CAClB,CAAC;AAEF,gBAAA,IAAI,qBAAqB,IAAI,OAAO,CAAC,UAAU,EAAE;AAC7C,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAA,2JAAA,CAA6J,CAChK,CAAC;oBACF,qBAAqB,GAAG,IAAI,CAAC;AAChC,iBAAA;;AAGD,gBAAA,IAAI,qBAAqB,EAAE;AACvB,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mEAAmE,CACtE,CAAC;AACF,oBAAA,gBAAgB,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;oBACrD,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACL,qBAAA;AACJ,iBAAA;qBAAM,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;AAC1D;;;AAGG;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uEAAuE,CAC1E,CAAC;AACF,oBAAA,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACpC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACL,qBAAA;AACJ,iBAAA;qBAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8DAA8D,CACjE,CAAC;AACF,oBAAA,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,oBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,iBAAA;AAAM,qBAAA,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE;;AAEjC,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8DAA8D,CACjE,CAAC;oBACF,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxD,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACL,qBAAA;AACJ,iBAAA;AACJ,aAAA;iBAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0EAA0E,CAC7E,CAAC;AACF,gBAAA,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,gBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gFAAgF,CACnF,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,YAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,YAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IACI,OAAO,CAAC,MAAM;AACd,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,gBAAgB,CAAC,SAAS,CACtB,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,gBAAgB,CAAC,mBAAmB,CAAC;AACjC,gBAAA,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAChD,gBAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW;AACzD,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAEpD,IAAI,OAAO,CAAC,YAAY,EAAE;;YAEtB,gBAAgB,CAAC,eAAe,EAAE,CAAC;;AAGnC,YAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;gBAC3D,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC3C,IAAI,CAAC,WAAW,CACnB,CAAC;;AAGF,gBAAA,IAAI,UAAU,CAAC;AACf,gBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,oBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,oBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,iBAAA;AAAM,qBAAA;oBACH,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D,iBAAA;AACD,gBAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C;AAED;;;AAGG;AACK,IAAA,0BAA0B,CAC9B,OAAgC,EAAA;AAEhC,QAAA,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,CAChD,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,OAAO,CAAC,qBAAqB,EAAE;AAC/B,YAAA,gBAAgB,CAAC,wBAAwB,CACrC,OAAO,CAAC,qBAAqB,CAChC,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;AACvB,YAAA,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC5D,SAAA;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;AACrB,YAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,YAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;AACpB,YAAA,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtD,SAAA;AAED,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;AAEpD,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C;IAEO,mBAAmB,CACvB,OAAgE,EAChE,gBAAyC,EAAA;AAEzC,QAAA,MAAM,uBAAuB,GACzB,OAAO,CAAC,oBAAoB;AAC5B,YAAA,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;;QAGlE,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE;YACnE,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;AAClE,YAAA,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC;AAC3D,SAAA;QAED,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,YAAA,gBAAgB,CAAC,uBAAuB,CACpC,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,OAAoB,EAAA;AAC1C,QAAA,OAAO,OAAO,CAAC,aAAa,EAAE,GAAG,IAAI,IAAI,CAAC;KAC7C;AAEO,IAAA,gBAAgB,CAAC,OAAoB,EAAA;AACzC,QAAA,OAAO,OAAO,CAAC,aAAa,EAAE,UAAU,IAAI,IAAI,CAAC;KACpD;AACJ;;;;"}