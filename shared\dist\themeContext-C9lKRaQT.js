"use strict";var vr=Object.defineProperty;var Tr=(i,t,o)=>t in i?vr(i,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[t]=o;var H=(i,t,o)=>Tr(i,typeof t!="symbol"?t+"":t,o);const R=require("react"),x=require("./index-olv4Xr2Y.js"),E=require("./logger-B08GnSxL.js");var se={exports:{}},U={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var De;function yr(){if(De)return U;De=1;var i=R,t=Symbol.for("react.element"),o=Symbol.for("react.fragment"),m=Object.prototype.hasOwnProperty,T=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function M(O,v,F){var b,y={},k=null,S=null;F!==void 0&&(k=""+F),v.key!==void 0&&(k=""+v.key),v.ref!==void 0&&(S=v.ref);for(b in v)m.call(v,b)&&!s.hasOwnProperty(b)&&(y[b]=v[b]);if(O&&O.defaultProps)for(b in v=O.defaultProps,v)y[b]===void 0&&(y[b]=v[b]);return{$$typeof:t,type:O,key:k,ref:S,props:y,_owner:T.current}}return U.Fragment=o,U.jsx=M,U.jsxs=M,U}var z={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ae;function Er(){return Ae||(Ae=1,process.env.NODE_ENV!=="production"&&function(){var i=R,t=Symbol.for("react.element"),o=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),M=Symbol.for("react.provider"),O=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),b=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),k=Symbol.for("react.lazy"),S=Symbol.for("react.offscreen"),G=Symbol.iterator,X="@@iterator";function Z(e){if(e===null||typeof e!="object")return null;var r=G&&e[G]||e[X];return typeof r=="function"?r:null}var j=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function h(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];p("error",e,n)}}function p(e,r,n){{var a=j.ReactDebugCurrentFrame,c=a.getStackAddendum();c!==""&&(r+="%s",n=n.concat([c]));var f=n.map(function(l){return String(l)});f.unshift("Warning: "+r),Function.prototype.apply.call(console[e],console,f)}}var A=!1,Le=!1,Ne=!1,Ve=!1,He=!1,le;le=Symbol.for("react.module.reference");function We(e){return!!(typeof e=="string"||typeof e=="function"||e===m||e===s||He||e===T||e===F||e===b||Ve||e===S||A||Le||Ne||typeof e=="object"&&e!==null&&(e.$$typeof===k||e.$$typeof===y||e.$$typeof===M||e.$$typeof===O||e.$$typeof===v||e.$$typeof===le||e.getModuleId!==void 0))}function Ye(e,r,n){var a=e.displayName;if(a)return a;var c=r.displayName||r.name||"";return c!==""?n+"("+c+")":n}function ce(e){return e.displayName||"Context"}function D(e){if(e==null)return null;if(typeof e.tag=="number"&&h("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case m:return"Fragment";case o:return"Portal";case s:return"Profiler";case T:return"StrictMode";case F:return"Suspense";case b:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case O:var r=e;return ce(r)+".Consumer";case M:var n=e;return ce(n._context)+".Provider";case v:return Ye(e,e.render,"ForwardRef");case y:var a=e.displayName||null;return a!==null?a:D(e.type)||"Memo";case k:{var c=e,f=c._payload,l=c._init;try{return D(l(f))}catch{return null}}}return null}var I=Object.assign,W=0,fe,he,me,de,ge,pe,ve;function Te(){}Te.__reactDisabledLog=!0;function Ue(){{if(W===0){fe=console.log,he=console.info,me=console.warn,de=console.error,ge=console.group,pe=console.groupCollapsed,ve=console.groupEnd;var e={configurable:!0,enumerable:!0,value:Te,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}W++}}function ze(){{if(W--,W===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:I({},e,{value:fe}),info:I({},e,{value:he}),warn:I({},e,{value:me}),error:I({},e,{value:de}),group:I({},e,{value:ge}),groupCollapsed:I({},e,{value:pe}),groupEnd:I({},e,{value:ve})})}W<0&&h("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Q=j.ReactCurrentDispatcher,ee;function q(e,r,n){{if(ee===void 0)try{throw Error()}catch(c){var a=c.stack.trim().match(/\n( *(at )?)/);ee=a&&a[1]||""}return`
`+ee+e}}var re=!1,K;{var Ge=typeof WeakMap=="function"?WeakMap:Map;K=new Ge}function ye(e,r){if(!e||re)return"";{var n=K.get(e);if(n!==void 0)return n}var a;re=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var f;f=Q.current,Q.current=null,Ue();try{if(r){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(l,[])}catch(_){a=_}Reflect.construct(e,[],l)}else{try{l.call()}catch(_){a=_}e.call(l.prototype)}}else{try{throw Error()}catch(_){a=_}e()}}catch(_){if(_&&a&&typeof _.stack=="string"){for(var u=_.stack.split(`
`),C=a.stack.split(`
`),d=u.length-1,g=C.length-1;d>=1&&g>=0&&u[d]!==C[g];)g--;for(;d>=1&&g>=0;d--,g--)if(u[d]!==C[g]){if(d!==1||g!==1)do if(d--,g--,g<0||u[d]!==C[g]){var P=`
`+u[d].replace(" at new "," at ");return e.displayName&&P.includes("<anonymous>")&&(P=P.replace("<anonymous>",e.displayName)),typeof e=="function"&&K.set(e,P),P}while(d>=1&&g>=0);break}}}finally{re=!1,Q.current=f,ze(),Error.prepareStackTrace=c}var V=e?e.displayName||e.name:"",$=V?q(V):"";return typeof e=="function"&&K.set(e,$),$}function qe(e,r,n){return ye(e,!1)}function Ke(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function B(e,r,n){if(e==null)return"";if(typeof e=="function")return ye(e,Ke(e));if(typeof e=="string")return q(e);switch(e){case F:return q("Suspense");case b:return q("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case v:return qe(e.render);case y:return B(e.type,r,n);case k:{var a=e,c=a._payload,f=a._init;try{return B(f(c),r,n)}catch{}}}return""}var Y=Object.prototype.hasOwnProperty,Ee={},be=j.ReactDebugCurrentFrame;function J(e){if(e){var r=e._owner,n=B(e.type,e._source,r?r.type:null);be.setExtraStackFrame(n)}else be.setExtraStackFrame(null)}function Be(e,r,n,a,c){{var f=Function.call.bind(Y);for(var l in e)if(f(e,l)){var u=void 0;try{if(typeof e[l]!="function"){var C=Error((a||"React class")+": "+n+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw C.name="Invariant Violation",C}u=e[l](r,l,a,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(d){u=d}u&&!(u instanceof Error)&&(J(c),h("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",a||"React class",n,l,typeof u),J(null)),u instanceof Error&&!(u.message in Ee)&&(Ee[u.message]=!0,J(c),h("Failed %s type: %s",n,u.message),J(null))}}}var Je=Array.isArray;function te(e){return Je(e)}function Xe(e){{var r=typeof Symbol=="function"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n}}function Ze(e){try{return Ce(e),!1}catch{return!0}}function Ce(e){return""+e}function _e(e){if(Ze(e))return h("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Xe(e)),Ce(e)}var Re=j.ReactCurrentOwner,Qe={key:!0,ref:!0,__self:!0,__source:!0},we,Se;function er(e){if(Y.call(e,"ref")){var r=Object.getOwnPropertyDescriptor(e,"ref").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function rr(e){if(Y.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function tr(e,r){typeof e.ref=="string"&&Re.current}function nr(e,r){{var n=function(){we||(we=!0,h("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};n.isReactWarning=!0,Object.defineProperty(e,"key",{get:n,configurable:!0})}}function or(e,r){{var n=function(){Se||(Se=!0,h("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};n.isReactWarning=!0,Object.defineProperty(e,"ref",{get:n,configurable:!0})}}var ar=function(e,r,n,a,c,f,l){var u={$$typeof:t,type:e,key:r,ref:n,props:l,_owner:f};return u._store={},Object.defineProperty(u._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(u,"_self",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(u,"_source",{configurable:!1,enumerable:!1,writable:!1,value:c}),Object.freeze&&(Object.freeze(u.props),Object.freeze(u)),u};function ir(e,r,n,a,c){{var f,l={},u=null,C=null;n!==void 0&&(_e(n),u=""+n),rr(r)&&(_e(r.key),u=""+r.key),er(r)&&(C=r.ref,tr(r,c));for(f in r)Y.call(r,f)&&!Qe.hasOwnProperty(f)&&(l[f]=r[f]);if(e&&e.defaultProps){var d=e.defaultProps;for(f in d)l[f]===void 0&&(l[f]=d[f])}if(u||C){var g=typeof e=="function"?e.displayName||e.name||"Unknown":e;u&&nr(l,g),C&&or(l,g)}return ar(e,u,C,c,a,Re.current,l)}}var ne=j.ReactCurrentOwner,Pe=j.ReactDebugCurrentFrame;function N(e){if(e){var r=e._owner,n=B(e.type,e._source,r?r.type:null);Pe.setExtraStackFrame(n)}else Pe.setExtraStackFrame(null)}var oe;oe=!1;function ae(e){return typeof e=="object"&&e!==null&&e.$$typeof===t}function xe(){{if(ne.current){var e=D(ne.current.type);if(e)return`

Check the render method of \``+e+"`."}return""}}function sr(e){return""}var Oe={};function ur(e){{var r=xe();if(!r){var n=typeof e=="string"?e:e.displayName||e.name;n&&(r=`

Check the top-level render call using <`+n+">.")}return r}}function Me(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=ur(r);if(Oe[n])return;Oe[n]=!0;var a="";e&&e._owner&&e._owner!==ne.current&&(a=" It was passed a child from "+D(e._owner.type)+"."),N(e),h('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),N(null)}}function Fe(e,r){{if(typeof e!="object")return;if(te(e))for(var n=0;n<e.length;n++){var a=e[n];ae(a)&&Me(a,r)}else if(ae(e))e._store&&(e._store.validated=!0);else if(e){var c=Z(e);if(typeof c=="function"&&c!==e.entries)for(var f=c.call(e),l;!(l=f.next()).done;)ae(l.value)&&Me(l.value,r)}}}function lr(e){{var r=e.type;if(r==null||typeof r=="string")return;var n;if(typeof r=="function")n=r.propTypes;else if(typeof r=="object"&&(r.$$typeof===v||r.$$typeof===y))n=r.propTypes;else return;if(n){var a=D(r);Be(n,e.props,"prop",a,e)}else if(r.PropTypes!==void 0&&!oe){oe=!0;var c=D(r);h("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",c||"Unknown")}typeof r.getDefaultProps=="function"&&!r.getDefaultProps.isReactClassApproved&&h("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function cr(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!=="children"&&a!=="key"){N(e),h("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",a),N(null);break}}e.ref!==null&&(N(e),h("Invalid attribute `ref` supplied to `React.Fragment`."),N(null))}}var ke={};function je(e,r,n,a,c,f){{var l=We(e);if(!l){var u="";(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(u+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var C=sr();C?u+=C:u+=xe();var d;e===null?d="null":te(e)?d="array":e!==void 0&&e.$$typeof===t?(d="<"+(D(e.type)||"Unknown")+" />",u=" Did you accidentally export a JSX literal instead of a component?"):d=typeof e,h("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",d,u)}var g=ir(e,r,n,c,f);if(g==null)return g;if(l){var P=r.children;if(P!==void 0)if(a)if(te(P)){for(var V=0;V<P.length;V++)Fe(P[V],e);Object.freeze&&Object.freeze(P)}else h("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Fe(P,e)}if(Y.call(r,"key")){var $=D(e),_=Object.keys(r).filter(function(pr){return pr!=="key"}),ie=_.length>0?"{key: someKey, "+_.join(": ..., ")+": ...}":"{key: someKey}";if(!ke[$+ie]){var gr=_.length>0?"{"+_.join(": ..., ")+": ...}":"{}";h(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,ie,$,gr,$),ke[$+ie]=!0}}return e===m?cr(g):lr(g),g}}function fr(e,r,n){return je(e,r,n,!0)}function hr(e,r,n){return je(e,r,n,!1)}var mr=hr,dr=fr;z.Fragment=m,z.jsx=mr,z.jsxs=dr}()),z}process.env.NODE_ENV==="production"?se.exports=yr():se.exports=Er();var ue=se.exports,w=(i=>(i.THEME_CHANGED="theme_changed",i.THEME_LOADING="theme_loading",i.THEME_LOADED="theme_loaded",i.THEME_ERROR="theme_error",i.THEME_RESET="theme_reset",i))(w||{});class Ie{constructor(t={}){H(this,"currentTheme");H(this,"themeConfig");H(this,"options");H(this,"listeners",[]);H(this,"loadedStylesheets",new Set);this.options={enableAutoDetection:!0,enablePersistence:!0,storageKey:"crm-app-theme",defaultTheme:x.ThemeMode.CRM,...t},this.currentTheme=this.detectInitialTheme(),this.themeConfig=this.getThemeConfigForMode(this.currentTheme)}async initialize(){try{this.emitEvent(w.THEME_LOADING,this.currentTheme),await this.applyTheme(this.currentTheme),this.emitEvent(w.THEME_LOADED,this.currentTheme),E.logger.info("Theme manager initialized",{theme:this.currentTheme})}catch(t){const o=t instanceof Error?t.message:"Theme initialization failed";throw this.emitEvent(w.THEME_ERROR,this.currentTheme,{error:o}),E.logger.error("Theme manager initialization failed:",t),t}}async switchTheme(t){if(t===this.currentTheme)return;const o=this.currentTheme;try{this.emitEvent(w.THEME_LOADING,t,{previousTheme:o}),this.currentTheme=t,this.themeConfig=this.getThemeConfigForMode(t),await this.applyTheme(t),this.options.enablePersistence&&this.persistTheme(t),this.emitEvent(w.THEME_CHANGED,t,{previousTheme:o}),E.logger.info("Theme switched",{from:o,to:t})}catch(m){this.currentTheme=o,this.themeConfig=this.getThemeConfigForMode(o);const T=m instanceof Error?m.message:"Theme switch failed";throw this.emitEvent(w.THEME_ERROR,t,{error:T,previousTheme:o}),E.logger.error("Theme switch failed:",m),m}}async resetTheme(){const t=this.options.enableAutoDetection?this.detectThemeFromDeploymentContext():this.options.defaultTheme;await this.switchTheme(t),this.options.enablePersistence&&this.clearPersistedTheme(),this.emitEvent(w.THEME_RESET,t)}getCurrentTheme(){return this.currentTheme}getThemeConfig(){return this.themeConfig}onThemeChange(t){return this.listeners.push(t),()=>{const o=this.listeners.indexOf(t);o>-1&&this.listeners.splice(o,1)}}async applyTheme(t){document.documentElement.setAttribute("data-theme",t),document.body.setAttribute("data-theme",t),await this.loadThemeStylesheet(t),this.applyCSSCustomProperties(this.themeConfig),this.updateMetaThemeColor(this.themeConfig.primaryColor)}async loadThemeStylesheet(t){const o=`theme-${t}`;if(this.removeThemeStylesheets(),!this.loadedStylesheets.has(o))return new Promise((m,T)=>{const s=document.createElement("link");s.id=o,s.rel="stylesheet",s.type="text/css",s.href=this.getThemeStylesheetPath(t),s.onload=()=>{this.loadedStylesheets.add(o),m()},s.onerror=()=>{T(new Error(`Failed to load theme stylesheet: ${s.href}`))},document.head.appendChild(s)})}removeThemeStylesheets(){document.querySelectorAll('link[id^="theme-"]').forEach(o=>{o.remove(),this.loadedStylesheets.delete(o.id)})}getThemeStylesheetPath(t){return`/src/shared/styles/themes/${{[x.ThemeMode.CRM]:"dynamics-crm-theme.css",[x.ThemeMode.MFE]:"zb-champion-mfe-theme.css"}[t]}`}applyCSSCustomProperties(t){const o=document.documentElement;o.style.setProperty("--theme-primary",t.primaryColor),o.style.setProperty("--theme-secondary",t.secondaryColor),o.style.setProperty("--theme-bg-primary",t.backgroundColor),o.style.setProperty("--theme-text-primary",t.textColor),o.style.setProperty("--theme-border-primary",t.borderColor),o.style.setProperty("--theme-font-family",t.fontFamily),Object.entries(t.customProperties).forEach(([m,T])=>{o.style.setProperty(m,T)})}updateMetaThemeColor(t){let o=document.querySelector('meta[name="theme-color"]');o||(o=document.createElement("meta"),o.setAttribute("name","theme-color"),document.head.appendChild(o)),o.setAttribute("content",t)}detectInitialTheme(){if(this.options.enablePersistence){const t=this.getPersistedTheme();if(t)return t}return this.options.enableAutoDetection?this.detectThemeFromDeploymentContext():this.options.defaultTheme}detectThemeFromDeploymentContext(){try{return x.getThemeConfig().mode}catch(t){return E.logger.warn("Failed to detect theme from deployment context:",t),this.options.defaultTheme}}getThemeConfigForMode(t){try{const o=x.getDeploymentConfig();return o.theme.mode===t?o.theme:this.createThemeConfigForMode(t)}catch(o){return E.logger.warn("Failed to get theme config from deployment context:",o),this.createThemeConfigForMode(t)}}createThemeConfigForMode(t){switch(t){case x.ThemeMode.CRM:return{mode:x.ThemeMode.CRM,primaryColor:"#0078d4",secondaryColor:"#106ebe",backgroundColor:"#ffffff",textColor:"#323130",borderColor:"#8a8886",fontFamily:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',customProperties:{"--crm-header-bg":"#0078d4","--crm-sidebar-bg":"#f3f2f1","--crm-card-bg":"#ffffff","--crm-border-radius":"2px","--crm-shadow":"0 2px 4px rgba(0, 0, 0, 0.1)"}};case x.ThemeMode.MFE:return{mode:x.ThemeMode.MFE,primaryColor:"#6366f1",secondaryColor:"#4f46e5",backgroundColor:"#f8fafc",textColor:"#1e293b",borderColor:"#e2e8f0",fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',customProperties:{"--mfe-header-bg":"#1e293b","--mfe-sidebar-bg":"#f1f5f9","--mfe-card-bg":"#ffffff","--mfe-border-radius":"8px","--mfe-shadow":"0 4px 6px -1px rgba(0, 0, 0, 0.1)"}};default:throw new Error(`Unsupported theme mode: ${t}`)}}persistTheme(t){try{localStorage.setItem(this.options.storageKey,t)}catch(o){E.logger.warn("Failed to persist theme:",o)}}getPersistedTheme(){try{const t=localStorage.getItem(this.options.storageKey);if(t&&Object.values(x.ThemeMode).includes(t))return t}catch(t){E.logger.warn("Failed to get persisted theme:",t)}return null}clearPersistedTheme(){try{localStorage.removeItem(this.options.storageKey)}catch(t){E.logger.warn("Failed to clear persisted theme:",t)}}emitEvent(t,o,m){const T={event:t,theme:o,timestamp:new Date,...m};this.listeners.forEach(s=>{try{s(T)}catch(M){E.logger.error("Error in theme event listener:",M)}}),E.logger.debug("Theme event emitted:",T)}}const $e=R.createContext(null),br=({children:i,defaultTheme:t=x.ThemeMode.CRM,enableAutoDetection:o=!0,enablePersistence:m=!0,storageKey:T="crm-app-theme"})=>{const[s]=R.useState(()=>new Ie({defaultTheme:t,enableAutoDetection:o,enablePersistence:m,storageKey:T})),[M,O]=R.useState(t),[v,F]=R.useState(s.getThemeConfig()),[b,y]=R.useState(!0),[k,S]=R.useState(null);R.useEffect(()=>{(async()=>{try{y(!0),S(null),await s.initialize(),O(s.getCurrentTheme()),F(s.getThemeConfig()),y(!1),E.logger.info("Theme provider initialized",{theme:s.getCurrentTheme()})}catch(p){const A=p instanceof Error?p.message:"Theme initialization failed";S(A),y(!1),E.logger.error("Theme provider initialization failed:",p)}})()},[s]),R.useEffect(()=>s.onThemeChange(p=>{switch(p.event){case w.THEME_LOADING:y(!0),S(null);break;case w.THEME_CHANGED:case w.THEME_LOADED:case w.THEME_RESET:O(p.theme),F(s.getThemeConfig()),y(!1),S(null);break;case w.THEME_ERROR:y(!1),S(p.error||"Theme error occurred");break}}),[s]);const G=R.useCallback(async h=>{try{await s.switchTheme(h)}catch(p){const A=p instanceof Error?p.message:"Theme switch failed";S(A),E.logger.error("Theme switch failed:",p)}},[s]),X=R.useCallback(async()=>{try{await s.resetTheme()}catch(h){const p=h instanceof Error?h.message:"Theme reset failed";S(p),E.logger.error("Theme reset failed:",h)}},[s]),Z=R.useCallback(async h=>{try{await s.switchTheme(h)}catch(p){const A=p instanceof Error?p.message:"Theme application failed";S(A),E.logger.error("Theme application failed:",p)}},[s]),j={currentTheme:M,themeConfig:v,isLoading:b,error:k,switchTheme:G,resetTheme:X,applyTheme:Z};return ue.jsx($e.Provider,{value:j,children:i})},L=()=>{const i=R.useContext($e);if(!i)throw new Error("useTheme must be used within a ThemeProvider");return i},Cr=()=>{const{currentTheme:i}=L();return i},_r=()=>{const{themeConfig:i}=L();return i},Rr=i=>{const{currentTheme:t}=L();return t===i},wr=()=>{const{themeConfig:i}=L();return{primary:i.primaryColor,secondary:i.secondaryColor,background:i.backgroundColor,text:i.textColor,border:i.borderColor,fontFamily:i.fontFamily,...i.customProperties}},Sr=()=>{const{currentTheme:i,themeConfig:t}=L();return{currentTheme:i,themeConfig:t,getThemeClass:s=>`${s} theme-${i}`,getThemeStyle:s=>s[i]||{},getCSSVariable:s=>getComputedStyle(document.documentElement).getPropertyValue(s)}},Pr=i=>{const t=o=>{const m=L();return ue.jsx(i,{...o,theme:m})};return t.displayName=`withTheme(${i.displayName||i.name})`,t};exports.ThemeEvent=w;exports.ThemeManager=Ie;exports.ThemeProvider=br;exports.jsxRuntimeExports=ue;exports.useCurrentTheme=Cr;exports.useIsTheme=Rr;exports.useTheme=L;exports.useThemeConfig=_r;exports.useThemeStyles=Sr;exports.useThemeVariables=wr;exports.withTheme=Pr;
