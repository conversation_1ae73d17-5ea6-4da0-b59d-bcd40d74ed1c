{"version": 3, "file": "NativeInteractionClient.d.ts", "sourceRoot": "", "sources": ["../../src/interaction_client/NativeInteractionClient.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,MAAM,EACN,OAAO,EAIP,aAAa,EAEb,QAAQ,EAOR,kBAAkB,EAQlB,WAAW,EASX,0BAA0B,EAC1B,sBAAsB,EACzB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAE,oBAAoB,EAAE,MAAM,gDAAgD,CAAC;AACtF,OAAO,EAEH,KAAK,EAIR,MAAM,8BAA8B,CAAC;AACtC,OAAO,EAEH,kBAAkB,EACrB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,0CAA0C,CAAC;AAOhF,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AAKvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAI3E,qBAAa,uBAAwB,SAAQ,qBAAqB;IAC9D,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;IACvB,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IACrD,SAAS,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC/C,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;IACpD,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;IACvB,SAAS,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;gBAGrD,MAAM,EAAE,oBAAoB,EAC5B,cAAc,EAAE,mBAAmB,EACnC,aAAa,EAAE,OAAO,EACtB,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,YAAY,EAC1B,gBAAgB,EAAE,iBAAiB,EACnC,KAAK,EAAE,KAAK,EACZ,iBAAiB,EAAE,kBAAkB,EACrC,QAAQ,EAAE,oBAAoB,EAC9B,SAAS,EAAE,MAAM,EACjB,iBAAiB,EAAE,mBAAmB,EACtC,aAAa,CAAC,EAAE,MAAM;IA+C1B;;;;OAIG;IACH,OAAO,CAAC,cAAc;IAOtB;;;OAGG;IACG,YAAY,CACd,OAAO,EAAE,YAAY,GAAG,aAAa,GAAG,gBAAgB,GACzD,OAAO,CAAC,oBAAoB,CAAC;IAmFhC;;;;;OAKG;IACH,OAAO,CAAC,wBAAwB;IAahC;;;;;OAKG;cACa,sBAAsB,CAClC,eAAe,EAAE,MAAM,EACvB,OAAO,EAAE,kBAAkB,GAC5B,OAAO,CAAC,oBAAoB,CAAC;IA4ChC;;;;OAIG;IACG,oBAAoB,CACtB,OAAO,EAAE,eAAe,EACxB,eAAe,EAAE,0BAA0B,GAC5C,OAAO,CAAC,IAAI,CAAC;IAqDhB;;;;OAIG;IACG,qBAAqB,CACvB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAoEvC;;;OAGG;IACH,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAKvB;;;;;OAKG;cACa,oBAAoB,CAChC,QAAQ,EAAE,cAAc,EACxB,OAAO,EAAE,kBAAkB,EAC3B,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,oBAAoB,CAAC;IA6EhC;;;;;OAKG;IACH,SAAS,CAAC,2BAA2B,CACjC,QAAQ,EAAE,cAAc,EACxB,aAAa,EAAE,WAAW,GAC3B,MAAM;IAaT;;;;;OAKG;IACH,cAAc,CACV,QAAQ,EAAE,cAAc,EACxB,OAAO,EAAE,kBAAkB,GAC5B,QAAQ;IAMX;;;;OAIG;IACG,sBAAsB,CACxB,QAAQ,EAAE,cAAc,EACxB,OAAO,EAAE,kBAAkB,GAC5B,OAAO,CAAC,MAAM,CAAC;IA8ClB;;;;;;;;;OASG;cACa,4BAA4B,CACxC,QAAQ,EAAE,cAAc,EACxB,OAAO,EAAE,kBAAkB,EAC3B,aAAa,EAAE,WAAW,EAC1B,aAAa,EAAE,aAAa,EAC5B,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,oBAAoB,CAAC;IAmEhC;;;OAGG;IACH,YAAY,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAchD;;;;;;;;;OASG;IACH,iBAAiB,CACb,QAAQ,EAAE,cAAc,EACxB,OAAO,EAAE,kBAAkB,EAC3B,qBAAqB,EAAE,MAAM,EAC7B,aAAa,EAAE,WAAW,EAC1B,mBAAmB,EAAE,MAAM,EAC3B,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,MAAM,GACrB,IAAI;IAiDP,SAAS,CAAC,8BAA8B,CACpC,QAAQ,EAAE,cAAc,GACzB,IAAI,GAAG,IAAI;IAgCd;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IAkB9B;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAc3B;;;;OAIG;IACH,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAWlD;;;OAGG;cACa,uBAAuB,CACnC,OAAO,EAAE,YAAY,GAAG,gBAAgB,GACzC,OAAO,CAAC,kBAAkB,CAAC;IAwI9B;;;;OAIG;IACH,OAAO,CAAC,uBAAuB;CA2ClC"}