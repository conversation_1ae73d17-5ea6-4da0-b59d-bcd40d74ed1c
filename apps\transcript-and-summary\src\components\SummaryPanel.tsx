import React from 'react';
import { Button } from '@shared/components';

interface Transcript {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  duration: number;
  summary?: string;
}

interface SummaryPanelProps {
  transcript: Transcript;
}

const SummaryPanel: React.FC<SummaryPanelProps> = ({ transcript }) => {
  return (
    <div style={{ 
      width: '350px',
      padding: '2rem',
      backgroundColor: '#f9fafb',
      borderLeft: '1px solid #e5e7eb'
    }}>
      <h3 style={{ margin: '0 0 1rem 0', color: '#1f2937' }}>
        Summary & Insights
      </h3>

      {/* Summary Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ 
          margin: '0 0 0.5rem 0', 
          fontSize: '0.875rem', 
          fontWeight: '600',
          color: '#374151'
        }}>
          AI Summary
        </h4>
        
        <div style={{ 
          backgroundColor: '#fff',
          border: '1px solid #e5e7eb',
          borderRadius: '0.375rem',
          padding: '1rem',
          minHeight: '100px'
        }}>
          {transcript.summary ? (
            <p style={{ 
              margin: 0, 
              lineHeight: '1.5',
              color: '#374151',
              fontSize: '0.875rem'
            }}>
              {transcript.summary}
            </p>
          ) : (
            <p style={{ 
              margin: 0, 
              color: '#9ca3af',
              fontStyle: 'italic',
              fontSize: '0.875rem'
            }}>
              No summary available. Click "Generate Summary" to create one.
            </p>
          )}
        </div>
      </div>

      {/* Key Points Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ 
          margin: '0 0 0.5rem 0', 
          fontSize: '0.875rem', 
          fontWeight: '600',
          color: '#374151'
        }}>
          Key Points
        </h4>
        
        <div style={{ 
          backgroundColor: '#fff',
          border: '1px solid #e5e7eb',
          borderRadius: '0.375rem',
          padding: '1rem'
        }}>
          <ul style={{ 
            margin: 0, 
            paddingLeft: '1rem',
            fontSize: '0.875rem',
            color: '#374151'
          }}>
            <li>Customer inquiry about billing</li>
            <li>Payment method updated</li>
            <li>Issue resolved successfully</li>
          </ul>
        </div>
      </div>

      {/* Action Items Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ 
          margin: '0 0 0.5rem 0', 
          fontSize: '0.875rem', 
          fontWeight: '600',
          color: '#374151'
        }}>
          Action Items
        </h4>
        
        <div style={{ 
          backgroundColor: '#fff',
          border: '1px solid #e5e7eb',
          borderRadius: '0.375rem',
          padding: '1rem'
        }}>
          <div style={{ fontSize: '0.875rem' }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '0.5rem',
              marginBottom: '0.5rem'
            }}>
              <input type="checkbox" />
              <span>Follow up with customer in 24 hours</span>
            </div>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '0.5rem',
              marginBottom: '0.5rem'
            }}>
              <input type="checkbox" />
              <span>Update customer record</span>
            </div>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '0.5rem'
            }}>
              <input type="checkbox" />
              <span>Send confirmation email</span>
            </div>
          </div>
        </div>
      </div>

      {/* Sentiment Analysis */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{ 
          margin: '0 0 0.5rem 0', 
          fontSize: '0.875rem', 
          fontWeight: '600',
          color: '#374151'
        }}>
          Sentiment Analysis
        </h4>
        
        <div style={{ 
          backgroundColor: '#fff',
          border: '1px solid #e5e7eb',
          borderRadius: '0.375rem',
          padding: '1rem'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div style={{ 
              width: '12px', 
              height: '12px', 
              backgroundColor: '#10b981',
              borderRadius: '50%'
            }}></div>
            <span style={{ fontSize: '0.875rem', color: '#374151' }}>
              Positive (85%)
            </span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
        <Button size="small" style={{ width: '100%' }}>
          Export Summary
        </Button>
        <Button variant="secondary" size="small" style={{ width: '100%' }}>
          Add to CRM
        </Button>
        <Button variant="secondary" size="small" style={{ width: '100%' }}>
          Schedule Follow-up
        </Button>
      </div>
    </div>
  );
};

export default SummaryPanel;
