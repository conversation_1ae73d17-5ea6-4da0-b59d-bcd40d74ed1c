/**
 * Example Deployment Configuration
 * 
 * This file shows how to configure the application for different deployment scenarios.
 * Copy this file and customize it for your specific environment.
 */

import { DeploymentMode, DeploymentConfig } from '@shared/config/deploymentContext';

// Example configuration for Dynamics 365 web resource deployment
export const webResourceConfig: DeploymentConfig = {
  mode: DeploymentMode.WEB_RESOURCE,
  apiBaseUrl: '', // Not used in web resource mode
  authMethod: 'dynamics365',
  dynamicsConfig: {
    serverUrl: '', // Automatically detected from Dynamics 365 context
    version: '9.2'
  },
  features: {
    enableLogging: false, // Minimal logging in production
    enableOfflineMode: false,
    enableTelemetry: true
  }
};

// Example configuration for standalone SPA deployment
export const standaloneConfig: DeploymentConfig = {
  mode: DeploymentMode.STANDALONE,
  apiBaseUrl: 'https://your-org.api.crm.dynamics.com/api/data/v9.2',
  authMethod: 'msal',
  msalConfig: {
    clientId: 'your-azure-app-client-id',
    authority: 'https://login.microsoftonline.com/your-tenant-id',
    redirectUri: 'https://your-spa-domain.com',
    scopes: ['https://your-org.crm.dynamics.com/.default'],
    cacheLocation: 'localStorage'
  },
  features: {
    enableLogging: true, // Enhanced logging for debugging
    enableOfflineMode: true, // Enable offline capabilities
    enableTelemetry: true
  }
};

// Development configuration (for local testing)
export const developmentConfig: DeploymentConfig = {
  mode: DeploymentMode.STANDALONE,
  apiBaseUrl: 'https://your-dev-org.api.crm.dynamics.com/api/data/v9.2',
  authMethod: 'msal',
  msalConfig: {
    clientId: 'your-dev-azure-app-client-id',
    authority: 'https://login.microsoftonline.com/your-tenant-id',
    redirectUri: 'http://localhost:5173',
    scopes: ['https://your-dev-org.crm.dynamics.com/.default'],
    cacheLocation: 'sessionStorage' // Use session storage for development
  },
  features: {
    enableLogging: true,
    enableOfflineMode: false,
    enableTelemetry: false // Disable telemetry in development
  }
};

/**
 * Environment-specific configuration factory
 */
export function getEnvironmentConfig(): DeploymentConfig {
  const environment = process.env.NODE_ENV || 'development';
  
  switch (environment) {
    case 'production':
      // Determine if running in Dynamics 365 or standalone
      if (window.location.href.includes('.dynamics.com')) {
        return webResourceConfig;
      } else {
        return standaloneConfig;
      }
    
    case 'development':
      return developmentConfig;
    
    default:
      return developmentConfig;
  }
}

/**
 * Custom configuration override
 * Use this function to programmatically override configuration based on runtime conditions
 */
export function createCustomConfig(overrides: Partial<DeploymentConfig>): DeploymentConfig {
  const baseConfig = getEnvironmentConfig();
  
  return {
    ...baseConfig,
    ...overrides,
    features: {
      ...baseConfig.features,
      ...overrides.features
    },
    msalConfig: overrides.msalConfig ? {
      ...baseConfig.msalConfig,
      ...overrides.msalConfig
    } : baseConfig.msalConfig,
    dynamicsConfig: overrides.dynamicsConfig ? {
      ...baseConfig.dynamicsConfig,
      ...overrides.dynamicsConfig
    } : baseConfig.dynamicsConfig
  };
}
