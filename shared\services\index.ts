// Export legacy API client (for backward compatibility)
export { ExternalApiClient as ApiClient } from './apiClient';
export type { ApiClientConfig } from './apiClient';

// Export new API abstraction
export { getApiClient } from './api/apiFactory';
export type {
  IApiClient,
  ApiResponse,
  PaginatedResponse,
  QueryOptions,
  BatchRequest,
  BatchResponse
} from './api/apiTypes';

// Export auth services (legacy compatibility maintained)
export { authService, useAuth } from './auth';
export type { User, LoginCredentials, AuthState, AuthService } from './auth';

// Export new auth abstraction
export { getAuthService } from './auth/authFactory';
export type {
  IAuthService,
  AuthUser,
  AuthToken,
  AuthResult
} from './auth/authTypes';

// Export theme services
export * from './theme';
