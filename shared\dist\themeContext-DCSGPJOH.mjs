var Tr = Object.defineProperty;
var Er = (i, t, a) => t in i ? Tr(i, t, { enumerable: !0, configurable: !0, writable: !0, value: a }) : i[t] = a;
var N = (i, t, a) => Er(i, typeof t != "symbol" ? t + "" : t, a);
import $e, { createContext as br, useState as Y, useEffect as De, useCallback as se, useContext as Cr } from "react";
import { T as M, c as _r, g as Rr } from "./index-CIKzu-5v.mjs";
import { l as E } from "./logger-jecF8wz6.mjs";
var ue = { exports: {} }, U = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ae;
function wr() {
  if (Ae) return U;
  Ae = 1;
  var i = $e, t = Symbol.for("react.element"), a = Symbol.for("react.fragment"), m = Object.prototype.hasOwnProperty, y = i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, s = { key: !0, ref: !0, __self: !0, __source: !0 };
  function x(P, g, O) {
    var b, T = {}, F = null, R = null;
    O !== void 0 && (F = "" + O), g.key !== void 0 && (F = "" + g.key), g.ref !== void 0 && (R = g.ref);
    for (b in g) m.call(g, b) && !s.hasOwnProperty(b) && (T[b] = g[b]);
    if (P && P.defaultProps) for (b in g = P.defaultProps, g) T[b] === void 0 && (T[b] = g[b]);
    return { $$typeof: t, type: P, key: F, ref: R, props: T, _owner: y.current };
  }
  return U.Fragment = a, U.jsx = x, U.jsxs = x, U;
}
var z = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ie;
function Sr() {
  return Ie || (Ie = 1, process.env.NODE_ENV !== "production" && function() {
    var i = $e, t = Symbol.for("react.element"), a = Symbol.for("react.portal"), m = Symbol.for("react.fragment"), y = Symbol.for("react.strict_mode"), s = Symbol.for("react.profiler"), x = Symbol.for("react.provider"), P = Symbol.for("react.context"), g = Symbol.for("react.forward_ref"), O = Symbol.for("react.suspense"), b = Symbol.for("react.suspense_list"), T = Symbol.for("react.memo"), F = Symbol.for("react.lazy"), R = Symbol.for("react.offscreen"), G = Symbol.iterator, X = "@@iterator";
    function Z(e) {
      if (e === null || typeof e != "object")
        return null;
      var r = G && e[G] || e[X];
      return typeof r == "function" ? r : null;
    }
    var k = i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function h(e) {
      {
        for (var r = arguments.length, n = new Array(r > 1 ? r - 1 : 0), o = 1; o < r; o++)
          n[o - 1] = arguments[o];
        v("error", e, n);
      }
    }
    function v(e, r, n) {
      {
        var o = k.ReactDebugCurrentFrame, l = o.getStackAddendum();
        l !== "" && (r += "%s", n = n.concat([l]));
        var f = n.map(function(c) {
          return String(c);
        });
        f.unshift("Warning: " + r), Function.prototype.apply.call(console[e], console, f);
      }
    }
    var D = !1, He = !1, Ve = !1, We = !1, Ye = !1, ce;
    ce = Symbol.for("react.module.reference");
    function Ue(e) {
      return !!(typeof e == "string" || typeof e == "function" || e === m || e === s || Ye || e === y || e === O || e === b || We || e === R || D || He || Ve || typeof e == "object" && e !== null && (e.$$typeof === F || e.$$typeof === T || e.$$typeof === x || e.$$typeof === P || e.$$typeof === g || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      e.$$typeof === ce || e.getModuleId !== void 0));
    }
    function ze(e, r, n) {
      var o = e.displayName;
      if (o)
        return o;
      var l = r.displayName || r.name || "";
      return l !== "" ? n + "(" + l + ")" : n;
    }
    function le(e) {
      return e.displayName || "Context";
    }
    function j(e) {
      if (e == null)
        return null;
      if (typeof e.tag == "number" && h("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof e == "function")
        return e.displayName || e.name || null;
      if (typeof e == "string")
        return e;
      switch (e) {
        case m:
          return "Fragment";
        case a:
          return "Portal";
        case s:
          return "Profiler";
        case y:
          return "StrictMode";
        case O:
          return "Suspense";
        case b:
          return "SuspenseList";
      }
      if (typeof e == "object")
        switch (e.$$typeof) {
          case P:
            var r = e;
            return le(r) + ".Consumer";
          case x:
            var n = e;
            return le(n._context) + ".Provider";
          case g:
            return ze(e, e.render, "ForwardRef");
          case T:
            var o = e.displayName || null;
            return o !== null ? o : j(e.type) || "Memo";
          case F: {
            var l = e, f = l._payload, c = l._init;
            try {
              return j(c(f));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var A = Object.assign, V = 0, fe, he, me, de, pe, ve, ge;
    function ye() {
    }
    ye.__reactDisabledLog = !0;
    function Ge() {
      {
        if (V === 0) {
          fe = console.log, he = console.info, me = console.warn, de = console.error, pe = console.group, ve = console.groupCollapsed, ge = console.groupEnd;
          var e = {
            configurable: !0,
            enumerable: !0,
            value: ye,
            writable: !0
          };
          Object.defineProperties(console, {
            info: e,
            log: e,
            warn: e,
            error: e,
            group: e,
            groupCollapsed: e,
            groupEnd: e
          });
        }
        V++;
      }
    }
    function Ke() {
      {
        if (V--, V === 0) {
          var e = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: A({}, e, {
              value: fe
            }),
            info: A({}, e, {
              value: he
            }),
            warn: A({}, e, {
              value: me
            }),
            error: A({}, e, {
              value: de
            }),
            group: A({}, e, {
              value: pe
            }),
            groupCollapsed: A({}, e, {
              value: ve
            }),
            groupEnd: A({}, e, {
              value: ge
            })
          });
        }
        V < 0 && h("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var Q = k.ReactCurrentDispatcher, ee;
    function K(e, r, n) {
      {
        if (ee === void 0)
          try {
            throw Error();
          } catch (l) {
            var o = l.stack.trim().match(/\n( *(at )?)/);
            ee = o && o[1] || "";
          }
        return `
` + ee + e;
      }
    }
    var re = !1, q;
    {
      var qe = typeof WeakMap == "function" ? WeakMap : Map;
      q = new qe();
    }
    function Te(e, r) {
      if (!e || re)
        return "";
      {
        var n = q.get(e);
        if (n !== void 0)
          return n;
      }
      var o;
      re = !0;
      var l = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var f;
      f = Q.current, Q.current = null, Ge();
      try {
        if (r) {
          var c = function() {
            throw Error();
          };
          if (Object.defineProperty(c.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(c, []);
            } catch (_) {
              o = _;
            }
            Reflect.construct(e, [], c);
          } else {
            try {
              c.call();
            } catch (_) {
              o = _;
            }
            e.call(c.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (_) {
            o = _;
          }
          e();
        }
      } catch (_) {
        if (_ && o && typeof _.stack == "string") {
          for (var u = _.stack.split(`
`), C = o.stack.split(`
`), d = u.length - 1, p = C.length - 1; d >= 1 && p >= 0 && u[d] !== C[p]; )
            p--;
          for (; d >= 1 && p >= 0; d--, p--)
            if (u[d] !== C[p]) {
              if (d !== 1 || p !== 1)
                do
                  if (d--, p--, p < 0 || u[d] !== C[p]) {
                    var S = `
` + u[d].replace(" at new ", " at ");
                    return e.displayName && S.includes("<anonymous>") && (S = S.replace("<anonymous>", e.displayName)), typeof e == "function" && q.set(e, S), S;
                  }
                while (d >= 1 && p >= 0);
              break;
            }
        }
      } finally {
        re = !1, Q.current = f, Ke(), Error.prepareStackTrace = l;
      }
      var L = e ? e.displayName || e.name : "", I = L ? K(L) : "";
      return typeof e == "function" && q.set(e, I), I;
    }
    function Be(e, r, n) {
      return Te(e, !1);
    }
    function Je(e) {
      var r = e.prototype;
      return !!(r && r.isReactComponent);
    }
    function B(e, r, n) {
      if (e == null)
        return "";
      if (typeof e == "function")
        return Te(e, Je(e));
      if (typeof e == "string")
        return K(e);
      switch (e) {
        case O:
          return K("Suspense");
        case b:
          return K("SuspenseList");
      }
      if (typeof e == "object")
        switch (e.$$typeof) {
          case g:
            return Be(e.render);
          case T:
            return B(e.type, r, n);
          case F: {
            var o = e, l = o._payload, f = o._init;
            try {
              return B(f(l), r, n);
            } catch {
            }
          }
        }
      return "";
    }
    var W = Object.prototype.hasOwnProperty, Ee = {}, be = k.ReactDebugCurrentFrame;
    function J(e) {
      if (e) {
        var r = e._owner, n = B(e.type, e._source, r ? r.type : null);
        be.setExtraStackFrame(n);
      } else
        be.setExtraStackFrame(null);
    }
    function Xe(e, r, n, o, l) {
      {
        var f = Function.call.bind(W);
        for (var c in e)
          if (f(e, c)) {
            var u = void 0;
            try {
              if (typeof e[c] != "function") {
                var C = Error((o || "React class") + ": " + n + " type `" + c + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof e[c] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw C.name = "Invariant Violation", C;
              }
              u = e[c](r, c, o, n, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (d) {
              u = d;
            }
            u && !(u instanceof Error) && (J(l), h("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", o || "React class", n, c, typeof u), J(null)), u instanceof Error && !(u.message in Ee) && (Ee[u.message] = !0, J(l), h("Failed %s type: %s", n, u.message), J(null));
          }
      }
    }
    var Ze = Array.isArray;
    function te(e) {
      return Ze(e);
    }
    function Qe(e) {
      {
        var r = typeof Symbol == "function" && Symbol.toStringTag, n = r && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return n;
      }
    }
    function er(e) {
      try {
        return Ce(e), !1;
      } catch {
        return !0;
      }
    }
    function Ce(e) {
      return "" + e;
    }
    function _e(e) {
      if (er(e))
        return h("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Qe(e)), Ce(e);
    }
    var Re = k.ReactCurrentOwner, rr = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, we, Se;
    function tr(e) {
      if (W.call(e, "ref")) {
        var r = Object.getOwnPropertyDescriptor(e, "ref").get;
        if (r && r.isReactWarning)
          return !1;
      }
      return e.ref !== void 0;
    }
    function nr(e) {
      if (W.call(e, "key")) {
        var r = Object.getOwnPropertyDescriptor(e, "key").get;
        if (r && r.isReactWarning)
          return !1;
      }
      return e.key !== void 0;
    }
    function ar(e, r) {
      typeof e.ref == "string" && Re.current;
    }
    function or(e, r) {
      {
        var n = function() {
          we || (we = !0, h("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        n.isReactWarning = !0, Object.defineProperty(e, "key", {
          get: n,
          configurable: !0
        });
      }
    }
    function ir(e, r) {
      {
        var n = function() {
          Se || (Se = !0, h("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        n.isReactWarning = !0, Object.defineProperty(e, "ref", {
          get: n,
          configurable: !0
        });
      }
    }
    var sr = function(e, r, n, o, l, f, c) {
      var u = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: t,
        // Built-in properties that belong on the element
        type: e,
        key: r,
        ref: n,
        props: c,
        // Record the component responsible for creating this element.
        _owner: f
      };
      return u._store = {}, Object.defineProperty(u._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(u, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: o
      }), Object.defineProperty(u, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: l
      }), Object.freeze && (Object.freeze(u.props), Object.freeze(u)), u;
    };
    function ur(e, r, n, o, l) {
      {
        var f, c = {}, u = null, C = null;
        n !== void 0 && (_e(n), u = "" + n), nr(r) && (_e(r.key), u = "" + r.key), tr(r) && (C = r.ref, ar(r, l));
        for (f in r)
          W.call(r, f) && !rr.hasOwnProperty(f) && (c[f] = r[f]);
        if (e && e.defaultProps) {
          var d = e.defaultProps;
          for (f in d)
            c[f] === void 0 && (c[f] = d[f]);
        }
        if (u || C) {
          var p = typeof e == "function" ? e.displayName || e.name || "Unknown" : e;
          u && or(c, p), C && ir(c, p);
        }
        return sr(e, u, C, l, o, Re.current, c);
      }
    }
    var ne = k.ReactCurrentOwner, Pe = k.ReactDebugCurrentFrame;
    function $(e) {
      if (e) {
        var r = e._owner, n = B(e.type, e._source, r ? r.type : null);
        Pe.setExtraStackFrame(n);
      } else
        Pe.setExtraStackFrame(null);
    }
    var ae;
    ae = !1;
    function oe(e) {
      return typeof e == "object" && e !== null && e.$$typeof === t;
    }
    function xe() {
      {
        if (ne.current) {
          var e = j(ne.current.type);
          if (e)
            return `

Check the render method of \`` + e + "`.";
        }
        return "";
      }
    }
    function cr(e) {
      return "";
    }
    var Oe = {};
    function lr(e) {
      {
        var r = xe();
        if (!r) {
          var n = typeof e == "string" ? e : e.displayName || e.name;
          n && (r = `

Check the top-level render call using <` + n + ">.");
        }
        return r;
      }
    }
    function Fe(e, r) {
      {
        if (!e._store || e._store.validated || e.key != null)
          return;
        e._store.validated = !0;
        var n = lr(r);
        if (Oe[n])
          return;
        Oe[n] = !0;
        var o = "";
        e && e._owner && e._owner !== ne.current && (o = " It was passed a child from " + j(e._owner.type) + "."), $(e), h('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', n, o), $(null);
      }
    }
    function Me(e, r) {
      {
        if (typeof e != "object")
          return;
        if (te(e))
          for (var n = 0; n < e.length; n++) {
            var o = e[n];
            oe(o) && Fe(o, r);
          }
        else if (oe(e))
          e._store && (e._store.validated = !0);
        else if (e) {
          var l = Z(e);
          if (typeof l == "function" && l !== e.entries)
            for (var f = l.call(e), c; !(c = f.next()).done; )
              oe(c.value) && Fe(c.value, r);
        }
      }
    }
    function fr(e) {
      {
        var r = e.type;
        if (r == null || typeof r == "string")
          return;
        var n;
        if (typeof r == "function")
          n = r.propTypes;
        else if (typeof r == "object" && (r.$$typeof === g || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        r.$$typeof === T))
          n = r.propTypes;
        else
          return;
        if (n) {
          var o = j(r);
          Xe(n, e.props, "prop", o, e);
        } else if (r.PropTypes !== void 0 && !ae) {
          ae = !0;
          var l = j(r);
          h("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", l || "Unknown");
        }
        typeof r.getDefaultProps == "function" && !r.getDefaultProps.isReactClassApproved && h("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function hr(e) {
      {
        for (var r = Object.keys(e.props), n = 0; n < r.length; n++) {
          var o = r[n];
          if (o !== "children" && o !== "key") {
            $(e), h("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", o), $(null);
            break;
          }
        }
        e.ref !== null && ($(e), h("Invalid attribute `ref` supplied to `React.Fragment`."), $(null));
      }
    }
    var ke = {};
    function je(e, r, n, o, l, f) {
      {
        var c = Ue(e);
        if (!c) {
          var u = "";
          (e === void 0 || typeof e == "object" && e !== null && Object.keys(e).length === 0) && (u += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var C = cr();
          C ? u += C : u += xe();
          var d;
          e === null ? d = "null" : te(e) ? d = "array" : e !== void 0 && e.$$typeof === t ? (d = "<" + (j(e.type) || "Unknown") + " />", u = " Did you accidentally export a JSX literal instead of a component?") : d = typeof e, h("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", d, u);
        }
        var p = ur(e, r, n, l, f);
        if (p == null)
          return p;
        if (c) {
          var S = r.children;
          if (S !== void 0)
            if (o)
              if (te(S)) {
                for (var L = 0; L < S.length; L++)
                  Me(S[L], e);
                Object.freeze && Object.freeze(S);
              } else
                h("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              Me(S, e);
        }
        if (W.call(r, "key")) {
          var I = j(e), _ = Object.keys(r).filter(function(yr) {
            return yr !== "key";
          }), ie = _.length > 0 ? "{key: someKey, " + _.join(": ..., ") + ": ...}" : "{key: someKey}";
          if (!ke[I + ie]) {
            var gr = _.length > 0 ? "{" + _.join(": ..., ") + ": ...}" : "{}";
            h(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, ie, I, gr, I), ke[I + ie] = !0;
          }
        }
        return e === m ? hr(p) : fr(p), p;
      }
    }
    function mr(e, r, n) {
      return je(e, r, n, !0);
    }
    function dr(e, r, n) {
      return je(e, r, n, !1);
    }
    var pr = dr, vr = mr;
    z.Fragment = m, z.jsx = pr, z.jsxs = vr;
  }()), z;
}
process.env.NODE_ENV === "production" ? ue.exports = wr() : ue.exports = Sr();
var Le = ue.exports, w = /* @__PURE__ */ ((i) => (i.THEME_CHANGED = "theme_changed", i.THEME_LOADING = "theme_loading", i.THEME_LOADED = "theme_loaded", i.THEME_ERROR = "theme_error", i.THEME_RESET = "theme_reset", i))(w || {});
class Pr {
  constructor(t = {}) {
    N(this, "currentTheme");
    N(this, "themeConfig");
    N(this, "options");
    N(this, "listeners", []);
    N(this, "loadedStylesheets", /* @__PURE__ */ new Set());
    this.options = {
      enableAutoDetection: !0,
      enablePersistence: !0,
      storageKey: "crm-app-theme",
      defaultTheme: M.CRM,
      ...t
    }, this.currentTheme = this.detectInitialTheme(), this.themeConfig = this.getThemeConfigForMode(this.currentTheme);
  }
  /**
   * Initialize the theme manager
   */
  async initialize() {
    try {
      this.emitEvent(w.THEME_LOADING, this.currentTheme), await this.applyTheme(this.currentTheme), this.emitEvent(w.THEME_LOADED, this.currentTheme), E.info("Theme manager initialized", { theme: this.currentTheme });
    } catch (t) {
      const a = t instanceof Error ? t.message : "Theme initialization failed";
      throw this.emitEvent(w.THEME_ERROR, this.currentTheme, { error: a }), E.error("Theme manager initialization failed:", t), t;
    }
  }
  /**
   * Switch to a different theme
   */
  async switchTheme(t) {
    if (t === this.currentTheme)
      return;
    const a = this.currentTheme;
    try {
      this.emitEvent(w.THEME_LOADING, t, { previousTheme: a }), this.currentTheme = t, this.themeConfig = this.getThemeConfigForMode(t), await this.applyTheme(t), this.options.enablePersistence && this.persistTheme(t), this.emitEvent(w.THEME_CHANGED, t, { previousTheme: a }), E.info("Theme switched", { from: a, to: t });
    } catch (m) {
      this.currentTheme = a, this.themeConfig = this.getThemeConfigForMode(a);
      const y = m instanceof Error ? m.message : "Theme switch failed";
      throw this.emitEvent(w.THEME_ERROR, t, { error: y, previousTheme: a }), E.error("Theme switch failed:", m), m;
    }
  }
  /**
   * Reset theme to deployment context default
   */
  async resetTheme() {
    const t = this.options.enableAutoDetection ? this.detectThemeFromDeploymentContext() : this.options.defaultTheme;
    await this.switchTheme(t), this.options.enablePersistence && this.clearPersistedTheme(), this.emitEvent(w.THEME_RESET, t);
  }
  /**
   * Get current theme
   */
  getCurrentTheme() {
    return this.currentTheme;
  }
  /**
   * Get current theme configuration
   */
  getThemeConfig() {
    return this.themeConfig;
  }
  /**
   * Subscribe to theme events
   */
  onThemeChange(t) {
    return this.listeners.push(t), () => {
      const a = this.listeners.indexOf(t);
      a > -1 && this.listeners.splice(a, 1);
    };
  }
  /**
   * Apply theme to the DOM
   */
  async applyTheme(t) {
    document.documentElement.setAttribute("data-theme", t), document.body.setAttribute("data-theme", t), await this.loadThemeStylesheet(t), this.applyCSSCustomProperties(this.themeConfig), this.updateMetaThemeColor(this.themeConfig.primaryColor);
  }
  /**
   * Load theme-specific CSS stylesheet
   */
  async loadThemeStylesheet(t) {
    const a = `theme-${t}`;
    if (this.removeThemeStylesheets(), !this.loadedStylesheets.has(a))
      return new Promise((m, y) => {
        const s = document.createElement("link");
        s.id = a, s.rel = "stylesheet", s.type = "text/css", s.href = this.getThemeStylesheetPath(t), s.onload = () => {
          this.loadedStylesheets.add(a), m();
        }, s.onerror = () => {
          y(new Error(`Failed to load theme stylesheet: ${s.href}`));
        }, document.head.appendChild(s);
      });
  }
  /**
   * Remove existing theme stylesheets
   */
  removeThemeStylesheets() {
    document.querySelectorAll('link[id^="theme-"]').forEach((a) => {
      a.remove(), this.loadedStylesheets.delete(a.id);
    });
  }
  /**
   * Get theme stylesheet path
   */
  getThemeStylesheetPath(t) {
    return `/src/shared/styles/themes/${{
      [M.CRM]: "dynamics-crm-theme.css",
      [M.MFE]: "zb-champion-mfe-theme.css"
    }[t]}`;
  }
  /**
   * Apply CSS custom properties to document root
   */
  applyCSSCustomProperties(t) {
    const a = document.documentElement;
    a.style.setProperty("--theme-primary", t.primaryColor), a.style.setProperty("--theme-secondary", t.secondaryColor), a.style.setProperty("--theme-bg-primary", t.backgroundColor), a.style.setProperty("--theme-text-primary", t.textColor), a.style.setProperty("--theme-border-primary", t.borderColor), a.style.setProperty("--theme-font-family", t.fontFamily), Object.entries(t.customProperties).forEach(([m, y]) => {
      a.style.setProperty(m, y);
    });
  }
  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(t) {
    let a = document.querySelector('meta[name="theme-color"]');
    a || (a = document.createElement("meta"), a.setAttribute("name", "theme-color"), document.head.appendChild(a)), a.setAttribute("content", t);
  }
  /**
   * Detect initial theme based on options
   */
  detectInitialTheme() {
    if (this.options.enablePersistence) {
      const t = this.getPersistedTheme();
      if (t)
        return t;
    }
    return this.options.enableAutoDetection ? this.detectThemeFromDeploymentContext() : this.options.defaultTheme;
  }
  /**
   * Detect theme from deployment context
   */
  detectThemeFromDeploymentContext() {
    try {
      return _r().mode;
    } catch (t) {
      return E.warn("Failed to detect theme from deployment context:", t), this.options.defaultTheme;
    }
  }
  /**
   * Get theme configuration for a specific mode
   */
  getThemeConfigForMode(t) {
    try {
      const a = Rr();
      return a.theme.mode === t ? a.theme : this.createThemeConfigForMode(t);
    } catch (a) {
      return E.warn("Failed to get theme config from deployment context:", a), this.createThemeConfigForMode(t);
    }
  }
  /**
   * Create theme configuration for a specific mode
   */
  createThemeConfigForMode(t) {
    switch (t) {
      case M.CRM:
        return {
          mode: M.CRM,
          primaryColor: "#0078d4",
          secondaryColor: "#106ebe",
          backgroundColor: "#ffffff",
          textColor: "#323130",
          borderColor: "#8a8886",
          fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
          customProperties: {
            "--crm-header-bg": "#0078d4",
            "--crm-sidebar-bg": "#f3f2f1",
            "--crm-card-bg": "#ffffff",
            "--crm-border-radius": "2px",
            "--crm-shadow": "0 2px 4px rgba(0, 0, 0, 0.1)"
          }
        };
      case M.MFE:
        return {
          mode: M.MFE,
          primaryColor: "#6366f1",
          secondaryColor: "#4f46e5",
          backgroundColor: "#f8fafc",
          textColor: "#1e293b",
          borderColor: "#e2e8f0",
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
          customProperties: {
            "--mfe-header-bg": "#1e293b",
            "--mfe-sidebar-bg": "#f1f5f9",
            "--mfe-card-bg": "#ffffff",
            "--mfe-border-radius": "8px",
            "--mfe-shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
          }
        };
      default:
        throw new Error(`Unsupported theme mode: ${t}`);
    }
  }
  /**
   * Persist theme to storage
   */
  persistTheme(t) {
    try {
      localStorage.setItem(this.options.storageKey, t);
    } catch (a) {
      E.warn("Failed to persist theme:", a);
    }
  }
  /**
   * Get persisted theme from storage
   */
  getPersistedTheme() {
    try {
      const t = localStorage.getItem(this.options.storageKey);
      if (t && Object.values(M).includes(t))
        return t;
    } catch (t) {
      E.warn("Failed to get persisted theme:", t);
    }
    return null;
  }
  /**
   * Clear persisted theme from storage
   */
  clearPersistedTheme() {
    try {
      localStorage.removeItem(this.options.storageKey);
    } catch (t) {
      E.warn("Failed to clear persisted theme:", t);
    }
  }
  /**
   * Emit theme event
   */
  emitEvent(t, a, m) {
    const y = {
      event: t,
      theme: a,
      timestamp: /* @__PURE__ */ new Date(),
      ...m
    };
    this.listeners.forEach((s) => {
      try {
        s(y);
      } catch (x) {
        E.error("Error in theme event listener:", x);
      }
    }), E.debug("Theme event emitted:", y);
  }
}
const Ne = br(null), kr = ({
  children: i,
  defaultTheme: t = M.CRM,
  enableAutoDetection: a = !0,
  enablePersistence: m = !0,
  storageKey: y = "crm-app-theme"
}) => {
  const [s] = Y(() => new Pr({
    defaultTheme: t,
    enableAutoDetection: a,
    enablePersistence: m,
    storageKey: y
  })), [x, P] = Y(t), [g, O] = Y(s.getThemeConfig()), [b, T] = Y(!0), [F, R] = Y(null);
  De(() => {
    (async () => {
      try {
        T(!0), R(null), await s.initialize(), P(s.getCurrentTheme()), O(s.getThemeConfig()), T(!1), E.info("Theme provider initialized", {
          theme: s.getCurrentTheme()
        });
      } catch (v) {
        const D = v instanceof Error ? v.message : "Theme initialization failed";
        R(D), T(!1), E.error("Theme provider initialization failed:", v);
      }
    })();
  }, [s]), De(() => s.onThemeChange((v) => {
    switch (v.event) {
      case w.THEME_LOADING:
        T(!0), R(null);
        break;
      case w.THEME_CHANGED:
      case w.THEME_LOADED:
      case w.THEME_RESET:
        P(v.theme), O(s.getThemeConfig()), T(!1), R(null);
        break;
      case w.THEME_ERROR:
        T(!1), R(v.error || "Theme error occurred");
        break;
    }
  }), [s]);
  const G = se(async (h) => {
    try {
      await s.switchTheme(h);
    } catch (v) {
      const D = v instanceof Error ? v.message : "Theme switch failed";
      R(D), E.error("Theme switch failed:", v);
    }
  }, [s]), X = se(async () => {
    try {
      await s.resetTheme();
    } catch (h) {
      const v = h instanceof Error ? h.message : "Theme reset failed";
      R(v), E.error("Theme reset failed:", h);
    }
  }, [s]), Z = se(async (h) => {
    try {
      await s.switchTheme(h);
    } catch (v) {
      const D = v instanceof Error ? v.message : "Theme application failed";
      R(D), E.error("Theme application failed:", v);
    }
  }, [s]), k = {
    currentTheme: x,
    themeConfig: g,
    isLoading: b,
    error: F,
    switchTheme: G,
    resetTheme: X,
    applyTheme: Z
  };
  return /* @__PURE__ */ Le.jsx(Ne.Provider, { value: k, children: i });
}, H = () => {
  const i = Cr(Ne);
  if (!i)
    throw new Error("useTheme must be used within a ThemeProvider");
  return i;
}, jr = () => {
  const { currentTheme: i } = H();
  return i;
}, Dr = () => {
  const { themeConfig: i } = H();
  return i;
}, Ar = (i) => {
  const { currentTheme: t } = H();
  return t === i;
}, Ir = () => {
  const { themeConfig: i } = H();
  return {
    primary: i.primaryColor,
    secondary: i.secondaryColor,
    background: i.backgroundColor,
    text: i.textColor,
    border: i.borderColor,
    fontFamily: i.fontFamily,
    ...i.customProperties
  };
}, $r = () => {
  const { currentTheme: i, themeConfig: t } = H();
  return {
    currentTheme: i,
    themeConfig: t,
    getThemeClass: (s) => `${s} theme-${i}`,
    getThemeStyle: (s) => s[i] || {},
    getCSSVariable: (s) => getComputedStyle(document.documentElement).getPropertyValue(s)
  };
}, Lr = (i) => {
  const t = (a) => {
    const m = H();
    return /* @__PURE__ */ Le.jsx(i, { ...a, theme: m });
  };
  return t.displayName = `withTheme(${i.displayName || i.name})`, t;
};
export {
  w as T,
  Pr as a,
  kr as b,
  jr as c,
  Dr as d,
  Ar as e,
  Ir as f,
  $r as g,
  Le as j,
  H as u,
  Lr as w
};
