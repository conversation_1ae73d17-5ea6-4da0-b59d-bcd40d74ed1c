{"version": 3, "file": "ResponseHandler.mjs", "sources": ["../../src/response/ResponseHandler.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.stateNotFound", "ClientAuthErrorCodes.invalidState", "ClientAuthErrorCodes.stateMismatch", "ClientAuthErrorCodes.nonceMismatch", "ClientAuthErrorCodes.authTimeNotFound", "ClientAuthErrorCodes.invalidCacheEnvironment", "CacheHelpers.createIdTokenEntity", "CacheHelpers.createAccessTokenEntity", "CacheHelpers.createRefreshTokenEntity", "ClientAuthErrorCodes.keyIdMissing"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAoDH,SAAS,kBAAkB,CACvB,cAA+C,EAAA;IAE/C,MAAM,eAAe,GAAG,OAAO,CAAC;IAChC,MAAM,oBAAoB,GACtB,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;AAC3D,IAAA,OAAO,oBAAoB,IAAI,oBAAoB,IAAI,CAAC;AACpD,UAAE,cAAc,CAAC,SAAS,EAAE,SAAS,CAC/B,oBAAoB,GAAG,eAAe,CAAC,MAAM,CAChD;UACD,SAAS,CAAC;AACpB,CAAC;AAED;;;AAGG;MACU,eAAe,CAAA;AAUxB,IAAA,WAAA,CACI,QAAgB,EAChB,YAA0B,EAC1B,SAAkB,EAClB,MAAc,EACd,iBAAiD,EACjD,iBAAsC,EACtC,iBAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;;;AAKG;IACH,uCAAuC,CACnC,cAA+C,EAC/C,YAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;YACxC,MAAM,cAAc,CAAC,KAAK;kBACpB,qBAAqB,CACjBA,aAAkC,EAClC,cAAc,CACjB;kBACD,qBAAqB,CACjBA,aAAkC,EAClC,cAAc,CACjB,CAAC;AACX,SAAA;AAED,QAAA,IAAI,0BAAkC,CAAC;AACvC,QAAA,IAAI,mBAA2B,CAAC;QAEhC,IAAI;AACA,YAAA,0BAA0B,GAAG,kBAAkB,CAC3C,cAAc,CAAC,KAAK,CACvB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,MAAM,qBAAqB,CACvBC,YAAiC,EACjC,cAAc,CAAC,KAAK,CACvB,CAAC;AACL,SAAA;QAED,IAAI;AACA,YAAA,mBAAmB,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC1D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,MAAM,qBAAqB,CACvBA,YAAiC,EACjC,cAAc,CAAC,KAAK,CACvB,CAAC;AACL,SAAA;QAED,IAAI,0BAA0B,KAAK,mBAAmB,EAAE;AACpD,YAAA,MAAM,qBAAqB,CAACC,aAAkC,CAAC,CAAC;AACnE,SAAA;;QAGD,IACI,cAAc,CAAC,KAAK;AACpB,YAAA,cAAc,CAAC,iBAAiB;YAChC,cAAc,CAAC,QAAQ,EACzB;AACE,YAAA,MAAM,aAAa,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;AACzD,YAAA,IACI,0BAA0B,CACtB,cAAc,CAAC,KAAK,EACpB,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,CAC1B,EACH;AACE,gBAAA,MAAM,IAAI,4BAA4B,CAClC,cAAc,CAAC,KAAK,IAAI,EAAE,EAC1B,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,IAAI,EAAE,EAC9B,cAAc,CAAC,QAAQ,IAAI,EAAE,EAC7B,cAAc,CAAC,cAAc,IAAI,EAAE,EACnC,cAAc,CAAC,MAAM,IAAI,EAAE,EAC3B,aAAa,CAChB,CAAC;AACL,aAAA;AAED,YAAA,MAAM,IAAI,WAAW,CACjB,cAAc,CAAC,KAAK,IAAI,EAAE,EAC1B,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,aAAa,CAChB,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;IACH,qBAAqB,CACjB,cAAgD,EAChD,kBAA4B,EAAA;;QAG5B,IACI,cAAc,CAAC,KAAK;AACpB,YAAA,cAAc,CAAC,iBAAiB;YAChC,cAAc,CAAC,QAAQ,EACzB;AACE,YAAA,MAAM,SAAS,GAAG,CAAA,UAAA,EACd,cAAc,CAAC,WAAW,IAAI,SAAS,CAAC,aAC5C,CACI,cAAA,EAAA,cAAc,CAAC,SAAS,IAAI,SAAS,CAAC,aAC1C,mBACI,cAAc,CAAC,iBAAiB,IAAI,SAAS,CAAC,aAClD,CAAA,mBAAA,EACI,cAAc,CAAC,cAAc,IAAI,SAAS,CAAC,aAC/C,CACI,aAAA,EAAA,cAAc,CAAC,QAAQ,IAAI,SAAS,CAAC,aACzC,EAAE,CAAC;AACH,YAAA,MAAM,aAAa,GAAG,cAAc,CAAC,WAAW,EAAE,MAAM;AACpD,kBAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;kBAC7B,SAAS,CAAC;YAChB,MAAM,WAAW,GAAG,IAAI,WAAW,CAC/B,cAAc,CAAC,KAAK,EACpB,SAAS,EACT,cAAc,CAAC,QAAQ,EACvB,aAAa,EACb,cAAc,CAAC,MAAM,CACxB,CAAC;;AAGF,YAAA,IACI,kBAAkB;AAClB,gBAAA,cAAc,CAAC,MAAM;AACrB,gBAAA,cAAc,CAAC,MAAM,IAAI,UAAU,CAAC,wBAAwB;AAC5D,gBAAA,cAAc,CAAC,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAC5D;gBACE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAA6H,0HAAA,EAAA,WAAW,CAAE,CAAA,CAC7I,CAAC;;gBAGF,OAAO;;AAEV,aAAA;AAAM,iBAAA,IACH,kBAAkB;AAClB,gBAAA,cAAc,CAAC,MAAM;AACrB,gBAAA,cAAc,CAAC,MAAM,IAAI,UAAU,CAAC,wBAAwB;AAC5D,gBAAA,cAAc,CAAC,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAC5D;gBACE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAsH,mHAAA,EAAA,WAAW,CAAE,CAAA,CACtI,CAAC;;gBAGF,OAAO;AACV,aAAA;AAED,YAAA,IACI,0BAA0B,CACtB,cAAc,CAAC,KAAK,EACpB,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,CAC1B,EACH;gBACE,MAAM,IAAI,4BAA4B,CAClC,cAAc,CAAC,KAAK,EACpB,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAClD,cAAc,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EACjD,cAAc,CAAC,cAAc,IAAI,SAAS,CAAC,YAAY,EACvD,cAAc,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,EAC/C,aAAa,CAChB,CAAC;AACL,aAAA;AAED,YAAA,MAAM,WAAW,CAAC;AACrB,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,MAAM,yBAAyB,CAC3B,mBAAqD,EACrD,SAAoB,EACpB,YAAoB,EACpB,OAAwB,EACxB,eAA0C,EAC1C,iBAA0B,EAC1B,4BAAsC,EACtC,8BAAwC,EACxC,eAAwB,EAAA;AAExB,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,yBAAyB,EAC3C,mBAAmB,CAAC,cAAc,CACrC,CAAC;;AAGF,QAAA,IAAI,aAAsC,CAAC;QAC3C,IAAI,mBAAmB,CAAC,QAAQ,EAAE;AAC9B,YAAA,aAAa,GAAG,kBAAkB,CAC9B,mBAAmB,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EACtD,IAAI,CAAC,SAAS,CAAC,YAAY,CAC9B,CAAC;;AAGF,YAAA,IAAI,eAAe,IAAI,eAAe,CAAC,KAAK,EAAE;AAC1C,gBAAA,IAAI,aAAa,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,EAAE;AAC/C,oBAAA,MAAM,qBAAqB,CACvBC,aAAkC,CACrC,CAAC;AACL,iBAAA;AACJ,aAAA;;YAGD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACxC,gBAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,CAAC;gBACzC,IAAI,CAAC,QAAQ,EAAE;AACX,oBAAA,MAAM,qBAAqB,CACvBC,gBAAqC,CACxC,CAAC;AACL,iBAAA;AAED,gBAAA,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAC5D,mBAAmB,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,EACzD,SAAS,CAAC,aAAa,EACvB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,aAAa,CAChB,CAAC;;AAGF,QAAA,IAAI,eAA+C,CAAC;QACpD,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,eAAe,CAAC,KAAK,EAAE;AAC9C,YAAA,eAAe,GAAG,aAAa,CAAC,iBAAiB,CAC7C,IAAI,CAAC,SAAS,EACd,eAAe,CAAC,KAAK,CACxB,CAAC;AACL,SAAA;;AAGD,QAAA,mBAAmB,CAAC,MAAM;YACtB,mBAAmB,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;QAE9D,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CACxC,mBAAmB,EACnB,SAAS,EACT,YAAY,EACZ,OAAO,EACP,aAAa,EACb,iBAAiB,EACjB,eAAe,CAClB,CAAC;AACF,QAAA,IAAI,YAAY,CAAC;QACjB,IAAI;AACA,YAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAClD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gDAAgD,CACnD,CAAC;gBACF,YAAY,GAAG,IAAI,iBAAiB,CAChC,IAAI,CAAC,iBAAiB,EACtB,IAAI,CACP,CAAC;gBACF,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChE,aAAA;AACD;;;;;AAKG;AACH,YAAA,IACI,4BAA4B;AAC5B,gBAAA,CAAC,8BAA8B;gBAC/B,WAAW,CAAC,OAAO,EACrB;gBACE,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;AACrD,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CACxC,GAAG,EACH,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,MAAM,CACd,CAAC;gBACF,IAAI,CAAC,OAAO,EAAE;AACV,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qGAAqG,CACxG,CAAC;oBACF,OAAO,MAAM,eAAe,CAAC,4BAA4B,CACrD,IAAI,CAAC,SAAS,EACd,SAAS,EACT,WAAW,EACX,KAAK,EACL,OAAO,EACP,aAAa,EACb,eAAe,EACf,SAAS,EACT,eAAe,CAClB,CAAC;AACL,iBAAA;AACJ,aAAA;AACD,YAAA,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CACnC,WAAW,EACX,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,YAAY,CACvB,CAAC;AACL,SAAA;AAAS,gBAAA;YACN,IACI,IAAI,CAAC,iBAAiB;AACtB,gBAAA,IAAI,CAAC,iBAAiB;AACtB,gBAAA,YAAY,EACd;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+CAA+C,CAClD,CAAC;gBACF,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC/D,aAAA;AACJ,SAAA;QAED,OAAO,eAAe,CAAC,4BAA4B,CAC/C,IAAI,CAAC,SAAS,EACd,SAAS,EACT,WAAW,EACX,KAAK,EACL,OAAO,EACP,aAAa,EACb,eAAe,EACf,mBAAmB,EACnB,eAAe,CAClB,CAAC;KACL;AAED;;;;;AAKG;AACK,IAAA,mBAAmB,CACvB,mBAAqD,EACrD,SAAoB,EACpB,YAAoB,EACpB,OAAwB,EACxB,aAA2B,EAC3B,iBAA0B,EAC1B,eAA0C,EAAA;AAE1C,QAAA,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAC1C,IAAI,CAAC,GAAG,EAAE;AACN,YAAA,MAAM,qBAAqB,CACvBC,uBAA4C,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,cAAc,GAAG,4BAA4B,CAAC,aAAa,CAAC,CAAC;;AAGnE,QAAA,IAAI,aAAwC,CAAC;AAC7C,QAAA,IAAI,aAAwC,CAAC;AAC7C,QAAA,IAAI,mBAAmB,CAAC,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE;YACjD,aAAa,GAAGC,mBAAgC,CAC5C,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,QAAQ,EAC5B,IAAI,CAAC,QAAQ,EACb,cAAc,IAAI,EAAE,CACvB,CAAC;AAEF,YAAA,aAAa,GAAG,mBAAmB,CAC/B,IAAI,CAAC,YAAY,EACjB,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,SAAS,CAAC,YAAY,EAC3B,OAAO,CAAC,aAAa,EACrB,aAAa,EACb,mBAAmB,CAAC,WAAW,EAC/B,GAAG,EACH,cAAc,EACd,eAAe,EACf,SAAS;YACT,IAAI,CAAC,MAAM,CACd,CAAC;AACL,SAAA;;QAGD,IAAI,iBAAiB,GAA6B,IAAI,CAAC;QACvD,IAAI,mBAAmB,CAAC,YAAY,EAAE;;AAElC,YAAA,MAAM,cAAc,GAAG,mBAAmB,CAAC,KAAK;kBAC1C,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC;kBAC9C,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAEzC;;;AAGG;YACH,MAAM,SAAS,GACX,CAAC,OAAO,mBAAmB,CAAC,UAAU,KAAK,QAAQ;kBAC7C,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC;AAC9C,kBAAE,mBAAmB,CAAC,UAAU,KAAK,CAAC,CAAC;YAC/C,MAAM,YAAY,GACd,CAAC,OAAO,mBAAmB,CAAC,cAAc,KAAK,QAAQ;kBACjD,QAAQ,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC;AAClD,kBAAE,mBAAmB,CAAC,cAAc,KAAK,CAAC,CAAC;YACnD,MAAM,SAAS,GACX,CAAC,OAAO,mBAAmB,CAAC,UAAU,KAAK,QAAQ;kBAC7C,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC;AAC9C,kBAAE,mBAAmB,CAAC,UAAU,KAAK,SAAS,CAAC;AACvD,YAAA,MAAM,sBAAsB,GAAG,YAAY,GAAG,SAAS,CAAC;AACxD,YAAA,MAAM,8BAA8B,GAChC,sBAAsB,GAAG,YAAY,CAAC;AAC1C,YAAA,MAAM,gBAAgB,GAClB,SAAS,IAAI,SAAS,GAAG,CAAC;kBACpB,YAAY,GAAG,SAAS;kBACxB,SAAS,CAAC;;AAGpB,YAAA,iBAAiB,GAAGC,uBAAoC,CACpD,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,YAAY,EAChC,IAAI,CAAC,QAAQ,EACb,cAAc,IAAI,SAAS,CAAC,MAAM,IAAI,EAAE,EACxC,cAAc,CAAC,WAAW,EAAE,EAC5B,sBAAsB,EACtB,8BAA8B,EAC9B,IAAI,CAAC,SAAS,CAAC,YAAY,EAC3B,gBAAgB,EAChB,mBAAmB,CAAC,UAAU,EAC9B,iBAAiB,EACjB,mBAAmB,CAAC,MAAM,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;;QAGD,IAAI,kBAAkB,GAA8B,IAAI,CAAC;QACzD,IAAI,mBAAmB,CAAC,aAAa,EAAE;AACnC,YAAA,IAAI,WAA+B,CAAC;YACpC,IAAI,mBAAmB,CAAC,wBAAwB,EAAE;AAC9C,gBAAA,MAAM,WAAW,GACb,OAAO,mBAAmB,CAAC,wBAAwB;oBACnD,QAAQ;sBACF,QAAQ,CACJ,mBAAmB,CAAC,wBAAwB,EAC5C,EAAE,CACL;AACH,sBAAE,mBAAmB,CAAC,wBAAwB,CAAC;AACvD,gBAAA,WAAW,GAAG,YAAY,GAAG,WAAW,CAAC;AAC5C,aAAA;YACD,kBAAkB,GAAGC,wBAAqC,CACtD,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,aAAa,EACjC,IAAI,CAAC,QAAQ,EACb,mBAAmB,CAAC,IAAI,EACxB,iBAAiB,EACjB,WAAW,CACd,CAAC;AACL,SAAA;;QAGD,IAAI,iBAAiB,GAA6B,IAAI,CAAC;QACvD,IAAI,mBAAmB,CAAC,IAAI,EAAE;AAC1B,YAAA,iBAAiB,GAAG;gBAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,gBAAA,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,mBAAmB,CAAC,IAAI;aACrC,CAAC;AACL,SAAA;QAED,OAAO;AACH,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,WAAW,EAAE,iBAAiB;AAC9B,YAAA,YAAY,EAAE,kBAAkB;AAChC,YAAA,WAAW,EAAE,iBAAiB;SACjC,CAAC;KACL;AAED;;;;;;;;;AASG;IACH,aAAa,4BAA4B,CACrC,SAAkB,EAClB,SAAoB,EACpB,WAAwB,EACxB,cAAuB,EACvB,OAAwB,EACxB,aAA2B,EAC3B,YAAiC,EACjC,mBAAsD,EACtD,SAAkB,EAAA;AAElB,QAAA,IAAI,WAAW,GAAW,SAAS,CAAC,YAAY,CAAC;QACjD,IAAI,cAAc,GAAkB,EAAE,CAAC;QACvC,IAAI,SAAS,GAAgB,IAAI,CAAC;AAClC,QAAA,IAAI,YAA8B,CAAC;AACnC,QAAA,IAAI,SAA2B,CAAC;AAChC,QAAA,IAAI,QAAQ,GAAW,SAAS,CAAC,YAAY,CAAC;QAE9C,IAAI,WAAW,CAAC,WAAW,EAAE;AACzB;;;AAGG;AACH,YAAA,IACI,WAAW,CAAC,WAAW,CAAC,SAAS;AAC7B,gBAAA,oBAAoB,CAAC,GAAG;gBAC5B,CAAC,OAAO,CAAC,MAAM,EACjB;AACE,gBAAA,MAAM,iBAAiB,GACnB,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBACrC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,WAAW,CAAC;gBAElD,IAAI,CAAC,KAAK,EAAE;AACR,oBAAA,MAAM,qBAAqB,CACvBC,YAAiC,CACpC,CAAC;AACL,iBAAA;AAED,gBAAA,WAAW,GAAG,MAAM,iBAAiB,CAAC,YAAY,CAC9C,MAAM,EACN,KAAK,EACL,OAAO,CACV,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;AAChD,aAAA;AACD,YAAA,cAAc,GAAG,QAAQ,CAAC,UAAU,CAChC,WAAW,CAAC,WAAW,CAAC,MAAM,CACjC,CAAC,OAAO,EAAE,CAAC;AACZ,YAAA,SAAS,GAAG,IAAI,IAAI,CAChB,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CACnD,CAAC;AACF,YAAA,YAAY,GAAG,IAAI,IAAI,CACnB,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAC3D,CAAC;AACF,YAAA,IAAI,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE;AACnC,gBAAA,SAAS,GAAG,IAAI,IAAI,CAChB,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CACnD,CAAC;AACL,aAAA;AACJ,SAAA;QAED,IAAI,WAAW,CAAC,WAAW,EAAE;YACzB,QAAQ;AACJ,gBAAA,WAAW,CAAC,WAAW,CAAC,QAAQ,KAAK,aAAa;AAC9C,sBAAE,aAAa;sBACb,EAAE,CAAC;AAChB,SAAA;QACD,MAAM,GAAG,GAAG,aAAa,EAAE,GAAG,IAAI,aAAa,EAAE,GAAG,IAAI,EAAE,CAAC;AAC3D,QAAA,MAAM,GAAG,GAAG,aAAa,EAAE,GAAG,IAAI,EAAE,CAAC;;QAGrC,IAAI,mBAAmB,EAAE,aAAa,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE;YAC7D,WAAW,CAAC,OAAO,CAAC,eAAe;gBAC/B,mBAAmB,EAAE,aAAa,CAAC;AAC1C,SAAA;AAED,QAAA,MAAM,WAAW,GAAuB,WAAW,CAAC,OAAO;AACvD,cAAE,8BAA8B,CAC1B,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,EACpC,SAAS;AACT,YAAA,aAAa,EACb,WAAW,CAAC,OAAO,EAAE,MAAM,CAC9B;cACD,IAAI,CAAC;QAEX,OAAO;YACH,SAAS,EAAE,SAAS,CAAC,kBAAkB;AACvC,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,MAAM,EAAE,cAAc;AACtB,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,IAAI,EAAE;YAC3C,aAAa,EAAE,aAAa,IAAI,EAAE;AAClC,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,SAAS,EAAE,cAAc;AACzB,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,YAAY,EAAE,YAAY;AAC1B,YAAA,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE,OAAO,CAAC,aAAa;AACpC,YAAA,SAAS,EAAE,SAAS,IAAI,SAAS,CAAC,YAAY;AAC9C,YAAA,QAAQ,EAAE,QAAQ;YAClB,SAAS,EACL,WAAW,CAAC,WAAW,EAAE,SAAS,IAAI,SAAS,CAAC,YAAY;AAChE,YAAA,KAAK,EAAE,YAAY;kBACb,YAAY,CAAC,gBAAgB;kBAC7B,SAAS,CAAC,YAAY;AAC5B,YAAA,kBAAkB,EACd,WAAW,CAAC,OAAO,EAAE,kBAAkB;AACvC,gBAAA,SAAS,CAAC,YAAY;YAC1B,WAAW,EACP,WAAW,CAAC,OAAO,EAAE,WAAW,IAAI,SAAS,CAAC,YAAY;YAC9D,IAAI,EAAE,mBAAmB,EAAE,QAAQ;AACnC,YAAA,gBAAgB,EAAE,KAAK;SAC1B,CAAC;KACL;AACJ,CAAA;AAEK,SAAU,mBAAmB,CAC/B,YAA0B,EAC1B,SAAoB,EACpB,aAAqB,EACrB,YAAuC,EACvC,aAAqB,EACrB,aAA2B,EAC3B,UAAmB,EACnB,WAAoB,EACpB,cAA8B,EAC9B,eAA0C,EAC1C,eAAwB,EACxB,MAAe,EAAA;AAEf,IAAA,MAAM,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;;AAG3C,IAAA,MAAM,WAAW,GAAG,YAAY,CAAC,cAAc,EAAE,CAAC;IAClD,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,UAAkB,KAAI;AAC3D,QAAA,OAAO,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAChD,KAAC,CAAC,CAAC;IAEH,IAAI,aAAa,GAAyB,IAAI,CAAC;AAC/C,IAAA,IAAI,cAAc,EAAE;QAChB,aAAa,GAAG,YAAY,CAAC,UAAU,CACnC,cAAc,EACd,aAAa,EACb,MAAM,CACT,CAAC;AACL,KAAA;IAED,MAAM,WAAW,GACb,aAAa;QACb,aAAa,CAAC,aAAa,CACvB;YACI,aAAa;YACb,aAAa;YACb,UAAU;YACV,WAAW;YACX,kBAAkB,EAAE,eAAe,EAAE,qBAAqB;YAC1D,WAAW,EAAE,eAAe,EAAE,YAAY;AAC1C,YAAA,eAAe,EAAE,eAAe;AACnC,SAAA,EACD,SAAS,EACT,YAAY,CACf,CAAC;AAEN,IAAA,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,IAAI,EAAE,CAAC;AACxD,IAAA,MAAM,QAAQ,GAAG,cAAc,IAAI,WAAW,CAAC,KAAK,CAAC;AACrD,IAAA,IACI,QAAQ;AACR,QAAA,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,aAAa,KAAI;AACnC,YAAA,OAAO,aAAa,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC/C,SAAC,CAAC,EACJ;AACE,QAAA,MAAM,gBAAgB,GAAG,kBAAkB,CACvC,aAAa,EACb,WAAW,CAAC,cAAc,EAC1B,QAAQ,EACR,aAAa,CAChB,CAAC;AACF,QAAA,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACzC,KAAA;AACD,IAAA,WAAW,CAAC,cAAc,GAAG,cAAc,CAAC;AAE5C,IAAA,OAAO,WAAW,CAAC;AACvB;;;;"}