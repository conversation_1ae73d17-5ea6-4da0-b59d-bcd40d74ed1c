"use strict";var Et=Object.defineProperty;var St=(t,e,r)=>e in t?Et(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var T=(t,e,r)=>St(t,typeof e!="symbol"?e+"":e,r);const y=require("./logger-B08GnSxL.js"),P=require("./index-DgtzI76i.js"),L=require("react");function He(t,e){return function(){return t.apply(e,arguments)}}const{toString:At}=Object.prototype,{getPrototypeOf:Te}=Object,{iterator:re,toStringTag:We}=Symbol,ne=(t=>e=>{const r=At.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),D=t=>(t=t.toLowerCase(),e=>ne(e)===t),se=t=>e=>typeof e===t,{isArray:j}=Array,W=se("undefined");function V(t){return t!==null&&!W(t)&&t.constructor!==null&&!W(t.constructor)&&k(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Ve=D("ArrayBuffer");function Rt(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&Ve(t.buffer),e}const Tt=se("string"),k=se("function"),Je=se("number"),J=t=>t!==null&&typeof t=="object",Ct=t=>t===!0||t===!1,Q=t=>{if(ne(t)!=="object")return!1;const e=Te(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(We in t)&&!(re in t)},xt=t=>{if(!J(t)||V(t))return!1;try{return Object.keys(t).length===0&&Object.getPrototypeOf(t)===Object.prototype}catch{return!1}},kt=D("Date"),Ot=D("File"),Pt=D("Blob"),Lt=D("FileList"),Ut=t=>J(t)&&k(t.pipe),Dt=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||k(t.append)&&((e=ne(t))==="formdata"||e==="object"&&k(t.toString)&&t.toString()==="[object FormData]"))},It=D("URLSearchParams"),[Nt,Ft,_t,Bt]=["ReadableStream","Request","Response","Headers"].map(D),$t=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function K(t,e,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let n,s;if(typeof t!="object"&&(t=[t]),j(t))for(n=0,s=t.length;n<s;n++)e.call(null,t[n],n,t);else{if(V(t))return;const i=r?Object.getOwnPropertyNames(t):Object.keys(t),o=i.length;let c;for(n=0;n<o;n++)c=i[n],e.call(null,t[c],c,t)}}function Ke(t,e){if(V(t))return null;e=e.toLowerCase();const r=Object.keys(t);let n=r.length,s;for(;n-- >0;)if(s=r[n],e===s.toLowerCase())return s;return null}const M=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Xe=t=>!W(t)&&t!==M;function pe(){const{caseless:t}=Xe(this)&&this||{},e={},r=(n,s)=>{const i=t&&Ke(e,s)||s;Q(e[i])&&Q(n)?e[i]=pe(e[i],n):Q(n)?e[i]=pe({},n):j(n)?e[i]=n.slice():e[i]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&K(arguments[n],r);return e}const Mt=(t,e,r,{allOwnKeys:n}={})=>(K(e,(s,i)=>{r&&k(s)?t[i]=He(s,r):t[i]=s},{allOwnKeys:n}),t),vt=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),zt=(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},jt=(t,e,r,n)=>{let s,i,o;const c={};if(e=e||{},t==null)return e;do{for(s=Object.getOwnPropertyNames(t),i=s.length;i-- >0;)o=s[i],(!n||n(o,t,e))&&!c[o]&&(e[o]=t[o],c[o]=!0);t=r!==!1&&Te(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},qt=(t,e,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=e.length;const n=t.indexOf(e,r);return n!==-1&&n===r},Ht=t=>{if(!t)return null;if(j(t))return t;let e=t.length;if(!Je(e))return null;const r=new Array(e);for(;e-- >0;)r[e]=t[e];return r},Wt=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&Te(Uint8Array)),Vt=(t,e)=>{const n=(t&&t[re]).call(t);let s;for(;(s=n.next())&&!s.done;){const i=s.value;e.call(t,i[0],i[1])}},Jt=(t,e)=>{let r;const n=[];for(;(r=t.exec(e))!==null;)n.push(r);return n},Kt=D("HTMLFormElement"),Xt=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),Ue=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),Gt=D("RegExp"),Ge=(t,e)=>{const r=Object.getOwnPropertyDescriptors(t),n={};K(r,(s,i)=>{let o;(o=e(s,i,t))!==!1&&(n[i]=o||s)}),Object.defineProperties(t,n)},Qt=t=>{Ge(t,(e,r)=>{if(k(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=t[r];if(k(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Zt=(t,e)=>{const r={},n=s=>{s.forEach(i=>{r[i]=!0})};return j(t)?n(t):n(String(t).split(e)),r},Yt=()=>{},er=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function tr(t){return!!(t&&k(t.append)&&t[We]==="FormData"&&t[re])}const rr=t=>{const e=new Array(10),r=(n,s)=>{if(J(n)){if(e.indexOf(n)>=0)return;if(V(n))return n;if(!("toJSON"in n)){e[s]=n;const i=j(n)?[]:{};return K(n,(o,c)=>{const h=r(o,s+1);!W(h)&&(i[c]=h)}),e[s]=void 0,i}}return n};return r(t,0)},nr=D("AsyncFunction"),sr=t=>t&&(J(t)||k(t))&&k(t.then)&&k(t.catch),Qe=((t,e)=>t?setImmediate:e?((r,n)=>(M.addEventListener("message",({source:s,data:i})=>{s===M&&i===r&&n.length&&n.shift()()},!1),s=>{n.push(s),M.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",k(M.postMessage)),ir=typeof queueMicrotask<"u"?queueMicrotask.bind(M):typeof process<"u"&&process.nextTick||Qe,or=t=>t!=null&&k(t[re]),a={isArray:j,isArrayBuffer:Ve,isBuffer:V,isFormData:Dt,isArrayBufferView:Rt,isString:Tt,isNumber:Je,isBoolean:Ct,isObject:J,isPlainObject:Q,isEmptyObject:xt,isReadableStream:Nt,isRequest:Ft,isResponse:_t,isHeaders:Bt,isUndefined:W,isDate:kt,isFile:Ot,isBlob:Pt,isRegExp:Gt,isFunction:k,isStream:Ut,isURLSearchParams:It,isTypedArray:Wt,isFileList:Lt,forEach:K,merge:pe,extend:Mt,trim:$t,stripBOM:vt,inherits:zt,toFlatObject:jt,kindOf:ne,kindOfTest:D,endsWith:qt,toArray:Ht,forEachEntry:Vt,matchAll:Jt,isHTMLForm:Kt,hasOwnProperty:Ue,hasOwnProp:Ue,reduceDescriptors:Ge,freezeMethods:Qt,toObjectSet:Zt,toCamelCase:Xt,noop:Yt,toFiniteNumber:er,findKey:Ke,global:M,isContextDefined:Xe,isSpecCompliantForm:tr,toJSONObject:rr,isAsyncFn:nr,isThenable:sr,setImmediate:Qe,asap:ir,isIterable:or};function g(t,e,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(g,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ze=g.prototype,Ye={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Ye[t]={value:t}});Object.defineProperties(g,Ye);Object.defineProperty(Ze,"isAxiosError",{value:!0});g.from=(t,e,r,n,s,i)=>{const o=Object.create(Ze);return a.toFlatObject(t,o,function(h){return h!==Error.prototype},c=>c!=="isAxiosError"),g.call(o,t.message,e,r,n,s),o.cause=t,o.name=t.name,i&&Object.assign(o,i),o};const ar=null;function me(t){return a.isPlainObject(t)||a.isArray(t)}function et(t){return a.endsWith(t,"[]")?t.slice(0,-2):t}function De(t,e,r){return t?t.concat(e).map(function(s,i){return s=et(s),!r&&i?"["+s+"]":s}).join(r?".":""):e}function cr(t){return a.isArray(t)&&!t.some(me)}const ur=a.toFlatObject(a,{},null,function(e){return/^is[A-Z]/.test(e)});function ie(t,e,r){if(!a.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,r=a.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,p){return!a.isUndefined(p[m])});const n=r.metaTokens,s=r.visitor||l,i=r.dots,o=r.indexes,h=(r.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(e);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function u(d){if(d===null)return"";if(a.isDate(d))return d.toISOString();if(a.isBoolean(d))return d.toString();if(!h&&a.isBlob(d))throw new g("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(d)||a.isTypedArray(d)?h&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function l(d,m,p){let E=d;if(d&&!p&&typeof d=="object"){if(a.endsWith(m,"{}"))m=n?m:m.slice(0,-2),d=JSON.stringify(d);else if(a.isArray(d)&&cr(d)||(a.isFileList(d)||a.endsWith(m,"[]"))&&(E=a.toArray(d)))return m=et(m),E.forEach(function(R,N){!(a.isUndefined(R)||R===null)&&e.append(o===!0?De([m],N,i):o===null?m:m+"[]",u(R))}),!1}return me(d)?!0:(e.append(De(p,m,i),u(d)),!1)}const f=[],w=Object.assign(ur,{defaultVisitor:l,convertValue:u,isVisitable:me});function b(d,m){if(!a.isUndefined(d)){if(f.indexOf(d)!==-1)throw Error("Circular reference detected in "+m.join("."));f.push(d),a.forEach(d,function(E,A){(!(a.isUndefined(E)||E===null)&&s.call(e,E,a.isString(A)?A.trim():A,m,w))===!0&&b(E,m?m.concat(A):[A])}),f.pop()}}if(!a.isObject(t))throw new TypeError("data must be an object");return b(t),e}function Ie(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function Ce(t,e){this._pairs=[],t&&ie(t,this,e)}const tt=Ce.prototype;tt.append=function(e,r){this._pairs.push([e,r])};tt.toString=function(e){const r=e?function(n){return e.call(this,n,Ie)}:Ie;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function lr(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rt(t,e,r){if(!e)return t;const n=r&&r.encode||lr;a.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let i;if(s?i=s(e,r):i=a.isURLSearchParams(e)?e.toString():new Ce(e,r).toString(n),i){const o=t.indexOf("#");o!==-1&&(t=t.slice(0,o)),t+=(t.indexOf("?")===-1?"?":"&")+i}return t}class Ne{constructor(){this.handlers=[]}use(e,r,n){return this.handlers.push({fulfilled:e,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){a.forEach(this.handlers,function(n){n!==null&&e(n)})}}const nt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},hr=typeof URLSearchParams<"u"?URLSearchParams:Ce,dr=typeof FormData<"u"?FormData:null,fr=typeof Blob<"u"?Blob:null,pr={isBrowser:!0,classes:{URLSearchParams:hr,FormData:dr,Blob:fr},protocols:["http","https","file","blob","url","data"]},xe=typeof window<"u"&&typeof document<"u",ge=typeof navigator=="object"&&navigator||void 0,mr=xe&&(!ge||["ReactNative","NativeScript","NS"].indexOf(ge.product)<0),gr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",yr=xe&&window.location.href||"http://localhost",wr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:xe,hasStandardBrowserEnv:mr,hasStandardBrowserWebWorkerEnv:gr,navigator:ge,origin:yr},Symbol.toStringTag,{value:"Module"})),C={...wr,...pr};function br(t,e){return ie(t,new C.classes.URLSearchParams,{visitor:function(r,n,s,i){return C.isNode&&a.isBuffer(r)?(this.append(n,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...e})}function Er(t){return a.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Sr(t){const e={},r=Object.keys(t);let n;const s=r.length;let i;for(n=0;n<s;n++)i=r[n],e[i]=t[i];return e}function st(t){function e(r,n,s,i){let o=r[i++];if(o==="__proto__")return!0;const c=Number.isFinite(+o),h=i>=r.length;return o=!o&&a.isArray(s)?s.length:o,h?(a.hasOwnProp(s,o)?s[o]=[s[o],n]:s[o]=n,!c):((!s[o]||!a.isObject(s[o]))&&(s[o]=[]),e(r,n,s[o],i)&&a.isArray(s[o])&&(s[o]=Sr(s[o])),!c)}if(a.isFormData(t)&&a.isFunction(t.entries)){const r={};return a.forEachEntry(t,(n,s)=>{e(Er(n),s,r,0)}),r}return null}function Ar(t,e,r){if(a.isString(t))try{return(e||JSON.parse)(t),a.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(t)}const X={transitional:nt,adapter:["xhr","http","fetch"],transformRequest:[function(e,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,i=a.isObject(e);if(i&&a.isHTMLForm(e)&&(e=new FormData(e)),a.isFormData(e))return s?JSON.stringify(st(e)):e;if(a.isArrayBuffer(e)||a.isBuffer(e)||a.isStream(e)||a.isFile(e)||a.isBlob(e)||a.isReadableStream(e))return e;if(a.isArrayBufferView(e))return e.buffer;if(a.isURLSearchParams(e))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return br(e,this.formSerializer).toString();if((c=a.isFileList(e))||n.indexOf("multipart/form-data")>-1){const h=this.env&&this.env.FormData;return ie(c?{"files[]":e}:e,h&&new h,this.formSerializer)}}return i||s?(r.setContentType("application/json",!1),Ar(e)):e}],transformResponse:[function(e){const r=this.transitional||X.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(e)||a.isReadableStream(e))return e;if(e&&a.isString(e)&&(n&&!this.responseType||s)){const o=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(c){if(o)throw c.name==="SyntaxError"?g.from(c,g.ERR_BAD_RESPONSE,this,null,this.response):c}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:C.classes.FormData,Blob:C.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],t=>{X.headers[t]={}});const Rr=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Tr=t=>{const e={};let r,n,s;return t&&t.split(`
`).forEach(function(o){s=o.indexOf(":"),r=o.substring(0,s).trim().toLowerCase(),n=o.substring(s+1).trim(),!(!r||e[r]&&Rr[r])&&(r==="set-cookie"?e[r]?e[r].push(n):e[r]=[n]:e[r]=e[r]?e[r]+", "+n:n)}),e},Fe=Symbol("internals");function H(t){return t&&String(t).trim().toLowerCase()}function Z(t){return t===!1||t==null?t:a.isArray(t)?t.map(Z):String(t)}function Cr(t){const e=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(t);)e[n[1]]=n[2];return e}const xr=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function le(t,e,r,n,s){if(a.isFunction(n))return n.call(this,e,r);if(s&&(e=r),!!a.isString(e)){if(a.isString(n))return e.indexOf(n)!==-1;if(a.isRegExp(n))return n.test(e)}}function kr(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,r,n)=>r.toUpperCase()+n)}function Or(t,e){const r=a.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(s,i,o){return this[n].call(this,e,s,i,o)},configurable:!0})})}let O=class{constructor(e){e&&this.set(e)}set(e,r,n){const s=this;function i(c,h,u){const l=H(h);if(!l)throw new Error("header name must be a non-empty string");const f=a.findKey(s,l);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||h]=Z(c))}const o=(c,h)=>a.forEach(c,(u,l)=>i(u,l,h));if(a.isPlainObject(e)||e instanceof this.constructor)o(e,r);else if(a.isString(e)&&(e=e.trim())&&!xr(e))o(Tr(e),r);else if(a.isObject(e)&&a.isIterable(e)){let c={},h,u;for(const l of e){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[u=l[0]]=(h=c[u])?a.isArray(h)?[...h,l[1]]:[h,l[1]]:l[1]}o(c,r)}else e!=null&&i(r,e,n);return this}get(e,r){if(e=H(e),e){const n=a.findKey(this,e);if(n){const s=this[n];if(!r)return s;if(r===!0)return Cr(s);if(a.isFunction(r))return r.call(this,s,n);if(a.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,r){if(e=H(e),e){const n=a.findKey(this,e);return!!(n&&this[n]!==void 0&&(!r||le(this,this[n],n,r)))}return!1}delete(e,r){const n=this;let s=!1;function i(o){if(o=H(o),o){const c=a.findKey(n,o);c&&(!r||le(n,n[c],c,r))&&(delete n[c],s=!0)}}return a.isArray(e)?e.forEach(i):i(e),s}clear(e){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const i=r[n];(!e||le(this,this[i],i,e,!0))&&(delete this[i],s=!0)}return s}normalize(e){const r=this,n={};return a.forEach(this,(s,i)=>{const o=a.findKey(n,i);if(o){r[o]=Z(s),delete r[i];return}const c=e?kr(i):String(i).trim();c!==i&&delete r[i],r[c]=Z(s),n[c]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const r=Object.create(null);return a.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=e&&a.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,r])=>e+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...r){const n=new this(e);return r.forEach(s=>n.set(s)),n}static accessor(e){const n=(this[Fe]=this[Fe]={accessors:{}}).accessors,s=this.prototype;function i(o){const c=H(o);n[c]||(Or(s,o),n[c]=!0)}return a.isArray(e)?e.forEach(i):i(e),this}};O.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(O.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[r]=n}}});a.freezeMethods(O);function he(t,e){const r=this||X,n=e||r,s=O.from(n.headers);let i=n.data;return a.forEach(t,function(c){i=c.call(r,i,s.normalize(),e?e.status:void 0)}),s.normalize(),i}function it(t){return!!(t&&t.__CANCEL__)}function q(t,e,r){g.call(this,t??"canceled",g.ERR_CANCELED,e,r),this.name="CanceledError"}a.inherits(q,g,{__CANCEL__:!0});function ot(t,e,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new g("Request failed with status code "+r.status,[g.ERR_BAD_REQUEST,g.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Pr(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function Lr(t,e){t=t||10;const r=new Array(t),n=new Array(t);let s=0,i=0,o;return e=e!==void 0?e:1e3,function(h){const u=Date.now(),l=n[i];o||(o=u),r[s]=h,n[s]=u;let f=i,w=0;for(;f!==s;)w+=r[f++],f=f%t;if(s=(s+1)%t,s===i&&(i=(i+1)%t),u-o<e)return;const b=l&&u-l;return b?Math.round(w*1e3/b):void 0}}function Ur(t,e){let r=0,n=1e3/e,s,i;const o=(u,l=Date.now())=>{r=l,s=null,i&&(clearTimeout(i),i=null),t(...u)};return[(...u)=>{const l=Date.now(),f=l-r;f>=n?o(u,l):(s=u,i||(i=setTimeout(()=>{i=null,o(s)},n-f)))},()=>s&&o(s)]}const ee=(t,e,r=3)=>{let n=0;const s=Lr(50,250);return Ur(i=>{const o=i.loaded,c=i.lengthComputable?i.total:void 0,h=o-n,u=s(h),l=o<=c;n=o;const f={loaded:o,total:c,progress:c?o/c:void 0,bytes:h,rate:u||void 0,estimated:u&&c&&l?(c-o)/u:void 0,event:i,lengthComputable:c!=null,[e?"download":"upload"]:!0};t(f)},r)},_e=(t,e)=>{const r=t!=null;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},Be=t=>(...e)=>a.asap(()=>t(...e)),Dr=C.hasStandardBrowserEnv?((t,e)=>r=>(r=new URL(r,C.origin),t.protocol===r.protocol&&t.host===r.host&&(e||t.port===r.port)))(new URL(C.origin),C.navigator&&/(msie|trident)/i.test(C.navigator.userAgent)):()=>!0,Ir=C.hasStandardBrowserEnv?{write(t,e,r,n,s,i){const o=[t+"="+encodeURIComponent(e)];a.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),a.isString(n)&&o.push("path="+n),a.isString(s)&&o.push("domain="+s),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Nr(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function Fr(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function at(t,e,r){let n=!Nr(e);return t&&(n||r==!1)?Fr(t,e):e}const $e=t=>t instanceof O?{...t}:t;function z(t,e){e=e||{};const r={};function n(u,l,f,w){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:w},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function s(u,l,f,w){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u,f,w)}else return n(u,l,f,w)}function i(u,l){if(!a.isUndefined(l))return n(void 0,l)}function o(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u)}else return n(void 0,l)}function c(u,l,f){if(f in e)return n(u,l);if(f in t)return n(void 0,u)}const h={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:c,headers:(u,l,f)=>s($e(u),$e(l),f,!0)};return a.forEach(Object.keys({...t,...e}),function(l){const f=h[l]||s,w=f(t[l],e[l],l);a.isUndefined(w)&&f!==c||(r[l]=w)}),r}const ct=t=>{const e=z({},t);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:i,headers:o,auth:c}=e;e.headers=o=O.from(o),e.url=rt(at(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&o.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let h;if(a.isFormData(r)){if(C.hasStandardBrowserEnv||C.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((h=o.getContentType())!==!1){const[u,...l]=h?h.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...l].join("; "))}}if(C.hasStandardBrowserEnv&&(n&&a.isFunction(n)&&(n=n(e)),n||n!==!1&&Dr(e.url))){const u=s&&i&&Ir.read(i);u&&o.set(s,u)}return e},_r=typeof XMLHttpRequest<"u",Br=_r&&function(t){return new Promise(function(r,n){const s=ct(t);let i=s.data;const o=O.from(s.headers).normalize();let{responseType:c,onUploadProgress:h,onDownloadProgress:u}=s,l,f,w,b,d;function m(){b&&b(),d&&d(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function E(){if(!p)return;const R=O.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),x={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:R,config:t,request:p};ot(function($){r($),m()},function($){n($),m()},x),p=null}"onloadend"in p?p.onloadend=E:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(E)},p.onabort=function(){p&&(n(new g("Request aborted",g.ECONNABORTED,t,p)),p=null)},p.onerror=function(){n(new g("Network Error",g.ERR_NETWORK,t,p)),p=null},p.ontimeout=function(){let N=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const x=s.transitional||nt;s.timeoutErrorMessage&&(N=s.timeoutErrorMessage),n(new g(N,x.clarifyTimeoutError?g.ETIMEDOUT:g.ECONNABORTED,t,p)),p=null},i===void 0&&o.setContentType(null),"setRequestHeader"in p&&a.forEach(o.toJSON(),function(N,x){p.setRequestHeader(x,N)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),u&&([w,d]=ee(u,!0),p.addEventListener("progress",w)),h&&p.upload&&([f,b]=ee(h),p.upload.addEventListener("progress",f),p.upload.addEventListener("loadend",b)),(s.cancelToken||s.signal)&&(l=R=>{p&&(n(!R||R.type?new q(null,t,p):R),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const A=Pr(s.url);if(A&&C.protocols.indexOf(A)===-1){n(new g("Unsupported protocol "+A+":",g.ERR_BAD_REQUEST,t));return}p.send(i||null)})},$r=(t,e)=>{const{length:r}=t=t?t.filter(Boolean):[];if(e||r){let n=new AbortController,s;const i=function(u){if(!s){s=!0,c();const l=u instanceof Error?u:this.reason;n.abort(l instanceof g?l:new q(l instanceof Error?l.message:l))}};let o=e&&setTimeout(()=>{o=null,i(new g(`timeout ${e} of ms exceeded`,g.ETIMEDOUT))},e);const c=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),t=null)};t.forEach(u=>u.addEventListener("abort",i));const{signal:h}=n;return h.unsubscribe=()=>a.asap(c),h}},Mr=function*(t,e){let r=t.byteLength;if(r<e){yield t;return}let n=0,s;for(;n<r;)s=n+e,yield t.slice(n,s),n=s},vr=async function*(t,e){for await(const r of zr(t))yield*Mr(r,e)},zr=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:r,value:n}=await e.read();if(r)break;yield n}}finally{await e.cancel()}},Me=(t,e,r,n)=>{const s=vr(t,e);let i=0,o,c=h=>{o||(o=!0,n&&n(h))};return new ReadableStream({async pull(h){try{const{done:u,value:l}=await s.next();if(u){c(),h.close();return}let f=l.byteLength;if(r){let w=i+=f;r(w)}h.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(h){return c(h),s.return()}},{highWaterMark:2})},oe=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ut=oe&&typeof ReadableStream=="function",jr=oe&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),lt=(t,...e)=>{try{return!!t(...e)}catch{return!1}},qr=ut&&lt(()=>{let t=!1;const e=new Request(C.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),ve=64*1024,ye=ut&&lt(()=>a.isReadableStream(new Response("").body)),te={stream:ye&&(t=>t.body)};oe&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!te[e]&&(te[e]=a.isFunction(t[e])?r=>r[e]():(r,n)=>{throw new g(`Response type '${e}' is not supported`,g.ERR_NOT_SUPPORT,n)})})})(new Response);const Hr=async t=>{if(t==null)return 0;if(a.isBlob(t))return t.size;if(a.isSpecCompliantForm(t))return(await new Request(C.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(a.isArrayBufferView(t)||a.isArrayBuffer(t))return t.byteLength;if(a.isURLSearchParams(t)&&(t=t+""),a.isString(t))return(await jr(t)).byteLength},Wr=async(t,e)=>{const r=a.toFiniteNumber(t.getContentLength());return r??Hr(e)},Vr=oe&&(async t=>{let{url:e,method:r,data:n,signal:s,cancelToken:i,timeout:o,onDownloadProgress:c,onUploadProgress:h,responseType:u,headers:l,withCredentials:f="same-origin",fetchOptions:w}=ct(t);u=u?(u+"").toLowerCase():"text";let b=$r([s,i&&i.toAbortSignal()],o),d;const m=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let p;try{if(h&&qr&&r!=="get"&&r!=="head"&&(p=await Wr(l,n))!==0){let x=new Request(e,{method:"POST",body:n,duplex:"half"}),B;if(a.isFormData(n)&&(B=x.headers.get("content-type"))&&l.setContentType(B),x.body){const[$,G]=_e(p,ee(Be(h)));n=Me(x.body,ve,$,G)}}a.isString(f)||(f=f?"include":"omit");const E="credentials"in Request.prototype;d=new Request(e,{...w,signal:b,method:r.toUpperCase(),headers:l.normalize().toJSON(),body:n,duplex:"half",credentials:E?f:void 0});let A=await fetch(d,w);const R=ye&&(u==="stream"||u==="response");if(ye&&(c||R&&m)){const x={};["status","statusText","headers"].forEach(Le=>{x[Le]=A[Le]});const B=a.toFiniteNumber(A.headers.get("content-length")),[$,G]=c&&_e(B,ee(Be(c),!0))||[];A=new Response(Me(A.body,ve,$,()=>{G&&G(),m&&m()}),x)}u=u||"text";let N=await te[a.findKey(te,u)||"text"](A,t);return!R&&m&&m(),await new Promise((x,B)=>{ot(x,B,{data:N,headers:O.from(A.headers),status:A.status,statusText:A.statusText,config:t,request:d})})}catch(E){throw m&&m(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new g("Network Error",g.ERR_NETWORK,t,d),{cause:E.cause||E}):g.from(E,E&&E.code,t,d)}}),we={http:ar,xhr:Br,fetch:Vr};a.forEach(we,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const ze=t=>`- ${t}`,Jr=t=>a.isFunction(t)||t===null||t===!1,ht={getAdapter:t=>{t=a.isArray(t)?t:[t];const{length:e}=t;let r,n;const s={};for(let i=0;i<e;i++){r=t[i];let o;if(n=r,!Jr(r)&&(n=we[(o=String(r)).toLowerCase()],n===void 0))throw new g(`Unknown adapter '${o}'`);if(n)break;s[o||"#"+i]=n}if(!n){const i=Object.entries(s).map(([c,h])=>`adapter ${c} `+(h===!1?"is not supported by the environment":"is not available in the build"));let o=e?i.length>1?`since :
`+i.map(ze).join(`
`):" "+ze(i[0]):"as no adapter specified";throw new g("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return n},adapters:we};function de(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new q(null,t)}function je(t){return de(t),t.headers=O.from(t.headers),t.data=he.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),ht.getAdapter(t.adapter||X.adapter)(t).then(function(n){return de(t),n.data=he.call(t,t.transformResponse,n),n.headers=O.from(n.headers),n},function(n){return it(n)||(de(t),n&&n.response&&(n.response.data=he.call(t,t.transformResponse,n.response),n.response.headers=O.from(n.response.headers))),Promise.reject(n)})}const dt="1.11.0",ae={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{ae[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const qe={};ae.transitional=function(e,r,n){function s(i,o){return"[Axios v"+dt+"] Transitional option '"+i+"'"+o+(n?". "+n:"")}return(i,o,c)=>{if(e===!1)throw new g(s(o," has been removed"+(r?" in "+r:"")),g.ERR_DEPRECATED);return r&&!qe[o]&&(qe[o]=!0,console.warn(s(o," has been deprecated since v"+r+" and will be removed in the near future"))),e?e(i,o,c):!0}};ae.spelling=function(e){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function Kr(t,e,r){if(typeof t!="object")throw new g("options must be an object",g.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let s=n.length;for(;s-- >0;){const i=n[s],o=e[i];if(o){const c=t[i],h=c===void 0||o(c,i,t);if(h!==!0)throw new g("option "+i+" must be "+h,g.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new g("Unknown option "+i,g.ERR_BAD_OPTION)}}const Y={assertOptions:Kr,validators:ae},I=Y.validators;let v=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Ne,response:new Ne}}async request(e,r){try{return await this._request(e,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const i=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?i&&!String(n.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+i):n.stack=i}catch{}}throw n}}_request(e,r){typeof e=="string"?(r=r||{},r.url=e):r=e||{},r=z(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:i}=r;n!==void 0&&Y.assertOptions(n,{silentJSONParsing:I.transitional(I.boolean),forcedJSONParsing:I.transitional(I.boolean),clarifyTimeoutError:I.transitional(I.boolean)},!1),s!=null&&(a.isFunction(s)?r.paramsSerializer={serialize:s}:Y.assertOptions(s,{encode:I.function,serialize:I.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Y.assertOptions(r,{baseUrl:I.spelling("baseURL"),withXsrfToken:I.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let o=i&&a.merge(i.common,i[r.method]);i&&a.forEach(["delete","get","head","post","put","patch","common"],d=>{delete i[d]}),r.headers=O.concat(o,i);const c=[];let h=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(r)===!1||(h=h&&m.synchronous,c.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let l,f=0,w;if(!h){const d=[je.bind(this),void 0];for(d.unshift(...c),d.push(...u),w=d.length,l=Promise.resolve(r);f<w;)l=l.then(d[f++],d[f++]);return l}w=c.length;let b=r;for(f=0;f<w;){const d=c[f++],m=c[f++];try{b=d(b)}catch(p){m.call(this,p);break}}try{l=je.call(this,b)}catch(d){return Promise.reject(d)}for(f=0,w=u.length;f<w;)l=l.then(u[f++],u[f++]);return l}getUri(e){e=z(this.defaults,e);const r=at(e.baseURL,e.url,e.allowAbsoluteUrls);return rt(r,e.params,e.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(e){v.prototype[e]=function(r,n){return this.request(z(n||{},{method:e,url:r,data:(n||{}).data}))}});a.forEach(["post","put","patch"],function(e){function r(n){return function(i,o,c){return this.request(z(c||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}v.prototype[e]=r(),v.prototype[e+"Form"]=r(!0)});let Xr=class ft{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const n=this;this.promise.then(s=>{if(!n._listeners)return;let i=n._listeners.length;for(;i-- >0;)n._listeners[i](s);n._listeners=null}),this.promise.then=s=>{let i;const o=new Promise(c=>{n.subscribe(c),i=c}).then(s);return o.cancel=function(){n.unsubscribe(i)},o},e(function(i,o,c){n.reason||(n.reason=new q(i,o,c),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const r=this._listeners.indexOf(e);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const e=new AbortController,r=n=>{e.abort(n)};return this.subscribe(r),e.signal.unsubscribe=()=>this.unsubscribe(r),e.signal}static source(){let e;return{token:new ft(function(s){e=s}),cancel:e}}};function Gr(t){return function(r){return t.apply(null,r)}}function Qr(t){return a.isObject(t)&&t.isAxiosError===!0}const be={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(be).forEach(([t,e])=>{be[e]=t});function pt(t){const e=new v(t),r=He(v.prototype.request,e);return a.extend(r,v.prototype,e,{allOwnKeys:!0}),a.extend(r,e,null,{allOwnKeys:!0}),r.create=function(s){return pt(z(t,s))},r}const S=pt(X);S.Axios=v;S.CanceledError=q;S.CancelToken=Xr;S.isCancel=it;S.VERSION=dt;S.toFormData=ie;S.AxiosError=g;S.Cancel=S.CanceledError;S.all=function(e){return Promise.all(e)};S.spread=Gr;S.isAxiosError=Qr;S.mergeConfig=z;S.AxiosHeaders=O;S.formToJSON=t=>st(a.isHTMLForm(t)?new FormData(t):t);S.getAdapter=ht.getAdapter;S.HttpStatusCode=be;S.default=S;const{Axios:Tn,AxiosError:Cn,CanceledError:xn,isCancel:kn,CancelToken:On,VERSION:Pn,all:Ln,Cancel:Un,isAxiosError:Dn,spread:In,toFormData:Nn,AxiosHeaders:Fn,HttpStatusCode:_n,formToJSON:Bn,getAdapter:$n,mergeConfig:Mn}=S;class ke extends Error{constructor(r,n,s){super(r);T(this,"statusCode");T(this,"response");this.name="ApiError",this.statusCode=n,this.response=s}}class mt extends ke{constructor(e="Authentication failed"){super(e,401),this.name="AuthenticationError"}}class Zr extends ke{constructor(e="Network error"){super(e,0),this.name="NetworkError"}}var U=(t=>(t.LOGIN_START="login_start",t.LOGIN_SUCCESS="login_success",t.LOGIN_FAILURE="login_failure",t.LOGOUT="logout",t.TOKEN_REFRESH="token_refresh",t.TOKEN_EXPIRED="token_expired",t.AUTH_ERROR="auth_error",t))(U||{});class Yr{constructor(){T(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});T(this,"listeners",[]);T(this,"d365Context",null)}async initialize(){this.setLoading(!0);try{await this.waitForDynamicsContext();const e=await this.getDynamicsUser();if(e)this.authState={user:e,isAuthenticated:!0,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGIN_SUCCESS,{user:e}),y.logger.info("Dynamics 365 authentication initialized successfully");else throw new Error("Failed to get user information from Dynamics 365")}catch(e){const r=e instanceof Error?e.message:"Unknown authentication error";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:r,token:null},this.emitAuthEvent(U.AUTH_ERROR,{error:r}),y.logger.error("Dynamics 365 authentication initialization failed:",e)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(e){try{return this.authState.isAuthenticated&&this.authState.user?{success:!0,user:this.authState.user,token:this.authState.token||void 0}:(await this.initialize(),{success:this.authState.isAuthenticated,user:this.authState.user||void 0,error:this.authState.error||void 0})}catch(r){return{success:!1,error:r instanceof Error?r.message:"Login failed"}}}async logout(){this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGOUT),this.notifyListeners(),y.logger.info("Dynamics 365 authentication state cleared")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{return await this.getDynamicsUser()}catch(e){return y.logger.error("Failed to get current user:",e),null}}async getAccessToken(){return null}async refreshToken(){return this.authState.isAuthenticated}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(e){return this.listeners.push(e),()=>{const r=this.listeners.indexOf(e);r>-1&&this.listeners.splice(r,1)}}async waitForDynamicsContext(e=1e4){return new Promise((r,n)=>{const s=Date.now(),i=()=>{if(this.isDynamicsContextAvailable()){r();return}if(Date.now()-s>e){n(new Error("Timeout waiting for Dynamics 365 context"));return}setTimeout(i,100)};i()})}isDynamicsContextAvailable(){try{return!!window.Xrm&&!!window.Xrm.WebApi}catch{return!1}}async getDynamicsUser(){try{const e=window.Xrm;if(!e||!e.WebApi)throw new Error("Dynamics 365 context not available");const r=await e.WebApi.retrieveRecord("systemuser",e.Utility.getGlobalContext().getUserId(),"?$select=systemuserid,fullname,internalemailaddress"),n=await e.WebApi.retrieveMultipleRecords("role","?$filter=_parentroleid_value eq null&$select=name"),s=e.Utility.getGlobalContext().getOrganizationSettings();return this.d365Context={userId:r.systemuserid,userRoles:n.entities.map(i=>i.name),organizationId:s.organizationId,organizationName:s.uniqueName,serverUrl:e.Utility.getGlobalContext().getClientUrl(),version:e.Utility.getGlobalContext().getVersion()},{id:r.systemuserid,email:r.internalemailaddress,name:r.fullname,roles:this.d365Context.userRoles,organizationId:this.d365Context.organizationId}}catch(e){throw y.logger.error("Failed to get Dynamics 365 user information:",e),e}}setLoading(e){this.authState.isLoading=e,this.notifyListeners()}notifyListeners(){this.listeners.forEach(e=>{try{e(this.getAuthState())}catch(r){y.logger.error("Error in auth state listener:",r)}})}emitAuthEvent(e,r){const n={event:e,timestamp:new Date,...r};y.logger.debug("Auth event emitted:",n)}getDynamics365Context(){return this.d365Context}}class en{constructor(e){T(this,"authState",{user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null});T(this,"listeners",[]);T(this,"msalInstance",null);T(this,"config");T(this,"tokenRefreshTimer",null);this.config=e}async initialize(){this.setLoading(!0);try{await this.initializeMSAL(),await this.checkExistingAuth(),y.logger.info("MSAL authentication initialized successfully")}catch(e){const r=e instanceof Error?e.message:"MSAL initialization failed";this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:r,token:null},this.emitAuthEvent(U.AUTH_ERROR,{error:r}),y.logger.error("MSAL authentication initialization failed:",e)}this.notifyListeners()}getAuthState(){return{...this.authState}}async login(e){this.setLoading(!0),this.emitAuthEvent(U.LOGIN_START);try{if(!this.msalInstance)throw new Error("MSAL not initialized");const r={scopes:this.config.scopes,prompt:e!=null&&e.interactive?"select_account":void 0};let n;try{n=await this.msalInstance.acquireTokenSilent(r)}catch{n=await this.msalInstance.loginPopup(r)}const s=await this.processAuthResult(n);if(s)return this.authState={user:s,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(n)},this.startTokenRefreshTimer(),this.emitAuthEvent(U.LOGIN_SUCCESS,{user:s}),{success:!0,user:s,token:this.authState.token||void 0};throw new Error("Failed to process authentication result")}catch(r){const n=r instanceof Error?r.message:"Login failed";return this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:n,token:null},this.emitAuthEvent(U.LOGIN_FAILURE,{error:n}),{success:!1,error:n}}finally{this.notifyListeners()}}async logout(){try{this.tokenRefreshTimer&&(clearTimeout(this.tokenRefreshTimer),this.tokenRefreshTimer=null),this.msalInstance&&await this.msalInstance.logout({postLogoutRedirectUri:this.config.redirectUri})}catch(e){y.logger.error("Logout error:",e)}this.authState={user:null,isAuthenticated:!1,isLoading:!1,error:null,token:null},this.emitAuthEvent(U.LOGOUT),this.notifyListeners(),y.logger.info("User logged out successfully")}async getCurrentUser(){if(this.authState.user)return this.authState.user;try{if(!this.msalInstance)return null;const e=this.msalInstance.getActiveAccount();return e?this.createUserFromAccount(e):null}catch(e){return y.logger.error("Failed to get current user:",e),null}}async getAccessToken(){try{if(!this.msalInstance)return null;if(this.authState.token&&this.authState.token.expiresAt>new Date)return this.authState.token.accessToken;const e={scopes:this.config.scopes,account:this.msalInstance.getActiveAccount()},r=await this.msalInstance.acquireTokenSilent(e);return r?(this.authState.token=this.createTokenFromAuthResult(r),this.notifyListeners(),r.accessToken):null}catch(e){return y.logger.error("Failed to get access token:",e),this.emitAuthEvent(U.TOKEN_EXPIRED),null}}async refreshToken(){try{return await this.getAccessToken()?(this.emitAuthEvent(U.TOKEN_REFRESH),!0):!1}catch(e){return y.logger.error("Token refresh failed:",e),!1}}isAuthenticated(){return this.authState.isAuthenticated}onAuthStateChanged(e){return this.listeners.push(e),()=>{const r=this.listeners.indexOf(e);r>-1&&this.listeners.splice(r,1)}}async initializeMSAL(){try{const{PublicClientApplication:e}=await Promise.resolve().then(()=>require("./index-DAOl4Ulu.js")),r={auth:{clientId:this.config.clientId,authority:this.config.authority,redirectUri:this.config.redirectUri},cache:{cacheLocation:this.config.cacheLocation||"localStorage",storeAuthStateInCookie:!1}};this.msalInstance=new e(r),await this.msalInstance.initialize()}catch(e){if(e instanceof Error&&e.message.includes("Failed to resolve module"))y.logger.warn("MSAL library not available, using mock implementation"),this.msalInstance=this.createMockMSALInstance();else throw e}}async checkExistingAuth(){if(this.msalInstance)try{const e=this.msalInstance.getAllAccounts();if(e.length>0){this.msalInstance.setActiveAccount(e[0]);const r={scopes:this.config.scopes,account:e[0]},n=await this.msalInstance.acquireTokenSilent(r);if(n){const s=this.createUserFromAccount(e[0]);this.authState={user:s,isAuthenticated:!0,isLoading:!1,error:null,token:this.createTokenFromAuthResult(n)},this.startTokenRefreshTimer()}}}catch(e){y.logger.debug("No existing valid authentication found:",e)}}async processAuthResult(e){var r;return!e||!e.account?null:((r=this.msalInstance)==null||r.setActiveAccount(e.account),this.createUserFromAccount(e.account))}createUserFromAccount(e){return{id:e.homeAccountId||e.localAccountId,email:e.username,name:e.name||e.username,roles:[],tenantId:e.tenantId}}createTokenFromAuthResult(e){return{accessToken:e.accessToken,expiresAt:new Date(e.expiresOn),scopes:e.scopes||this.config.scopes}}startTokenRefreshTimer(){if(this.tokenRefreshTimer&&clearTimeout(this.tokenRefreshTimer),this.authState.token){const e=this.authState.token.expiresAt.getTime()-Date.now()-3e5;e>0&&(this.tokenRefreshTimer=setTimeout(()=>{this.refreshToken()},e))}}setLoading(e){this.authState.isLoading=e,this.notifyListeners()}notifyListeners(){this.listeners.forEach(e=>{try{e(this.getAuthState())}catch(r){y.logger.error("Error in auth state listener:",r)}})}emitAuthEvent(e,r){const n={event:e,timestamp:new Date,...r};y.logger.debug("Auth event emitted:",n)}createMockMSALInstance(){return{initialize:async()=>{},loginPopup:async()=>({account:{username:"<EMAIL>",name:"Mock User"},accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),loginRedirect:async()=>{},logout:async()=>{},acquireTokenSilent:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),acquireTokenPopup:async()=>({accessToken:"mock-token",expiresOn:new Date(Date.now()+36e5)}),getAllAccounts:()=>[],getActiveAccount:()=>null,setActiveAccount:()=>{}}}}const F=class F{static getInstance(){return F._instance||(F._instance=F.createAuthService()),F._instance}static createAuthService(){const e=P.getDeploymentConfig();switch(y.logger.info(`Creating authentication service for ${e.mode} deployment mode`),e.mode){case P.DeploymentMode.WEB_RESOURCE:return new Yr;case P.DeploymentMode.EMBEDDED_SPA:case P.DeploymentMode.STANDALONE_MFE:if(!e.msalConfig)throw new Error("MSAL configuration is required for standalone deployment mode");return new en(e.msalConfig);default:throw new Error(`Unsupported deployment mode: ${e.mode}`)}}static reset(){F._instance=null}static forceInstance(e){F._instance=e}};T(F,"_instance",null);let Ee=F;function ce(){return Ee.getInstance()}const fe={};class Oe{constructor(e){T(this,"client");T(this,"authService",ce());this.client=S.create({baseURL:e.baseURL,timeout:e.timeout||3e4,headers:{"Content-Type":"application/json","OData-MaxVersion":"4.0","OData-Version":"4.0",Accept:"application/json",Prefer:"return=representation",...e.headers}}),this.setupInterceptors()}async initialize(){try{await this.authService.initialize(),y.logger.info("External API client initialized successfully")}catch(e){throw y.logger.error("Failed to initialize external API client:",e),e}}setupInterceptors(){this.client.interceptors.request.use(async e=>{var n;y.logger.info(`API Request: ${(n=e.method)==null?void 0:n.toUpperCase()} ${e.url}`);const r=await this.getAuthToken();return r&&(e.headers=e.headers||{},e.headers.Authorization=`Bearer ${r}`),e},e=>(y.logger.error("API Request Error:",e),Promise.reject(this.createApiError(e)))),this.client.interceptors.response.use(e=>(y.logger.info(`API Response: ${e.status} ${e.config.url}`),e),async e=>{var r;if(y.logger.error("API Response Error:",e),((r=e.response)==null?void 0:r.status)===401&&await this.handleUnauthorized()&&e.config){const s=await this.getAuthToken();if(s)return e.config.headers.Authorization=`Bearer ${s}`,this.client.request(e.config)}return Promise.reject(this.createApiError(e))})}async getAuthToken(){try{return await this.authService.getAccessToken()}catch(e){return y.logger.error("Failed to get auth token:",e),null}}async handleUnauthorized(){try{return await this.authService.refreshToken()?(y.logger.info("Token refreshed successfully"),!0):(y.logger.warn("Token refresh failed, triggering re-authentication"),await this.authService.login({interactive:!0}),!0)}catch(e){return y.logger.error("Failed to handle unauthorized access:",e),!1}}async get(e,r){try{const n=this.convertConfig(r),s=await this.client.get(e,n);return{data:s.data,success:!0,statusCode:s.status}}catch(n){return this.handleError(n)}}async post(e,r,n){try{const s=this.convertConfig(n),i=await this.client.post(e,r,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async put(e,r,n){try{const s=this.convertConfig(n),i=await this.client.put(e,r,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async patch(e,r,n){try{const s=this.convertConfig(n),i=await this.client.patch(e,r,s);return{data:i.data,success:!0,statusCode:i.status}}catch(s){return this.handleError(s)}}async delete(e,r){try{const n=this.convertConfig(r),s=await this.client.delete(e,n);return{data:s.data,success:!0,statusCode:s.status}}catch(n){return this.handleError(n)}}async retrieveRecord(e,r,n){const s=this.buildQueryString(n),i=`${e}(${r})${s}`;return this.get(i)}async retrieveMultipleRecords(e,r){try{const n=this.buildQueryString(r),s=`${e}${n}`,i=await this.get(s);return i.success&&i.data?{data:i.data.value,success:!0,statusCode:i.statusCode,pagination:{page:Math.floor(((r==null?void 0:r.skip)||0)/((r==null?void 0:r.top)||50))+1,pageSize:i.data.value.length,totalCount:i.data["@odata.count"]||i.data.value.length,hasNext:!!i.data["@odata.nextLink"],hasPrevious:((r==null?void 0:r.skip)||0)>0}}:this.handleErrorPaginated(new Error("Invalid response format"))}catch(n){return this.handleErrorPaginated(n)}}async createRecord(e,r){return this.post(e,r)}async updateRecord(e,r,n){const s=`${e}(${r})`;return this.patch(s,n)}async deleteRecord(e,r){const n=`${e}(${r})`;return this.delete(n)}async executeFunction(e,r){let n=e;if(r){const s=Object.keys(r).map(i=>`${i}=${encodeURIComponent(r[i])}`).join(",");n+=`(${s})`}return this.get(n)}async executeBatch(e){try{const r=this.generateBatchId(),n=this.generateChangesetId(),s=this.buildBatchBody(e,r,n),i=await this.client.post("$batch",s,{headers:{"Content-Type":`multipart/mixed; boundary=batch_${r}`}}),o=this.parseBatchResponse(i.data);return{responses:o,success:o.every(c=>c.success)}}catch(r){return y.logger.error("Batch execution failed:",r),{responses:[],success:!1,errors:[r instanceof Error?r.message:"Batch execution failed"]}}}convertConfig(e){return e?{headers:e.headers,timeout:e.timeout}:{}}buildQueryString(e){if(!e)return"";const r=[];return e.select&&r.push(`$select=${e.select.join(",")}`),e.filter&&r.push(`$filter=${encodeURIComponent(e.filter)}`),e.orderBy&&r.push(`$orderby=${encodeURIComponent(e.orderBy)}`),e.expand&&r.push(`$expand=${e.expand.join(",")}`),e.top&&r.push(`$top=${e.top}`),e.skip&&r.push(`$skip=${e.skip}`),r.length>0?`?${r.join("&")}`:""}generateBatchId(){return Math.random().toString(36).substring(2,15)}generateChangesetId(){return Math.random().toString(36).substring(2,15)}buildBatchBody(e,r,n){return e.map(s=>`--batch_${r}
Content-Type: application/http

${s.method} ${s.url} HTTP/1.1

`).join("")}parseBatchResponse(e){return[]}createApiError(e){var r,n,s,i,o,c;return e.code==="ECONNABORTED"||e.code==="ENOTFOUND"?new Zr(e.message):((r=e.response)==null?void 0:r.status)===401?new mt(((n=e.response.data)==null?void 0:n.message)||"Authentication failed"):new ke(((i=(s=e.response)==null?void 0:s.data)==null?void 0:i.message)||e.message||"An error occurred",((o=e.response)==null?void 0:o.status)||500,(c=e.response)==null?void 0:c.data)}handleError(e){const r=this.createApiError(e);return{data:null,success:!1,message:r.message,statusCode:r.statusCode,errors:[r.message]}}handleErrorPaginated(e){return{...this.handleError(e),data:[],pagination:{page:1,pageSize:0,totalCount:0,hasNext:!1,hasPrevious:!1}}}}const tn=()=>(fe==null?void 0:fe.VITE_API_BASE_URL)||"http://localhost:3001/api";new Oe({baseURL:tn()});class rn{constructor(){T(this,"xrmWebApi",null)}async initialize(){try{if(await this.waitForXrmContext(),this.xrmWebApi=window.Xrm.WebApi,!this.xrmWebApi)throw new Error("Xrm.WebApi is not available");y.logger.info("Dynamics 365 API client initialized successfully")}catch(e){throw y.logger.error("Failed to initialize Dynamics 365 API client:",e),e}}async get(e,r){try{return this.ensureInitialized(),{data:await this.xrmWebApi.retrieveRecord("systemuser",e),success:!0}}catch(n){return this.handleError(n)}}async post(e,r,n){try{this.ensureInitialized();const s=this.extractEntityNameFromUrl(e);return{data:await this.xrmWebApi.createRecord(s,r),success:!0}}catch(s){return this.handleError(s)}}async put(e,r,n){try{this.ensureInitialized();const{entityName:s,id:i}=this.parseEntityUrl(e);return await this.xrmWebApi.updateRecord(s,i,r),{data:null,success:!0}}catch(s){return this.handleError(s)}}async patch(e,r,n){return this.put(e,r,n)}async delete(e,r){try{this.ensureInitialized();const{entityName:n,id:s}=this.parseEntityUrl(e);return await this.xrmWebApi.deleteRecord(n,s),{data:null,success:!0}}catch(n){return this.handleError(n)}}async retrieveRecord(e,r,n){try{this.ensureInitialized();const s=this.buildQueryString(n);return{data:await this.xrmWebApi.retrieveRecord(e,r,s),success:!0}}catch(s){return this.handleError(s)}}async retrieveMultipleRecords(e,r){try{this.ensureInitialized();const n=this.buildQueryString(r),s=await this.xrmWebApi.retrieveMultipleRecords(e,n);return{data:s.entities,success:!0,pagination:{page:1,pageSize:s.entities.length,totalCount:s.entities.length,hasNext:!!s["@odata.nextLink"],hasPrevious:!1}}}catch(n){return this.handleErrorPaginated(n)}}async createRecord(e,r){try{return this.ensureInitialized(),{data:await this.xrmWebApi.createRecord(e,r),success:!0}}catch(n){return this.handleError(n)}}async updateRecord(e,r,n){try{return this.ensureInitialized(),await this.xrmWebApi.updateRecord(e,r,n),{data:null,success:!0}}catch(s){return this.handleError(s)}}async deleteRecord(e,r){try{return this.ensureInitialized(),await this.xrmWebApi.deleteRecord(e,r),{data:void 0,success:!0}}catch(n){return this.handleError(n)}}async executeFunction(e,r){try{this.ensureInitialized();let n=e;if(r){const i=Object.keys(r).map(o=>`${o}=${encodeURIComponent(r[o])}`).join(",");n+=`(${i})`}return{data:await this.xrmWebApi.online.executeFunction(n),success:!0}}catch(n){return this.handleError(n)}}async executeBatch(e){try{this.ensureInitialized();const r=[];for(const n of e)try{let s;switch(n.method){case"GET":s=await this.get(n.url);break;case"POST":s=await this.post(n.url,n.data);break;case"PUT":s=await this.put(n.url,n.data);break;case"PATCH":s=await this.patch(n.url,n.data);break;case"DELETE":s=await this.delete(n.url);break;default:throw new Error(`Unsupported method: ${n.method}`)}r.push(s)}catch(s){r.push(this.handleError(s))}return{responses:r,success:r.every(n=>n.success)}}catch(r){return y.logger.error("Batch execution failed:",r),{responses:[],success:!1,errors:[r instanceof Error?r.message:"Batch execution failed"]}}}async waitForXrmContext(e=1e4){return new Promise((r,n)=>{const s=Date.now(),i=()=>{if(window.Xrm&&window.Xrm.WebApi){r();return}if(Date.now()-s>e){n(new Error("Timeout waiting for Dynamics 365 context"));return}setTimeout(i,100)};i()})}ensureInitialized(){if(!this.xrmWebApi)throw new mt("Dynamics 365 API client not initialized")}buildQueryString(e){if(!e)return"";const r=[];return e.select&&r.push(`$select=${e.select.join(",")}`),e.filter&&r.push(`$filter=${encodeURIComponent(e.filter)}`),e.orderBy&&r.push(`$orderby=${encodeURIComponent(e.orderBy)}`),e.expand&&r.push(`$expand=${e.expand.join(",")}`),e.top&&r.push(`$top=${e.top}`),e.skip&&r.push(`$skip=${e.skip}`),r.length>0?`?${r.join("&")}`:""}extractEntityNameFromUrl(e){const r=e.match(/\/([^\/\?]+)/);return r?r[1]:"systemuser"}parseEntityUrl(e){const r=e.match(/\/([^\/]+)\/([^\/\?]+)/);return{entityName:r?r[1]:"systemuser",id:r?r[2]:""}}handleError(e){y.logger.error("Dynamics 365 API error:",e);let r="An error occurred",n=500;return e&&e.message&&(r=e.message),e&&e.status&&(n=e.status),{data:null,success:!1,message:r,statusCode:n,errors:[r]}}handleErrorPaginated(e){return{...this.handleError(e),data:[],pagination:{page:1,pageSize:0,totalCount:0,hasNext:!1,hasPrevious:!1}}}}const _=class _{static async getInstance(){return _._instance||(_._instance=await _.createApiClient()),_._instance}static async createApiClient(){const e=P.getDeploymentConfig();y.logger.info(`Creating API client for ${e.mode} deployment mode`);let r;switch(e.mode){case P.DeploymentMode.WEB_RESOURCE:r=new rn;break;case P.DeploymentMode.EMBEDDED_SPA:case P.DeploymentMode.STANDALONE_MFE:r=new Oe({baseURL:e.apiBaseUrl,timeout:3e4,headers:{"OData-MaxVersion":"4.0","OData-Version":"4.0",Accept:"application/json",Prefer:"return=representation"}});break;default:throw new Error(`Unsupported deployment mode: ${e.mode}`)}return await r.initialize(),r}static reset(){_._instance=null}static forceInstance(e){_._instance=e}};T(_,"_instance",null);let Se=_;async function nn(){return Se.getInstance()}class sn{constructor(){T(this,"authService",ce())}async initialize(){return this.authService.initialize()}getAuthState(){return this.authService.getAuthState()}async login(e){return await this.authService.login(e)}async logout(){return this.authService.logout()}async refreshToken(){return this.authService.refreshToken()}async getCurrentUser(){return this.authService.getCurrentUser()}async getAccessToken(){return this.authService.getAccessToken()}isAuthenticated(){return this.authService.isAuthenticated()}onAuthStateChanged(e){return this.authService.onAuthStateChanged(e)}}const on=new sn,an=()=>{const[t,e]=L.useState({user:null,isAuthenticated:!1,isLoading:!0,error:null,token:null}),r=ce();L.useEffect(()=>{const b=(async()=>{try{await r.initialize();const d=r.onAuthStateChanged(m=>{e(m)});return e(r.getAuthState()),d}catch(d){y.logger.error("Auth initialization error:",d),e(m=>({...m,error:d instanceof Error?d.message:"Authentication initialization failed",isLoading:!1}))}})();return()=>{b.then(d=>{d&&d()})}},[r]);const n=L.useCallback(async w=>await r.login(w),[r]),s=L.useCallback(async()=>{await r.logout()},[r]),i=L.useCallback(async()=>await r.refreshToken(),[r]),o=L.useCallback(async()=>await r.getCurrentUser(),[r]),c=L.useCallback(async()=>await r.getAccessToken(),[r]),h=L.useCallback(()=>r.isAuthenticated(),[r]),u=L.useCallback(w=>r.onAuthStateChanged(w),[r]),l=L.useCallback(()=>r.getAuthState(),[r]),f=L.useCallback(async()=>r.initialize(),[r]);return{...t,login:n,logout:s,refreshToken:i,getCurrentUser:o,getAccessToken:c,checkAuthenticated:h,onAuthStateChanged:u,getAuthState:l,initialize:f}};function gt(t){return typeof document>"u"?"":getComputedStyle(document.documentElement).getPropertyValue(t).trim()}function yt(t,e){typeof document>"u"||document.documentElement.style.setProperty(t,e)}function cn(){if(typeof document>"u")return{};const t=getComputedStyle(document.documentElement),e={};for(let r=0;r<t.length;r++){const n=t[r];n.startsWith("--")&&(e[n]=t.getPropertyValue(n).trim())}return e}function un(t){typeof document>"u"||Object.entries(t).forEach(([e,r])=>{yt(e,String(r))})}function ln(t){const e=[];return e.push(`:root[data-theme="${t.mode}"] {`),e.push(`  --theme-primary: ${t.primaryColor};`),e.push(`  --theme-secondary: ${t.secondaryColor};`),e.push(`  --theme-bg-primary: ${t.backgroundColor};`),e.push(`  --theme-text-primary: ${t.textColor};`),e.push(`  --theme-border-primary: ${t.borderColor};`),e.push(`  --theme-font-family: ${t.fontFamily};`),Object.entries(t.customProperties).forEach(([r,n])=>{e.push(`  ${r}: ${n};`)}),e.push("}"),e.join(`
`)}function hn(t){return["mode","primaryColor","secondaryColor","backgroundColor","textColor","borderColor","fontFamily","customProperties"].every(r=>r in t)}function ue(t){const e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null}function Pe(t,e,r){return"#"+((1<<24)+(t<<16)+(e<<8)+r).toString(16).slice(1)}function wt(t,e){const r=ue(t);if(!r)return t;const{r:n,g:s,b:i}=r,o=Math.round(2.55*e);return Pe(Math.min(255,n+o),Math.min(255,s+o),Math.min(255,i+o))}function Ae(t,e){const r=ue(t);if(!r)return t;const{r:n,g:s,b:i}=r,o=Math.round(2.55*e);return Pe(Math.max(0,n-o),Math.max(0,s-o),Math.max(0,i-o))}function bt(t,e){const r=c=>{const h=ue(c);if(!h)return 0;const{r:u,g:l,b:f}=h,[w,b,d]=[u,l,f].map(m=>(m=m/255,m<=.03928?m/12.92:Math.pow((m+.055)/1.055,2.4)));return .2126*w+.7152*b+.0722*d},n=r(t),s=r(e),i=Math.max(n,s),o=Math.min(n,s);return(i+.05)/(o+.05)}function Re(t,e,r="AA"){const n=bt(t,e);return r==="AA"?n>=4.5:n>=7}function dn(t){const e=wt(t,20),r=Ae(t,20);let n=t;const s="#ffffff";if(!Re(t,s))for(let i=10;i<=80;i+=10){const o=Ae(t,i);if(Re(o,s)){n=o;break}}return{light:e,dark:r,accessible:n}}function fn(t,e){return e?`${t} ${t}--${e}`:t}function pn(t,e,r={}){return t[e]||r}function mn(t,e){return{...t,...e,customProperties:{...t.customProperties,...e.customProperties}}}function gn(){const t={};return["--theme-primary","--theme-secondary","--theme-bg-primary","--theme-bg-secondary","--theme-text-primary","--theme-text-secondary","--theme-border-primary","--theme-success","--theme-error","--theme-warning","--theme-info"].forEach(r=>{const n=gt(r);n&&(t[r]=n)}),t}function yn(t,e){t.setAttribute("data-theme",e.mode),t.style.setProperty("--theme-primary",e.primaryColor),t.style.setProperty("--theme-secondary",e.secondaryColor),t.style.setProperty("--theme-bg-primary",e.backgroundColor),t.style.setProperty("--theme-text-primary",e.textColor),t.style.setProperty("--theme-border-primary",e.borderColor),t.style.setProperty("--theme-font-family",e.fontFamily),Object.entries(e.customProperties).forEach(([r,n])=>{t.style.setProperty(r,n)})}function wn(t){t.removeAttribute("data-theme"),["--theme-primary","--theme-secondary","--theme-bg-primary","--theme-text-primary","--theme-border-primary","--theme-font-family"].forEach(r=>{t.style.removeProperty(r)})}function bn(t){return`(prefers-color-scheme: ${t===P.ThemeMode.MFE?"dark":"light"})`}function En(){return typeof window>"u"?P.ThemeMode.CRM:window.matchMedia("(prefers-color-scheme: dark)").matches?P.ThemeMode.MFE:P.ThemeMode.CRM}exports.ExternalApiClient=Oe;exports.applyThemeToElement=yn;exports.authService=on;exports.createThemeClass=fn;exports.createThemeMediaQuery=bn;exports.darkenColor=Ae;exports.detectSystemTheme=En;exports.extractThemeColors=gn;exports.generateAccessibleColors=dn;exports.generateThemeCSS=ln;exports.getApiClient=nn;exports.getAuthService=ce;exports.getCSSVariable=gt;exports.getCSSVariables=cn;exports.getContrastRatio=bt;exports.getThemeStyle=pn;exports.hexToRgb=ue;exports.isAccessible=Re;exports.lightenColor=wt;exports.mergeThemeConfigs=mn;exports.removeThemeFromElement=wn;exports.rgbToHex=Pe;exports.setCSSVariable=yt;exports.setCSSVariables=un;exports.useAuth=an;exports.validateThemeConfig=hn;
