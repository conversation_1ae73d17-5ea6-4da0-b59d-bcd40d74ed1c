import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig(({ mode }) => {
  const deploymentMode = process.env.VITE_DEPLOYMENT_MODE || 'web_resource';

  return {
    plugins: [react()],
    css: {
      preprocessorOptions: {
        css: {
          charset: false,
        },
      },
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../../shared'),
        '@shared/components': resolve(__dirname, '../../shared/components'),
        '@shared/services': resolve(__dirname, '../../shared/services'),
        '@shared/utils': resolve(__dirname, '../../shared/utils'),
        '@shared/config': resolve(__dirname, '../../shared/config'),
        '@shared/styles': resolve(__dirname, '../../shared/styles'),
      },
    },
    define: {
      'import.meta.env.VITE_DEPLOYMENT_MODE': JSON.stringify(deploymentMode),
      'import.meta.env.VITE_THEME_MODE': JSON.stringify(process.env.VITE_THEME_MODE),
    },
    server: {
      port: 5173,
      open: true,
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              if (assetInfo.name.includes('theme')) {
                return 'assets/themes/[name].[ext]';
              }
              return 'assets/styles/[name].[ext]';
            }
            return 'assets/[name].[ext]';
          },
        },
      },
    },
  };
});
