{"name": "crm-react-apps", "version": "1.0.0", "description": "Multi-app React monorepo with Vite, shared packages, and PCF components for Dynamics 365", "private": true, "workspaces": ["apps/*", "shared", "pcf/*"], "scripts": {"install:all": "npm install", "dev:transcript-and-summary": "npm run dev --workspace=apps/transcript-and-summary", "dev:if-party-master": "npm run dev --workspace=apps/if-party-master", "dev:transcript-and-summary:webresource": "npm run dev:webresource --workspace=apps/transcript-and-summary", "dev:transcript-and-summary:standalone": "npm run dev:standalone --workspace=apps/transcript-and-summary", "dev:if-party-master:webresource": "npm run dev:webresource --workspace=apps/if-party-master", "dev:if-party-master:standalone": "npm run dev:standalone --workspace=apps/if-party-master", "build:transcript-and-summary": "npm run build --workspace=apps/transcript-and-summary", "build:if-party-master": "npm run build --workspace=apps/if-party-master", "build:transcript-and-summary:webresource": "npm run build:webresource --workspace=apps/transcript-and-summary", "build:transcript-and-summary:standalone": "npm run build:standalone --workspace=apps/transcript-and-summary", "build:if-party-master:webresource": "npm run build:webresource --workspace=apps/if-party-master", "build:if-party-master:standalone": "npm run build:standalone --workspace=apps/if-party-master", "build:shared": "npm run build --workspace=shared", "build:shared:webresource": "npm run build:webresource --workspace=shared", "build:shared:standalone": "npm run build:standalone --workspace=shared", "build:pcf": "npm run build --workspace=pcf/contact-timeline-control", "build:all": "npm run build:shared && npm run build:transcript-and-summary && npm run build:if-party-master", "build:all:webresource": "npm run build:shared:webresource && npm run build:transcript-and-summary:webresource && npm run build:if-party-master:webresource", "build:all:standalone": "npm run build:shared:standalone && npm run build:transcript-and-summary:standalone && npm run build:if-party-master:standalone", "preview:transcript-and-summary": "npm run preview --workspace=apps/transcript-and-summary", "preview:if-party-master": "npm run preview --workspace=apps/if-party-master", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx --ignore-path .gitignore", "clean": "rimraf apps/*/dist shared/dist pcf/*/dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:shared": "jest --selectProjects shared", "test:transcript-and-summary": "jest --selectProjects transcript-and-summary", "test:if-party-master": "jest --selectProjects if-party-master", "test:simple": "jest shared/__tests__/simple.test.tsx", "test:theme": "jest shared/__tests__/theme-basic.test.tsx", "test:auth": "jest shared/__tests__/auth-basic.test.tsx"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "rimraf": "^5.0.5", "ts-jest": "^29.4.1", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/crm-react-apps.git"}, "keywords": ["react", "vite", "monorepo", "typescript", "dynamics365", "pcf"], "author": "Your Organization", "license": "MIT"}