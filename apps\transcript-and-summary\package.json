{"name": "@crm/transcript-and-summary", "version": "1.0.0", "description": "Transcript and Summary React application", "type": "module", "scripts": {"dev": "vite", "dev:webresource": "cross-env VITE_DEPLOYMENT_MODE=web_resource vite", "dev:standalone": "cross-env VITE_DEPLOYMENT_MODE=standalone vite", "build": "tsc && vite build", "build:webresource": "cross-env VITE_DEPLOYMENT_MODE=web_resource npm run build", "build:standalone": "cross-env VITE_DEPLOYMENT_MODE=standalone npm run build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@crm/shared": "file:../../shared"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "cross-env": "^7.0.3", "typescript": "^5.3.0", "vite": "^5.0.8"}}