# Migration Guide: Dynamics 365 Web Resources to Standalone SPAs

This guide explains how to migrate your CRM React applications from Dynamics 365 web resource deployment to standalone Single Page Applications (SPAs).

## 🎯 Overview

The monorepo has been refactored with an abstraction layer that supports both deployment modes:

- **Current**: Dynamics 365 web resources with implicit authentication
- **Future**: Standalone SPAs with MSAL authentication and external API calls

## 🏗️ Architecture Changes

### Deployment Context Detection

The system automatically detects the deployment environment:

```typescript
import { getDeploymentConfig, isWebResourceMode, isStandaloneMode } from '@shared/config/deploymentContext';

// Automatically detects context
const config = getDeploymentConfig();
console.log('Deployment mode:', config.mode);

// Helper functions
if (isWebResourceMode()) {
  // Running as Dynamics 365 web resource
}

if (isStandaloneMode()) {
  // Running as standalone SPA
}
```

### Authentication Abstraction

Authentication is now handled through a unified interface:

```typescript
import { useAuth } from '@shared/services/auth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();
  
  // Works in both deployment modes
  const handleLogin = async () => {
    await login({ interactive: true });
  };
  
  return (
    <div>
      {isAuthenticated ? (
        <p>Welcome, {user?.name}</p>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  );
}
```

### API Client Abstraction

API calls are automatically routed based on deployment context:

```typescript
import { getApiClient } from '@shared/services/api/apiFactory';

async function fetchContacts() {
  const apiClient = await getApiClient();
  
  // Automatically uses Xrm.WebApi in D365 or external API in standalone
  const response = await apiClient.retrieveMultipleRecords('contact', {
    select: ['contactid', 'fullname', 'emailaddress1'],
    filter: "statecode eq 0",
    top: 50
  });
  
  return response.data;
}
```

## 🔧 Configuration

### Environment Variables

Create environment-specific configuration files:

#### `.env.webresource` (for web resource builds)
```bash
VITE_DEPLOYMENT_MODE=web_resource
VITE_ENABLE_LOGGING=false
VITE_ENABLE_TELEMETRY=true
```

#### `.env.standalone` (for standalone builds)
```bash
VITE_DEPLOYMENT_MODE=standalone
VITE_API_BASE_URL=https://your-org.api.crm.dynamics.com/api/data/v9.2
VITE_MSAL_CLIENT_ID=your-azure-app-client-id
VITE_MSAL_AUTHORITY=https://login.microsoftonline.com/your-tenant-id
VITE_MSAL_REDIRECT_URI=https://your-spa-domain.com
VITE_MSAL_SCOPES=https://your-org.crm.dynamics.com/.default
VITE_ENABLE_LOGGING=true
VITE_ENABLE_TELEMETRY=true
```

### Azure AD App Registration

For standalone deployment, register an Azure AD application:

1. **Register Application**:
   - Go to Azure Portal > Azure Active Directory > App registrations
   - Click "New registration"
   - Name: "CRM React SPA"
   - Supported account types: "Accounts in this organizational directory only"
   - Redirect URI: "Single-page application (SPA)" → `https://your-domain.com`

2. **Configure API Permissions**:
   - Add permission: "Dynamics CRM" → "Delegated permissions" → "user_impersonation"
   - Grant admin consent

3. **Configure Authentication**:
   - Platform configurations → Add "Single-page application"
   - Redirect URIs: Add your SPA domain
   - Enable "Access tokens" and "ID tokens"

## 🚀 Build and Deployment

### Development

```bash
# Web resource mode (current)
npm run dev:transcript-and-summary:webresource
npm run dev:if-party-master:webresource

# Standalone mode (future)
npm run dev:transcript-and-summary:standalone
npm run dev:if-party-master:standalone
```

### Production Builds

```bash
# Build for web resource deployment
npm run build:all:webresource

# Build for standalone deployment
npm run build:all:standalone
```

### Deployment Strategies

#### Web Resource Deployment (Current)
1. Build with web resource mode: `npm run build:all:webresource`
2. Package the `dist` files as web resources
3. Upload to Dynamics 365 using Solution Explorer or Power Platform CLI
4. Configure forms to reference the web resources

#### Standalone SPA Deployment (Future)
1. Build with standalone mode: `npm run build:all:standalone`
2. Deploy `dist` files to your web hosting service (Azure Static Web Apps, Netlify, etc.)
3. Configure CORS in Dynamics 365 to allow your SPA domain
4. Update DNS and SSL certificates as needed

## 🔄 Migration Process

### Phase 1: Preparation (Current State)
- ✅ Abstraction layer implemented
- ✅ Dual build system configured
- ✅ Environment detection working
- ✅ Existing functionality preserved

### Phase 2: Testing and Validation
1. **Test Web Resource Mode**:
   ```bash
   npm run build:all:webresource
   # Deploy and test in Dynamics 365
   ```

2. **Test Standalone Mode**:
   ```bash
   npm run build:all:standalone
   # Deploy to test environment and validate
   ```

3. **Validate Feature Parity**:
   - Authentication flows
   - API operations (CRUD)
   - Error handling
   - Performance

### Phase 3: Gradual Migration
1. **Parallel Deployment**:
   - Keep web resources active
   - Deploy standalone version to test domain
   - Validate with subset of users

2. **Feature Flags**:
   ```typescript
   import { getDeploymentConfig } from '@shared/config/deploymentContext';
   
   const config = getDeploymentConfig();
   if (config.features.enableNewFeature) {
     // Enable new functionality only in standalone mode
   }
   ```

3. **Traffic Switching**:
   - Gradually redirect users to standalone SPA
   - Monitor performance and error rates
   - Rollback capability maintained

### Phase 4: Complete Migration
1. **DNS Cutover**:
   - Update DNS to point to standalone SPA
   - Remove web resource references from Dynamics 365

2. **Cleanup**:
   - Remove web resource build configurations
   - Clean up Dynamics 365 web resources
   - Update documentation

## 🛠️ Code Changes Required

### Minimal Changes Needed

Most existing code will work without changes. Only these scenarios require updates:

#### 1. Direct Xrm API Usage
```typescript
// Before (direct Xrm usage)
const result = await Xrm.WebApi.retrieveRecord('contact', contactId);

// After (abstracted)
import { getApiClient } from '@shared/services/api/apiFactory';
const apiClient = await getApiClient();
const result = await apiClient.retrieveRecord('contact', contactId);
```

#### 2. Authentication State Checks
```typescript
// Before (manual checks)
const isLoggedIn = !!localStorage.getItem('authToken');

// After (abstracted)
import { useAuth } from '@shared/services/auth';
const { isAuthenticated } = useAuth();
```

#### 3. Environment-Specific Logic
```typescript
// Before (manual detection)
const isDynamics = window.location.href.includes('.dynamics.com');

// After (abstracted)
import { isWebResourceMode } from '@shared/config/deploymentContext';
const isDynamics = isWebResourceMode();
```

## 🔍 Testing Strategy

### Unit Tests
```typescript
import { DeploymentContextDetector, DeploymentMode } from '@shared/config/deploymentContext';

describe('Deployment Context', () => {
  it('should detect web resource mode', () => {
    const detector = DeploymentContextDetector.getInstance();
    detector.forceDeploymentMode(DeploymentMode.WEB_RESOURCE);
    
    expect(detector.detectDeploymentMode()).toBe(DeploymentMode.WEB_RESOURCE);
  });
});
```

### Integration Tests
```typescript
import { getApiClient } from '@shared/services/api/apiFactory';

describe('API Client', () => {
  it('should work in both deployment modes', async () => {
    const apiClient = await getApiClient();
    const response = await apiClient.retrieveMultipleRecords('systemuser');
    
    expect(response.success).toBe(true);
    expect(response.data).toBeDefined();
  });
});
```

## 📊 Monitoring and Observability

### Logging
```typescript
import { logger } from '@shared/utils/logger';

// Automatically includes deployment context
logger.info('User action completed', { 
  action: 'create_contact',
  userId: user.id 
});
```

### Error Tracking
```typescript
import { getDeploymentConfig } from '@shared/config/deploymentContext';

const config = getDeploymentConfig();
if (config.features.enableTelemetry) {
  // Send telemetry data
  analytics.track('error', { 
    deploymentMode: config.mode,
    error: error.message 
  });
}
```

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors in Standalone Mode**:
   - Configure Dynamics 365 CORS settings
   - Verify redirect URIs in Azure AD

2. **Authentication Loops**:
   - Check MSAL configuration
   - Verify token scopes

3. **API Permission Errors**:
   - Ensure proper Dynamics 365 permissions
   - Check Azure AD app permissions

### Debug Mode
```bash
# Enable debug logging
VITE_ENABLE_LOGGING=true npm run dev:standalone
```

## 📚 Additional Resources

- [Azure AD SPA Authentication](https://docs.microsoft.com/en-us/azure/active-directory/develop/scenario-spa-overview)
- [Dynamics 365 Web API](https://docs.microsoft.com/en-us/powerapps/developer/data-platform/webapi/overview)
- [MSAL.js Documentation](https://github.com/AzureAD/microsoft-authentication-library-for-js)

## 🤝 Support

For migration assistance:
1. Review this guide thoroughly
2. Test in development environment first
3. Contact the development team for complex scenarios
4. Document any custom requirements or edge cases
