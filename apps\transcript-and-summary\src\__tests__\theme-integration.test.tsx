/**
 * Theme Integration Tests for Transcript and Summary App
 * 
 * Tests app-specific theme customizations and integration
 */

import React from 'react';
import { screen } from '@testing-library/react';
import App from '../App';
import { ThemeMode } from '@shared/services/theme';
import { DeploymentMode } from '@shared/config/deploymentContext';
import {
  renderWithAppContext,
  testScenarios,
  createComponentTestSuite,
} from '@shared/testing/render-utils';
import { setupTestMocks, cleanupTestMocks } from '@shared/testing/mock-utils';

describe('Transcript App Theme Integration', () => {
  beforeEach(() => {
    setupTestMocks();
  });

  afterEach(() => {
    cleanupTestMocks();
  });

  describe('App-Specific Theme Classes', () => {
    test('applies transcript-app class in CRM theme', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
        deploymentMode: DeploymentMode.WEB_RESOURCE,
      });

      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');
      expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
    });

    test('applies transcript-app class in MFE theme', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.MFE,
        deploymentMode: DeploymentMode.STANDALONE_MFE,
      });

      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');
      expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
    });
  });

  describe('Theme-Specific Styling', () => {
    test('uses CRM theme variables', () => {
      // Mock CSS custom properties for CRM theme
      document.documentElement.style.setProperty('--transcript-primary', '#0078d4');
      document.documentElement.style.setProperty('--transcript-header-height', '48px');
      document.documentElement.style.setProperty('--transcript-content-padding', '16px');

      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      // Verify CSS custom properties are available
      const computedStyle = getComputedStyle(document.documentElement);
      expect(computedStyle.getPropertyValue('--transcript-primary')).toBe('#0078d4');
      expect(computedStyle.getPropertyValue('--transcript-header-height')).toBe('48px');
    });

    test('uses MFE theme variables', () => {
      // Mock CSS custom properties for MFE theme
      document.documentElement.style.setProperty('--transcript-primary', '#5e10b1');
      document.documentElement.style.setProperty('--transcript-header-height', '64px');
      document.documentElement.style.setProperty('--transcript-content-padding', '24px');

      renderWithAppContext(<App />, {
        theme: ThemeMode.MFE,
      });

      // Verify CSS custom properties are available
      const computedStyle = getComputedStyle(document.documentElement);
      expect(computedStyle.getPropertyValue('--transcript-primary')).toBe('#5e10b1');
      expect(computedStyle.getPropertyValue('--transcript-header-height')).toBe('64px');
    });
  });

  describe('Component Theme Classes', () => {
    test('applies theme classes to header', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      const header = document.querySelector('.transcript-header');
      expect(header).toBeInTheDocument();
      
      const title = document.querySelector('.transcript-header__title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveTextContent('Transcript and Summary');
    });

    test('applies theme classes to buttons', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.MFE,
        isAuthenticated: false, // Show login screen
      });

      const loginButton = screen.getByText('Demo Login');
      expect(loginButton).toHaveClass('transcript-button');
      expect(loginButton).toHaveClass('transcript-button--primary');
    });

    test('applies theme classes to content areas', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      const mainContent = screen.getByTestId('app-content');
      expect(mainContent).toHaveClass('transcript-content');
    });
  });

  describe('Responsive Behavior', () => {
    test('adapts to mobile breakpoint in CRM theme', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 767, // Just below CRM mobile breakpoint
      });

      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      // Verify mobile-specific behavior
      expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
    });

    test('adapts to mobile breakpoint in MFE theme', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 839, // Just below MFE mobile breakpoint
      });

      renderWithAppContext(<App />, {
        theme: ThemeMode.MFE,
      });

      // Verify mobile-specific behavior
      expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
    });
  });

  describe('Theme Switching', () => {
    test('maintains app styling when switching themes', () => {
      const { rerender } = renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      // Verify initial CRM theme
      expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');

      // Switch to MFE theme
      rerender(
        <App />
      );
      
      // Update theme manually for test
      document.documentElement.setAttribute('data-theme', 'mfe');

      // Verify MFE theme
      expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
      expect(appContainer).toHaveClass('transcript-app');
    });
  });

  describe('Deployment Scenarios', () => {
    test('works correctly in CRM web resource scenario', () => {
      renderWithAppContext(<App />, testScenarios.crmWebResource);

      expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
      expect(screen.getByTestId('app-container')).toHaveClass('transcript-app');
      expect(global.Xrm).toBeDefined();
    });

    test('works correctly in MFE standalone scenario', () => {
      renderWithAppContext(<App />, testScenarios.mfeStandalone);

      expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
      expect(screen.getByTestId('app-container')).toHaveClass('transcript-app');
      expect(global.Xrm).toBeUndefined();
    });
  });

  describe('Accessibility', () => {
    test('maintains accessibility in CRM theme', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.CRM,
      });

      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toBeInTheDocument();
      
      // Check for proper heading structure
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Transcript and Summary');
    });

    test('maintains accessibility in MFE theme', () => {
      renderWithAppContext(<App />, {
        theme: ThemeMode.MFE,
      });

      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toBeInTheDocument();
      
      // Check for proper heading structure
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Transcript and Summary');
    });
  });
});

// Create comprehensive test suite for the app
createComponentTestSuite('Transcript App with Themes', () => <App />, {
  themeTests: (theme) => {
    test(`renders correctly with ${theme} theme styles`, () => {
      renderWithAppContext(<App />, { theme });
      
      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');
      expect(document.documentElement).toHaveAttribute('data-theme', theme);
    });
  },
  
  authTests: (authState) => {
    test(`handles ${authState.isAuthenticated ? 'authenticated' : 'unauthenticated'} state with themes`, () => {
      renderWithAppContext(<App />, authState);
      
      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');
    });
  },
  
  scenarioTests: (scenario) => {
    test(`works in ${scenario.deploymentMode} with proper theme integration`, () => {
      renderWithAppContext(<App />, scenario);
      
      const appContainer = screen.getByTestId('app-container');
      expect(appContainer).toHaveClass('transcript-app');
      expect(document.documentElement).toHaveAttribute('data-theme', scenario.theme);
    });
  },
});
