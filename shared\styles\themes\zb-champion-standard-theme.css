.zb-champion-standard-theme .zb-brand-logo {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 207px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo {
        height:32px
    }
}

.zb-champion-standard-theme .zb-breadcrumbs {
    display: block
}

.zb-champion-standard-theme .zb-breadcrumbs:after {
    content: "";
    margin-bottom: -8px;
    display: block
}

.zb-champion-standard-theme .zb-breadcrumbs ol {
    padding: 0;
    margin: 0
}

.zb-champion-standard-theme .zb-breadcrumbs li {
    display: inline
}

.zb-champion-standard-theme .zb-breadcrumb {
    display: inline-block;
    -webkit-text-decoration: underline;
    text-decoration: underline;
    white-space: nowrap;
    margin-bottom: 8px
}

.zb-champion-standard-theme .zb-breadcrumb:after {
    content: "";
    display: inline-block;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='none' stroke='%23646068' stroke-width='2' d='M5 2l6 6-5.971 5.97'/%3E%3C/svg%3E");
    background-position: 50%;
    background-repeat: no-repeat;
    vertical-align: bottom
}

.zb-champion-standard-theme .zb-breadcrumb-is-current {
    text-decoration: none;
    color: #000
}

.zb-champion-standard-theme .zb-breadcrumb-is-current:hover {
    color: #000
}

.zb-champion-standard-theme .zb-breadcrumb-is-current:after {
    display: none
}

.zb-champion-standard-theme .zb-breadcrumb-is-current-accessibility-hint {
    position: absolute;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px
}

.zb-champion-standard-theme .zb-breadcrumb {
    padding: 4px 0 4px 4px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-breadcrumb {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-breadcrumb:after {
    height: 12px;
    width: 12px;
    margin-left: 2px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-breadcrumb:after {
        width:14px;
        height: 14px
    }
}

.zb-champion-standard-theme .zb-breadcrumb-is-current {
    padding-right: 4px
}

.zb-champion-standard-theme .zb-button {
    display: inline-block;
    text-align: center;
    padding: 0 32px;
    cursor: pointer;
    text-decoration: none;
    font-family: RNHouseSans,Arial,sans-serif;
    min-width: 96px;
    border-radius: 25px
}

.zb-champion-standard-theme .zb-button.zb-button-is-disabled,.zb-champion-standard-theme .zb-button:disabled {
    cursor: auto
}

.zb-champion-standard-theme .zb-button:hover {
    text-decoration: none
}

.zb-champion-standard-theme .zb-button-primary {
    padding: 12px 32px;
    background-color: #5e10b1;
    color: #fff;
    border: none;
    line-height: 1.45
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-primary {
        line-height:1.3;
        padding: 12px 32px
    }
}

.zb-champion-standard-theme .zb-button-primary:hover {
    background-color: #3c1053;
    color: #fff;
    border: none
}

.zb-champion-standard-theme .zb-button-primary.zb-button-is-disabled,.zb-champion-standard-theme .zb-button-primary:disabled {
    background-color: #bf9fe0;
    color: #fff;
    border: none
}

.zb-champion-standard-theme .zb-button-secondary {
    padding: 10px 32px;
    background-color: #fff;
    color: #5e10b1;
    border: 2px solid #5e10b1;
    line-height: 1.45
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-secondary {
        line-height:1.3;
        padding: 11px 32px
    }
}

.zb-champion-standard-theme .zb-button-secondary:hover {
    background-color: #f2eaf9;
    color: #3c1053;
    border: 2px solid #3c1053;
    padding: 10px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-secondary:hover {
        padding:11px 32px
    }
}

.zb-champion-standard-theme .zb-button-secondary:disabled {
    background-color: #fff;
    color: #bf9fe0;
    border: 2px solid #bf9fe0;
    padding: 10px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-secondary:disabled {
        padding:11px 32px
    }
}

.zb-champion-standard-theme .zb-button-secondary.zb-button-is-disabled {
    background-color: #fff;
    color: #bf9fe0;
    border: 2px solid #bf9fe0;
    padding: 10px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-secondary.zb-button-is-disabled {
        padding:11px 32px
    }
}

.zb-champion-standard-theme .zb-button-with-icon-after:hover>.zb-icon,.zb-champion-standard-theme .zb-button-with-icon-before:hover>.zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-button-with-icon-after>.zb-icon,.zb-champion-standard-theme .zb-button-with-icon-before>.zb-icon {
    vertical-align: bottom
}

.zb-champion-standard-theme .zb-button-with-icon-before>.zb-icon {
    margin: 0 7px 4px -12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-with-icon-before>.zb-icon {
        margin:0 7px 0 -12px
    }
}

.zb-champion-standard-theme .zb-button-with-icon-after>.zb-icon {
    margin: 0 -12px 4px 7px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-with-icon-after>.zb-icon {
        margin:0 -12px 0 7px
    }
}

.zb-champion-standard-theme .zb-button-link {
    display: inline;
    color: #5e10b1;
    -webkit-text-decoration: underline;
    text-decoration: underline;
    background: transparent;
    padding: 0;
    margin: 0;
    border: 0;
    font: inherit;
    line-height: inherit;
    cursor: pointer;
    -moz-user-select: text
}

.zb-champion-standard-theme .zb-button-link::-moz-focus-inner {
    padding: 0;
    border: 0
}

.zb-champion-standard-theme .zb-button-link:hover {
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-button-link.zb-button-with-icon-after .zb-icon,.zb-champion-standard-theme .zb-button-link.zb-button-with-icon-after:hover .zb-icon,.zb-champion-standard-theme .zb-button-link.zb-button-with-icon-before .zb-icon,.zb-champion-standard-theme .zb-button-link.zb-button-with-icon-before:hover .zb-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-button {
    font-size: 18px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme a.zb-button:focus {
    text-decoration: none
}

.zb-champion-standard-theme .zb-button-with-icon-after .zb-icon,.zb-champion-standard-theme .zb-button-with-icon-after:hover>.zb-icon,.zb-champion-standard-theme .zb-button-with-icon-before .zb-icon,.zb-champion-standard-theme .zb-button-with-icon-before:hover>.zb-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-button-primary-alternate {
    padding: 12px 32px;
    background-color: #fff;
    color: #5e10b1;
    border: none;
    line-height: 1.45
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-primary-alternate {
        line-height:1.3;
        padding: 12px 32px
    }
}

.zb-champion-standard-theme .zb-button-primary-alternate:hover {
    background-color: hsla(0,0%,100%,.85);
    color: #3c1053;
    border: none
}

.zb-champion-standard-theme .zb-button-primary-alternate.zb-button-is-disabled,.zb-champion-standard-theme .zb-button-primary-alternate:disabled {
    background-color: hsla(0,0%,100%,.6);
    color: #5e10b1;
    border: none
}

.zb-champion-standard-theme .zb-button-secondary-alternate {
    padding: 10px 32px;
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
    line-height: 1.45
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-secondary-alternate {
        line-height:1.3;
        padding: 11px 32px
    }
}

.zb-champion-standard-theme .zb-button-secondary-alternate:hover {
    background-color: hsla(0,0%,100%,.15);
    color: #fff;
    border: 2px solid #fff
}

.zb-champion-standard-theme .zb-button-secondary-alternate.zb-button-is-disabled,.zb-champion-standard-theme .zb-button-secondary-alternate:disabled {
    background-color: transparent;
    color: hsla(0,0%,100%,.4);
    border: 2px solid hsla(0,0%,100%,.4)
}

.zb-champion-standard-theme .zb-button.zb-button-small.zb-button-primary {
    padding: 9px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button.zb-button-small.zb-button-primary {
        padding:9px 32px
    }
}

.zb-champion-standard-theme .zb-button.zb-button-small.zb-button-primary-alternate {
    padding: 9px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button.zb-button-small.zb-button-primary-alternate {
        padding:9px 32px
    }
}

.zb-champion-standard-theme .zb-button.zb-button-small.zb-button-secondary {
    padding: 8px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button.zb-button-small.zb-button-secondary {
        padding:8px 32px
    }
}

.zb-champion-standard-theme .zb-button.zb-button-small.zb-button-secondary-alternate {
    padding: 8px 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button.zb-button-small.zb-button-secondary-alternate {
        padding:8px 32px
    }
}

.zb-champion-standard-theme .zb-card {
    display: block;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    border-radius: 16px
}

.zb-champion-standard-theme .zb-card-body {
    background: #fff;
    border-radius: 16px
}

.zb-champion-standard-theme .zb-card-body+.zb-card-body {
    border-top: 1px solid #cccfd0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.zb-champion-standard-theme .zb-card-header {
    border-bottom: 1px solid #cccfd0;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    background: #fff;
    padding: 0 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-header {
        padding:0 20px
    }
}

.zb-champion-standard-theme .zb-card-header+.zb-card-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.zb-champion-standard-theme .zb-card-header-title {
    font-size: 1.5rem;
    font-weight: 400;
    color: #333;
    margin: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-header-title {
        font-size:1.125rem
    }
}

.zb-champion-standard-theme .zb-card-description {
    background: #cccfd0;
    padding: 30px;
    margin: -30px -30px 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-description {
        margin:-20px -20px 20px;
        padding: 20px
    }
}

.zb-champion-standard-theme .zb-card-body {
    padding: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-body {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-card-body {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-body {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-card-header {
    padding-top: 0;
    padding-bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.zb-champion-standard-theme .zb-card-header-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-height: 65px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    line-height: 1.15em;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */
html {
    font-family: sans-serif;
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,aside,footer,header,nav,section {
    display: block
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

figcaption,figure,main {
    display: block
}

figure {
    margin: 1em 40px
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

pre {
    font-family: monospace,monospace;
    font-size: 1em
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

a:active,a:hover {
    outline-width: 0
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

b,strong {
    font-weight: inherit;
    font-weight: bolder
}

code,kbd,samp {
    font-family: monospace,monospace;
    font-size: 1em
}

dfn {
    font-style: italic
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

audio,video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

img {
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

button,input,optgroup,select,textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,input {
    overflow: visible
}

button,select {
    text-transform: none
}

[type=reset],[type=submit],button,html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner {
    border-style: none;
    padding: 0
}

[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring,button:-moz-focusring {
    outline: 1px dotted ButtonText
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

progress {
    display: inline-block;
    vertical-align: baseline
}

textarea {
    overflow: auto
}

[type=checkbox],[type=radio] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

details,menu {
    display: block
}

summary {
    display: list-item
}

canvas {
    display: inline-block
}

[hidden],template {
    display: none
}

.zb-champion-standard-theme {
    font-family: RNHouseSans,Arial,sans-serif;
    color: #646068;
    font-size: 1rem;
    font-weight: 400;
    font-style: normal;
    line-height: 1.25
}

@media (max-width: 840px) {
    .zb-champion-standard-theme {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-text-small {
    font-size: .8125rem;
    line-height: .8125rem;
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme a {
    color: #5e10b1;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme a:hover {
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme input,.zb-champion-standard-theme textarea {
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme .zb-input {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 44px;
    padding: 0 12px;
    color: #646068;
    border: 1px solid #646068;
    border-radius: 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input {
        height:40px
    }
}

.zb-champion-standard-theme .zb-input::-ms-clear {
    display: none
}

.zb-champion-standard-theme .zb-input:-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-input:hover {
    border: 1px solid #5e10b1;
    padding: 0 12px
}

.zb-champion-standard-theme .zb-input:focus {
    outline: none;
    padding: 0 12px;
    border: 1px solid #646068;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input:disabled {
    border: 1px solid #c1bfc3;
    padding: 0 12px;
    background: #fff;
    color: #999
}

.zb-champion-standard-theme .zb-input:disabled:-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-input.zb-input-is-error {
    border: 1px solid #cf223f;
    background: #fff
}

.zb-champion-standard-theme .zb-input.zb-input-is-error,.zb-champion-standard-theme .zb-input.zb-input-is-error:focus {
    padding: 0 12px;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-input-wrapper {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    height: 44px;
    padding: 0 12px;
    color: #646068;
    border: 1px solid #646068;
    border-radius: 8px;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input-wrapper {
        height:40px
    }
}

.zb-champion-standard-theme .zb-input-wrapper>input,.zb-champion-standard-theme .zb-input-wrapper>input:disabled,.zb-champion-standard-theme .zb-input-wrapper>input:focus {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 42px;
    height: 100%;
    border: none;
    outline: none;
    background: none;
    padding: 0;
    vertical-align: baseline
}

.zb-champion-standard-theme .zb-input-wrapper>input::-ms-clear {
    display: none
}

.zb-champion-standard-theme .zb-input-wrapper:hover {
    border: 1px solid #5e10b1;
    padding: 0 12px
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-focused {
    padding: 0 12px;
    border: 1px solid #646068;
    outline: none;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-focused>input,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-focused>input:disabled,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-focused>input:focus {
    line-height: 42px
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-disabled {
    padding: 0 12px;
    border: 1px solid #c1bfc3;
    color: #999
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-disabled>input,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-disabled>input:disabled,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-disabled>input:focus {
    line-height: 42px
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error {
    border: 1px solid #cf223f;
    padding: 0 12px;
    background: #fff;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error>input,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error>input:disabled,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error>input:focus {
    line-height: 42px
}

.zb-champion-standard-theme .zb-input-wrapper>.zb-icon {
    vertical-align: bottom;
    height: 100%;
    line-height: 42px
}

.zb-champion-standard-theme .zb-textarea {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #646068;
    border: 1px solid #646068;
    border-radius: 8px;
    padding: 10px 12px;
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme .zb-textarea:-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-textarea:hover {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-textarea:focus {
    outline: none;
    padding: 10px 12px;
    border: 1px solid #646068;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-textarea.zb-textarea-is-error {
    border: 1px solid #cf223f;
    background: #fff
}

.zb-champion-standard-theme .zb-textarea.zb-textarea-is-error,.zb-champion-standard-theme .zb-textarea.zb-textarea-is-error:focus {
    padding: 10px 12px;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme ::-webkit-input-placeholder {
    color: #747077;
    opacity: 1
}

.zb-champion-standard-theme ::-moz-placeholder {
    color: #747077;
    opacity: 1
}

.zb-champion-standard-theme :-ms-input-placeholder {
    opacity: 1
}

.zb-champion-standard-theme ::-ms-input-placeholder {
    color: #747077;
    opacity: 1
}

.zb-champion-standard-theme ::placeholder {
    color: #747077;
    opacity: 1
}

.zb-champion-standard-theme :-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar {
    width: 20px;
    height: 20px
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track {
    background-color: #fff;
    border-radius: 10px;
    background-clip: content-box
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb {
    background-color: #cccfd0;
    border-radius: 10px;
    background-clip: content-box
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:horizontal:hover,.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:vertical:hover {
    background-color: #5e10b1
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:vertical,.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track:vertical {
    border: 7px solid transparent;
    border-right-width: 6px
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:horizontal,.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track:horizontal {
    border: solid transparent;
    border-width: 7px 7px 6px
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track-piece:vertical {
    -webkit-box-shadow: inset 1px 0 #cccfd0;
    box-shadow: inset 1px 0 #cccfd0
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track-piece:horizontal {
    -webkit-box-shadow: inset 0 1px #cccfd0;
    box-shadow: inset 0 1px #cccfd0
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-corner {
    background-color: #cccfd0
}

.zb-champion-standard-theme {
    background-color: #f2f2f8
}

.zb-champion-standard-theme .zb-heading1 {
    font-family: knileblack,Arial,sans-serif;
    font-size: 2rem;
    font-weight: 400;
    line-height: 1.5;
    color: #333
}

.zb-champion-standard-theme .zb-heading2 {
    font-family: RNHouseSans,Arial,sans-serif;
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.5;
    color: #333
}

.zb-champion-standard-theme .zb-heading3 {
    font-weight: 400
}

.zb-champion-standard-theme .zb-heading3,.zb-champion-standard-theme .zb-heading4 {
    font-family: RNHouseSans,Arial,sans-serif;
    font-size: 1.25rem;
    line-height: 1.6;
    color: #333
}

.zb-champion-standard-theme .zb-heading4 {
    font-weight: 700
}

.zb-champion-standard-theme .zb-heading5 {
    font-family: RNHouseSans,Arial,sans-serif;
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.22222;
    color: #333
}

.zb-champion-standard-theme .zb-heading6 {
    font-family: RNHouseSans,Arial,sans-serif;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.25;
    color: #333
}

.zb-champion-standard-theme .zb-body2-small {
    font-size: .875rem;
    line-height: 1.15;
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme .zb-body-small,.zb-champion-standard-theme .zb-body-small-bold {
    font-size: .8125rem;
    line-height: 1.23077;
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme .zb-body-small-bold {
    font-weight: 700
}

.zb-champion-standard-theme .zb-input {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-textarea {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-textarea {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-input-wrapper>input {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input-wrapper>input {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-input-wrapper>input:focus {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input-wrapper>input:focus {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-input-wrapper>input:disabled {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-input-wrapper>input:disabled {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme input {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme input {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme textarea {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme textarea {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-dropdown-container {
    min-width: 104px;
    min-height: 88px
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar {
    background-color: #fff;
    width: 24px;
    height: 24px
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:vertical,.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track:vertical {
    border-top-left-radius: .5px;
    border-bottom-left-radius: .5px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border: 8px solid transparent
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb:horizontal,.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-track:horizontal {
    border-top-left-radius: .5px;
    border-top-right-radius: .5px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 8px solid transparent
}

.zb-champion-standard-theme a:active {
    color: #5a287d;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme a:focus {
    outline: 1px solid #5e10b1;
    outline-offset: 4px;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme a.zb-tertiary-cta {
    font-size: 1rem;
    color: #5e10b1;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

@media (max-width: 840px) {
    .zb-champion-standard-theme a.zb-tertiary-cta {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme a.zb-tertiary-cta:hover {
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme a.zb-tertiary-cta:active {
    color: #5a287d
}

.zb-champion-standard-theme a.zb-tertiary-cta:focus {
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme a.zb-tertiary-cta .zb-icon {
    color: inherit;
    width: 12px;
    height: 12px;
    line-height: 12px;
    border-left: 8px solid transparent;
    vertical-align: middle
}

@media (max-width: 840px) {
    .zb-champion-standard-theme a.zb-tertiary-cta .zb-icon {
        line-height:12px;
        height: 12px;
        width: 12px
    }
}

.zb-champion-standard-theme a.zb-inverted-cta,.zb-champion-standard-theme a.zb-inverted-cta:active,.zb-champion-standard-theme a.zb-inverted-cta:hover,.zb-champion-standard-theme a.zb-tertiary-cta.zb-inverted-cta,.zb-champion-standard-theme a.zb-tertiary-cta.zb-inverted-cta:active,.zb-champion-standard-theme a.zb-tertiary-cta.zb-inverted-cta:hover {
    color: #fff
}

.zb-champion-standard-theme .zb-feature-button {
    display: inline-block;
    padding: 10px 52px 10px 32px;
    min-width: 180px;
    border: 2px solid #5e10b1;
    cursor: pointer;
    text-decoration: none;
    font-family: RNHouseSans,Arial,sans-serif;
    text-align: left;
    border-radius: 8px;
    color: #5e10b1
}

.zb-champion-standard-theme .zb-feature-button:hover {
    text-decoration: none;
    background: #f2eaf9;
    color: #5e10b1
}

.zb-champion-standard-theme .zb-feature-button:focus {
    outline: none;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-feature-button-arrowed-content {
    display: block;
    position: relative
}

.zb-champion-standard-theme .zb-feature-button-arrowed-content:after {
    content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='none' stroke='%235E10B1' stroke-width='2' d='M5 2l6 6-5.971 5.97'/%3E%3C/svg%3E");
    position: absolute;
    line-height: 10px;
    top: 50%;
    right: -20px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.zb-champion-standard-theme .zb-feature-button-arrowed-content.zb-feature-button-arrowed-content-asset:after {
    content: url(/CWSLogon/resources/images/zb-champion-standard/feature-button-arrow.svg)
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled {
    border-color: #bf9fe0
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled .zb-icon {
    color: #bf9fe0
}

.zb-champion-standard-theme .zb-feature-button {
    background-color: #fff;
    font-size: 18px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-feature-button {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled,.zb-champion-standard-theme .zb-feature-button:disabled {
    cursor: default;
    background-color: #fff;
    color: #bf9fe0;
    opacity: 1;
    border-color: #fff
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled:hover,.zb-champion-standard-theme .zb-feature-button:disabled:hover {
    background: #fff
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled:focus,.zb-champion-standard-theme .zb-feature-button:disabled:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled .zb-feature-button-arrowed-content:after,.zb-champion-standard-theme .zb-feature-button:disabled .zb-feature-button-arrowed-content:after {
    content: url(/CWSLogon/resources/images/zb-champion-standard/feature-button-arrow-disabled.svg)
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-is-disabled .zb-icon,.zb-champion-standard-theme .zb-feature-button:disabled .zb-icon {
    color: #333
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-multiline {
    padding: 20px 52px 20px 32px
}

.zb-champion-standard-theme .zb-feature-button.zb-feature-button-multiline>:first-child {
    margin-bottom: 10px;
    display: block
}

.zb-champion-standard-theme .zb-feature-button .zb-feature-button-title {
    font-size: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-feature-button .zb-feature-button-title {
        font-size:16px
    }
}

.zb-champion-standard-theme .zb-feature-button-arrowed-content {
    line-height: 1.45
}

.zb-champion-standard-theme .zb-feature-button-arrowed-content:after {
    height: 16px;
    width: 10px
}

.zb-champion-standard-theme .zb-footer {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    background: #5a287d;
    padding: 16px 0 24px;
    font-size: .8125rem
}

@media (max-width: 1023px) {
    .zb-champion-standard-theme .zb-footer {
        padding:14px 6px
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-footer {
        padding:22px 4px
    }
}

.zb-champion-standard-theme .zb-footer-nav-item {
    color: #cdbfd8;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-footer-nav-item:hover {
    color: #fff;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-footer-text {
    color: #fff
}

.zb-champion-standard-theme .zb-footer-nav-item {
    display: block;
    margin-bottom: 16px
}

@media (max-width: 1023px) {
    .zb-champion-standard-theme .zb-footer-nav-item {
        margin-bottom:20px
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-footer-nav-item {
        margin-bottom:20px
    }
}

.zb-champion-standard-theme .zb-footer-nav-item:last-child {
    margin-bottom: 0
}

@media (max-width: 1023px) {
    .zb-champion-standard-theme .zb-footer-nav-item:last-child {
        margin-bottom:30px
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-footer-nav-item:last-child {
        margin-bottom:20px
    }
}

.zb-champion-standard-theme .zb-footer-text {
    display: block;
    margin-top: 0
}

@media (max-width: 1023px) {
    .zb-champion-standard-theme .zb-footer-text {
        margin-top:0
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-footer-text {
        margin-top:10px
    }
}

.zb-champion-standard-theme .zb-left-hand-nav {
    display: block;
    background: #fff;
    border: 1px solid #d3d3d3
}

.zb-champion-standard-theme .zb-left-hand-nav ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header {
    margin: 0;
    padding: 11px 15px;
    display: block;
    color: #000;
    font-weight: 400;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 18px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header:first-child {
    border-top: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item {
    margin: 0;
    padding: 0;
    display: block
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item-link {
    margin: 0;
    padding: 11px 15px;
    cursor: pointer;
    display: block;
    text-decoration: none;
    color: #5e10b1;
    line-height: 18px;
    background: #fff
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item-link:hover {
    background: #f2eaf9;
    color: #5e10b1
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header:last-child {
    border-top: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link {
    font-weight: 700
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.disabled,.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item:disabled {
    opacity: .3
}

.zb-champion-standard-theme .zb-left-hand-nav ul:last-child .zb-left-hand-nav-item:last-child {
    border-bottom: none
}

.zb-champion-standard-theme .zb-left-hand-nav.zb-nav {
    border: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu {
    position: static;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-group,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu.zb-nav-menu-is-open {
    display: block
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item:hover .zb-nav-item-label .zb-nav-item-right-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu .zb-nav-menu-item-label {
    padding-left: 0;
    padding-right: 0
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item:last-of-type,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item:last-of-type {
    border-bottom: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label.zb-nav-menu-item-is-selected .zb-left-hand-nav-item-link {
    font-weight: 700
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu.zb-nav-submenu-is-open {
    display: block
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label .zb-nav-menu-item-open-icon {
    margin: 0
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header {
    background: #f9f9fc;
    color: #333;
    border-top: 1px solid #d3d3d3;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header {
    background: #f9f9fc;
    color: #333;
    border-top: 1px solid #d3d3d3;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header:last-child,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header:last-child {
    border-top: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link {
    font-weight: 700
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.disabled,.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item:disabled,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item.disabled,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item:disabled {
    opacity: .3
}

.zb-champion-standard-theme .zb-left-hand-nav ul:last-child .zb-left-hand-nav-item:last-child,.zb-champion-standard-theme .zb-nav-vertical ul:last-child .zb-left-hand-nav-item:last-child {
    border-bottom: none
}

.zb-champion-standard-theme .zb-left-hand-nav.zb-nav,.zb-champion-standard-theme .zb-nav-vertical.zb-nav {
    border: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu {
    position: static;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-group,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu.zb-nav-menu-is-open,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu.zb-nav-menu-is-open {
    display: block
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected>.zb-nav-item-label,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open>.zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected>.zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open>.zb-nav-item-label {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item:hover .zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open>.zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item:hover .zb-nav-item-label .zb-nav-item-right-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item {
    background: #fff
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu {
    border-left: 30px solid #fff
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label[aria-expanded=true],.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] {
    border-bottom: 1px solid #d3d3d3
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-is-open,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-is-open {
    border-top: 1px solid #d3d3d3
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item:last-of-type,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item:last-of-type,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item:last-of-type,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item:last-of-type {
    border-bottom: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item-label {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item-label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-left-hand-nav.zb-nav .zb-left-hand-nav-header,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-nav-vertical.zb-nav .zb-left-hand-nav-header {
    padding: 16px 8px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label.zb-nav-menu-item-is-selected .zb-left-hand-nav-item-link,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label.zb-nav-menu-item-is-selected .zb-left-hand-nav-item-link {
    font-weight: 700
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu.zb-nav-submenu-is-open,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu.zb-nav-submenu-is-open {
    display: block
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-menu-item-label .zb-nav-menu-item-open-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label .zb-nav-menu-item-open-icon {
    margin: 0
}

.zb-champion-standard-theme ul.zb-list {
    margin: 0;
    padding: 0;
    list-style: square url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='12'%3E%3Ccircle fill='%23646068' cx='4' cy='6' r='4'/%3E%3C/svg%3E") outside
}

.zb-champion-standard-theme ul.zb-list.zb-list-large-spacing li {
    padding: 8px 0 8px 8px
}

.zb-champion-standard-theme ul.zb-list li {
    color: #000;
    padding: 4px 0 4px 8px;
    margin: 0 0 0 16px
}

.zb-champion-standard-theme ul.zb-list li:first-of-type {
    padding-top: 0
}

.zb-champion-standard-theme ul.zb-list li:last-of-type {
    padding-bottom: 0
}

.zb-champion-standard-theme .zb-loader {
    content: "";
    -webkit-animation: zb-loader-spin 1s linear infinite;
    animation: zb-loader-spin 1s linear infinite;
    width: 48px;
    height: 48px;
    display: inline-block;
    background-size: contain;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Ccircle cx='50%25' cy='50%25' r='43.5%25' fill='none' stroke-width='12.5%25' stroke='%23646068' stroke-dasharray='205%25'/%3E%3C/svg%3E") 50% 50% no-repeat
}

.zb-champion-standard-theme .zb-loader.zb-loader-inverted {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Ccircle cx='50%25' cy='50%25' r='43.5%25' fill='none' stroke-width='12.5%25' stroke='%23fff' stroke-dasharray='205%25'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-loader.zb-loader-asset {
    background-image: url(/CWSLogon/resources/images/zb-champion-standard/loader-dark.svg)
}

.zb-champion-standard-theme .zb-loader-content {
    text-align: center;
    padding: 8px 0
}

.zb-champion-standard-theme .zb-loader-content .zb-loader {
    display: block;
    margin: 0 auto
}

.zb-champion-standard-theme .zb-loader-text {
    margin-top: 8px
}

.zb-champion-standard-theme .zb-loader-block {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.zb-champion-standard-theme .zb-loader-block-inverted {
    background-color: rgba(0,0,0,.6)
}

.zb-champion-standard-theme .zb-loader-block-inverted .zb-loader {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Ccircle cx='50%25' cy='50%25' r='43.5%25' fill='none' stroke-width='12.5%25' stroke='%23fff' stroke-dasharray='205%25'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-loader-block-inverted .zb-loader-text {
    color: #fff
}

.zb-champion-standard-theme .zb-loader-block.zb-loader-block-3rd {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.zb-champion-standard-theme .zb-loader-block.zb-loader-block-3rd .zb-loader-content {
    position: relative;
    display: block;
    top: 33.33%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

@-webkit-keyframes zb-loader-spin {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes zb-loader-spin {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.zb-champion-standard-theme .zb-loader-text {
    color: #646068;
    display: block;
    margin-top: 0;
    line-height: 1.5rem
}

.zb-champion-standard-theme .zb-loader-block.zb-loader-block-2rd {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.zb-champion-standard-theme .zb-loader-block.zb-loader-block-2rd .zb-loader-content {
    position: relative;
    display: block;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.zb-champion-standard-theme .zb-masthead {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    height: 82px;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead {
        height:52px
    }
}

.zb-champion-standard-theme .zb-masthead-brand-logo,.zb-champion-standard-theme .zb-masthead-brand-name,.zb-champion-standard-theme .zb-masthead-nav-item {
    display: inline-block;
    position: relative;
    vertical-align: top;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.zb-champion-standard-theme .zb-masthead-brand-name {
    color: #5a287d;
    font-size: 1.5rem;
    font-weight: 400
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-brand-name {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-masthead-brand-logo {
    margin-right: 16px;
    height: 50px;
    width: 144px;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E")
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-brand-logo {
        background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
        width: 92px;
        height: 32px
    }
}

.zb-champion-standard-theme .zb-masthead-brand-logo.zb-masthead-brand-logo-asset {
    background-image: url(/CWSLogon/resources/images/zb-champion-standard/natwest_brand_logo.svg);
    background-repeat: none
}

.zb-champion-standard-theme .zb-masthead-nav {
    float: right;
    height: 100%
}

.zb-champion-standard-theme .zb-masthead-nav-list {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.zb-champion-standard-theme .zb-masthead-nav-list li {
    display: inline-block
}

.zb-champion-standard-theme .zb-masthead-nav-item {
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none;
    font-size: .813rem;
    padding: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-nav-item {
        padding:5px 16px
    }
}

.zb-champion-standard-theme .zb-masthead-nav-item:hover {
    color: #5e10b1;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-masthead-nav-item:last-child {
    padding-right: 0
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav {
        clip:rect(0 0 0 0);
        height: 1px;
        width: 1px;
        overflow: hidden;
        position: absolute;
        top: 52px;
        right: 0;
        background-color: #fff;
        -webkit-box-shadow: -50px 0 0 0 rgba(0,0,0,.8);
        box-shadow: -50px 0 0 0 rgba(0,0,0,.8);
        z-index: 1
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav[focus-within],.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav {
            height:calc(100vh - 52px)
        }
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav.zb-masthead-nav-focus-within {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within {
            height:calc(100vh - 52px)
        }
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-links,.zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item {
        padding: 5px 20px;
        color: #5e10b1;
        background: #fff;
        display: block
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:hover {
        color: #5e10b1;
        background: #f2eaf9
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:first-of-type {
        padding-top: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:last-of-type {
        padding-bottom: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label>.zb-nav-item-right-icon {
        display: inline-block;
        margin-top: 16px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label {
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item {
        color: #5e10b1;
        background: inherit;
        display: block
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-menu-item-label {
        color: inherit;
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-menu-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-menu-item-label .zb-icon {
        color: #5e10b1
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item:hover,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item:hover {
        color: #5e10b1;
        background: #f2eaf9
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item:hover .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item:hover .zb-nav-item-label {
        color: inherit
    }

    .zb-champion-standard-theme .zb-masthead+.zb-nav {
        display: none
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        height: 50px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        padding: 0 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical span.zb-nav-item-label,.zb-champion-standard-theme .zb-masthead span.zb-nav-menu-item-label {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        line-height: 50px
    }

    .zb-champion-standard-theme .zb-masthead span.zb-nav-menu-item-label[aria-expanded=true] .zb-icon-chev-down-xsmall {
        -webkit-transform: scaleY(-1);
        transform: scaleY(-1)
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu,.zb-champion-standard-theme .zb-masthead .zb-nav-submenu {
        position: static;
        -webkit-box-shadow: unset;
        box-shadow: unset;
        padding: 0
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label {
        padding-left: 30px;
        padding-right: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu {
        padding-left: 30px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu.zb-nav-submenu-is-open {
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu .zb-nav-menu-item-label {
        padding-left: 0;
        padding-right: 0
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu .zb-nav-menu-item:last-of-type .zb-nav-menu-item-label {
        border-bottom: none
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav {
        background-color: #fff
    }

    .zb-champion-standard-theme .zb-masthead-nav-list li,.zb-champion-standard-theme .zb-masthead .zb-nav,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-group,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-is-open,.zb-champion-standard-theme .zb-masthead .zb-nav-submenu-is-open {
        display: block
    }
}

.zb-champion-standard-theme .zb-masthead-container {
    background: #fff
}

.zb-champion-standard-theme .zb-masthead {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    max-width: 1280px;
    margin: 0 auto;
    padding-left: 0;
    padding-right: 0;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead {
        padding-right:15px;
        padding-left: 15px
    }
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-links {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: none
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-logout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 20px;
    padding-right: 20px;
    margin-right: 0;
    color: #fff;
    height: 82px;
    text-decoration: none;
    margin-left: 20px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    max-width: 71px;
    font-size: .8125rem;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-masthead-logout {
        height:52px;
        margin-right: -15px
    }
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 20px;
    padding-right: 20px;
    margin-right: 0;
    color: #fff;
    text-decoration: none;
    margin-left: 20px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    max-width: 71px;
    font-size: .8125rem;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
        margin-right:-15px
    }
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-logout .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-masthead-toggler .zb-icon {
    color: #fff;
    margin-left: 5px
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle {
    display: none
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav.zb-masthead-nav-focus-within+.zb-masthead-toggler,.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav[focus-within]+.zb-masthead-toggler,.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav+.zb-masthead-toggler {
    background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M2.343.929L8 6.586 13.657.929l1.414 1.414L9.414 8l5.657 5.657-1.414 1.414L8 9.414l-5.657 5.657L.93 13.657 6.586 8 .929 2.343 2.343.93z'/%3E%3C/svg%3E") #5e10b1
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within+.zb-masthead-toggler {
    background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M2.343.929L8 6.586 13.657.929l1.414 1.414L9.414 8l5.657 5.657-1.414 1.414L8 9.414l-5.657 5.657L.93 13.657 6.586 8 .929 2.343 2.343.93z'/%3E%3C/svg%3E") #5e10b1
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
    display: none;
    padding-left: 0;
    padding-right: 0;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 82px;
    height: 82px;
    background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='14'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M0 0h18v2H0zm0 6h18v2H0zm0 6h18v2H0z'/%3E%3C/svg%3E") #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
        height:52px;
        width: 52px
    }
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-brand-name {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-brand-logo,.zb-champion-standard-theme .zb-masthead .zb-masthead-brand-name,.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item {
    display: inline-block;
    position: relative;
    vertical-align: top;
    top: 0;
    -webkit-transform: none;
    transform: none
}

.zb-champion-standard-theme .zb-masthead [class^=zb-icon] {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    display: block;
    color: #fff
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-nav {
    float: none;
    height: auto
}

.zb-champion-standard-theme .zb-masthead .zb-masthead-brand-logo {
    background-repeat: no-repeat
}

.zb-champion-standard-theme .zb-masthead+.zb-nav {
    border-top: 1px solid hsla(0,0%,100%,.2);
    background: #fff;
    max-width: 1280px;
    margin: 0 auto;
    padding-left: 0;
    padding-right: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead+.zb-nav {
        padding-right:15px;
        padding-left: 15px
    }
}

.zb-champion-standard-theme .zb-masthead .zb-nav {
    display: none
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav {
        clip:rect(0 0 0 0);
        height: 1px;
        width: 1px;
        overflow: hidden;
        position: absolute;
        top: 52px;
        right: 0;
        background-color: #fff;
        -webkit-box-shadow: -50px 0 0 0 rgba(0,0,0,.8);
        box-shadow: -50px 0 0 0 rgba(0,0,0,.8);
        z-index: 1
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav[focus-within],.zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav {
            height:calc(100vh - 52px)
        }
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav.zb-masthead-nav-focus-within {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within {
        height: calc(100vh - 52px);
        width: calc(100% - 50px);
        clip: inherit;
        overflow-y: auto
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within {
            height:calc(100vh - 52px)
        }
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-links,.zb-champion-standard-theme .zb-masthead .zb-masthead-toggler {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item {
        padding: 5px 20px;
        color: #5e10b1;
        background: #fff;
        display: block
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:hover {
        color: #5e10b1;
        background: #f2eaf9
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:first-of-type {
        padding-top: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:last-of-type {
        padding-bottom: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label>.zb-nav-item-right-icon {
        display: inline-block;
        margin-top: 16px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label {
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item {
        color: #5e10b1;
        background: inherit;
        display: block
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-menu-item-label {
        color: inherit;
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-item .zb-nav-menu-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item .zb-nav-menu-item-label .zb-icon {
        color: #5e10b1
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item:hover,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item:hover {
        color: #5e10b1;
        background: #f2eaf9
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-item:hover .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item:hover .zb-nav-item-label {
        color: inherit
    }

    .zb-champion-standard-theme .zb-masthead+.zb-nav {
        display: none
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        height: 50px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        padding: 0 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical span.zb-nav-item-label,.zb-champion-standard-theme .zb-masthead span.zb-nav-menu-item-label {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        line-height: 50px
    }

    .zb-champion-standard-theme .zb-masthead span.zb-nav-menu-item-label[aria-expanded=true] .zb-icon-chev-down-xsmall {
        -webkit-transform: scaleY(-1);
        transform: scaleY(-1)
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu,.zb-champion-standard-theme .zb-masthead .zb-nav-submenu {
        position: static;
        -webkit-box-shadow: unset;
        box-shadow: unset;
        padding: 0
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label {
        padding-left: 30px;
        padding-right: 20px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu {
        padding-left: 30px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu.zb-nav-submenu-is-open {
        border-bottom: 1px solid #c9c6c6
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu .zb-nav-menu-item-label {
        padding-left: 0;
        padding-right: 0
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-submenu .zb-nav-menu-item:last-of-type .zb-nav-menu-item-label {
        border-bottom: none
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav {
        background-color: #fff
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-group,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-is-open,.zb-champion-standard-theme .zb-masthead .zb-nav-submenu-is-open {
        display: block
    }
}

.zb-champion-standard-theme .zb-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 300;
    overflow: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background: rgba(0,0,0,.6)
}

.zb-champion-standard-theme .zb-overlay.zb-overlay-is-full-screen {
    position: fixed
}

.zb-champion-standard-theme .zb-table {
    border-spacing: 0;
    border-collapse: collapse;
    border-bottom: 0;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-table .zb-table-button {
    display: inline-block;
    position: relative;
    padding-right: 4px;
    margin-right: 16px
}

.zb-champion-standard-theme .zb-table .zb-table-button>.zb-icon {
    margin: 0;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(100%,-50%);
    transform: translate(100%,-50%);
    right: 0
}

.zb-champion-standard-theme .zb-table th {
    font-weight: 400;
    text-align: left
}

.zb-champion-standard-theme .zb-table tfoot th,.zb-champion-standard-theme .zb-table thead th {
    height: auto;
    padding: 12px 16px;
    background: #f9f9fc;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-table tfoot th:first-child,.zb-champion-standard-theme .zb-table thead th:first-child {
    border-left: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-table tfoot th:last-child,.zb-champion-standard-theme .zb-table thead th:last-child {
    border-right: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-table thead tr.zb-table-columns th {
    color: #333;
    font-size: .875rem;
    line-height: 1.2
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table thead tr.zb-table-columns th {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-table td {
    padding: 14px 16px;
    color: #333;
    line-height: 1.25;
    background: #fff;
    border: 1px solid #cccfd0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table td {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-table tbody th {
    padding: 14px 16px;
    color: #333;
    line-height: 1.25;
    background: #fff;
    border: 1px solid #cccfd0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table tbody th {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-table tbody tr:hover td {
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table tbody tr:hover td {
        background:#fff
    }
}

.zb-champion-standard-theme .zb-table tbody tr:hover th {
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table tbody tr:hover th {
        background:#fff
    }
}

.zb-champion-standard-theme .zb-table .zb-pager {
    line-height: 44px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table .zb-pager {
        line-height:40px
    }
}

.zb-champion-standard-theme .zb-table .zb-pager-form:last-child,.zb-champion-standard-theme .zb-table .zb-pager-text:last-child {
    float: right
}

.zb-champion-standard-theme .zb-table .zb-table-accordion-toggle {
    display: none;
    position: absolute;
    right: 30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    padding: 6px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table .zb-table-accordion-toggle {
        right:20px
    }
}

.zb-champion-standard-theme .zb-table .zb-checkbox-input+label {
    font-weight: 400
}

[dir=ltr] .zb-champion-standard-theme .zb-table caption {
    text-align: left
}

[dir=rtl] .zb-champion-standard-theme .zb-table caption {
    text-align: right
}

.zb-champion-standard-theme .zb-table caption {
    text-align: left;
    padding: 0 0 14px 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table caption {
        padding:0 0 20px
    }
}

.zb-champion-standard-theme .zb-checked-table td .zb-checkbox:first-child,.zb-champion-standard-theme .zb-checked-table th .zb-checkbox:first-child {
    margin-right: 8px
}

.zb-champion-standard-theme .zb-nested-table td {
    padding: 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table td {
        padding:4px
    }
}

.zb-champion-standard-theme .zb-nested-table thead th {
    padding: 12px 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table thead th {
        padding:8px
    }
}

.zb-champion-standard-theme .zb-nested-table thead th:last-child {
    padding-right: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table thead th:last-child {
        padding-right:16px
    }
}

.zb-champion-standard-theme .zb-nested-table td:last-child {
    padding-right: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table td:last-child {
        padding-right:16px
    }
}

.zb-champion-standard-theme .zb-nested-table thead th:first-child {
    padding-left: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table thead th:first-child {
        padding-left:16px
    }
}

.zb-champion-standard-theme .zb-nested-table td:first-child {
    padding-left: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nested-table td:first-child {
        padding-left:16px
    }
}

.zb-champion-standard-theme .zb-table-toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    line-height: 44px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table-toolbar {
        line-height:40px
    }
}

.zb-champion-standard-theme .zb-table-toolbar .zb-table-button {
    margin-right: 28px
}

.zb-champion-standard-theme .zb-accordion .zb-table,.zb-champion-standard-theme .zb-card .zb-table {
    width: 100%
}

.zb-champion-standard-theme .zb-card-body .zb-flushed-table {
    width: calc(100% + 60px);
    margin-left: -30px;
    margin-right: -30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-body .zb-flushed-table {
        margin-right:-20px;
        margin-left: -20px;
        width: calc(100% + 40px)
    }
}

.zb-champion-standard-theme .zb-flushed-table td {
    padding: 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table td {
        padding:4px
    }
}

.zb-champion-standard-theme .zb-flushed-table thead th {
    padding: 12px 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table thead th {
        padding:12px 8px
    }
}

.zb-champion-standard-theme .zb-flushed-table thead th:last-child {
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table thead th:last-child {
        padding-right:20px
    }
}

.zb-champion-standard-theme .zb-flushed-table td:last-child {
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table td:last-child {
        padding-right:20px
    }
}

.zb-champion-standard-theme .zb-flushed-table thead th:first-child {
    padding-left: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table thead th:first-child {
        padding-left:20px
    }
}

.zb-champion-standard-theme .zb-flushed-table td:first-child {
    padding-left: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flushed-table td:first-child {
        padding-left:20px
    }
}

.zb-champion-standard-theme .zb-flushed-table tbody td:first-child,.zb-champion-standard-theme .zb-flushed-table tbody th:first-child,.zb-champion-standard-theme .zb-flushed-table td:first-child,.zb-champion-standard-theme .zb-flushed-table tfoot th:first-child,.zb-champion-standard-theme .zb-flushed-table th:first-child,.zb-champion-standard-theme .zb-flushed-table thead,.zb-champion-standard-theme .zb-flushed-table thead th:first-child {
    border-left: none
}

.zb-champion-standard-theme .zb-flushed-table tbody td:last-child,.zb-champion-standard-theme .zb-flushed-table tbody th:last-child,.zb-champion-standard-theme .zb-flushed-table td:last-child,.zb-champion-standard-theme .zb-flushed-table tfoot th:last-child,.zb-champion-standard-theme .zb-flushed-table th:last-child,.zb-champion-standard-theme .zb-flushed-table thead th:last-child {
    border-right: none
}

.zb-champion-standard-theme .zb-flushed-table thead tr:first-child,.zb-champion-standard-theme .zb-flushed-table thead tr:first-child th {
    border-top: none
}

.zb-champion-standard-theme .zb-flushed-table tbody:last-child tr:last-child td,.zb-champion-standard-theme .zb-flushed-table tfoot:last-child tr:last-child th {
    border-bottom: none
}

.zb-champion-standard-theme .zb-complex-table tbody td,.zb-champion-standard-theme .zb-complex-table tbody th {
    border-left: none;
    border-right: none
}

.zb-champion-standard-theme .zb-complex-table thead th {
    padding: 8px;
    height: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table thead th {
        height:auto;
        padding: 8px
    }
}

.zb-champion-standard-theme .zb-complex-table tfoot th {
    padding: 8px;
    height: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table tfoot th {
        height:auto;
        padding: 8px
    }
}

.zb-champion-standard-theme .zb-complex-table td {
    padding: 12px;
    height: 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table td {
        height:auto;
        padding: 12px 20px
    }
}

.zb-champion-standard-theme .zb-complex-table tbody th {
    padding: 12px;
    height: 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table tbody th {
        height:auto;
        padding: 12px 20px
    }
}

.zb-champion-standard-theme .zb-complex-table th.zb-table-toolbar-container {
    height: 44px;
    padding: 10px 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table th.zb-table-toolbar-container {
        padding:10px 12px;
        height: auto
    }
}

.zb-champion-standard-theme .zb-complex-table td:first-child {
    padding-left: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table td:first-child {
        padding-left:20px
    }
}

.zb-champion-standard-theme .zb-complex-table th:first-child {
    padding-left: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table th:first-child {
        padding-left:20px
    }
}

.zb-champion-standard-theme .zb-complex-table td:last-child {
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table td:last-child {
        padding-right:20px
    }
}

.zb-champion-standard-theme .zb-complex-table th:last-child {
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table th:last-child {
        padding-right:20px
    }
}

.zb-champion-standard-theme .zb-complex-table .zb-select-input-wrapper .zb-icon {
    width: 16px;
    height: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-complex-table .zb-select-input-wrapper .zb-icon {
        height:10px;
        width: 10px
    }
}

.zb-champion-standard-theme .zb-table-toolbar-actions {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.zb-champion-standard-theme .zb-table-toolbar-actions .zb-table-toolbar-select .zb-lookup:hover {
    text-decoration: underline
}

.zb-champion-standard-theme .zb-table-dropdown {
    display: inline-block
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-responsive-table tbody,.zb-champion-standard-theme .zb-responsive-table td,.zb-champion-standard-theme .zb-responsive-table th,.zb-champion-standard-theme .zb-responsive-table thead,.zb-champion-standard-theme .zb-responsive-table tr {
        display:block;
        border: none
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table caption {
            padding:20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table thead tr.zb-table-columns {
        display: none
    }

    .zb-champion-standard-theme .zb-responsive-table tfoot td,.zb-champion-standard-theme .zb-responsive-table tfoot td:first-child,.zb-champion-standard-theme .zb-responsive-table tfoot td:last-child,.zb-champion-standard-theme .zb-responsive-table tfoot th,.zb-champion-standard-theme .zb-responsive-table tfoot th:first-child,.zb-champion-standard-theme .zb-responsive-table tfoot th:last-child,.zb-champion-standard-theme .zb-responsive-table thead td,.zb-champion-standard-theme .zb-responsive-table thead td:first-child,.zb-champion-standard-theme .zb-responsive-table thead td:last-child,.zb-champion-standard-theme .zb-responsive-table thead th,.zb-champion-standard-theme .zb-responsive-table thead th:first-child,.zb-champion-standard-theme .zb-responsive-table thead th:last-child {
        border: none
    }

    .zb-champion-standard-theme .zb-responsive-table tfoot tr:last-child,.zb-champion-standard-theme .zb-responsive-table thead tr:last-child {
        border-bottom: none
    }

    .zb-champion-standard-theme .zb-responsive-table tr {
        border: 1px solid #cccfd0;
        border-bottom: 0
    }

    .zb-champion-standard-theme .zb-responsive-table tr:last-child {
        border-bottom: 1px solid #cccfd0
    }

    .zb-champion-standard-theme .zb-responsive-table tbody td,.zb-champion-standard-theme .zb-responsive-table tbody th {
        padding-top: 0;
        padding-bottom: 0;
        font-weight: 700
    }

    .zb-champion-standard-theme .zb-responsive-table tbody td[data-heading]:before,.zb-champion-standard-theme .zb-responsive-table tbody th[data-heading]:before {
        content: attr(data-heading) ": ";
        color: #333;
        font-weight: 400
    }

    .zb-champion-standard-theme .zb-responsive-table tbody td:first-child,.zb-champion-standard-theme .zb-responsive-table tbody th:first-child {
        padding-top: 20px;
        padding-bottom: 20px
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table tbody th:first-child {
            padding-bottom:20px;
            padding-top: 20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table tbody td:first-child[data-heading]:before,.zb-champion-standard-theme .zb-responsive-table tbody th:first-child[data-heading]:before {
        content: ""
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table tbody td:last-child,.zb-champion-standard-theme .zb-responsive-table tbody th:last-child {
            padding-bottom:20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table tbody th {
        border: none
    }

    .zb-champion-standard-theme .zb-responsive-table .zb-table-accordion-toggle {
        display: inline-block;
        width: 16px;
        height: 16px
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody td,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th {
        display: none
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody td:first-child,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th:first-child {
        display: block;
        padding: 14px 20px;
        position: relative
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th:first-child {
            padding:14px 20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody td:first-child:before,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th:first-child:before {
        content: ""
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody td:first-child+td,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody td:first-child+th,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th:first-child+td,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody th:first-child+th {
            padding-top:20px
        }
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:last-child td:last-child {
            padding-bottom:20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:hover td,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:hover th {
        background: inherit
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:hover td:first-child,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:hover th:first-child {
        background: #fff;
        color: #5e10b1
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table.zb-checked-table tbody tr:hover th:first-child {
            color:#5e10b1;
            background: #fff
        }
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table .zb-table-accordion-open td,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table .zb-table-accordion-open th {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-checked-table .zb-table-accordion-open td:first-child,.zb-champion-standard-theme .zb-responsive-table.zb-checked-table .zb-table-accordion-open th:first-child {
        border-bottom: 1px solid #cccfd0
    }

    .zb-champion-standard-theme .zb-responsive-table.zb-complex-table th.zb-table-toolbar-container,.zb-champion-standard-theme .zb-responsive-table th.zb-table-toolbar-container {
        padding: 0
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-responsive-table.zb-complex-table th.zb-table-toolbar-container .zb-table-toolbar>*,.zb-champion-standard-theme .zb-responsive-table th.zb-table-toolbar-container .zb-table-toolbar>* {
            padding:14px 20px
        }
    }

    .zb-champion-standard-theme .zb-responsive-table .zb-table-toolbar {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        line-height: inherit
    }

    .zb-champion-standard-theme .zb-responsive-table .zb-table-toolbar>* {
        border-top: 1px solid #cccfd0
    }

    .zb-champion-standard-theme .zb-responsive-table .zb-table-toolbar>:first-child {
        border-top: none
    }

    .zb-champion-standard-theme .zb-accordion .zb-responsive-table,.zb-champion-standard-theme .zb-card .zb-responsive-table {
        margin-left: -20px;
        margin-right: -20px;
        width: calc(100% + 40px)
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-accordion .zb-responsive-table {
            width:calc(100% + 40px);
            margin-right: -20px;
            margin-left: -20px
        }
    }

    .zb-champion-standard-theme .zb-accordion .zb-responsive-table tr,.zb-champion-standard-theme .zb-card .zb-responsive-table tr {
        border-top: 1px solid #cccfd0;
        border-left: 0;
        border-right: 0
    }

    .zb-champion-standard-theme .zb-accordion .zb-responsive-table tr:last-child,.zb-champion-standard-theme .zb-card .zb-responsive-table tr:last-child {
        border-bottom: 0
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-accordion .zb-responsive-table:first-child,.zb-champion-standard-theme .zb-card .zb-responsive-table:first-child {
            margin-top:-20px
        }
    }

    .zb-champion-standard-theme .zb-table .zb-pager-form .zb-input {
        min-width: 5em!important
    }

    .zb-champion-standard-theme .zb-table .zb-pager-form .zb-button-secondary {
        padding-left: 11px;
        padding-right: 11px
    }

    .zb-champion-standard-theme .zb-button-with-icon-after.zb-table-button {
        text-indent: -1000px;
        padding: 0 16px;
        margin-right: 0
    }

    .zb-champion-standard-theme .zb-button-with-icon-after.zb-table-button .zb-icon {
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%)
    }

    .zb-champion-standard-theme .zb-table-toolbar .zb-lookup-input {
        min-height: 28px;
        line-height: 28px;
        font-size: .8125rem
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-table-toolbar .zb-lookup-input-wrapper {
            min-height:28px
        }
    }

    .zb-champion-standard-theme .zb-table-toolbar .zb-table-dropdown {
        border: none;
        min-height: auto;
        background: inherit;
        display: inline-block;
        color: #5e10b1
    }

    .zb-champion-standard-theme .zb-table-toolbar .zb-table-dropdown .zb-select-input-wrapper {
        padding: 0 24px 0 0
    }

    .zb-champion-standard-theme .zb-table-toolbar .zb-table-dropdown .zb-select-input-wrapper .zb-icon {
        padding: 0
    }

    .zb-champion-standard-theme .zb-flushed-table tbody td,.zb-champion-standard-theme .zb-flushed-table tbody th,.zb-champion-standard-theme .zb-flushed-table td,.zb-champion-standard-theme .zb-flushed-table tfoot th,.zb-champion-standard-theme .zb-flushed-table th,.zb-champion-standard-theme .zb-flushed-table thead,.zb-champion-standard-theme .zb-flushed-table thead th,.zb-champion-standard-theme .zb-nested-table tbody td,.zb-champion-standard-theme .zb-nested-table tbody th,.zb-champion-standard-theme .zb-nested-table td,.zb-champion-standard-theme .zb-nested-table tfoot th,.zb-champion-standard-theme .zb-nested-table th,.zb-champion-standard-theme .zb-nested-table thead,.zb-champion-standard-theme .zb-nested-table thead th {
        border-left: none;
        border-right: none
    }

    .zb-champion-standard-theme .zb-nested-table tbody td:first-child,.zb-champion-standard-theme .zb-nested-table tbody th:first-child,.zb-champion-standard-theme .zb-nested-table td:first-child,.zb-champion-standard-theme .zb-nested-table tfoot th:first-child,.zb-champion-standard-theme .zb-nested-table th:first-child,.zb-champion-standard-theme .zb-nested-table thead th:first-child {
        border-left: 1px solid #cccfd0
    }

    .zb-champion-standard-theme .zb-nested-table tbody td:last-child,.zb-champion-standard-theme .zb-nested-table tbody th:last-child,.zb-champion-standard-theme .zb-nested-table td:last-child,.zb-champion-standard-theme .zb-nested-table tfoot th:last-child,.zb-champion-standard-theme .zb-nested-table th:last-child,.zb-champion-standard-theme .zb-nested-table thead th:last-child {
        border-right: 1px solid #cccfd0
    }
}

.zb-champion-standard-theme .zb-tag {
    font-size: 14px;
    line-height: 1.15;
    padding: 4px 8px;
    background-color: #5e10b1;
    color: #fff;
    border: none;
    border-radius: 4px;
    margin-right: 8px;
    text-decoration: none;
    display: inline-block
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-tag {
        border-radius:2px
    }
}

.zb-champion-standard-theme .zb-tag:hover {
    background-color: #3c1053;
    color: #fff;
    border: none
}

.zb-champion-standard-theme .zb-tag:focus,.zb-champion-standard-theme .zb-tag[focus-within] {
    outline: 2px solid #5e10b1;
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-tag:focus-within {
    outline: 2px solid #5e10b1;
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-tag.zb-tag-is-focused {
    outline: 2px solid #5e10b1;
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-tag.zb-tag-is-disabled,.zb-champion-standard-theme .zb-tag:disabled {
    background-color: #bf9fe0;
    color: #fff;
    border: none
}

.zb-champion-standard-theme .zb-tag-with-icon-after .zb-icon,.zb-champion-standard-theme .zb-tag-with-icon-after:hover>.zb-icon,.zb-champion-standard-theme .zb-tag-with-icon-before .zb-icon,.zb-champion-standard-theme .zb-tag-with-icon-before:hover>.zb-icon {
    color: inherit;
    vertical-align: bottom
}

.zb-champion-standard-theme .zb-tag-with-icon-after .zb-icon:focus,.zb-champion-standard-theme .zb-tag-with-icon-before .zb-icon:focus {
    outline: 1px solid #fff;
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-tag-with-icon-before .zb-icon {
    margin-right: 8px
}

.zb-champion-standard-theme .zb-tag-with-icon-after .zb-icon {
    margin-left: 8px
}

.zb-accordion {
    margin: 0
}

.zb-champion-standard-theme .zb-accordion {
    border-radius: 16px;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header {
    position: relative;
    z-index: 100;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 14px 66px 14px 30px;
    background-color: #fff;
    color: #333;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom-color: #cccfd0;
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.5
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-header {
        font-size:1.125rem;
        padding: 0 56px 0 20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header.zb-accordion-is-last {
    padding: 0 66px 0 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-header.zb-accordion-is-last {
        padding:0 40px 0 20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header:focus {
    outline: 1px solid #5e10b1;
    border: 1px solid #5e10b1;
    z-index: 101
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header-icon {
    vertical-align: middle;
    margin-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-header-icon {
        margin-right:20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-content {
    display: none;
    margin: 0;
    background-color: #fff;
    color: #646068;
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-open {
    display: block;
    padding: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-is-open {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-open>* {
    margin-top: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-last {
    border-bottom: 1px solid transparent
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-last.zb-accordion-is-active {
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-last.zb-accordion-is-active:focus {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing {
    padding: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing.zb-accordion-is-open {
    display: block;
    border: none
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion {
    border: none
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-header {
    padding: 0 30px 0 52px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-header {
        padding:0 20px 0 40px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-header.zb-accordion-is-last {
    padding: 0 30px 0 52px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-header.zb-accordion-is-last {
        padding:0 20px 0 40px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-content {
    padding: 20px 30px 20px 47px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-no-spacing .zb-accordion .zb-accordion-content {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion {
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header {
    background-color: #fff;
    color: #000;
    font-size: .875rem;
    padding: 0 30px 0 52px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header {
        padding:0 30px 0 40px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header.zb-accordion-is-last {
    padding: 0 30px 0 52px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header.zb-accordion-is-last {
        padding:0 30px 0 40px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-content {
    padding: 20px 30px 20px 47px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-content {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-is-active {
    border-bottom: 1px solid transparent
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-is-active:focus {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-is-active,.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-is-active {
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-content {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion-content {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header-icon {
    width: 20px;
    height: 20px;
    line-height: 20px
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-height: 65px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    line-height: 1.15em
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-header .zb-accordion-header-icon {
    position: absolute;
    right: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header {
    min-height: 50px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header {
        font-size:.8125rem;
        min-height: 40px
    }
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header .zb-accordion-header-icon {
    left: 30px;
    margin-right: 0;
    right: auto;
    width: 12px;
    height: 12px;
    line-height: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header .zb-accordion-header-icon {
        left:20px
    }
}

.zb-champion-standard-theme .zb-carousel {
    position: relative
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-item {
    display: none
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-item.zb-carousel-item-active {
    display: block
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-button {
    width: 48px;
    height: 48px;
    background: #5e10b1;
    padding: 0;
    border-radius: 10px;
    border: none;
    cursor: pointer
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-button:hover {
    background: #3c1053
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-button:focus {
    background: #5e10b1;
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-button.zb-carousel-button-disabled {
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-button .zb-icon {
    color: #fff;
    width: 24px;
    height: 24px;
    vertical-align: middle
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators {
    padding: 0 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators button {
    width: 10px;
    height: 10px;
    padding: 0;
    border-radius: 4px;
    background: transparent;
    border: 2px solid #5e10b1;
    cursor: pointer
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators button:hover {
    border: 3px solid #5e10b1
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators button+button {
    margin-left: 14px
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators button.zb-carousel-indicator-active {
    border-color: #3c1053;
    background: #3c1053
}

.zb-champion-standard-theme .zb-carousel .zb-carousel-controls .zb-carousel-indicators button:focus {
    border-color: #5e10b1;
    outline: 1px solid #5e10b1;
    outline-offset: 1px
}

.zb-button-group {
    display: inline-block
}

.zb-button-group-sr-only {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.zb-button-group input:focus+.zb-button-group-item {
    outline-width: 2px;
    outline-style: solid;
    outline-color: Highlight
}

@media (-webkit-min-device-pixel-ratio: 0) {
    .zb-button-group input:focus+.zb-button-group-item {
        outline-color:-webkit-focus-ring-color;
        outline-style: auto
    }
}

.zb-champion-standard-theme .zb-button-group {
    padding: 0;
    background-color: #fff;
    border: 1px solid #c9c6c6;
    border-radius: 20px
}

.zb-champion-standard-theme .zb-button-group-item {
    padding: 12px 15px;
    line-height: 1;
    border: 0;
    background: transparent;
    border-radius: 0;
    font-family: RNHouseSans,Arial,sans-serif;
    font-size: 1rem;
    color: #5e10b1;
    display: inline-block;
    cursor: pointer
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-group-item {
        font-size:.8125rem;
        padding: 8px
    }
}

.zb-champion-standard-theme .zb-button-group-item+.zb-button-group-item {
    margin-left: 0
}

.zb-champion-standard-theme .zb-button-group-item:hover {
    color: #3c1053;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-button-group-item-is-selected {
    color: #fff;
    background: #5e10b1
}

.zb-champion-standard-theme .zb-button-group-item-is-selected:hover {
    color: #fff;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-button-group-item:first-of-type {
    padding-left: 30px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px
}

.zb-champion-standard-theme .zb-button-group-item:last-of-type {
    padding-right: 30px;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px
}

.zb-champion-standard-theme .zb-button-group-item-is-selected {
    margin: -1px 0;
    border-top: 1px solid #5e10b1;
    border-bottom: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-alternate {
    background: transparent;
    border: 1px solid #fff
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-alternate .zb-button-group-item {
    background: transparent;
    color: #fff
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-alternate .zb-button-group-item-is-selected {
    border: 1px solid #fff;
    background: #fff;
    color: #5a287d
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple {
    background: transparent;
    border: 1px solid transparent
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple .zb-button-group-item {
    background: transparent;
    color: #fff;
    border-radius: 20px;
    margin-left: 20px;
    padding: 12px 20px;
    border: 1px solid #fff
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple .zb-button-group-item:hover {
    background: hsla(0,0%,100%,.15)
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple .zb-button-group-item:first-of-type {
    margin-left: 0
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple .zb-button-group-item-is-selected {
    border: 1px solid transparent;
    background: #fff;
    color: #5a287d
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-multiple .zb-button-group-item-is-selected:hover {
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-button-group,.zb-champion-standard-theme .zb-button-group-item {
        display:-webkit-box;
        display: -ms-flexbox;
        display: flex
    }

    .zb-champion-standard-theme .zb-button-group-item {
        -webkit-box-flex: 1;
        -ms-flex: 1 1 0px;
        flex: 1 1 0;
        text-align: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: distribute;
        justify-content: space-around;
        line-height: 1.25
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-button-group-item:first-of-type {
            padding-left:8px
        }
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-button-group-item:last-of-type {
            padding-right:8px
        }
    }
}

.zb-date-picker-trigger {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    min-width: 140px
}

.zb-date-picker-input {
    border: none;
    padding: 0;
    background: none;
    outline: none;
    margin: 0
}

.zb-date-picker-dropdown {
    position: absolute;
    z-index: 310;
    background: #fff;
    border-radius: 2px;
    display: none
}

.zb-date-picker-dropdown-is-shown {
    display: block
}

.zb-day-calendar-container,.zb-month-picker-container,.zb-year-picker-container {
    display: none
}

.zb-day-calendar-container-is-shown,.zb-month-picker-container-is-shown,.zb-year-picker-container-is-shown {
    display: block
}

.zb-date-picker.pika-single {
    background-color: transparent;
    border: none;
    z-index: 320
}

.zb-date-picker .pika-lendar {
    margin: 0;
    overflow: hidden
}

.zb-date-picker .pika-next,.zb-date-picker .pika-prev {
    height: 20px
}

.zb-date-picker .pika-label {
    font-size: 1rem;
    font-weight: 700;
    padding: 0 3px
}

.zb-date-picker .pika-table th {
    font-size: 1rem
}

.zb-date-picker .pika-table td {
    padding: 0
}

.zb-date-picker .pika-table tr:first-of-type td {
    padding-top: 11px
}

.zb-date-picker .pika-table tr:last-of-type td {
    padding-bottom: 11px
}

.zb-date-picker .pika-table abbr[title] {
    text-decoration: none
}

.zb-date-picker .pika-button {
    padding: .52rem;
    text-align: center
}

.zb-date-picker .is-selected .pika-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-date-picker-is-mobile {
    width: 100%;
    -webkit-appearance: none
}

.zb-date-picker-is-mobile::-webkit-clear-button,.zb-date-picker-is-mobile::-webkit-inner-spin-button {
    display: none;
    -webkit-appearance: none
}

.zb-date-picker-is-mobile::-webkit-calendar-picker-indicator {
    opacity: 0
}

.zb-date-picker-is-mobile::-webkit-date-and-time-value {
    margin: 0
}

.zb-date-picker-wrapper {
    display: inline-block;
    position: relative
}

.zb-date-picker-is-mobile-wrapper .zb-icon {
    position: absolute;
    pointer-events: none;
    background: transparent
}

.zb-champion-standard-theme.zb-date-picker-dropdown {
    max-width: 304px;
    border: 1px solid #cccfd0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

@media (max-width: 840px) {
    .zb-champion-standard-theme.zb-date-picker-dropdown {
        max-width:280px
    }
}

.zb-champion-standard-theme.zb-date-picker-dropdown.zb-date-picker-dropdown-position-bottom {
    margin-top: 4px
}

.zb-champion-standard-theme.zb-date-picker-dropdown.zb-date-picker-dropdown-position-top {
    margin-top: -4px
}

.zb-champion-standard-theme .zb-date-picker-trigger {
    height: 44px;
    background-color: #fff;
    border: 1px solid #646068;
    padding: 0 12px;
    border-radius: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-trigger {
        height:40px
    }
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-focused {
    padding: 0 12px;
    border: 1px solid #646068;
    outline: none
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-focused .zb-date-picker-input {
    line-height: 42px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-focused .zb-date-picker-input {
        line-height:38px
    }
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-disabled {
    padding: 0 12px;
    border: 1px solid #e0e2e3
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-disabled .zb-date-picker-icon {
    color: #646068
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-disabled .zb-date-picker-input {
    line-height: 42px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-disabled .zb-date-picker-input {
        line-height:38px
    }
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-is-error {
    border: 1px solid #cf223f;
    padding: 0 12px;
    background: #fff;
    outline: none
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-is-error .zb-date-picker-icon {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input {
    text-transform: uppercase;
    font-family: RNHouseSans,Arial,sans-serif;
    width: calc(100% - 30px);
    vertical-align: baseline;
    line-height: 42px;
    height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input {
        line-height:38px
    }
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input::-webkit-input-placeholder {
    text-transform: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input::-moz-placeholder {
    text-transform: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input::-ms-input-placeholder {
    text-transform: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input::placeholder {
    text-transform: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input:-ms-input-placeholder {
    text-transform: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-input::-ms-clear {
    display: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-icon-wrapper {
    outline: none
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-icon {
    height: 100%;
    vertical-align: bottom;
    float: right
}

.zb-champion-standard-theme .zb-date-picker-is-mobile-wrapper .zb-date-picker-icon {
    right: 10px;
    top: 10px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-is-mobile-wrapper .zb-date-picker-icon {
        top:8px
    }
}

.zb-champion-standard-theme .zb-date-picker-is-mobile {
    text-transform: uppercase;
    font-family: RNHouseSans,Arial,sans-serif;
    vertical-align: baseline;
    line-height: 44px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-is-mobile {
        line-height:40px
    }
}

.zb-champion-standard-theme .zb-date-picker-is-mobile::-ms-clear {
    display: none
}

.zb-champion-standard-theme .zb-date-picker.pika-single {
    font-family: RNHouseSans,Arial,sans-serif
}

.zb-champion-standard-theme .zb-date-picker .pika-lendar {
    width: 304px;
    background-color: #f2f2f8
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-lendar {
        width:280px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-label {
    background-color: #f2f2f8;
    color: #5e10b1;
    font-weight: 400;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-date-picker .pika-label:hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-date-picker .pika-title {
    padding: 12px 0;
    height: 24px;
    text-transform: capitalize
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-title {
        padding:10px 0
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-prev {
    background: transparent;
    height: 16px;
    opacity: 1;
    padding-top: 2px;
    margin-left: 13px
}

.zb-champion-standard-theme .zb-date-picker .pika-prev:before {
    content: "";
    display: block;
    height: 16px;
    width: 16px;
    background: none
}

.zb-champion-standard-theme .zb-date-picker .pika-prev.is-disabled:before {
    background: none
}

.zb-champion-standard-theme .zb-date-picker .pika-next {
    background: transparent;
    height: 16px;
    opacity: 1;
    padding-top: 2px;
    margin-right: 9px
}

.zb-champion-standard-theme .zb-date-picker .pika-next:before {
    content: "";
    display: block;
    height: 16px;
    width: 16px;
    background: none
}

.zb-champion-standard-theme .zb-date-picker .pika-next.is-disabled:before {
    background: none
}

.zb-champion-standard-theme .zb-date-picker .pika-table {
    margin-top: 0
}

.zb-champion-standard-theme .zb-date-picker .pika-table th {
    background-color: #f2f2f8;
    color: #5e10b1;
    font-size: 1rem;
    font-weight: 400;
    height: 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table th {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table th:first-of-type {
    padding-left: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table th:first-of-type {
        padding-left:6px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table td:first-of-type {
    padding-left: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table td:first-of-type {
        padding-left:6px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table th:last-of-type {
    padding-right: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table th:last-of-type {
        padding-right:6px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table td:last-of-type {
    padding-right: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table td:last-of-type {
        padding-right:6px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table td {
    padding-top: 0
}

.zb-champion-standard-theme .zb-date-picker .pika-button {
    font-family: RNHouseSans,Arial,sans-serif;
    background-color: #fff;
    text-align: center;
    padding: 0;
    margin: 0;
    border-radius: 0;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-button {
        font-size:.8125rem;
        height: 40px;
        width: 40px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-button:hover {
    background-color: #f2eaf9;
    color: #5e10b1;
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused {
    background-color: #f2eaf9
}

.zb-champion-standard-theme .zb-date-picker .is-today .pika-button {
    color: #5e10b1;
    border: 1px solid #5e10b1;
    opacity: 1;
    font-weight: 400
}

.zb-champion-standard-theme .zb-date-picker .is-today .pika-button:hover {
    background-color: #f2eaf9;
    color: #5e10b1;
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .is-selected .pika-button {
    font-weight: 400;
    border: none
}

.zb-champion-standard-theme .zb-date-picker .is-selected .pika-button:hover {
    background-color: #3c1053;
    border: none
}

.zb-champion-standard-theme .zb-date-picker .is-disabled .pika-button,.zb-champion-standard-theme .zb-date-picker .is-outside-current-month .pika-button {
    color: #bf9fe0;
    opacity: 1
}

.zb-champion-standard-theme .zb-date-picker .is-outside-current-month .pika-button:hover {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-date-picker tbody {
    border-top: 1px solid #cccfd0;
    background-color: #fff
}

.zb-champion-standard-theme .zb-month-picker {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 304px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-picker {
        width:280px
    }
}

.zb-champion-standard-theme .zb-month-picker-header {
    background: #f2f2f8;
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-month-calendar {
    background: #fff
}

.zb-champion-standard-theme .zb-month-calendar-months {
    padding: 8px 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-calendar-months {
        padding:5px 0
    }
}

.zb-champion-standard-theme .zb-month-calendar-month {
    display: inline-block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 40px;
    width: 40px;
    border-radius: 0;
    text-align: center;
    margin: 6px 26px;
    outline: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: #5e10b1;
    vertical-align: bottom;
    text-transform: capitalize
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-calendar-month {
        margin:5px 26px;
        width: 40px;
        height: 40px
    }
}

.zb-champion-standard-theme .zb-month-calendar-month:hover {
    background: #f2eaf9;
    color: #5e10b1;
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-month-calendar-month-is-focused,.zb-champion-standard-theme .zb-month-calendar-month:focus {
    background: #f2eaf9;
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-month-calendar-month-is-selected,.zb-champion-standard-theme .zb-month-calendar-month-is-selected.zb-month-calendar-month-is-focused,.zb-champion-standard-theme .zb-month-calendar-month-is-selected:focus,.zb-champion-standard-theme .zb-month-calendar-month-is-selected:hover {
    font-weight: 400;
    background: #3c1053;
    color: #fff;
    border: none
}

.zb-champion-standard-theme .zb-month-calendar-month-is-selected.zb-month-calendar-month-is-current,.zb-champion-standard-theme .zb-month-calendar-month-is-selected.zb-month-calendar-month-is-focused.zb-month-calendar-month-is-current,.zb-champion-standard-theme .zb-month-calendar-month-is-selected:focus.zb-month-calendar-month-is-current,.zb-champion-standard-theme .zb-month-calendar-month-is-selected:hover.zb-month-calendar-month-is-current {
    border: none
}

.zb-champion-standard-theme .zb-month-calendar-month-is-disabled {
    color: #646068
}

.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:focus,.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:hover {
    cursor: auto;
    background: none
}

.zb-champion-standard-theme .zb-month-calendar-month-is-current {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-date-stepper-button {
    position: absolute;
    background: none;
    border: none;
    padding: 0;
    height: 16px;
    cursor: pointer
}

.zb-champion-standard-theme .zb-date-stepper-button.zb-date-stepper-button-is-disabled {
    cursor: auto
}

.zb-champion-standard-theme .zb-date-stepper-button.zb-date-stepper-button-is-disabled .zb-icon {
    color: #bf9fe0
}

.zb-champion-standard-theme .zb-date-stepper-prev-button {
    left: 13px
}

.zb-champion-standard-theme .zb-date-stepper-next-button {
    right: 13px
}

.zb-champion-standard-theme .zb-date-stepper-label {
    display: table;
    margin: 0 auto;
    font-size: 1rem;
    color: #5e10b1;
    font-weight: 400;
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.zb-champion-standard-theme .zb-month-calendar {
    max-width: 304px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-calendar {
        max-width:280px
    }
}

.zb-champion-standard-theme .zb-date-picker-dropdown {
    top: calc(100% + 4px);
    max-width: 304px;
    border: 1px solid #cccfd0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker-dropdown {
        max-width:280px
    }
}

.zb-champion-standard-theme .zb-date-picker-dropdown.zb-date-picker-dropdown-position-bottom {
    margin-top: 4px
}

.zb-champion-standard-theme .zb-date-picker-dropdown.zb-date-picker-dropdown-position-top {
    margin-top: -4px
}

.zb-champion-standard-theme .zb-date-picker .pika-button {
    width: 40px;
    height: 40px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-button {
        height:30px;
        width: 30px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused,.zb-champion-standard-theme .zb-month-calendar-month-is-focused,.zb-champion-standard-theme .zb-month-calendar-month:focus {
    background: transparent;
    border: 2px solid #5e10b1;
    color: #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused:hover,.zb-champion-standard-theme .zb-month-calendar-month-is-focused:hover,.zb-champion-standard-theme .zb-month-calendar-month:focus:hover {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused.zb-month-calendar-month-is-selected,.zb-champion-standard-theme .zb-month-calendar-month-is-focused.zb-month-calendar-month-is-selected,.zb-champion-standard-theme .zb-month-calendar-month:focus.zb-month-calendar-month-is-selected {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused.zb-month-calendar-month-is-selected.zb-month-calendar-month-is-current,.zb-champion-standard-theme .zb-month-calendar-month-is-focused.zb-month-calendar-month-is-selected.zb-month-calendar-month-is-current,.zb-champion-standard-theme .zb-month-calendar-month:focus.zb-month-calendar-month-is-selected.zb-month-calendar-month-is-current {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .is-selected .pika-button {
    background-color: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-date-picker .is-selected .pika-button:hover {
    color: #fff
}

.zb-champion-standard-theme .zb-date-picker .pika-title {
    line-height: 24px
}

.zb-champion-standard-theme .zb-date-picker .pika-table tr:first-of-type td {
    padding-top: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table tr:first-of-type td {
        padding-top:10px
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table tr:last-of-type td {
    padding-bottom: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-table tr:last-of-type td {
        padding-bottom:10px
    }
}

.zb-champion-standard-theme .zb-month-calendar-month {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-calendar-month {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-date-picker .pika-table th {
    border-bottom: 1px solid #cccfd0;
    color: #646068
}

.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:focus,.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-date-stepper {
    padding: 12px 0 41px;
    position: relative
}

.zb-champion-standard-theme .zb-date-stepper-button {
    top: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-stepper-button {
        top:14px
    }
}

.zb-champion-standard-theme .zb-date-stepper-prev-button {
    left: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-stepper-prev-button {
        left:10px
    }
}

.zb-champion-standard-theme .zb-date-stepper-next-button {
    right: 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-stepper-next-button {
        right:10px
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-button {
        margin:4px
    }
}

.zb-lookup {
    display: inline-block;
    position: relative
}

.zb-lookup.zb-lookup-is-open .zb-dropdown-container {
    display: block
}

.zb-lookup .zb-dropdown-container {
    z-index: 320;
    position: absolute;
    display: none;
    min-width: 100%;
    right: 0
}

.zb-lookup-input {
    border: 0;
    position: relative;
    z-index: 1;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: transparent
}

.zb-lookup-input:focus {
    outline: none
}

.zb-lookup-ghost-renderer-ghost {
    position: absolute;
    z-index: 0;
    overflow: hidden;
    left: 0;
    right: 0
}

.zb-lookup-ghost-renderer-ghost .zb-lookup-ghost-renderer-ghost-text {
    position: relative;
    display: block;
    white-space: nowrap
}

.zb-dropdown-container {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-width: 100px
}

.zb-dropdown-list {
    margin: 0;
    padding: 0;
    overflow: auto
}

.zb-select .zb-lookup-input-wrapper {
    cursor: pointer
}

.zb-combobox .zb-lookup-ghost-renderer {
    width: 100%
}

.zb-combobox .zb-icon {
    z-index: 100
}

.zb-dropdownlist-trigger-wrapper {
    position: absolute;
    z-index: 200;
    display: none;
    width: 100vw;
    top: 0
}

.zb-dropdownlist-trigger-wrapper.zb-dropdownlist-trigger-wrapper-is-shown {
    display: block
}

.zb-dropdownlist-trigger-container {
    position: absolute;
    display: inline-block
}

.zb-select-is-mobile-wrapper {
    position: relative;
    display: inline-block
}

.zb-select-is-mobile-wrapper .zb-select-is-mobile {
    border: none;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.zb-select-is-mobile-wrapper .zb-select-is-mobile::-ms-expand {
    display: none
}

.zb-select-is-mobile-wrapper .zb-icon {
    position: absolute;
    top: 50%;
    margin-top: -8px;
    pointer-events: none
}

.zb-tag-lookup .zb-lookup-ghost-renderer {
    width: auto;
    position: relative
}

.zb-tag-lookup .zb-tag-lookup-tags {
    outline: none
}

.zb-tag-lookup .zb-lookup-input-wrapper .zb-tag .zb-icon {
    position: static;
    padding: 0;
    -webkit-transform: none;
    transform: none
}

.zb-champion-standard-theme .zb-lookup-input-wrapper {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-height: 42px;
    border: 1px solid #646068;
    padding: 0;
    border-radius: 8px;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-lookup-input-wrapper {
        min-height:40px
    }
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-focused {
    border: 1px solid #646068;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-error,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-error {
    border: 1px solid #cf223f;
    background: #fff;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-error .zb-icon,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-error .zb-icon {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-disabled,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-disabled {
    border: 1px solid #e0e2e3;
    color: #999;
    -webkit-box-shadow: none;
    box-shadow: none;
    cursor: default
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-disabled .zb-lookup-input,.zb-champion-standard-theme .zb-lookup-input-wrapper-is-disabled .zb-select-input-wrapper .zb-lookup-input,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-disabled .zb-lookup-input,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-disabled .zb-select-input-wrapper .zb-lookup-input {
    color: #999;
    cursor: default
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-disabled .zb-lookup-ghost-renderer-ghost,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-disabled .zb-lookup-ghost-renderer-ghost {
    color: #999
}

.zb-champion-standard-theme .zb-lookup-ghost-renderer {
    display: inline-block;
    line-height: inherit;
    padding: 0
}

.zb-champion-standard-theme .zb-lookup-input {
    display: inline-block;
    min-height: 42px;
    line-height: 42px;
    padding: 0 12px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-lookup-input {
        font-size:.8125rem;
        line-height: 40px;
        min-height: 40px
    }
}

.zb-champion-standard-theme .zb-lookup-input::-webkit-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-lookup-input::-moz-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-lookup-input:-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-lookup-input::-ms-input-placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-lookup-input::placeholder {
    color: #747077
}

.zb-champion-standard-theme .zb-lookup-input::-ms-clear {
    display: none
}

.zb-champion-standard-theme .zb-lookup-ghost-renderer-ghost {
    color: #646068;
    line-height: 42px;
    padding: 0 12px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-lookup-ghost-renderer-ghost {
        font-size:.8125rem;
        line-height: 40px
    }
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-ghost-renderer-ghost,.zb-champion-standard-theme .zb-combobox .zb-lookup-input {
    padding: 0 40px 0 12px
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-ghost-renderer {
    margin: 0
}

.zb-champion-standard-theme .zb-select-input-wrapper {
    padding: 0 40px 0 12px;
    outline: 0;
    cursor: pointer
}

.zb-champion-standard-theme .zb-select-input-wrapper .zb-lookup-input {
    color: #000;
    cursor: pointer;
    padding: 0
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper .zb-icon,.zb-champion-standard-theme .zb-select .zb-select-input-wrapper .zb-icon {
    cursor: pointer;
    position: absolute;
    top: 50%;
    right: 4px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper .zb-icon.zb-icon-search {
    pointer-events: none;
    cursor: default
}

.zb-champion-standard-theme .zb-select-is-mobile-wrapper {
    border: 1px solid #646068;
    border-radius: 8px;
    background: #fff
}

.zb-champion-standard-theme .zb-select-is-mobile-wrapper .zb-select-is-mobile {
    min-height: 42px;
    padding: 0 40px 0 12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-select-is-mobile-wrapper .zb-select-is-mobile {
        min-height:40px
    }
}

.zb-champion-standard-theme .zb-select-is-mobile-wrapper .zb-icon {
    right: 8px
}

.zb-champion-standard-theme .zb-dropdown-container {
    padding: 0;
    margin: 4px 0 0;
    border: 1px solid #cccfd0;
    line-height: 1.5;
    background: #fff;
    border-radius: 0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-dropdown-container:focus {
    outline: none
}

.zb-champion-standard-theme .zb-dropdown-container .zb-select-dropdown-list {
    margin: 0;
    padding-left: 0
}

.zb-champion-standard-theme .zb-dropdown-arrow {
    position: absolute;
    height: 12px;
    width: 12px;
    top: -12px
}

.zb-champion-standard-theme .zb-dropdown-arrow:after,.zb-champion-standard-theme .zb-dropdown-arrow:before {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    display: block;
    height: 0;
    width: 0;
    z-index: 1;
    border: 0 solid transparent
}

.zb-champion-standard-theme .zb-dropdown-arrow:after {
    border-bottom-color: #fff
}

.zb-champion-standard-theme .zb-dropdown-arrow:before {
    border-bottom-color: #cccfd0;
    margin-top: -1px
}

.zb-champion-standard-theme .zb-dropdown-header {
    position: relative;
    z-index: 2;
    padding: 10px 12px;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #f2f2f8;
    border-bottom: 1px solid #cccfd0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-header {
        padding:10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-header:empty {
    padding: 0;
    border-bottom: none
}

.zb-champion-standard-theme .zb-dropdown-footer {
    padding: 10px 12px;
    border-top: 1px solid #cccfd0;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-footer {
        padding:10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-list {
    max-height: 420px
}

.zb-champion-standard-theme .zb-dropdown-list-group-items {
    margin: 0;
    padding: 0
}

.zb-champion-standard-theme .zb-dropdown-list-group-label {
    display: block;
    padding: 10px 12px;
    background: #f2f2f8;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid #cccfd0;
    color: #000;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-list-group-label {
        font-size:.8125rem;
        padding: 10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-list-group-label-is-sticky {
    position: relative;
    z-index: 1;
    border-top-color: transparent
}

.zb-champion-standard-theme .zb-dropdown-list-group:first-child .zb-dropdown-list-group-label {
    border-top-color: transparent
}

.zb-champion-standard-theme .zb-dropdown-list-item {
    position: relative;
    padding: 10px 12px;
    background: #fff;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid transparent;
    color: #333;
    cursor: pointer;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-list-item {
        font-size:.8125rem;
        padding: 10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-list-item:first-child {
    border-top-color: transparent
}

.zb-champion-standard-theme .zb-dropdown-list-item:last-child {
    border-bottom-color: transparent
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused {
    color: #333;
    padding: 10px 12px;
    background: #f2eaf9;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid transparent
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-list-item-is-focused {
        padding:10px 12px
    }
}

.zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:focus {
    color: #333;
    padding: 10px 12px;
    background: #f2eaf9;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid transparent
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:focus {
        padding:10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused+.zb-dropdown-list-item {
    border-top: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-disabled {
    color: #646068;
    padding: 10px 12px;
    border-top: 1px solid #cccfd0;
    border-bottom: 1px solid transparent
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-list-item-is-disabled {
        padding:10px 12px
    }
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused .zb-dropdown-list-item-text-dimmed,.zb-champion-standard-theme .zb-dropdown-list-item-text-dimmed {
    color: #646068
}

.zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected .zb-dropdown-list-item-text-dimmed {
    color: #fff
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused .zb-dropdown-list-item-text-highlight,.zb-champion-standard-theme .zb-dropdown-list-item-text-highlight {
    color: #000
}

.zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected .zb-dropdown-list-item-text-highlight {
    color: #fff
}

.zb-champion-standard-theme .zb-select-dropdown-list .zb-icon-core-dropdown-item-selected {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 8px;
    color: #fff
}

.zb-champion-standard-theme .zb-select-dropdown-list .zb-dropdown-list-item-is-focused .zb-icon-core-dropdown-item-selected {
    color: #fff
}

.zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected {
    color: #fff;
    padding: 10px 30px 10px 12px;
    background: #3c1053
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected {
        padding:10px 30px 10px 12px
    }
}

.zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:checked {
    color: #fff;
    padding: 10px 30px 10px 12px;
    background: #3c1053
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:checked {
        padding:10px 30px 10px 12px
    }
}

.zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected.zb-dropdown-list-item-is-disabled,.zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:checked.zb-dropdown-list-item-is-disabled {
    color: #646068
}

.zb-champion-standard-theme .zb-select-dropdown-list-item-is-selected.zb-dropdown-list-item-is-focused,.zb-champion-standard-theme .zb-select-is-mobile .zb-dropdown-list-item:checked.zb-dropdown-list-item-is-focused {
    color: #fff;
    background: #3c1053
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list .zb-dropdown-list-group-label {
    cursor: pointer
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-is-selected .zb-multiselect-dropdown-list-group-label-icon .zb-icon {
    visibility: visible
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected {
    color: #000;
    background: #fff
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected.zb-dropdown-list-item-is-disabled {
    color: #646068
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected.zb-dropdown-list-item-is-focused {
    color: #333;
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected .zb-multiselect-dropdown-list-item-icon .zb-icon {
    visibility: visible
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-disabled .zb-multiselect-dropdown-list-item-icon {
    opacity: .4
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
    display: inline-block;
    border: 2px solid #646068;
    border-radius: 8px;
    padding: 1px;
    background: #fff;
    line-height: 22px;
    margin-right: 6px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
        line-height:14px;
        border-radius: 4px
    }
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon {
    display: inline-block;
    border: 2px solid #646068;
    border-radius: 8px;
    padding: 1px;
    background: #fff;
    line-height: 22px;
    margin-right: 6px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon {
        line-height:14px;
        border-radius: 4px
    }
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon .zb-icon {
    display: inline-block;
    visibility: hidden;
    line-height: 22px;
    height: 22px;
    width: 22px;
    vertical-align: top
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon .zb-icon {
        width:14px;
        height: 14px;
        line-height: 14px
    }
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon .zb-icon {
    display: inline-block;
    visibility: hidden;
    line-height: 22px;
    height: 22px;
    width: 22px;
    vertical-align: top
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon .zb-icon {
        width:14px;
        height: 14px;
        line-height: 14px
    }
}

.zb-champion-standard-theme .zb-tag-lookup .zb-tag-lookup-tags {
    margin-left: 8px
}

.zb-champion-standard-theme .zb-dropdown-header {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-header {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-dropdown-footer {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-dropdown-footer {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper .zb-icon,.zb-champion-standard-theme .zb-select .zb-select-input-wrapper .zb-icon {
    padding: 8px
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
    padding: 0;
    margin-right: 8px;
    width: 22px;
    height: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
        height:14px;
        width: 14px
    }
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon {
    padding: 0;
    margin-right: 8px;
    width: 22px;
    height: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon {
        height:14px;
        width: 14px
    }
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected .zb-multiselect-dropdown-list-group-label-icon,.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected .zb-multiselect-dropdown-list-item-icon {
    background: #5e10b1;
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-is-selected .zb-icon-core-dropdown-item-selected {
    color: #fff
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-disabled {
    cursor: default
}

.zb-champion-standard-theme .ddl-trigger .zb-icon {
    width: 20px;
    height: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .ddl-trigger .zb-icon {
        height:14px;
        width: 14px
    }
}

.zb-champion-standard-theme .zb-lookup-input-wrapper-is-disabled .zb-icon,.zb-champion-standard-theme .zb-select-is-mobile.zb-select-mobile-is-disabled .zb-icon {
    color: #bf9fe0
}

.zb-champion-standard-theme .zb-tag-lookup .zb-lookup-input-wrapper .zb-tag .zb-icon {
    position: static;
    padding: 0;
    -webkit-transform: none;
    transform: none
}

.zb-champion-standard-theme .zb-tag-lookup .zb-lookup-input-wrapper-is-error .zb-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-tag-lookup .zb-tag {
    margin: 8px 0 0 8px
}

.zb-champion-standard-theme .zb-tag-lookup .zb-tag:first-of-type {
    margin-left: 0
}

.zb-champion-standard-theme .zb-tag-lookup .zb-tag-lookup-remove-dropdown {
    margin-left: 8px
}

.zb-flyout {
    position: relative;
    display: none;
    z-index: 300
}

.zb-flyout.zb-flyout-is-shown {
    display: inline-block
}

.zb-flyout-wrapper {
    position: absolute;
    width: 100%
}

.zb-flyout-container {
    position: absolute
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-has-close-button .zb-flyout-body {
    padding-right: 45px
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-direction-bottom {
    padding-bottom: 12px
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-direction-top {
    padding-top: 12px
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-direction-left {
    padding-left: 12px
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-direction-right {
    padding-right: 12px
}

.zb-champion-standard-theme .zb-flyout-body {
    padding: 15px;
    background: #fff;
    border: 2px solid #cccfd0;
    border-radius: 8px;
    max-width: 400px;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-flyout-arrow {
    position: absolute
}

.zb-champion-standard-theme .zb-flyout-arrow:after,.zb-champion-standard-theme .zb-flyout-arrow:before {
    position: absolute;
    content: "";
    display: block;
    height: 0;
    width: 0;
    border: 8px solid transparent
}

.zb-champion-standard-theme .zb-flyout-direction-top .zb-flyout-arrow {
    top: -4px;
    margin-left: -8px
}

.zb-champion-standard-theme .zb-flyout-direction-top .zb-flyout-arrow:after {
    margin-top: 2px;
    border-bottom-color: #fff
}

.zb-champion-standard-theme .zb-flyout-direction-top .zb-flyout-arrow:before {
    border-bottom-color: #cccfd0
}

.zb-champion-standard-theme .zb-flyout-direction-bottom .zb-flyout-arrow {
    bottom: 12px;
    margin-left: -8px
}

.zb-champion-standard-theme .zb-flyout-direction-bottom .zb-flyout-arrow:after {
    margin-top: -2px;
    border-top-color: #fff
}

.zb-champion-standard-theme .zb-flyout-direction-bottom .zb-flyout-arrow:before {
    border-top-color: #cccfd0
}

.zb-champion-standard-theme .zb-flyout-direction-right .zb-flyout-arrow {
    right: 12px;
    margin-top: -8px
}

.zb-champion-standard-theme .zb-flyout-direction-right .zb-flyout-arrow:after {
    margin-left: -2px;
    border-left-color: #fff
}

.zb-champion-standard-theme .zb-flyout-direction-right .zb-flyout-arrow:before {
    border-left-color: #cccfd0
}

.zb-champion-standard-theme .zb-flyout-direction-left .zb-flyout-arrow {
    left: -4px;
    margin-top: -8px
}

.zb-champion-standard-theme .zb-flyout-direction-left .zb-flyout-arrow:after {
    margin-left: 2px;
    border-right-color: #fff
}

.zb-champion-standard-theme .zb-flyout-direction-left .zb-flyout-arrow:before {
    border-right-color: #cccfd0
}

.zb-champion-standard-theme .zb-flyout-is-error .zb-flyout-body {
    border: 2px solid #cf223f;
    background: #fff
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-bottom .zb-flyout-arrow:after {
    border-top-color: #fff
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-top .zb-flyout-arrow:after {
    border-bottom-color: #fff
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-left .zb-flyout-arrow:after {
    border-right-color: #fff
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-right .zb-flyout-arrow:after {
    border-left-color: #fff
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-bottom .zb-flyout-arrow:before {
    border-top-color: #cf223f
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-top .zb-flyout-arrow:before {
    border-bottom-color: #cf223f
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-left .zb-flyout-arrow:before {
    border-right-color: #cf223f
}

.zb-champion-standard-theme .zb-flyout-is-error.zb-flyout-direction-right .zb-flyout-arrow:before {
    border-left-color: #cf223f
}

.zb-champion-standard-theme .zb-flyout.zb-flyout-has-close-button .zb-flyout-body {
    padding-right: 39px
}

.zb-champion-standard-theme .zb-flyout-body {
    font-size: 1rem;
    color: #333
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-flyout-body {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-flyout-close-button {
    float: right;
    margin-top: 15px;
    margin-right: 15px
}

.zb-champion-standard-theme .zb-flyout-close-button .zb-icon-core-flyout-close {
    color: #5e10b1;
    width: 14px;
    height: 14px
}

.zb-champion-standard-theme .zb-flyout-container {
    max-width: 50vw
}

.zb-icon {
    display: inline-block;
    height: 16px;
    width: 16px;
    line-height: 16px;
    fill: currentColor
}

svg.zb-icon use {
    pointer-events: none
}

.zb-icon-xsmall {
    height: 16px;
    width: 16px;
    line-height: 16px
}

.zb-icon-small {
    height: 20px;
    width: 20px;
    line-height: 20px
}

.zb-icon-medium {
    height: 24px;
    width: 24px;
    line-height: 24px
}

.zb-icon-large {
    height: 32px;
    width: 32px;
    line-height: 32px
}

.zb-icon-xlarge {
    height: 48px;
    width: 48px;
    line-height: 48px
}

.zb-champion-standard-theme .zb-icon {
    color: #5e10b1
}

.zb-checkbox {
    display: inline-block;
    outline: none
}

.zb-checkbox-tick {
    visibility: hidden
}

.zb-checkbox-input,.zb-radio-button-input {
    position: absolute;
    opacity: 0;
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    width: 1px
}

.zb-checkbox-is-checked .zb-checkbox-tick {
    visibility: visible
}

.zb-radio-button {
    display: inline-block;
    border-radius: 100%;
    outline: none
}

.zb-radio-button-circle {
    display: inline-block;
    visibility: hidden;
    border-radius: 100%;
    vertical-align: bottom
}

.zb-radio-button-is-checked .zb-radio-button-circle {
    visibility: visible
}

.zb-file-input-label {
    position: relative;
    display: inline-block;
    overflow: hidden
}

.zb-file-input-is-hover {
    cursor: -webkit-grabbing;
    cursor: grabbing
}

.zb-file-input-is-invalid {
    cursor: no-drop
}

.zb-file-input-input {
    border: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
    opacity: 0;
    position: absolute;
    left: -200px;
    top: 0;
    right: 0;
    bottom: 0;
    cursor: pointer
}

.zb-file-input-input:disabled {
    cursor: default
}

.zb-file-input-files {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.zb-file-input-files dd,.zb-file-input-files dt {
    margin: 0;
    padding: 0
}

.zb-file-input-files dt {
    -webkit-box-flex: 1;
    -ms-flex: 1 1;
    flex: 1 1;
    min-width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 1em
}

.zb-file-input-files dd {
    white-space: nowrap
}

.zb-champion-standard-theme .zb-checkbox {
    border: 2px solid #646068;
    border-radius: 8px;
    padding: 2px;
    background: #fff;
    cursor: pointer;
    line-height: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox {
        line-height:16px;
        padding: 0;
        border-radius: 4px
    }
}

.zb-champion-standard-theme .zb-checkbox-is-checked {
    background: #5e10b1;
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-checkbox-is-checked .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-checkbox-is-focused {
    border: 2px solid #5e10b1;
    padding: 2px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-is-focused {
        padding:0
    }
}

.zb-champion-standard-theme .zb-checkbox-is-disabled {
    border: 2px solid #ebecec;
    cursor: default
}

.zb-champion-standard-theme .zb-checkbox-is-disabled.zb-checkbox-is-checked {
    border: 2px solid #bf9fe0;
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-checkbox-is-disabled .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-checkbox-is-error {
    border: 2px solid #cf223f;
    padding: 2px;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-is-error {
        padding:0
    }
}

.zb-champion-standard-theme .zb-checkbox-is-error.zb-checkbox-is-focused {
    border: 2px solid #5e10b1;
    padding: 2px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-is-error.zb-checkbox-is-focused {
        padding:0
    }
}

.zb-champion-standard-theme .zb-checkbox-is-error .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-checkbox-tick {
    display: inline-block;
    line-height: 24px;
    height: 24px;
    width: 24px;
    vertical-align: top;
    color: #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-tick {
        width:16px;
        height: 16px;
        line-height: 16px
    }
}

.zb-champion-standard-theme .zb-checkbox-tick .zb-icon {
    height: 24px;
    width: 24px;
    line-height: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-tick .zb-icon {
        line-height:16px;
        width: 16px;
        height: 16px
    }
}

.zb-champion-standard-theme .zb-checkbox-labelled {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    padding: 6px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.zb-champion-standard-theme .zb-checkbox-labelled .zb-checkbox {
    margin-right: 8px;
    -ms-flex-item-align: start;
    align-self: flex-start
}

.zb-champion-standard-theme .zb-checkbox-radio-button-labelled-is-disabled .zb-radio-button-label {
    color: #999
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-focused,.zb-champion-standard-theme .zb-checkbox-labelled-is-focused.zb-checkbox-labelled-is-error {
    border: 2px solid #5e10b1;
    padding: 4px
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error {
    border: 2px solid #cf223f;
    padding: 4px
}

.zb-champion-standard-theme .zb-radio-button {
    border: 2px solid #646068;
    padding: 5px;
    line-height: 16px;
    background: #fff;
    cursor: pointer
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button {
        line-height:10px;
        padding: 3px
    }
}

.zb-champion-standard-theme .zb-radio-button-circle {
    height: 16px;
    width: 16px;
    background: #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-circle {
        width:10px;
        height: 10px
    }
}

.zb-champion-standard-theme .zb-radio-button-is-checked {
    border: 2px solid #5e10b1;
    padding: 5px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-checked {
        padding:3px;
        border: 2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-is-checked .zb-radio-button-circle {
    visibility: visible
}

.zb-champion-standard-theme .zb-radio-button-is-focused {
    border: 2px solid #5e10b1;
    padding: 5px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-focused {
        padding:3px;
        border: 2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-is-disabled {
    cursor: default;
    border: 2px solid #c1bfc3
}

.zb-champion-standard-theme .zb-radio-button-is-disabled.zb-radio-button-is-checked {
    border: 2px solid #bf9fe0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-disabled.zb-radio-button-is-checked {
        border:2px solid #8e58c8
    }
}

.zb-champion-standard-theme .zb-radio-button-is-disabled .zb-radio-button-circle {
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-radio-button-is-error {
    padding: 5px;
    border: 2px solid #cf223f;
    background: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-error {
        padding:3px
    }
}

.zb-champion-standard-theme .zb-radio-button-is-error.zb-radio-button-is-focused {
    border: 2px solid #5e10b1;
    padding: 5px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-error.zb-radio-button-is-focused {
        padding:3px;
        border: 2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-is-error .zb-radio-button-circle {
    background: #cf223f
}

.zb-champion-standard-theme .zb-radio-button-labelled {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    padding: 6px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.zb-champion-standard-theme .zb-radio-button-labelled .zb-radio-button {
    -ms-flex-item-align: start;
    align-self: flex-start
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-disabled .zb-radio-button-label {
    color: #999
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-focused,.zb-champion-standard-theme .zb-radio-button-labelled-is-focused.zb-radio-button-labelled-is-error {
    border: 2px solid #5e10b1;
    padding: 4px
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error {
    border: 2px solid #cf223f;
    padding: 4px
}

.zb-champion-standard-theme .zb-file-input,.zb-champion-standard-theme .zb-file-input-is-disabled {
    background: transparent;
    padding: 1rem
}

.zb-champion-standard-theme .zb-file-input-is-error,.zb-champion-standard-theme .zb-file-input-is-hover {
    padding: 1rem
}

.zb-champion-standard-theme .zb-file-input-is-focused {
    background: transparent;
    padding: 1rem
}

.zb-champion-standard-theme .zb-file-input-is-hover {
    background-color: #efe7f7
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-focused .zb-checkbox {
    border: 2px solid #646068
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-focused .zb-checkbox.zb-checkbox-is-checked {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error .zb-checkbox {
    border: 2px solid #646068;
    background: #fff
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error .zb-checkbox.zb-checkbox-is-checked {
    background: #5e10b1;
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-focused .zb-radio-button.zb-radio-button-is-checked {
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-labelled-is-focused .zb-radio-button.zb-radio-button-is-checked {
        border:2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button {
    border: 2px solid #646068;
    background: #fff
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button.zb-radio-button-is-selected {
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button.zb-radio-button-is-selected {
        border:2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button .zb-radio-button-circle {
    background: #5e10b1
}

.zb-champion-standard-theme .zb-file-input-is-focused .zb-file-input-label {
    outline: none;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-checkbox {
    margin-right: 4px
}

.zb-champion-standard-theme .zb-checkbox-labelled {
    margin-right: 8px
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-disabled .zb-checkbox-label {
    color: #999
}

.zb-champion-standard-theme .zb-checkbox-input+label {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-input+label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-radio-button {
    margin-right: 4px
}

.zb-champion-standard-theme .zb-radio-button-labelled .zb-radio-button {
    margin-right: 8px
}

.zb-champion-standard-theme .zb-radio-button-input+label {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-input+label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-file-input,.zb-champion-standard-theme .zb-file-input-is-disabled,.zb-champion-standard-theme .zb-file-input-is-focused {
    border: none;
    background: inherit;
    padding: 0
}

.zb-champion-standard-theme .zb-file-input-is-error {
    background: #fae9ec;
    border: none;
    padding: 0
}

.zb-champion-standard-theme .zb-file-input-is-hover {
    background: #efe7f7;
    border: none;
    padding: 0
}

.zb-champion-standard-theme .zb-file-input-is-invalid {
    background-color: #fdf6e6
}

.zb-champion-standard-theme .zb-file-field {
    line-height: 1.25
}

.zb-champion-standard-theme .zb-file-field-inner {
    background: #fff;
    color: #646068;
    min-height: 76px;
    padding: 16px 16px 16px 72px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    position: relative;
    width: 100%;
    border: solid #333;
    border-width: 2px 2px 0;
    border-radius: 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-file-field-inner {
        padding-left:50px;
        min-height: 56px
    }
}

.zb-champion-standard-theme .zb-file-field-inner:first-of-type {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.zb-champion-standard-theme .zb-file-field-inner:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-width: 2px
}

.zb-champion-standard-theme .zb-file-field-inner:first-of-type:last-of-type {
    border-radius: 8px
}

.zb-champion-standard-theme .zb-file-field-inner.zb-file-input-is-error {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-file-field-inner.zb-file-input-is-hover {
    border-style: dashed
}

.zb-champion-standard-theme .zb-file-field-inner.zb-file-input-is-hover .zb-file-field-body-hover {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.zb-champion-standard-theme .zb-file-field-body {
    width: 100%;
    position: relative
}

.zb-champion-standard-theme .zb-file-field-body-hover {
    position: absolute;
    background: #fff;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    text-align: center;
    pointer-events: none;
    padding: 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.zb-champion-standard-theme .zb-file-field-icon {
    color: #333;
    display: inline-block;
    position: absolute;
    left: 24px;
    top: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-file-field-icon {
        top:20px;
        left: 20px
    }
}

.zb-champion-standard-theme .zb-file-field-icon.zb-file-field-icon-error {
    left: 32px;
    top: 34px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-file-field-icon.zb-file-field-icon-error {
        top:30px;
        left: 28px
    }
}

.zb-modal-placeholder {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.zb-modal-wrapper {
    position: absolute
}

.zb-champion-standard-theme .zb-modal-background {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(0,0,0,.6);
    pointer-events: all
}

.zb-champion-standard-theme .zb-modal {
    z-index: 200;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    display: none;
    position: fixed;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    pointer-events: none;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.zb-champion-standard-theme .zb-modal-card,.zb-champion-standard-theme .zb-modal.zb-modal-is-active {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.zb-champion-standard-theme .zb-modal-card {
    height: 100vh;
    width: 100%;
    max-width: 100%;
    pointer-events: all;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #fff
}

.zb-champion-standard-theme .zb-modal-close {
    position: absolute;
    top: 26px;
    right: 26px;
    border: none;
    background: none;
    padding: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-close {
        right:16px;
        top: 16px
    }
}

.zb-champion-standard-theme .zb-modal-close .zb-icon {
    display: block
}

.zb-champion-standard-theme .zb-modal-card-head .zb-modal-close {
    top: calc(50% - 12px);
    right: 26px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-head .zb-modal-close {
        right:16px
    }
}

.zb-champion-standard-theme .zb-modal-card-foot,.zb-champion-standard-theme .zb-modal-card-head {
    position: relative
}

.zb-champion-standard-theme .zb-modal-card-head {
    padding: 0 54px 0 30px;
    border-bottom: 1px solid #dfdddd;
    background-color: #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-head {
        padding:0 40px 0 20px
    }
}

.zb-champion-standard-theme .zb-modal-card-title {
    color: #333;
    font-size: 1.5rem;
    line-height: 1;
    padding: 0;
    margin: 0;
    font-weight: 400
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-title {
        font-size:1.125rem
    }
}

.zb-champion-standard-theme .zb-modal-card-foot {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: transparent;
    padding: 0 30px 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-foot {
        padding:0 20px 20px
    }
}

.zb-champion-standard-theme .zb-modal-card-foot.zb-modal-is-sectioned {
    background-color: #fff;
    border-top: 1px solid #dfdddd;
    padding: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-foot.zb-modal-is-sectioned {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-modal-card-foot-actions {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-button,.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-modal-action {
    margin: 8px
}

.zb-champion-standard-theme .zb-modal-body {
    overflow: auto;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.zb-champion-standard-theme .zb-modal-card-body {
    padding: 30px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-body {
        font-size:.8125rem;
        padding: 20px
    }
}

@media screen and (min-width: 599px) {
    .zb-champion-standard-theme .zb-modal {
        padding:20px
    }

    .zb-champion-standard-theme .zb-modal-card {
        height: auto;
        max-height: calc(100vh - 41px);
        -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
        box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
        border-radius: 16px
    }

    .zb-champion-standard-theme .zb-modal-card-head {
        border-top-left-radius: 16px;
        border-top-right-radius: 16px
    }

    .zb-champion-standard-theme .zb-modal-card-foot {
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px
    }

    .zb-champion-standard-theme .zb-modal-card-foot-actions {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .zb-champion-standard-theme .zb-modal-card,.zb-champion-standard-theme .zb-modal-content {
        width: 40rem
    }
}

.zb-champion-standard-theme .zb-modal-close {
    padding: 4px
}

.zb-champion-standard-theme .zb-modal-card-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-height: 64px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    line-height: 1.15em
}

.zb-champion-standard-theme .zb-modal-card-foot-actions {
    margin: -8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-foot-actions {
        margin:-5px
    }
}

.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-button {
    margin: 8px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-foot-actions .zb-button {
        font-size:.8125rem;
        margin: 5px
    }
}

.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-modal-action {
    margin: 8px;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-card-foot-actions .zb-modal-action {
        font-size:.8125rem;
        margin: 5px
    }
}

.zb-champion-standard-theme .zb-modal-centered .zb-modal-body,.zb-champion-standard-theme .zb-modal-centered .zb-modal-card-title {
    text-align: center
}

.zb-champion-standard-theme .zb-modal-centered .zb-modal-card-foot-actions {
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.zb-champion-standard-theme .zb-modal-without-header .zb-modal-card {
    padding: 20px 0;
    text-align: center
}

.zb-champion-standard-theme .zb-modal-without-header .zb-modal-close {
    top: 26px;
    right: 26px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal-without-header .zb-modal-close {
        right:16px;
        top: 16px
    }
}

.zb-champion-standard-theme .zb-modal-without-header .zb-modal-card-head {
    padding: 0;
    border-bottom: none
}

.zb-champion-standard-theme .zb-modal-without-header .zb-modal-card-head .zb-modal-close {
    top: 10px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-modal {
        padding:20px
    }

    .zb-champion-standard-theme .zb-modal-card {
        height: auto;
        max-height: 80vh
    }

    .zb-champion-standard-theme .zb-modal-card-foot-actions {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .zb-champion-standard-theme .zb-modal-card-foot-actions .zb-button {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 auto;
        flex: 1 0 auto
    }
}

.zb-nav {
    display: block
}

.zb-nav>ul {
    list-style: none;
    margin: 0;
    padding: 0
}

.zb-nav-item {
    display: inline-block;
    position: relative
}

.zb-nav-item-label {
    cursor: pointer
}

.zb-nav-menu {
    position: fixed;
    display: none;
    z-index: 100
}

.zb-nav-menu.zb-nav-menu-is-open {
    display: table
}

.zb-nav-menu-group {
    display: table-cell
}

.zb-nav-menu-group-label {
    display: inline-block
}

ul.zb-nav-menu-group-list {
    list-style: none;
    margin: 0;
    padding: 0
}

.zb-nav-menu-item,.zb-nav-menu-item-label {
    display: block
}

.zb-nav-submenu {
    position: fixed;
    display: none;
    z-index: 101
}

.zb-nav-submenu.zb-nav-submenu-is-open {
    display: table
}

.zb-champion-standard-theme .zb-nav {
    background: transparent;
    line-height: 1.5
}

.zb-champion-standard-theme .zb-nav-item,.zb-champion-standard-theme .zb-nav-item:hover,.zb-champion-standard-theme .zb-nav-menu {
    background: #fff
}

.zb-champion-standard-theme .zb-nav-item:hover .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-open {
    background: #fff
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-has-rightDivider {
    border-right: 1px solid #7e40c1
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-selected {
    background: #fff
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label {
    color: #3c1053
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected {
    background: #fff
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-item-label {
    display: inline-block;
    padding: 16px 8px;
    font-size: 1rem;
    color: #5e10b1;
    line-height: 1rem;
    text-decoration: none
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-item-label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-item-label:hover {
    text-decoration: none
}

.zb-champion-standard-theme .zb-nav-item-label>.zb-icon {
    vertical-align: bottom
}

.zb-champion-standard-theme .zb-nav-item-label>.zb-nav-item-right-icon {
    vertical-align: bottom;
    margin-left: 8px
}

.zb-champion-standard-theme .zb-nav-menu {
    padding: 12px 0;
    -webkit-box-shadow: 0 5px 7px -1px hsla(0,0%,50.2%,.6);
    box-shadow: 0 5px 7px -1px hsla(0,0%,50.2%,.6)
}

.zb-champion-standard-theme .zb-nav-menu-group {
    padding: 0
}

.zb-champion-standard-theme .zb-nav-menu-item:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-nav-menu-item-is-selected,.zb-champion-standard-theme .zb-nav-menu-item-is-selected:hover {
    background: #3c1053
}

.zb-champion-standard-theme .zb-nav-menu-item-is-selected .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-nav-menu-item-is-selected:hover .zb-nav-menu-item-label {
    color: #fff
}

.zb-champion-standard-theme .zb-nav-menu-group-label {
    font-size: 1.25rem;
    color: #333;
    padding: 8px 24px;
    text-align: left
}

.zb-champion-standard-theme .zb-nav-menu-item-label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    cursor: pointer;
    padding: 12px 24px;
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-nav-menu-item-open-icon {
    -ms-flex-item-align: center;
    align-self: center;
    margin-left: 24px;
    margin-right: -8px
}

.zb-champion-standard-theme .zb-nav-submenu {
    padding: 0;
    background: #fff;
    -webkit-box-shadow: 0 1px 7px -1px hsla(0,0%,50.2%,.6);
    box-shadow: 0 1px 7px -1px hsla(0,0%,50.2%,.6)
}

.zb-champion-standard-theme .zb-nav-item {
    -webkit-transition: background-color .2s;
    transition: background-color .2s
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label:after,.zb-champion-standard-theme .zb-nav-item:hover .zb-nav-item-label:after {
    content: "";
    position: absolute;
    bottom: 7px;
    left: 12px;
    right: 12px;
    height: 4px;
    background: #3c1053
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label:after {
    display: none
}

.zb-champion-standard-theme .zb-nav a:focus,.zb-champion-standard-theme .zb-nav span.zb-nav-item-label:focus,.zb-champion-standard-theme .zb-nav span.zb-nav-menu-item-label:focus {
    outline: 2px solid #5e10b1;
    outline-offset: -2px
}

.zb-champion-standard-theme .zb-nav-item-label:hover {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-item-label .zb-icon {
    color: inherit
}

.zb-champion-standard-theme .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical.zb-nav-item-is-selected .zb-nav-item-label:after,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label:after,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item:hover .zb-nav-item-label:after,.zb-champion-standard-theme .zb-nav-vertical:hover .zb-nav-item-label:after {
    display: none
}

.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label {
    padding: 11px 15px
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label {
    font-weight: 700;
    color: #3c1053;
    padding: 11px 15px
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label:before {
    content: "";
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right: none;
    border-left: 6px solid #3c1053;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px);
    display: inline-block
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item:hover .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label {
    border-left: 3px solid #5e10b1;
    padding: 11px 15px 11px 12px
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label {
    background: #f6f3f9
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-is-selected .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-is-selected:hover .zb-nav-menu-item-label {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label {
    -webkit-transition: background-color .2s;
    transition: background-color .2s
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item:hover {
    background: inherit
}

.zb-champion-standard-theme .zb-nav-vertical span.zb-nav-menu-item-label:hover {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-is-open .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected .zb-nav-item-label {
    background-color: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] {
    background: #f6f3f9
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu {
    border-left: none
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu .zb-nav-menu-item-label {
    padding: 11px 15px
}

.zb-champion-standard-theme .zb-nav-item {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-item {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-menu-item-open-icon {
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label {
    display: block
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label {
    line-height: inherit
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label>.zb-nav-item-right-icon {
    margin-top: 3px
}

.zb-champion-standard-theme .zb-nav-vertical span.zb-nav-item-label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    cursor: pointer;
    color: #5e10b1;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-right-icon {
    display: inline-block
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label {
    display: block
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] .zb-nav-menu-item-open-icon {
    -webkit-transform: scaleY(-1);
    transform: scaleY(-1)
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-is-open .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] {
    font-weight: 700
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true]:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label {
    position: relative;
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label:hover {
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-is-selected .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-is-selected:hover .zb-nav-menu-item-label {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-is-open .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open.zb-nav-item-is-selected .zb-nav-item-label .zb-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label .zb-icon {
    color: inherit
}

.zb-notification {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.zb-notification,.zb-notification-inner {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative
}

.zb-notification-inner {
    width: 100%
}

.zb-notification-body {
    -ms-flex-item-align: center;
    align-self: center;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.zb-notification-title {
    display: block;
    margin: 0
}

.zb-notification-icon {
    display: inline-block;
    position: absolute
}

.zb-champion-standard-theme .zb-notification {
    line-height: 1.25;
    color: #646068
}

.zb-champion-standard-theme .zb-notification-small {
    min-height: 56px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small {
        min-height:56px
    }
}

.zb-champion-standard-theme .zb-notification-small .zb-notification-inner {
    min-height: 56px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small .zb-notification-inner {
        padding-left:56px;
        min-height: 56px
    }
}

.zb-champion-standard-theme .zb-notification-small .zb-notification-icon {
    top: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small .zb-notification-icon {
        top:20px
    }
}

.zb-champion-standard-theme .zb-notification-small .zb-notification-icon .zb-icon {
    width: 20px;
    height: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small .zb-notification-icon .zb-icon {
        height:20px;
        width: 20px
    }
}

.zb-champion-standard-theme .zb-notification-medium {
    min-height: 76px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium {
        min-height:56px
    }
}

.zb-champion-standard-theme .zb-notification-medium .zb-notification-inner {
    min-height: 76px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium .zb-notification-inner {
        padding-left:56px;
        min-height: 56px
    }
}

.zb-champion-standard-theme .zb-notification-medium .zb-notification-icon {
    top: 24px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium .zb-notification-icon {
        top:20px
    }
}

.zb-champion-standard-theme .zb-notification-medium .zb-notification-icon .zb-icon {
    width: 32px;
    height: 32px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium .zb-notification-icon .zb-icon {
        height:20px;
        width: 20px
    }
}

.zb-champion-standard-theme .zb-notification-large {
    min-height: 92px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large {
        min-height:56px
    }
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-inner {
    min-height: 92px;
    padding: 16px 16px 16px 86px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-inner {
        padding-left:56px;
        min-height: 56px
    }
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-icon {
    top: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-icon {
        top:20px
    }
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-icon .zb-icon {
    width: 40px;
    height: 40px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-icon .zb-icon {
        height:20px;
        width: 20px
    }
}

.zb-champion-standard-theme .zb-notification-title {
    margin-top: -5px;
    font-size: 1.25rem;
    font-weight: 400;
    color: #333
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-title {
        font-weight:700;
        font-size: .8125rem
    }
}

.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-error .zb-notification-inner {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-info .zb-notification-inner {
    border: 2px solid #333
}

.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-warning .zb-notification-inner {
    border: 2px solid #fcb900
}

.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-success .zb-notification-inner {
    border: 2px solid #429448
}

.zb-champion-standard-theme .zb-notification-has-background {
    background: #fff
}

.zb-champion-standard-theme .zb-notification-has-status-background {
    color: #646068
}

.zb-champion-standard-theme .zb-notification-has-status-background.zb-notification-status-error .zb-notification-inner {
    background: #fae9ec
}

.zb-champion-standard-theme .zb-notification-has-status-background.zb-notification-status-info .zb-notification-inner {
    background: #f5f5f5
}

.zb-champion-standard-theme .zb-notification-has-status-background.zb-notification-status-warning .zb-notification-inner {
    background: #fff8e6
}

.zb-champion-standard-theme .zb-notification-has-status-background.zb-notification-status-success .zb-notification-inner {
    background: #ecf4ed
}

.zb-champion-standard-theme .zb-notification-arrow,.zb-champion-standard-theme .zb-notification-arrow:after {
    position: absolute;
    height: 0;
    width: 0;
    border: 10px solid transparent
}

.zb-champion-standard-theme .zb-notification-arrow:after {
    content: "";
    top: -10px;
    left: -10px
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-error .zb-notification-arrow {
    border-color: #cf223f
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-error .zb-notification-arrow:after {
    border-color: #fae9ec
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-info .zb-notification-arrow {
    border-color: #333
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-info .zb-notification-arrow:after {
    border-color: #f5f5f5
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-warning .zb-notification-arrow {
    border-color: #fcb900
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-warning .zb-notification-arrow:after {
    border-color: #fff8e6
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-success .zb-notification-arrow {
    border-color: #429448
}

.zb-champion-standard-theme .zb-notification-has-arrow.zb-notification-status-success .zb-notification-arrow:after {
    border-color: #ecf4ed
}

.zb-champion-standard-theme .zb-notification-has-arrow-top .zb-notification-arrow {
    bottom: 100%;
    left: 0
}

.zb-champion-standard-theme .zb-notification-has-arrow-top .zb-notification-arrow:after {
    margin-top: 2px
}

.zb-champion-standard-theme .zb-notification-has-arrow-top .zb-notification-arrow,.zb-champion-standard-theme .zb-notification-has-arrow-top .zb-notification-arrow:after {
    border-top-color: transparent!important;
    border-right-color: transparent!important;
    border-left-color: transparent!important
}

.zb-champion-standard-theme .zb-notification-has-arrow-right .zb-notification-arrow {
    top: 0;
    left: 100%
}

.zb-champion-standard-theme .zb-notification-has-arrow-right .zb-notification-arrow:after {
    margin-left: -2px
}

.zb-champion-standard-theme .zb-notification-has-arrow-right .zb-notification-arrow,.zb-champion-standard-theme .zb-notification-has-arrow-right .zb-notification-arrow:after {
    border-top-color: transparent!important;
    border-bottom-color: transparent!important;
    border-right-color: transparent!important
}

.zb-champion-standard-theme .zb-notification-has-arrow-bottom .zb-notification-arrow {
    top: 100%;
    left: 0
}

.zb-champion-standard-theme .zb-notification-has-arrow-bottom .zb-notification-arrow:after {
    margin-top: -2px
}

.zb-champion-standard-theme .zb-notification-has-arrow-bottom .zb-notification-arrow,.zb-champion-standard-theme .zb-notification-has-arrow-bottom .zb-notification-arrow:after {
    border-bottom-color: transparent!important;
    border-left-color: transparent!important;
    border-right-color: transparent!important
}

.zb-champion-standard-theme .zb-notification-has-arrow-left .zb-notification-arrow {
    top: 0;
    right: 100%
}

.zb-champion-standard-theme .zb-notification-has-arrow-left .zb-notification-arrow:after {
    margin-left: 2px
}

.zb-champion-standard-theme .zb-notification-has-arrow-left .zb-notification-arrow,.zb-champion-standard-theme .zb-notification-has-arrow-left .zb-notification-arrow:after {
    border-top-color: transparent!important;
    border-bottom-color: transparent!important;
    border-left-color: transparent!important
}

.zb-champion-standard-theme .zb-notification.zb-notification-large {
    padding-bottom: 8px
}

.zb-champion-standard-theme .zb-notification.zb-notification-large .zb-notification-inner {
    min-height: 84px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification.zb-notification-large .zb-notification-inner {
        min-height:48px
    }
}

.zb-champion-standard-theme .zb-notification.zb-notification-large .zb-notification-inner:after {
    position: absolute;
    content: "";
    left: -2px;
    right: -2px;
    bottom: -18px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 8px solid transparent
}

.zb-champion-standard-theme .zb-notification.zb-notification-large.zb-notification-status-info .zb-notification-inner:after {
    border-top: 8px solid #666
}

.zb-champion-standard-theme .zb-notification.zb-notification-large.zb-notification-status-success .zb-notification-inner:after {
    border-top: 8px solid #429448
}

.zb-champion-standard-theme .zb-notification.zb-notification-large.zb-notification-status-warning .zb-notification-inner:after {
    border-top: 8px solid #e6a000
}

.zb-champion-standard-theme .zb-notification.zb-notification-large.zb-notification-status-error .zb-notification-inner:after {
    border-top: 8px solid #e60303
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification {
    margin-left: -30px;
    margin-right: -30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification {
        margin-right:-20px;
        margin-left: -20px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-small .zb-notification-inner {
    padding-left: 66px;
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-small .zb-notification-inner {
        padding-right:20px;
        padding-left: 50px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-medium .zb-notification-inner {
    padding-left: 78px;
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-medium .zb-notification-inner {
        padding-right:20px;
        padding-left: 50px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-large .zb-notification-inner {
    padding-left: 86px;
    padding-right: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges.zb-notification-large .zb-notification-inner {
        padding-right:20px;
        padding-left: 50px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges .zb-notification-icon {
    left: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges .zb-notification-icon {
        left:20px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-inside-card {
    max-width: 670px
}

.zb-champion-standard-theme .zb-notification-small .zb-notification-inner {
    padding: 16px 16px 16px 56px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small .zb-notification-inner {
        padding-left:50px
    }
}

.zb-champion-standard-theme .zb-notification-small .zb-notification-icon {
    left: 20px;
    top: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-small .zb-notification-icon {
        left:20px
    }
}

.zb-champion-standard-theme .zb-notification-medium .zb-notification-inner {
    padding: 16px 16px 16px 72px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium .zb-notification-inner {
        padding-left:50px
    }
}

.zb-champion-standard-theme .zb-notification-medium .zb-notification-icon {
    left: 24px;
    top: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-medium .zb-notification-icon {
        left:20px
    }
}

.zb-champion-standard-theme .zb-notification-large {
    color: #646068
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-inner {
    padding: 16px 30px 16px 86px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-inner {
        padding-right:20px;
        padding-left: 50px
    }
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-icon {
    left: 30px;
    top: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-icon {
        left:20px
    }
}

.zb-champion-standard-theme .zb-notification-large .zb-notification-title {
    font-size: 1.5rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-notification-large .zb-notification-title {
        font-size:.8125rem
    }
}

.zb-pager {
    display: block
}

.zb-pager .zb-pager-ellipsis,.zb-pager .zb-pager-navigation,.zb-pager ul {
    display: inline-block
}

.zb-pager ul {
    margin: 0;
    padding: 0
}

.zb-pager li {
    list-style: none;
    display: inline-block
}

.zb-pager .zb-pager-sr-only {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.zb-pager .zb-pager-form .zb-input {
    min-width: 120px;
    margin-right: 8px;
    margin-left: 16px
}

.zb-pager .zb-pager-form .zb-button {
    min-width: 24px
}

.zb-champion-standard-theme .zb-pager-navigation {
    cursor: pointer;
    padding: 0 2px
}

.zb-champion-standard-theme .zb-pager-ellipsis {
    padding: 0 2px
}

.zb-champion-standard-theme .zb-pager-navigation-is-disabled,.zb-champion-standard-theme .zb-pager-navigation-is-disabled:hover {
    cursor: default;
    color: #dfdddd
}

.zb-champion-standard-theme .zb-pager-navigation-is-disabled .zb-icon,.zb-champion-standard-theme .zb-pager-navigation-is-disabled:hover .zb-icon {
    color: #dfdddd
}

.zb-champion-standard-theme .zb-pager-navigation-is-active,.zb-champion-standard-theme .zb-pager-navigation-is-active:hover {
    color: #000;
    font-weight: 400;
    -webkit-text-decoration: none;
    text-decoration: none
}

.zb-champion-standard-theme .zb-pager-next,.zb-champion-standard-theme .zb-pager-previous {
    vertical-align: middle
}

.zb-champion-standard-theme .zb-pager {
    font-size: 1rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-pager {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-pager .zb-icon {
    width: 16px;
    height: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-pager .zb-icon {
        height:13px;
        width: 13px
    }
}

.zb-champion-standard-theme .zb-pager>.zb-pager-navigation:first-of-type {
    padding-left: 0
}

.zb-champion-standard-theme .zb-pager>.zb-pager-navigation:last-of-type {
    padding-right: 0
}

.zb-champion-standard-theme .zb-pager>.zb-pager-navigation:last-of-type+.zb-pager-text {
    margin-left: 8px
}

.zb-champion-standard-theme .zb-pager-ellipsis,.zb-champion-standard-theme .zb-pager-navigation {
    min-width: 1.5em;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center
}

.zb-champion-standard-theme .zb-progress-indicator .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-steps .zb-step {
    background: #cccfd0
}

.zb-champion-standard-theme .zb-steps .zb-step .zb-step-inner {
    border-radius: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-steps .zb-step .zb-step-inner {
        border-radius:8px
    }
}

.zb-champion-standard-theme .zb-steps .zb-step .zb-step-index {
    color: #646068
}

.zb-champion-standard-theme .zb-steps .zb-step .zb-step-description {
    color: #646068;
    font-size: .8125rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-steps .zb-step .zb-step-description {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-past,.zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-past .zb-step-inner {
    background: #3c1053
}

.zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-inner {
    background: #3c1053;
    border-radius: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-inner {
        border-radius:8px
    }
}

.zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-inner:after {
    border-top: none
}

.zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-index {
    color: #fff;
    font-size: 1.25rem
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-index {
        font-size:.8125rem
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-step-description {
        border:0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        width: 1px
    }

    .zb-champion-standard-theme .zb-progress-indicator .zb-icon {
        width: 12px;
        height: 12px;
        line-height: 12px
    }

    .zb-champion-standard-theme .zb-steps {
        counter-reset: zb-step;
        position: relative
    }

    .zb-champion-standard-theme .zb-steps:after {
        content: counter(zb-step);
        position: absolute;
        font-size: .8125rem;
        top: calc(31px + 1.25rem);
        right: 24px;
        color: #646068;
        speak: none
    }

    .zb-champion-standard-theme .zb-steps .zb-step {
        counter-increment: zb-step
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-steps .zb-step .zb-step-index {
            font-size:.8125rem
        }
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-description {
        text-align: left;
        left: 24px;
        right: 36px;
        -webkit-transform: translateY(1.25rem);
        transform: translateY(1.25rem);
        width: auto;
        height: auto;
        clip: auto
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-description:after {
        color: #646068;
        content: counter(zb-step) " of ";
        float: right;
        speak: none
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-inner:after {
        left: auto;
        display: block;
        width: 32px
    }

    .zb-champion-standard-theme .zb-steps .zb-step,.zb-champion-standard-theme .zb-steps .zb-step .zb-step-inner {
        position: static
    }
}

.zb-progress-indicator {
    margin-bottom: 52px
}

@media (max-width: 840px) {
    .zb-progress-indicator {
        margin-bottom:32px
    }
}

.zb-progress-indicator .zb-icon {
    vertical-align: top
}

.zb-steps {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 52px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 24px;
    margin: 0
}

@media (max-width: 840px) {
    .zb-steps {
        height:32px
    }
}

.zb-steps .zb-step {
    height: 4px;
    position: relative;
    width: auto;
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

@media (max-width: 840px) {
    .zb-steps .zb-step {
        height:1px
    }
}

.zb-steps .zb-step.zb-step-is-past .zb-step-inner {
    border: 0
}

.zb-steps .zb-step.zb-step-is-past .zb-step-inner .zb-step-description {
    color: #646068
}

.zb-steps .zb-step:last-of-type {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none
}

.zb-steps .zb-step .zb-step-inner {
    white-space: nowrap;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-width: 40px;
    max-width: 100%;
    height: 40px;
    position: relative;
    background: #fff;
    -ms-flex-item-align: center;
    align-self: center;
    border: 1px solid #aaa;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

@media (max-width: 840px) {
    .zb-steps .zb-step .zb-step-inner {
        height:24px;
        min-width: 24px
    }
}

.zb-steps .zb-step .zb-step-index {
    line-height: 1
}

.zb-steps .zb-step .zb-step-description {
    position: absolute;
    left: 50%;
    top: 52px;
    -webkit-transform: translate(-50%,8px);
    transform: translate(-50%,8px)
}

@media (max-width: 840px) {
    .zb-steps .zb-step .zb-step-description {
        top:32px
    }
}

.zb-steps .zb-step.zb-step-is-selected .zb-step-inner {
    min-height: 52px;
    max-height: 52px;
    min-width: 52px;
    color: #fff;
    border: 0
}

@media (max-width: 840px) {
    .zb-steps .zb-step.zb-step-is-selected .zb-step-inner {
        min-width:32px;
        max-height: 32px;
        min-height: 32px
    }
}

.zb-steps .zb-step.zb-step-is-selected .zb-step-inner .zb-step-description {
    color: #333
}

.zb-steps .zb-step.zb-step-is-selected .zb-step-inner:after {
    position: absolute;
    content: "";
    bottom: -8px;
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 4px solid transparent;
    left: 0
}

.zb-steps .zb-step.zb-step-is-selected .zb-step-description {
    -webkit-transform: translate(-50%,16px);
    transform: translate(-50%,16px)
}

.zb-steps .zb-step-metadata-info {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-step-description {
        border:0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        width: 1px
    }

    .zb-champion-standard-theme .zb-progress-indicator .zb-icon {
        width: 12px;
        height: 12px;
        line-height: 12px
    }

    .zb-champion-standard-theme .zb-steps {
        counter-reset: zb-step;
        position: relative
    }

    .zb-champion-standard-theme .zb-steps:after {
        content: counter(zb-step);
        position: absolute;
        font-size: .8125rem;
        top: calc(31px + 1.25rem);
        right: 24px;
        color: #646068;
        speak: none
    }

    .zb-champion-standard-theme .zb-steps .zb-step {
        counter-increment: zb-step
    }

    @media (max-width: 840px) {
        .zb-champion-standard-theme .zb-steps .zb-step .zb-step-index {
            font-size:.8125rem
        }
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-description {
        text-align: left;
        left: 24px;
        right: 36px;
        -webkit-transform: translateY(1.25rem);
        transform: translateY(1.25rem);
        width: auto;
        height: auto;
        clip: auto
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-description:after {
        color: #646068;
        content: counter(zb-step) " of ";
        float: right;
        speak: none
    }

    .zb-champion-standard-theme .zb-steps .zb-step.zb-step-is-selected .zb-step-inner:after {
        left: auto;
        display: block;
        width: 32px
    }

    .zb-champion-standard-theme .zb-steps .zb-step,.zb-champion-standard-theme .zb-steps .zb-step .zb-step-inner {
        position: static
    }
}

.zb-champion-standard-theme .zb-progress-indicator {
    padding-bottom: 52px;
    margin-bottom: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-progress-indicator {
        padding-bottom:32px
    }
}

.zb-sidepanel {
    overflow: hidden;
    z-index: 110
}

.zb-sidepanel-inner {
    -webkit-animation-name: zbSlideInRight;
    animation-name: zbSlideInRight;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.zb-sidepanel-header:not(:first-child),.zb-sidepanel-header:nth-child(2n)+.zb-sidepanel-body.zb-sidepanel-body-should-animate {
    -webkit-animation-name: zbSlideBottomTop1;
    animation-name: zbSlideBottomTop1;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.zb-sidepanel-header:nth-child(odd)+.zb-sidepanel-body.zb-sidepanel-body-should-animate {
    -webkit-animation-name: zbSlideBottomTop2;
    animation-name: zbSlideBottomTop2;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes zbSlideInRight {
    0% {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        -webkit-transform: translate3d(1e-8%,0,0);
        transform: translate3d(1e-8%,0,0)
    }
}

@keyframes zbSlideInRight {
    0% {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        -webkit-transform: translate3d(1e-8%,0,0);
        transform: translate3d(1e-8%,0,0)
    }
}

@-webkit-keyframes zbSlideBottomTop1 {
    0% {
        -webkit-transform: translate3d(0,-50px,0);
        transform: translate3d(0,-50px,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes zbSlideBottomTop1 {
    0% {
        -webkit-transform: translate3d(0,-50px,0);
        transform: translate3d(0,-50px,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes zbSlideBottomTop2 {
    0% {
        -webkit-transform: translate3d(0,-50px,0);
        transform: translate3d(0,-50px,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes zbSlideBottomTop2 {
    0% {
        -webkit-transform: translate3d(0,-50px,0);
        transform: translate3d(0,-50px,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.zb-champion-standard-theme .zb-sidepanel {
    top: 0;
    bottom: 0;
    right: 0;
    width: 600px;
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 2px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel {
        width:calc(100% - 40px)
    }
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner {
    min-height: 100%;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-shadow: -2px 3px 0 rgba(0,0,0,.1);
    box-shadow: -2px 3px 0 rgba(0,0,0,.1);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    background: #fff
}

.zb-champion-standard-theme .zb-sidepanel-header {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 7px 30px;
    border-bottom: 1px solid #dfdddd;
    background: #f2f2f8;
    border-top-left-radius: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-header {
        padding:7px 20px
    }
}

.zb-champion-standard-theme .zb-sidepanel-header.zb-sidepanel-header-is-last {
    background: #fff
}

.zb-champion-standard-theme .zb-sidepanel-header:not(:first-child) {
    margin-top: -4px;
    -webkit-box-shadow: 0 -2px 3px 0 rgba(0,0,0,.1);
    box-shadow: 0 -2px 3px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-sidepanel-header-content {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.zb-champion-standard-theme .zb-sidepanel-header-actions {
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -ms-flex-item-align: center;
    align-self: center
}

.zb-champion-standard-theme .zb-sidepanel-close {
    border: none;
    background: none;
    padding: 0;
    vertical-align: bottom;
    cursor: pointer
}

.zb-champion-standard-theme .zb-sidepanel-body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow-y: auto;
    background: #fff
}

.zb-champion-standard-theme .zb-sidepanel-body-content {
    padding: 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-body-content {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-sidepanel-footer {
    padding: 30px;
    -webkit-box-shadow: 0 -2px 3px 0 rgba(0,0,0,.1);
    box-shadow: 0 -2px 3px 0 rgba(0,0,0,.1);
    background: #fff;
    border-bottom-left-radius: 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-footer {
        padding:20px
    }
}

.zb-champion-standard-theme .zb-sidepanel {
    background: transparent;
    padding-left: 0;
    font-size: 1rem;
    bottom: auto;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    overflow: visible
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel {
        -webkit-transform:translateY(0);
        transform: translateY(0);
        top: 0;
        bottom: 0;
        font-size: .8125rem
    }
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner {
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-sidepanel-close .zb-icon {
    display: block
}

.zb-champion-standard-theme .zb-sidepanel-body {
    max-height: 330px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-body {
        max-height:100%
    }
}

.zb-champion-standard-theme .zb-sidepanel-footer {
    border-top: 1px solid #dfdddd;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-sidepanel-body-content p {
    margin-top: 0
}

.zb-champion-standard-theme .zb-sidepanel-body-content ul {
    padding-left: 18px
}

.zb-champion-standard-theme .zb-sidepanel-body-content ul li {
    margin-bottom: 1em
}

.zb-champion-standard-theme .zb-sidepanel-header {
    margin-top: 0;
    margin-bottom: 0;
    font-weight: 400
}

.zb-champion-standard-theme .zb-sidepanel-header:not(:first-child) {
    margin-top: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-sidepanel-header .zb-sidepanel-header-content {
    margin-top: 0;
    margin-bottom: 0
}

.zb-champion-standard-theme .zb-sidepanel-header-content {
    font-size: 1.5rem;
    line-height: 1.5;
    color: #333;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-header-content {
        min-height:34px;
        line-height: 1.6;
        font-size: 1.25rem
    }
}

.zb-champion-standard-theme .zb-sidepanel-footer {
    background-color: #f2f2f8
}

.zb-champion-standard-theme .zb-sidepanel-wrapper {
    position: absolute
}

.zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel-inner-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 9999;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden
}

.zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel {
    bottom: 0;
    top: 0;
    -webkit-transform: translateY(0);
    transform: translateY(0)
}

.zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel-body {
    max-height: inherit
}

.zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel-background {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba(0,0,0,.6);
    pointer-events: all
}

.zb-slider {
    width: 100%
}

.zb-slider-tooltip {
    position: absolute
}

.zb-slider-description-sr-only {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled {
    opacity: .4
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-decrease-button:hover,.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-increase-button:hover {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-webkit-slider-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: default
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-moz-range-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: default
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-ms-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: default
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-webkit-slider-runnable-track {
    cursor: default
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-moz-range-track {
    cursor: default
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-ms-track {
    cursor: default
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button {
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid #5e10b1;
    border-radius: 8px;
    padding: 0
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:hover,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:hover {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:focus,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:focus {
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button .zb-icon,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button .zb-icon {
    position: relative;
    margin-left: 0
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button {
    margin-right: 16px;
    float: left
}

.zb-champion-standard-theme .zb-slider .zb-slider-increase-button {
    margin-left: 16px;
    float: right
}

.zb-champion-standard-theme .zb-slider .zb-slider-container {
    display: inline-block;
    height: 32px;
    position: relative
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-input {
    margin: 0;
    width: 100%;
    height: 32px
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-input-is-inverted {
    direction: rtl
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-progress-bar-wrapper {
    position: absolute;
    left: 0;
    top: 14px;
    float: left;
    height: 4px;
    pointer-events: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-progress-bar {
    position: absolute;
    left: 0;
    height: 4px;
    background: #3c1053
}

.zb-champion-standard-theme .zb-slider .zb-slider-input {
    -webkit-appearance: none;
    background: transparent;
    padding: 0
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    border: none;
    height: 32px;
    width: 32px;
    border-radius: 8px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: pointer
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-moz-range-thumb {
    border: none;
    height: 32px;
    width: 32px;
    border-radius: 8px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: pointer
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-ms-thumb {
    border: none;
    height: 32px;
    width: 32px;
    border-radius: 8px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%235E10B1' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E");
    cursor: pointer
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%233C1053' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-moz-range-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%233C1053' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-ms-thumb:hover {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cpath fill='%233C1053' d='M0 0h32v32H0z'/%3E%3Cpath fill='%23FFF' d='M11 21h2V11h-2zM15 21h2V11h-2zM19 21h2V11h-2z'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus {
    outline: none
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus::-webkit-slider-thumb {
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus::-moz-range-thumb {
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus::-ms-thumb {
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-runnable-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: #cccfd0
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-moz-range-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: #cccfd0
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-ms-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
    border-width: 16px 0
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-ms-fill-lower,.zb-champion-standard-theme .zb-slider .zb-slider-input::-ms-fill-upper {
    background: #cccfd0;
    height: 4px
}

.zb-champion-standard-theme .zb-slider.zb-slider-with-numerical {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.zb-champion-standard-theme .zb-slider.zb-slider-with-numerical .zb-slider-container {
    width: calc(100% - 200px)
}

.zb-champion-standard-theme .zb-slider-tooltip-track {
    position: absolute;
    left: 16px;
    right: 16px
}

.zb-champion-standard-theme .zb-slider-tooltip {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    padding: 4px 8px;
    bottom: 11px;
    font-size: .8125rem;
    background: #fff;
    border: 1px solid #cccfd0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.1)
}

.zb-champion-standard-theme .zb-slider-tooltip:after,.zb-champion-standard-theme .zb-slider-tooltip:before {
    content: "";
    display: block;
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -7px;
    margin-top: 1px;
    border-top: 7px solid #cccfd0;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent
}

.zb-champion-standard-theme .zb-slider-tooltip:after {
    border-top-color: #fff;
    margin-top: 0
}

.zb-champion-standard-theme .zb-slider-labeller {
    height: 92px
}

.zb-champion-standard-theme .zb-slider-labeller .zb-slider-value-label {
    display: block;
    text-align: center;
    padding-bottom: 8px
}

.zb-champion-standard-theme .zb-slider-labeller .zb-slider-min-label {
    padding-top: 8px;
    float: left
}

.zb-champion-standard-theme .zb-slider-labeller .zb-slider-max-label {
    padding-top: 8px;
    float: right
}

.zb-champion-standard-theme .zb-slider-numerical-input {
    width: 104px;
    margin-top: -5px;
    margin-left: 16px;
    padding: 0 5px;
    height: 34px
}

.zb-champion-standard-theme .zb-slider-numerical-input:disabled,.zb-champion-standard-theme .zb-slider-numerical-input:focus,.zb-champion-standard-theme .zb-slider-numerical-input:hover {
    padding: 0 5px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
    margin-top: -10px;
    background-position: 50%
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-thumb:hover,.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-input::-webkit-slider-thumb:hover {
    background-position: 50%
}

.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-decrease-button:hover,.zb-champion-standard-theme .zb-slider.zb-slider-is-disabled .zb-slider-increase-button:hover {
    padding: 1px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button {
    width: 24px;
    height: 24px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button .zb-icon,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button .zb-icon {
    margin-top: 3px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:hover .zb-icon,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:hover .zb-icon {
    margin-top: 2px
}

.zb-champion-standard-theme .zb-slider .zb-slider-container {
    height: 24px;
    width: calc(100% - 80px)
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-input {
    height: 24px
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-progress-bar-wrapper {
    top: 10px;
    right: 24px
}

.zb-split-button {
    display: inline-block
}

.zb-champion-standard-theme .zb-split-button {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    cursor: pointer;
    outline: none
}

.zb-champion-standard-theme .zb-split-button.zb-split-button-is-focused {
    border-radius: 22px;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button.zb-split-button-is-focused {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-split-button.zb-split-button-is-disabled {
    cursor: auto
}

.zb-champion-standard-theme .zb-split-button-label {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    text-align: center;
    min-width: 110px;
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-label {
        border-bottom-left-radius:20px;
        border-top-left-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-label:focus {
    outline: none
}

.zb-champion-standard-theme .zb-split-button-icon {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: top;
    border-left-width: 0!important;
    border-top-right-radius: 22px;
    border-bottom-right-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-icon {
        border-bottom-right-radius:20px;
        border-top-right-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-icon:focus {
    outline: none
}

.zb-champion-standard-theme .zb-split-button-icon .zb-icon {
    color: inherit;
    vertical-align: text-bottom
}

.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-label:hover {
    border: none;
    border-right: 1px solid #fff
}

.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-icon:hover {
    border: none
}

.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-icon:focus,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-icon:hover,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-disabled .zb-split-button-label:hover {
    pointer-events: none;
    color: #fff;
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label {
    width: calc(100% - 44px);
    line-height: 1;
    padding: 14px 32px;
    border: none;
    border-right: 1px solid #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label {
        padding:12px 32px;
        line-height: 1.25;
        width: calc(100% - 40px)
    }
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon {
    width: 44px;
    padding: 12px;
    border: none
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon {
        padding:12px;
        width: 40px
    }
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label {
    color: #fff;
    background: #5e10b1
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon:hover,.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label:hover {
    background: #3c1053
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon:focus,.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label:focus {
    background: #5e10b1
}

.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label:hover {
    border: 2px solid #bf9fe0;
    border-right-width: 1px
}

.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-icon:hover {
    border: 2px solid #bf9fe0
}

.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-icon:focus,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-icon:hover,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label:hover {
    pointer-events: none;
    color: #bf9fe0;
    background: #fff
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label {
    width: calc(100% - 44px);
    line-height: 1;
    padding: 13px 32px;
    border: 2px solid #5e10b1;
    border-right-width: 1px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label {
        padding:11px 32px;
        line-height: 1.25;
        width: calc(100% - 40px)
    }
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label:focus,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label:hover {
    border: 2px solid #5e10b1;
    border-right-width: 1px
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon {
    width: 44px;
    padding: 11px 12px;
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon {
        padding:11px 12px;
        width: 40px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon:focus,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon:hover {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label {
    color: #5e10b1;
    background: #fff
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon:hover,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label:hover {
    color: #5e10b1;
    background: #f2eaf9
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon:focus,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label:focus {
    color: #5e10b1;
    background: #fff
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label {
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-primary .zb-split-button-label {
        border-bottom-left-radius:20px;
        border-top-left-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon {
    border-top-right-radius: 22px;
    border-bottom-right-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-primary .zb-split-button-icon {
        border-bottom-right-radius:20px;
        border-top-right-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label {
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label {
        border-bottom-left-radius:20px;
        border-top-left-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon {
    border-top-right-radius: 22px;
    border-bottom-right-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-icon {
        border-bottom-right-radius:20px;
        border-top-right-radius: 20px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled):hover .zb-split-button-icon {
    padding: 11px 12px;
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled):hover .zb-split-button-icon {
        padding:11px 12px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled).zb-split-button-is-focused .zb-split-button-icon {
    padding: 11px 12px;
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled).zb-split-button-is-focused .zb-split-button-icon {
        padding:11px 12px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled):hover .zb-split-button-label {
    padding: 13px 32px;
    border: 2px solid #5e10b1;
    border-right-width: 1px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled):hover .zb-split-button-label {
        padding:11px 32px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled).zb-split-button-is-focused .zb-split-button-label {
    padding: 13px 32px;
    border: 2px solid #5e10b1;
    border-right-width: 1px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled).zb-split-button-is-focused .zb-split-button-label {
        padding:11px 32px
    }
}

.zb-champion-standard-theme .zb-split-button {
    font-size: 1rem;
    line-height: 1.25
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button {
        font-size:.8125rem
    }
}

.zb-champion-standard-theme .zb-split-button-label {
    vertical-align: top;
    border-radius: 0
}

.zb-tab-bar {
    display: block
}

.zb-tab-bar:focus {
    outline: none
}

.zb-champion-standard-theme .zb-tab-bar-has-separator {
    background: linear-gradient(0deg,#646068 0,#646068 1px,transparent 0);
    background-position-y: 0
}

.zb-champion-standard-theme .zb-tab-bar-has-separator .zb-tab-bar-item {
    border: 1px solid #646068
}

.zb-champion-standard-theme .zb-tab-bar-has-separator .zb-tab-bar-item.zb-tab-bar-item-is-selected {
    border: 1px solid #646068;
    border-bottom-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-tab-bar-has-separator .zb-tab-bar-item.zb-tab-bar-item-is-focused {
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-tab-bar-item-container {
    display: inline-block;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background: transparent
}

.zb-champion-standard-theme .zb-tab-bar-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    margin: 0 2px;
    color: #5e10b1;
    font-size: 1rem;
    cursor: pointer;
    padding: 9px 16px;
    background: transparent;
    border: 1px solid #646068;
    border-bottom-color: transparent
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-tab-bar-item {
        padding:4px 8px;
        font-size: .8125rem
    }
}

.zb-champion-standard-theme .zb-tab-bar-item:focus {
    outline: none
}

.zb-champion-standard-theme .zb-tab-bar-item:first-child {
    border-top-left-radius: 0;
    margin-left: 0
}

.zb-champion-standard-theme .zb-tab-bar-item:last-child {
    border-top-right-radius: 0
}

.zb-champion-standard-theme .zb-tab-bar-item.zb-tab-bar-item-is-focused {
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-tab-bar-item .zb-tab-bar-item-is-full-size {
    margin: -9px -16px;
    padding: 9px 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-tab-bar-item .zb-tab-bar-item-is-full-size {
        padding:4px 8px
    }
}

.zb-champion-standard-theme .zb-tab-bar-item-is-selected {
    position: relative;
    background: #fff;
    color: #3c1053;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border: 1px solid;
    border-color: #fff #fff transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-tab-bar-item-is-selected.zb-tab-bar-item-is-focused {
    outline: none;
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-tab-bar-item-is-selected:after {
    position: absolute;
    width: 0;
    height: 0;
    content: "";
    border: 0 solid transparent;
    border-top-color: #000;
    top: 100%;
    left: 50%;
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
    margin-left: 0;
    display: block
}

.zb-champion-standard-theme .zb-tab-bar-item-is-disabled {
    border-color: #c9c6c6;
    border-bottom: 1px solid transparent
}

.zb-champion-standard-theme .zb-tab-bar-has-separator .zb-tab-bar-item.zb-tab-bar-item-is-disabled {
    border-color: #c9c6c6;
    border-bottom: 1px solid #646068
}

.zb-champion-standard-theme .zb-tab-bar-item-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.zb-champion-standard-theme .zb-tab-bar-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    line-height: 1.25;
    text-align: center
}

.zb-champion-standard-theme .zb-tab-bar-item:hover {
    text-decoration: underline
}

.zb-champion-standard-theme .zb-tab-bar-item-is-selected:hover {
    text-decoration: none
}

.zb-champion-standard-theme .zb-tab-bar-item-is-disabled {
    color: #9e70d0;
    cursor: default;
    background-color: transparent;
    pointer-events: none
}

.zb-champion-standard-theme .zb-tab-bar-item-is-disabled:hover {
    text-decoration: none
}

.zb-toggle-star,.zb-toggle-switch {
    display: inline-block;
    cursor: pointer
}

.zb-champion-standard-theme .zb-toggle-star .zb-icon {
    vertical-align: top;
    margin-top: 4px;
    color: transparent
}

.zb-champion-standard-theme .zb-toggle-star:hover .zb-icon {
    color: transparent
}

.zb-champion-standard-theme .zb-toggle-star.zb-toggle-is-pressed .zb-icon {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-toggle-star.zb-toggle-is-pressed:hover .zb-icon {
    color: #4b0d8e
}

.zb-champion-standard-theme .zb-toggle-star.zb-toggle-is-focused,.zb-champion-standard-theme .zb-toggle-star:focus {
    outline: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-toggle-switch {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    line-height: 1.5;
    border-radius: 8px;
    border: 1px solid #cccfd0;
    color: #646068;
    padding: 3px 1px;
    background-color: #fff;
    font-size: .8125rem
}

.zb-champion-standard-theme .zb-toggle-switch:hover .zb-toggle-switch-button {
    background: #f2f2f8
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-focused,.zb-champion-standard-theme .zb-toggle-switch:focus {
    outline: 2px solid #5e10b1;
    outline-offset: 2px;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed {
    border: 1px solid #cccfd0;
    color: #646068;
    padding: 3px 1px
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-button {
    background: #429448;
    left: 0;
    float: right
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed:hover .zb-toggle-switch-button {
    background: #429448
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-label {
    float: right
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-label-invert {
    float: left
}

.zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-button {
    background: #f2f2f8;
    border: 1px solid #cccfd0;
    float: left;
    left: 0
}

.zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label {
    float: left;
    padding: 3px 2px 0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label {
        padding:2px 2px 0
    }
}

.zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label-invert {
    display: inline;
    padding: 3px 2px 0;
    float: right
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label-invert {
        padding:2px 2px 0
    }
}

.zb-champion-standard-theme .zb-toggle-switch-button {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    position: inherit;
    height: 24px;
    width: 40px;
    border-radius: 12px;
    top: 3px;
    -webkit-transition: all .1s;
    transition: all .1s
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-toggle-switch-button {
        width:40px;
        height: 21px
    }
}

.zb-champion-standard-theme .zb-toggle-switch-button-inner {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    top: -1px;
    left: -1px;
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #646068;
    display: inline-block;
    margin-left: 0;
    -webkit-transition: margin-left .1s;
    transition: margin-left .1s;
    border-radius: 4px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-toggle-switch-button-inner {
        height:21px;
        width: 21px
    }
}

.zb-champion-standard-theme .zb-toggle-switch:hover .zb-toggle-switch-button-inner {
    border: 2px solid #fff
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-button-inner {
    background: #fff;
    margin-left: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-button-inner {
        margin-left:19px
    }
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed:hover .zb-toggle-switch-button-inner {
    background: #fff;
    border-color: #fff
}

.zb-champion-standard-theme .zb-toggle.zb-toggle-is-disabled {
    opacity: .4;
    pointer-events: none
}

.zb-champion-standard-theme .zb-toggle.zb-toggle-switch.zb-toggle-is-disabled .zb-toggle-switch-button-inner {
    border: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-toggle-button .zb-button+.zb-button {
    margin-left: 20px
}

.zb-champion-standard-theme .zb-accordion dt:first-child>.zb-accordion-header {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-content.zb-accordion-is-last,.zb-champion-standard-theme .zb-accordion .zb-accordion-header.zb-accordion-is-last {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px
}

.zb-champion-standard-theme .zb-accordion .zb-accordion-content.zb-accordion-is-last.zb-accordion-is-active,.zb-champion-standard-theme .zb-accordion .zb-accordion-header.zb-accordion-is-last.zb-accordion-is-active {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion {
    border-radius: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header .zb-accordion-header-icon.zb-icon-core-accordion-collapse {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header .zb-accordion-header-icon.zb-icon-core-accordion-expand {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg)
}

.zb-champion-standard-theme .zb-accordion .zb-accordion dt:first-child>.zb-accordion-header {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-content.zb-accordion-is-last,.zb-champion-standard-theme .zb-accordion .zb-accordion .zb-accordion-header.zb-accordion-is-last {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.zb-champion-standard-theme .zb-heading2-alternate {
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.5;
    color: #333;
    font-family: knilebold,Arial,sans-serif
}

.zb-champion-standard-theme .zb-heading3-alternate {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    color: #333;
    font-family: knilebold,Arial,sans-serif
}

.zb-champion-standard-theme a:focus {
    outline: 2px solid #5e10b1
}

.zb-champion-standard-theme a.zb-inverted-cta:focus,.zb-champion-standard-theme a.zb-tertiary-cta.zb-inverted-cta:focus {
    outline: 2px solid #fff
}

.zb-champion-standard-theme .zb-button-link:focus {
    outline: 2px solid #5e10b1;
    outline-offset: 4px;
    text-decoration: none
}

.zb-champion-standard-theme .zb-button.zb-button-is-disabled,.zb-champion-standard-theme .zb-button:disabled {
    pointer-events: none;
    opacity: .4
}

.zb-champion-standard-theme .zb-button:focus {
    outline: 2px solid #5e10b1;
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-button:focus.zb-button-primary-alternate,.zb-champion-standard-theme .zb-button:focus.zb-button-secondary-alternate {
    outline-color: #fff
}

.zb-champion-standard-theme .zb-button.zb-button-primary.zb-button-is-disabled,.zb-champion-standard-theme .zb-button.zb-button-primary:disabled {
    background: #5e10b1
}

.zb-champion-standard-theme .zb-button.zb-button-secondary.zb-button-is-disabled,.zb-champion-standard-theme .zb-button.zb-button-secondary:disabled {
    border-color: #5e10b1;
    color: #5e10b1
}

.zb-champion-standard-theme .zb-button.zb-button-primary-alternate.zb-button-is-disabled,.zb-champion-standard-theme .zb-button.zb-button-primary-alternate:disabled {
    background: #fff
}

.zb-champion-standard-theme .zb-input {
    color: #000
}

.zb-champion-standard-theme .zb-input:hover:not(:disabled) {
    -webkit-box-shadow: 0 0 0 1px #5e10b1;
    box-shadow: 0 0 0 1px #5e10b1
}

.zb-champion-standard-theme .zb-input.zb-input-is-error:focus {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input.zb-input-is-error:hover {
    border: 1px solid #5e10b1;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-input:focus:hover {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-textarea:hover:not(:disabled) {
    -webkit-box-shadow: 0 0 0 1px #5e10b1;
    box-shadow: 0 0 0 1px #5e10b1
}

.zb-champion-standard-theme .zb-textarea.zb-textarea-is-error:focus {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-textarea.zb-textarea-is-error:hover {
    border: 1px solid #5e10b1;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-textarea:focus:hover {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-textarea:disabled {
    border: 1px solid #c1bfc3;
    background: #fff;
    color: #999
}

.zb-champion-standard-theme .zb-input-wrapper:hover:not(:disabled) {
    -webkit-box-shadow: 0 0 0 1px #5e10b1;
    box-shadow: 0 0 0 1px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-disabled:hover {
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-input-wrapper[focus-within] {
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper:focus-within {
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-focused:hover {
    border: 1px solid #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error.zb-input-wrapper-is-focused,.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error[focus-within] {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error:focus-within {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-input-wrapper.zb-input-wrapper-is-error:hover {
    border: 1px solid #5e10b1;
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-left-hand-nav,.zb-champion-standard-theme .zb-nav-vertical {
    border: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-header,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header {
    background: #f2f2f8;
    padding-left: 24px;
    padding-right: 24px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item .zb-left-hand-nav-item-link,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item .zb-left-hand-nav-item-link {
    padding-left: 24px;
    padding-right: 24px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item .zb-left-hand-nav-item-link:focus,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item .zb-left-hand-nav-item-link:focus {
    outline-offset: -2px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link {
    font-weight: 400
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link:focus,.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-item.zb-left-hand-nav-item-is-selected .zb-left-hand-nav-item-link:focus {
    outline-color: #8b3fb2
}

.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header {
    background: #fff;
    font-size: .875rem;
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item {
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item .zb-nav-menu-item-label {
    font-size: .875rem
}

.zb-champion-standard-theme .zb-footer a:focus {
    outline-color: #fff
}

.zb-champion-standard-theme .zb-breadcrumb {
    position: relative;
    margin-right: 12px;
    padding: 4px 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-breadcrumb {
        margin-right:14px
    }
}

.zb-champion-standard-theme .zb-breadcrumb:after {
    position: absolute;
    margin-top: 4px;
    right: -12px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-breadcrumb:after {
        right:-14px
    }
}

.zb-champion-standard-theme .zb-breadcrumb:focus {
    outline-offset: 0
}

.zb-champion-standard-theme .zb-breadcrumb-is-current {
    margin-right: 0;
    color: #3c1053
}

.zb-champion-standard-theme .zb-breadcrumb-is-current:hover {
    color: #3c1053
}

.zb-champion-standard-theme ul.zb-list li {
    color: #646068
}

.zb-champion-standard-theme .zb-feature-button:hover {
    color: #3c1053;
    border: 2px solid #3c1053
}

.zb-champion-standard-theme .zb-feature-button:hover .zb-icon {
    color: #3c1053
}

.zb-champion-standard-theme .zb-feature-button:hover .zb-feature-button-arrowed-content:after {
    content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='none' stroke='%233C1053' stroke-width='2' d='M5 2l6 6-5.971 5.97'/%3E%3C/svg%3E")
}

.zb-champion-standard-theme .zb-feature-button:hover.zb-feature-button-is-disabled,.zb-champion-standard-theme .zb-feature-button:hover:disabled {
    color: #bf9fe0;
    border-color: #fff
}

.zb-champion-standard-theme .zb-feature-button:hover.zb-feature-button-is-disabled .zb-icon,.zb-champion-standard-theme .zb-feature-button:hover:disabled .zb-icon {
    color: #bf9fe0
}

.zb-champion-standard-theme .zb-feature-button:hover.zb-feature-button-is-disabled .zb-feature-button-arrowed-content:after,.zb-champion-standard-theme .zb-feature-button:hover:disabled .zb-feature-button-arrowed-content:after {
    content: url(/CWSLogon/resources/images/zb-champion-standard/feature-button-arrow-disabled.svg)
}

.zb-champion-standard-theme .zb-brand-logo-natwest {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 207px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-natwest {
        height:32px;
        width: 94px
    }
}

.zb-champion-standard-theme .zb-brand-logo-rb {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='244' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M72 36c0 33.387-2.613 36-36 36S0 69.387 0 36 2.613 0 36 0s36 2.613 36 36' fill='%233C1053'/%3E%3Cpath d='M32.948 28.005c-3.723 1.336-5.003 4.955-5.003 4.955-.501 1.335-1.831 2.337-3.332 2.337h-7.058c-1.944 0-3.555-1.616-3.555-3.562 0-1.95 1.333-3.507 3.555-3.507l5.598.01-8.1-8.135a3.531 3.531 0 010-5.005 3.518 3.518 0 015.002 0l8.057 8.054V17.6a3.546 3.546 0 013.555-3.558c1.949 0 3.504 1.612 3.504 3.558v7.066c0 1.503-.89 2.896-2.223 3.34m-1.28 29.938c-1.942 0-3.501-1.284-3.444-3.507l.01-5.641-8.123 8.143c-1.388 1.395-3.667 1.395-5 0-1.388-1.388-1.388-3.67 0-5.004l8.035-8.074h-5.535c-2 0-3.554-1.612-3.554-3.554a3.546 3.546 0 013.554-3.564h7.056c1.5 0 2.834.888 3.336 2.226 1.279 3.78 4.886 5.06 4.886 5.06a3.496 3.496 0 012.337 3.34v7.068c0 1.948-1.558 3.507-3.559 3.507M54.39 43.808l-5.608-.006 8.107 8.133a3.524 3.524 0 010 5.004 3.51 3.51 0 01-5 0l-8.055-8.068v5.566c0 2.117-1.613 3.563-3.557 3.563-1.946 0-3.557-1.615-3.557-3.563v-7.069c0-1.503.945-2.894 2.28-3.339 3.72-1.28 4.999-4.95 4.999-4.95.5-1.332 1.834-2.336 3.334-2.336h7.057c1.947 0 3.556 1.614 3.556 3.564 0 1.942-1.444 3.501-3.556 3.501m.056-8.568h-7.058c-1.499 0-2.89-.89-3.333-2.225-1.28-3.73-4.944-5.066-4.944-5.066-1.334-.501-2.335-1.78-2.335-3.34v-7.065c0-1.946 1.611-3.502 3.558-3.502 1.943 0 3.5 1.446 3.5 3.502l.001 5.663 8.11-8.165a3.522 3.522 0 015 0c1.39 1.387 1.39 3.669 0 5.061l-7.999 8.013h5.5C56.39 28.116 58 29.73 58 31.68c0 1.947-1.61 3.561-3.555 3.561' fill='%23FFF'/%3E%3Cpath d='M94.542 9.546c.44-.07 1.088-.1 1.733-.1 2.786 0 4.146 1.594 4.146 3.802 0 2.144-1.633 3.624-4.283 3.624-.68 0-1.29-.031-1.596-.066v-7.26zm0 10.533h.375l6.187 9.896h5.474l-5.338-8.024c-.546-.812-1.193-1.774-1.838-2.625 3.195-.546 5.645-3.114 5.645-6.52 0-4.698-3.266-6.97-8.33-6.97-2.655 0-4.795.034-6.6.137v24.002h4.425V20.08zm20.79 6.525c-2.483 0-3.774-1.766-3.774-5.243 0-3.469 1.906-4.97 3.844-4.97 2.548 0 3.739 1.77 3.739 5.042 0 3.437-1.666 5.171-3.81 5.171m.374-13.886c-5.133 0-8.638 3.809-8.638 9.088 0 6.023 3.131 8.507 7.922 8.507 5.036 0 8.64-3.812 8.64-9.122 0-5.821-3.094-8.473-7.924-8.473m19.062 18.212l6.697-17.871h-4.66l-2.003 5.753c-.544 1.562-1.26 3.846-1.632 5.376h-.037a66.358 66.358 0 00-1.324-4.663l-2.278-6.466h-4.866l6.258 16.916-.544 1.5c-.918 2.652-2.412 3.332-3.84 3.332a7.339 7.339 0 01-2.14-.34l-.512 3.746c.645.138 1.664.24 2.38.24 3.909 0 6.291-1.67 8.5-7.523m17.333-8.477v.446c0 2.04-1.424 4.08-3.534 4.08-1.26 0-2.039-.889-2.039-1.974 0-1.461 1.19-2.552 5.573-2.552m-4.925 7.86c2.395 0 4.377-1.207 5.266-2.972v2.634h4.149a63.837 63.837 0 01-.136-4.354V18.64c0-3.78-1.562-5.958-6.797-5.958-2.283 0-4.696.479-6.395 1.16l.713 3.335c1.495-.65 3.57-1.158 5.101-1.158 2.245 0 3.024.918 3.024 2.965v.61c-5.915 0-9.758 1.87-9.758 5.89 0 2.757 1.772 4.829 4.833 4.829m12.815-.338h4.354V5.125h-4.354v24.85zm19.187-10.77c.476-.033 1.63-.033 1.938-.033 3.027 0 4.863 1.129 4.863 3.308 0 2.009-1.464 3.99-4.355 3.99-.817 0-1.902-.07-2.446-.104v-7.162zm0-9.625c.712-.034 1.53-.069 2.481-.069 2.686 0 3.707 1.267 3.707 2.969 0 2.043-1.363 3.39-4.388 3.39-.71 0-1.256 0-1.8-.036V9.58zm11.526 12.764c0-2.555-1.666-4.735-4.114-5.249v-.069c2.208-.883 3.469-2.978 3.469-5.26 0-3.673-2.755-5.793-7.685-5.793-2.79 0-5.678.07-7.616.167v23.836c2.89 0 5.166.066 6.699.066 6.727 0 9.247-4.156 9.247-7.698zm12.298.555c0 2.04-1.428 4.08-3.535 4.08-1.259 0-2.04-.888-2.04-1.974 0-1.461 1.186-2.552 5.575-2.552v.446zm-9.76 2.585c0 2.757 1.769 4.829 4.832 4.829 2.396 0 4.38-1.207 5.268-2.974v2.637h4.148c-.103-1.393-.134-2.997-.134-4.356v-6.98c0-3.779-1.565-5.958-6.802-5.958-2.277 0-4.692.479-6.396 1.16l.716 3.334c1.498-.65 3.57-1.157 5.099-1.157 2.247 0 3.03.918 3.03 2.965v.61c-5.916 0-9.761 1.87-9.761 5.89zm22.012-3.678c0-2.964 1.732-5.447 3.91-5.447 1.633 0 2.107 1.055 2.107 2.792v10.824h4.317V18.093c0-2.961-1.221-5.375-5.032-5.375-2.588 0-4.475 1.24-5.542 3.03v-2.69H210.9v16.917h4.353v-8.169zm18.075.351l5.235 7.819H244l-6.683-9.19 6.246-7.728h-5.308l-4.928 6.215V5.125h-4.353v24.85h4.353v-7.818zM97.124 63.1c-2.482 0-3.777-1.774-3.777-5.241 0-3.476 1.905-4.975 3.845-4.975 2.55 0 3.742 1.77 3.742 5.041 0 3.435-1.668 5.175-3.81 5.175m.372-13.893c-5.132 0-8.635 3.815-8.635 9.092 0 6.027 3.13 8.51 7.922 8.51 5.031 0 8.636-3.814 8.636-9.123 0-5.82-3.093-8.479-7.923-8.479m12.362-1.002v1.393h-2.961v3.373h2.96v13.548h4.318V52.97h3.946v-3.373h-3.946v-1.43c0-2.244.848-3.232 2.755-3.232.64 0 1.427.068 2.108.307l.305-3.575c-.887-.203-1.836-.275-2.754-.275-4.624 0-6.731 2.25-6.731 6.812m20.386.7c0-1.632 1.19-2.756 3.773-2.756 1.36 0 2.686.238 3.947.645l.405-3.883a21.87 21.87 0 00-4.146-.436c-5.544 0-8.673 2.823-8.673 6.907 0 7.559 9.654 6.194 9.654 10.584 0 2.147-1.765 2.998-3.841 2.998a12.97 12.97 0 01-4.997-.99l-.475 4.253c1.53.412 3.265.616 5.304.616 5.305 0 8.875-3.167 8.875-7.421 0-7.284-9.826-6.432-9.826-10.518m20.15 14.13c-2.889 0-3.94-2.01-3.94-4.902 0-3.137 1.323-5.212 3.907-5.212 1.122 0 2.243.27 3.162.644l.68-3.742c-1.088-.376-2.415-.578-3.705-.578-5.849 0-8.605 4.048-8.605 9.326 0 5.755 2.927 8.206 7.617 8.206 1.634 0 3.436-.275 4.762-.782l-.58-3.647c-.985.446-2.14.686-3.297.686m13.103.067c-2.484 0-3.775-1.774-3.775-5.241 0-3.476 1.901-4.975 3.843-4.975 2.55 0 3.74 1.77 3.74 5.041 0 3.435-1.665 5.175-3.808 5.175m.373-13.893c-5.133 0-8.638 3.815-8.638 9.092 0 6.027 3.13 8.51 7.924 8.51 5.034 0 8.639-3.814 8.639-9.123 0-5.82-3.098-8.479-7.925-8.479m18.625 13.925c-1.497 0-2.003-.613-2.003-2.793v-7.42h4.113v-3.371h-4.113V42.46l-4.32 1.155v5.932h-2.925v3.372h2.925v8.576c0 3.95 1.157 5.312 4.895 5.312 1.19 0 2.586-.202 3.675-.477l-.308-3.437c-.578.169-1.222.238-1.939.238m5.013 3.387h4.35V41.664h-4.35v24.854zm16.625-7.09c0 2.046-1.426 4.088-3.532 4.088-1.26 0-2.042-.888-2.042-1.978 0-1.461 1.188-2.552 5.574-2.552v.442zm4.351-4.256c0-3.776-1.561-5.955-6.8-5.955-2.275 0-4.69.477-6.39 1.158l.713 3.334c1.5-.645 3.57-1.155 5.1-1.155 2.247 0 3.026.916 3.026 2.96v.613c-5.916 0-9.757 1.875-9.757 5.889 0 2.76 1.769 4.834 4.826 4.834 2.402 0 4.39-1.216 5.272-2.98v2.642h4.15a63.138 63.138 0 01-.14-4.357v-6.983zm12.562-5.915c-2.59 0-4.479 1.245-5.544 3.034v-2.694h-4.112v16.921h4.353v-8.17c0-2.961 1.733-5.448 3.908-5.448 1.632 0 2.109 1.054 2.109 2.79v10.828h4.32V54.636c0-2.962-1.224-5.38-5.034-5.38m18.635 8.464c0 2.891-1.362 5.548-3.64 5.548-1.972 0-2.888-1.636-2.888-4.8 0-4.19 1.767-5.753 4.25-5.753.746 0 1.498.101 2.278.272v4.733zm0-16.073v7.765a14.868 14.868 0 00-2.141-.135c-5.614 0-8.946 3.945-8.946 9.668 0 4.833 2.042 7.93 6.157 7.93 2.465 0 4.272-1.214 5.2-2.954v2.58H244V41.647h-4.32z' fill='%233C1053'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 244px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-rb {
        height:32px;
        width: 110px
    }
}

.zb-champion-standard-theme .zb-brand-logo-ulsterbank {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='261' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M143.131 22.991l-4.332 1.217v5.487h-3.526v3.664h3.526v9.715c0 4.354 1.415 5.857 5.304 5.857 1.095 0 2.47-.128 3.722-.408l.171-.04v-3.704c-.972.29-1.86.329-2.633.329-1.623 0-2.196-.491-2.196-3.25v-8.499h4.828v-3.664h-4.865V22.99l.001.001zm-14.244 14.474c-1.945-.812-3.083-1.379-3.083-2.685 0-1.583 1.456-2.027 3.21-2.027 1.331 0 2.71.282 3.635.606v-3.7a20.787 20.787 0 00-3.884-.366c-4.5 0-7.46 2.119-7.46 5.731 0 3.42 2.194 4.556 4.946 5.737 1.904.774 3.16 1.219 3.16 2.637s-1.056 1.996-3.207 1.996c-1.816 0-3.316-.369-4.41-.813l-.491 3.859c1.296.324 3.085.533 4.543.533 4.9 0 8.058-2.115 8.058-6.018 0-3.49-2.589-4.435-5.018-5.49h.001zm-15.14-14.385h4.379v25.522h-4.379V23.08zm-8.022 14.878c0 .85 0 1.988-.128 2.682-.36 3.134-1.94 4.554-5.5 4.554-3.25 0-4.91-1.222-5.396-3.657-.158-.774-.236-2.202-.236-3.13V23.08h-4.454v15.687c0 1.177.079 2.599.32 3.783.929 4.183 4.012 6.423 9.399 6.423 5.955 0 9.393-2.484 10.17-7.316.152-.979.197-2.476.197-3.659V23.08h-4.372v14.878zm53.06-8.618c-5.225 0-8.629 3.897-8.629 9.916 0 6.466 3.04 9.718 9.115 9.718 2.383 0 4.577-.405 6.484-1.218l.117-.043-.397-3.618-.288.118c-1.503.773-3.443 1.183-5.263 1.183-1.82 0-3.045-.445-3.93-1.341-.894-.98-1.345-2.396-1.345-4.313v-.162h11.745l.125-2.032c0-5.204-2.835-8.21-7.734-8.21v.002zm-4.009 7.03c.443-2.28 1.824-3.698 3.89-3.698 1.011 0 1.863.32 2.51.975.649.65.97 1.543.97 2.726h-7.37v-.002zm18.688-3.698c.119-1.1.193-2.076.193-2.77v-.206h-4.205v18.908h4.414v-7.722c0-3.374.646-5.564 1.904-6.585a3.485 3.485 0 012.224-.774c.326 0 .773.038 1.304.164l.201-4.347c-.408-.045-.65-.045-.894-.045-2.756 0-4.38 1.746-5.137 3.377h-.004zm83.843 10.924c-1.816-2.479-3.726-5.116-3.805-5.198.079-.083 1.863-2.277 3.565-4.436 1.012-1.256 2.182-2.64 3.443-4.231h-5.39c-.929 1.265-1.823 2.404-2.592 3.42a292.823 292.823 0 01-3.07 4.066V23.116h-4.385v25.522h4.385v-8.901c.284.486 1.82 2.766 3.241 4.836.844 1.262 1.739 2.601 2.754 4.065H261a1069.018 1069.018 0 01-3.69-5.042h-.003zm-21.435-14.222c-2.717 0-4.823 1.138-6.117 3.21.12-.933.12-1.869.12-2.646v-.206h-4.211V48.64h4.42v-9.137c0-3.708 1.944-6.432 4.572-6.432 1.826 0 2.554 1.021 2.554 3.46V48.64h4.339V35.476c0-4.026-1.95-6.1-5.675-6.1l-.002-.002zm-13.655 6.417c0-4.426-2.23-6.417-7.416-6.417-2.145 0-4.458.409-6.524 1.217l-.162.085.407 3.859.284-.162c1.418-.933 3.57-1.464 5.39-1.464 2.59 0 3.642.897 3.642 3.289v.981c-6.841.04-10.853 2.561-10.853 6.914 0 3.05 2.066 4.92 5.347 4.92 2.47 0 4.701-1.144 5.87-3.05a24.01 24.01 0 00-.118 2.482v.196h4.25v-.235c-.117-1.47-.117-3.298-.117-5.048v-7.567zm-4.38 4.64c0 2.44-1.66 5.037-4.331 5.037-1.415 0-2.313-.817-2.313-2.07 0-2.036 2.233-3.094 6.645-3.134v.168zm-17.81-5.207c1.913-.725 3.893-2.478 3.893-5.609 0-4.06-3.237-6.543-8.22-6.543h-8.183v25.533h7.778c6.03 0 9.474-3.094 9.474-7.563 0-3.298-2.104-5.166-4.743-5.816v-.002zm-8.055-8.575h3.48c2.393 0 3.732.98 3.732 3.371 0 2.393-1.818 3.662-4.14 3.662h-3.072v-7.033zm3.647 18.248h-3.647v-7.644h3.436c2.761 0 4.58 1.301 4.58 3.785 0 2.725-1.904 3.86-4.371 3.86h.002z' fill='%233C1053'/%3E%3Cg fill-rule='nonzero'%3E%3Cpath d='M71.994 35.998C71.994 69.388 69.382 72 35.999 72 2.615 72 0 69.388 0 35.998S2.612 0 35.999 0s35.995 2.612 35.995 35.998z' fill='%233C1053'/%3E%3Cpath d='M28.3 23.108v-5.331a3.433 3.433 0 016.786-.817c.065.311.096.628.092.945v6.726a3.42 3.42 0 01-2.35 3.503 8.389 8.389 0 00-4.637 4.637c-.772 1.753-1.818 2.493-3.71 2.493h-6.693a3.489 3.489 0 01-3.242-2.756 3.362 3.362 0 011.887-3.875 4.85 4.85 0 011.62-.323c1.726-.036 3.457 0 5.298 0-.15-.189-.234-.316-.34-.423-2.478-2.538-4.964-5.07-7.457-7.6a3.525 3.525 0 01-.5-4.461 3.424 3.424 0 015.37-.46c2.393 2.387 4.761 4.794 7.14 7.2.19.22.366.45.528.69.068-.05.139-.092.207-.139M48.483 43.693c1.51 1.525 2.883 2.907 4.25 4.293l3.674 3.719a3.504 3.504 0 01.135 4.952l-.041.041a3.384 3.384 0 01-4.784.119c-.05-.047-.097-.097-.142-.142-2.47-2.466-4.912-4.954-7.345-7.429-.139-.142-.29-.275-.537-.51v5.62a3.379 3.379 0 01-3.277 3.402c-1.81.083-3.517-1.258-3.586-3.072a105.79 105.79 0 010-7.926 3.02 3.02 0 012.131-2.791 8.183 8.183 0 004.808-4.684c.734-1.717 1.915-2.567 3.769-2.534 2.185.036 4.375 0 6.566 0a3.277 3.277 0 013.08 2.016 3.352 3.352 0 01-.38 3.636 2.949 2.949 0 01-2.36 1.249c-1.708.036-3.416 0-5.128 0l-.826.036-.007.005zM28.25 48.717c-.917.95-1.803 1.91-2.726 2.848a501.377 501.377 0 01-4.959 5.018c-1.226 1.217-2.755 1.498-4.132.8a3.521 3.521 0 01-1.525-4.742v-.002c.178-.34.409-.65.684-.918 2.007-2.06 4.035-4.104 6.06-6.149.542-.55 1.102-1.092 1.744-1.726-.27-.032-.417-.065-.568-.065h-4.876a3.51 3.51 0 01-1.255-6.764c.474-.16.97-.24 1.469-.234h6.566c1.836 0 2.85.758 3.522 2.466a8.263 8.263 0 004.591 4.678c1.611.693 2.378 1.782 2.387 3.559v6.791a3.411 3.411 0 01-2.214 3.215 3.568 3.568 0 01-3.815-.85 3.093 3.093 0 01-.794-2.14v-5.69l-.166-.091M48.731 28.192h5.284a3.514 3.514 0 013.215 5.05 3.393 3.393 0 01-3.215 2.02c-2.25.032-4.5.047-6.753 0a3.411 3.411 0 01-3.407-2.383 8.323 8.323 0 00-4.71-4.808 3.586 3.586 0 01-2.342-3.595V17.81a3.425 3.425 0 013.348-3.494h.092a3.323 3.323 0 013.438 3.206v5.661s0 .036.11.19c.615-.658 1.198-1.304 1.803-1.925 2.016-2.057 4.022-4.133 6.056-6.149a3.443 3.443 0 015.749 3.393 5.171 5.171 0 01-1.184 1.887c-2.295 2.377-4.619 4.701-6.933 7.048-.16.157-.308.323-.56.593' fill='%23FFF'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 261px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-ulsterbank {
        height:32px;
        width: 116px
    }
}

.zb-champion-standard-theme .zb-brand-logo-natwestGroup {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='308' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.974%25' y1='41.675%25' x2='75.006%25' y2='58.313%25' id='a'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.65%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.33%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.05%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='99.97%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.105%25' y1='35.504%25' x2='36.235%25' y2='59.456%25' id='b'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.66%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.34%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.07%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.44%25' y1='29.336%25' x2='63.811%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.66%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.34%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.07%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cg%3E%3Cellipse fill='%233C1053' cx='36.035' cy='36' rx='36.035' ry='36'/%3E%3Cpath d='M72.069 36c0 33.387-2.615 36-36.035 36C2.615 72 0 69.387 0 36S2.615 0 36.034 0C69.454 0 72.07 2.613 72.07 36' fill='%233C1053'/%3E%3Cg fill='%23BF5CFF'%3E%3Cpath d='M46.798 43.914h-14.34l7.166 12.414h14.345zM43.206 12.894h-14.34l-7.167 12.402h14.339zM25.278 43.912l7.166-12.401h-14.34l-7.166 12.401-.005.005z'/%3E%3C/g%3E%3Cg fill='%238F52D1'%3E%3Cpath d='M53.966 31.512l7.17 12.407-7.167 12.409-7.171-12.414zM36.038 25.296l7.171 12.411 7.168-12.405-7.171-12.408zM32.45 56.328H18.103l-7.171-12.41.005-.006h14.34z'/%3E%3C/g%3E%3Cpath fill='url(%23a)' d='M46.786 31.512l-3.577 6.194h-7.173l-3.58 6.208h14.342l7.168-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.298 31.51h7.146l3.592 6.196h7.173l-7.171-12.41h-14.34z'/%3E%3Cpath fill='url(%23c)' d='M36.031 50.106l-3.575-6.192 3.58-6.208-3.592-6.195-7.166 12.401 7.17 12.416z'/%3E%3C/g%3E%3Cpath d='M237.538 34.799v11.575c-.586.354-1.25.664-1.994.93-.743.264-1.516.491-2.315.68a21.727 21.727 0 01-4.749.547c-2.028 0-3.835-.332-5.424-.995-1.588-.664-2.93-1.559-4.022-2.687a11.532 11.532 0 01-2.5-3.93 13.044 13.044 0 01-.863-4.727c0-1.746.31-3.371.93-4.875a12.013 12.013 0 012.568-3.914c1.093-1.105 2.405-1.973 3.938-2.604 1.532-.63 3.21-.945 5.036-.945 2.208 0 4.01.27 5.407.813 1.398.542 2.57 1.288 3.516 2.238l-2.299 2.422c-.991-.93-2.04-1.576-3.143-1.94a11.02 11.02 0 00-3.481-.548c-1.375 0-2.614.248-3.718.746a8.824 8.824 0 00-2.84 2.006 8.806 8.806 0 00-1.824 2.97 10.224 10.224 0 00-.642 3.63c0 1.327.236 2.56.71 3.699a9.144 9.144 0 001.96 2.969 9.036 9.036 0 002.94 1.973c1.127.476 2.354.713 3.684.713 1.15 0 2.237-.104 3.262-.315 1.025-.21 1.897-.514 2.62-.912v-6.533h-5.307V34.8h8.55v-.001zm4.552-2.587h3.041v2.421h.068c.203-.42.474-.802.812-1.144a5.424 5.424 0 011.132-.88 6.074 6.074 0 011.368-.58 5.31 5.31 0 011.488-.215c.495 0 .946.067 1.352.2l-.135 3.217a8.854 8.854 0 00-.744-.166 4.23 4.23 0 00-.743-.067c-1.487 0-2.625.41-3.413 1.227-.79.818-1.184 2.09-1.184 3.814v7.894h-3.041V32.212h-.001zm11.104 7.86c0-1.15.22-2.222.659-3.217a8.39 8.39 0 011.808-2.62 8.479 8.479 0 012.704-1.776 8.624 8.624 0 013.346-.646c1.194 0 2.31.215 3.346.646a8.458 8.458 0 012.704 1.775 8.385 8.385 0 011.808 2.62c.44.996.66 2.068.66 3.218a7.99 7.99 0 01-.66 3.234 8.124 8.124 0 01-1.808 2.62 8.595 8.595 0 01-2.704 1.757 8.627 8.627 0 01-3.346.647 8.62 8.62 0 01-3.346-.647 8.595 8.595 0 01-2.704-1.758 8.124 8.124 0 01-1.808-2.62 7.99 7.99 0 01-.66-3.233zm3.245 0c0 .796.124 1.531.372 2.206.247.675.596 1.25 1.048 1.725.45.476 1.002.852 1.656 1.127.653.277 1.386.415 2.197.415.811 0 1.543-.137 2.197-.415a4.85 4.85 0 001.656-1.127c.45-.475.8-1.05 1.048-1.725.247-.674.372-1.41.372-2.206s-.124-1.53-.372-2.205a4.977 4.977 0 00-1.048-1.725 4.862 4.862 0 00-1.656-1.128c-.654-.276-1.386-.415-2.197-.415-.811 0-1.544.139-2.197.415a4.853 4.853 0 00-1.656 1.128 4.96 4.96 0 00-1.048 1.725 6.337 6.337 0 00-.372 2.205zm30.93 7.86h-3.04v-2.42h-.068c-.384.84-1.048 1.52-1.995 2.04-.946.519-2.04.779-3.278.779a7.04 7.04 0 01-2.23-.348 5.023 5.023 0 01-1.843-1.079c-.529-.486-.952-1.111-1.267-1.873-.316-.764-.474-1.664-.474-2.704V32.211h3.042v9.287c0 .73.101 1.355.304 1.874.203.519.473.94.81 1.26.338.32.728.553 1.167.696.44.144.896.215 1.368.215.631 0 1.217-.1 1.758-.298.54-.2 1.014-.514 1.42-.946.405-.43.72-.978.946-1.642.225-.663.337-1.448.337-2.354V32.21h3.042v15.723h.002zm3.8-15.72h3.04v2.256h.069c.586-.84 1.391-1.493 2.416-1.958a7.591 7.591 0 013.16-.696c1.24 0 2.36.21 3.364.63a7.459 7.459 0 012.569 1.758 7.877 7.877 0 011.639 2.637 9.026 9.026 0 01.574 3.234c0 1.173-.192 2.261-.574 3.267a7.708 7.708 0 01-1.64 2.62 7.556 7.556 0 01-2.568 1.742c-1.003.42-2.124.63-3.363.63a7.556 7.556 0 01-3.245-.713c-1.014-.475-1.791-1.121-2.332-1.94h-.068v11.607h-3.042V32.212zm8.314 2.387c-.811 0-1.544.139-2.197.415a4.853 4.853 0 00-1.656 1.128 4.96 4.96 0 00-1.048 1.725 6.337 6.337 0 00-.372 2.205c0 .796.123 1.531.372 2.206.247.675.596 1.25 1.048 1.725.45.476 1.002.852 1.656 1.127.653.277 1.386.415 2.197.415.811 0 1.543-.137 2.197-.415a4.85 4.85 0 001.656-1.127c.45-.475.8-1.05 1.048-1.725.247-.674.372-1.41.372-2.206s-.124-1.53-.372-2.205a4.977 4.977 0 00-1.048-1.725 4.862 4.862 0 00-1.656-1.128c-.654-.276-1.387-.415-2.197-.415zm-161.916-5v3.573h-4.356v9.31c0 1.662.787 1.984 2.24 1.984.849 0 1.488-.163 1.792-.257l.324-.102v3.582l-.181.044c-1.223.31-2.245.413-3.752.413-1.044 0-4.447-.343-4.447-4.806V33.172h-2.557v-.24c-.003-.173-.003-1.402 0-2.546V29.6h2.557v-4.583l4.025-1.408v5.99h4.355zm27.023-5.841l-4.818 24.003h-4.089l-4.256-16.714-4.326 16.714h-4.023l-5.036-24.003h4.2l3.263 16.286 4.057-16.286h3.915c.388 1.522 4.064 16.085 4.112 16.266.028-.195 2.891-15.734 2.992-16.266h4.009zm4.17 13.042c.104-1.713 1.314-3.948 3.873-3.948 2.778 0 3.623 2.452 3.716 3.948h-7.589zm4.054-7.526c-3.068 0-8.248 1.998-8.248 9.493 0 8.924 7.106 9.379 8.53 9.379 3.016 0 4.364-.629 5.688-1.248l.144-.068v-3.796l-.382.23c-.961.6-2.823 1.274-4.911 1.274-4.224 0-4.82-3.018-4.875-4.289h11.611l.023-.205c.368-2.504.006-6.012-2.054-8.4-1.36-1.57-3.223-2.37-5.526-2.37m22.456 13.432c0 2.723-2.37 5.476-6.883 5.476-1.979 0-4.165-.49-5.736-1.248l-.141-.071v-3.897l.373.202c1.307.704 3.4 1.45 5.398 1.45 1.823 0 2.871-.652 2.871-1.794 0-1.072-.642-1.402-2.228-2.103l-.625-.266c-.768-.33-1.439-.626-2.543-1.133-1.06-.476-3.533-1.592-3.533-4.907 0-1.783 1.369-5.136 6.508-5.136 2.12 0 4.218.573 5.046.973l.147.072v3.829l-.373-.176c-1.64-.81-3.08-1.186-4.678-1.186-.588 0-2.53.117-2.53 1.475 0 1.03 1.224 1.57 2.212 2.013l.19.08c.712.314 1.273.575 1.755.766l.517.222c3.094 1.353 4.253 2.798 4.253 5.359m-90.888-18.948h3.927V47.76h-3.818L94.008 31.043v16.718h-3.921V23.758h3.917l10.38 16.844V23.758h-.002zm97.864 9.414v9.31c0 1.669.785 1.984 2.246 1.984a6.65 6.65 0 001.784-.25l.329-.109v3.582l-.207.044c-1.204.31-2.225.413-3.736.413-1.044 0-4.428-.343-4.428-4.806V33.172h-2.57v-.24c-.01-.173-.01-1.402 0-2.546V29.6h2.57v-4.583l4.012-1.408v5.99h4.359v3.574h-4.36zM121.47 42.46c-.463.568-1.907 2.07-4.159 2.07-1.729 0-2.835-.993-2.835-2.53 0-1.575 1.262-2.482 3.457-2.482h3.535v2.942h.002zm-3.098-13.242c-2.042 0-4.026.355-5.422.962l-.16.058v3.697l.365-.182c.944-.457 3.203-.873 4.62-.873 3.53 0 3.685 1.342 3.696 3.089h-3.773c-4.997 0-7.272 3.13-7.272 6.031 0 4.045 3.22 6.147 6.417 6.147 2.178 0 3.549-.815 4.654-1.794v1.409h3.98V35.489c-.001-5.66-4.972-6.271-7.105-6.271' fill='%233C1053'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 308px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-natwestGroup {
        height:32px;
        width: 142px
    }
}

.zb-champion-standard-theme .zb-brand-logo-dark-natwest {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3' fill='%23fff'/%3E%3Cpath d='M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 207px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-dark-natwest {
        height:32px;
        width: 94px
    }
}

.zb-champion-standard-theme .zb-brand-logo-dark-rb {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='244' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M72 36c0 33.387-2.613 36-36 36S0 69.387 0 36 2.613 0 36 0s36 2.613 36 36' fill='%233C1053'/%3E%3Cpath d='M32.948 28.005c-3.723 1.336-5.003 4.955-5.003 4.955-.501 1.335-1.831 2.337-3.332 2.337h-7.058c-1.944 0-3.555-1.616-3.555-3.562 0-1.95 1.333-3.507 3.555-3.507l5.598.01-8.1-8.135a3.531 3.531 0 010-5.005 3.518 3.518 0 015.002 0l8.057 8.054V17.6a3.546 3.546 0 013.555-3.558c1.949 0 3.504 1.612 3.504 3.558v7.066c0 1.503-.89 2.896-2.223 3.34m-1.28 29.938c-1.942 0-3.501-1.284-3.444-3.507l.01-5.641-8.123 8.143c-1.388 1.395-3.667 1.395-5 0-1.388-1.388-1.388-3.67 0-5.004l8.035-8.074h-5.535c-2 0-3.554-1.612-3.554-3.554a3.546 3.546 0 013.554-3.564h7.056c1.5 0 2.834.888 3.336 2.226 1.279 3.78 4.886 5.06 4.886 5.06a3.496 3.496 0 012.337 3.34v7.068c0 1.948-1.558 3.507-3.559 3.507M54.39 43.808l-5.608-.006 8.107 8.133a3.524 3.524 0 010 5.004 3.51 3.51 0 01-5 0l-8.055-8.068v5.566c0 2.117-1.613 3.563-3.557 3.563-1.946 0-3.557-1.615-3.557-3.563v-7.069c0-1.503.945-2.894 2.28-3.339 3.72-1.28 4.999-4.95 4.999-4.95.5-1.332 1.834-2.336 3.334-2.336h7.057c1.947 0 3.556 1.614 3.556 3.564 0 1.942-1.444 3.501-3.556 3.501m.056-8.568h-7.058c-1.499 0-2.89-.89-3.333-2.225-1.28-3.73-4.944-5.066-4.944-5.066-1.334-.501-2.335-1.78-2.335-3.34v-7.065c0-1.946 1.611-3.502 3.558-3.502 1.943 0 3.5 1.446 3.5 3.502l.001 5.663 8.11-8.165a3.522 3.522 0 015 0c1.39 1.387 1.39 3.669 0 5.061l-7.999 8.013h5.5C56.39 28.116 58 29.73 58 31.68c0 1.947-1.61 3.561-3.555 3.561' fill='%23FFF'/%3E%3Cpath d='M94.542 9.546c.44-.07 1.088-.1 1.733-.1 2.786 0 4.146 1.594 4.146 3.802 0 2.144-1.633 3.624-4.283 3.624-.68 0-1.29-.031-1.596-.066v-7.26zm0 10.533h.375l6.187 9.896h5.474l-5.338-8.024c-.546-.812-1.193-1.774-1.838-2.625 3.195-.546 5.645-3.114 5.645-6.52 0-4.698-3.266-6.97-8.33-6.97-2.655 0-4.795.034-6.6.137v24.002h4.425V20.08zm20.79 6.525c-2.483 0-3.774-1.766-3.774-5.243 0-3.469 1.906-4.97 3.844-4.97 2.548 0 3.739 1.77 3.739 5.042 0 3.437-1.666 5.171-3.81 5.171m.374-13.886c-5.133 0-8.638 3.809-8.638 9.088 0 6.023 3.131 8.507 7.922 8.507 5.036 0 8.64-3.812 8.64-9.122 0-5.821-3.094-8.473-7.924-8.473m19.062 18.212l6.697-17.871h-4.66l-2.003 5.753c-.544 1.562-1.26 3.846-1.632 5.376h-.037a66.358 66.358 0 00-1.324-4.663l-2.278-6.466h-4.866l6.258 16.916-.544 1.5c-.918 2.652-2.412 3.332-3.84 3.332a7.339 7.339 0 01-2.14-.34l-.512 3.746c.645.138 1.664.24 2.38.24 3.909 0 6.291-1.67 8.5-7.523m17.333-8.477v.446c0 2.04-1.424 4.08-3.534 4.08-1.26 0-2.039-.889-2.039-1.974 0-1.461 1.19-2.552 5.573-2.552m-4.925 7.86c2.395 0 4.377-1.207 5.266-2.972v2.634h4.149a63.837 63.837 0 01-.136-4.354V18.64c0-3.78-1.562-5.958-6.797-5.958-2.283 0-4.696.479-6.395 1.16l.713 3.335c1.495-.65 3.57-1.158 5.101-1.158 2.245 0 3.024.918 3.024 2.965v.61c-5.915 0-9.758 1.87-9.758 5.89 0 2.757 1.772 4.829 4.833 4.829m12.815-.338h4.354V5.125h-4.354v24.85zm19.187-10.77c.476-.033 1.63-.033 1.938-.033 3.027 0 4.863 1.129 4.863 3.308 0 2.009-1.464 3.99-4.355 3.99-.817 0-1.902-.07-2.446-.104v-7.162zm0-9.625c.712-.034 1.53-.069 2.481-.069 2.686 0 3.707 1.267 3.707 2.969 0 2.043-1.363 3.39-4.388 3.39-.71 0-1.256 0-1.8-.036V9.58zm11.526 12.764c0-2.555-1.666-4.735-4.114-5.249v-.069c2.208-.883 3.469-2.978 3.469-5.26 0-3.673-2.755-5.793-7.685-5.793-2.79 0-5.678.07-7.616.167v23.836c2.89 0 5.166.066 6.699.066 6.727 0 9.247-4.156 9.247-7.698zm12.298.555c0 2.04-1.428 4.08-3.535 4.08-1.259 0-2.04-.888-2.04-1.974 0-1.461 1.186-2.552 5.575-2.552v.446zm-9.76 2.585c0 2.757 1.769 4.829 4.832 4.829 2.396 0 4.38-1.207 5.268-2.974v2.637h4.148c-.103-1.393-.134-2.997-.134-4.356v-6.98c0-3.779-1.565-5.958-6.802-5.958-2.277 0-4.692.479-6.396 1.16l.716 3.334c1.498-.65 3.57-1.157 5.099-1.157 2.247 0 3.03.918 3.03 2.965v.61c-5.916 0-9.761 1.87-9.761 5.89zm22.012-3.678c0-2.964 1.732-5.447 3.91-5.447 1.633 0 2.107 1.055 2.107 2.792v10.824h4.317V18.093c0-2.961-1.221-5.375-5.032-5.375-2.588 0-4.475 1.24-5.542 3.03v-2.69H210.9v16.917h4.353v-8.169zm18.075.351l5.235 7.819H244l-6.683-9.19 6.246-7.728h-5.308l-4.928 6.215V5.125h-4.353v24.85h4.353v-7.818zM97.124 63.1c-2.482 0-3.777-1.774-3.777-5.241 0-3.476 1.905-4.975 3.845-4.975 2.55 0 3.742 1.77 3.742 5.041 0 3.435-1.668 5.175-3.81 5.175m.372-13.893c-5.132 0-8.635 3.815-8.635 9.092 0 6.027 3.13 8.51 7.922 8.51 5.031 0 8.636-3.814 8.636-9.123 0-5.82-3.093-8.479-7.923-8.479m12.362-1.002v1.393h-2.961v3.373h2.96v13.548h4.318V52.97h3.946v-3.373h-3.946v-1.43c0-2.244.848-3.232 2.755-3.232.64 0 1.427.068 2.108.307l.305-3.575c-.887-.203-1.836-.275-2.754-.275-4.624 0-6.731 2.25-6.731 6.812m20.386.7c0-1.632 1.19-2.756 3.773-2.756 1.36 0 2.686.238 3.947.645l.405-3.883a21.87 21.87 0 00-4.146-.436c-5.544 0-8.673 2.823-8.673 6.907 0 7.559 9.654 6.194 9.654 10.584 0 2.147-1.765 2.998-3.841 2.998a12.97 12.97 0 01-4.997-.99l-.475 4.253c1.53.412 3.265.616 5.304.616 5.305 0 8.875-3.167 8.875-7.421 0-7.284-9.826-6.432-9.826-10.518m20.15 14.13c-2.889 0-3.94-2.01-3.94-4.902 0-3.137 1.323-5.212 3.907-5.212 1.122 0 2.243.27 3.162.644l.68-3.742c-1.088-.376-2.415-.578-3.705-.578-5.849 0-8.605 4.048-8.605 9.326 0 5.755 2.927 8.206 7.617 8.206 1.634 0 3.436-.275 4.762-.782l-.58-3.647c-.985.446-2.14.686-3.297.686m13.103.067c-2.484 0-3.775-1.774-3.775-5.241 0-3.476 1.901-4.975 3.843-4.975 2.55 0 3.74 1.77 3.74 5.041 0 3.435-1.665 5.175-3.808 5.175m.373-13.893c-5.133 0-8.638 3.815-8.638 9.092 0 6.027 3.13 8.51 7.924 8.51 5.034 0 8.639-3.814 8.639-9.123 0-5.82-3.098-8.479-7.925-8.479m18.625 13.925c-1.497 0-2.003-.613-2.003-2.793v-7.42h4.113v-3.371h-4.113V42.46l-4.32 1.155v5.932h-2.925v3.372h2.925v8.576c0 3.95 1.157 5.312 4.895 5.312 1.19 0 2.586-.202 3.675-.477l-.308-3.437c-.578.169-1.222.238-1.939.238m5.013 3.387h4.35V41.664h-4.35v24.854zm16.625-7.09c0 2.046-1.426 4.088-3.532 4.088-1.26 0-2.042-.888-2.042-1.978 0-1.461 1.188-2.552 5.574-2.552v.442zm4.351-4.256c0-3.776-1.561-5.955-6.8-5.955-2.275 0-4.69.477-6.39 1.158l.713 3.334c1.5-.645 3.57-1.155 5.1-1.155 2.247 0 3.026.916 3.026 2.96v.613c-5.916 0-9.757 1.875-9.757 5.889 0 2.76 1.769 4.834 4.826 4.834 2.402 0 4.39-1.216 5.272-2.98v2.642h4.15a63.138 63.138 0 01-.14-4.357v-6.983zm12.562-5.915c-2.59 0-4.479 1.245-5.544 3.034v-2.694h-4.112v16.921h4.353v-8.17c0-2.961 1.733-5.448 3.908-5.448 1.632 0 2.109 1.054 2.109 2.79v10.828h4.32V54.636c0-2.962-1.224-5.38-5.034-5.38m18.635 8.464c0 2.891-1.362 5.548-3.64 5.548-1.972 0-2.888-1.636-2.888-4.8 0-4.19 1.767-5.753 4.25-5.753.746 0 1.498.101 2.278.272v4.733zm0-16.073v7.765a14.868 14.868 0 00-2.141-.135c-5.614 0-8.946 3.945-8.946 9.668 0 4.833 2.042 7.93 6.157 7.93 2.465 0 4.272-1.214 5.2-2.954v2.58H244V41.647h-4.32z' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 244px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-dark-rb {
        height:32px;
        width: 110px
    }
}

.zb-champion-standard-theme .zb-brand-logo-dark-ulsterbank {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='261' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M143.131 22.991l-4.332 1.217v5.487h-3.526v3.664h3.526v9.715c0 4.354 1.415 5.857 5.304 5.857 1.095 0 2.47-.128 3.722-.408l.171-.04v-3.704c-.972.29-1.86.329-2.633.329-1.623 0-2.196-.491-2.196-3.25v-8.499h4.828v-3.664h-4.865V22.99l.001.001zm-14.244 14.474c-1.945-.812-3.083-1.379-3.083-2.685 0-1.583 1.456-2.027 3.21-2.027 1.331 0 2.71.282 3.635.606v-3.7a20.787 20.787 0 00-3.884-.366c-4.5 0-7.46 2.119-7.46 5.731 0 3.42 2.194 4.556 4.946 5.737 1.904.774 3.16 1.219 3.16 2.637s-1.056 1.996-3.207 1.996c-1.816 0-3.316-.369-4.41-.813l-.491 3.859c1.296.324 3.085.533 4.543.533 4.9 0 8.058-2.115 8.058-6.018 0-3.49-2.589-4.435-5.018-5.49h.001zm-15.14-14.385h4.379v25.522h-4.379V23.08zm-8.022 14.878c0 .85 0 1.988-.128 2.682-.36 3.134-1.94 4.554-5.5 4.554-3.25 0-4.91-1.222-5.396-3.657-.158-.774-.236-2.202-.236-3.13V23.08h-4.454v15.687c0 1.177.079 2.599.32 3.783.929 4.183 4.012 6.423 9.399 6.423 5.955 0 9.393-2.484 10.17-7.316.152-.979.197-2.476.197-3.659V23.08h-4.372v14.878zm53.06-8.618c-5.225 0-8.629 3.897-8.629 9.916 0 6.466 3.04 9.718 9.115 9.718 2.383 0 4.577-.405 6.484-1.218l.117-.043-.397-3.618-.288.118c-1.503.773-3.443 1.183-5.263 1.183-1.82 0-3.045-.445-3.93-1.341-.894-.98-1.345-2.396-1.345-4.313v-.162h11.745l.125-2.032c0-5.204-2.835-8.21-7.734-8.21v.002zm-4.009 7.03c.443-2.28 1.824-3.698 3.89-3.698 1.011 0 1.863.32 2.51.975.649.65.97 1.543.97 2.726h-7.37v-.002zm18.688-3.698c.119-1.1.193-2.076.193-2.77v-.206h-4.205v18.908h4.414v-7.722c0-3.374.646-5.564 1.904-6.585a3.485 3.485 0 012.224-.774c.326 0 .773.038 1.304.164l.201-4.347c-.408-.045-.65-.045-.894-.045-2.756 0-4.38 1.746-5.137 3.377h-.004zm83.843 10.924c-1.816-2.479-3.726-5.116-3.805-5.198.079-.083 1.863-2.277 3.565-4.436 1.012-1.256 2.182-2.64 3.443-4.231h-5.39c-.929 1.265-1.823 2.404-2.592 3.42a292.823 292.823 0 01-3.07 4.066V23.116h-4.385v25.522h4.385v-8.901c.284.486 1.82 2.766 3.241 4.836.844 1.262 1.739 2.601 2.754 4.065H261a1069.018 1069.018 0 01-3.69-5.042h-.003zm-21.435-14.222c-2.717 0-4.823 1.138-6.117 3.21.12-.933.12-1.869.12-2.646v-.206h-4.211V48.64h4.42v-9.137c0-3.708 1.944-6.432 4.572-6.432 1.826 0 2.554 1.021 2.554 3.46V48.64h4.339V35.476c0-4.026-1.95-6.1-5.675-6.1l-.002-.002zm-13.655 6.417c0-4.426-2.23-6.417-7.416-6.417-2.145 0-4.458.409-6.524 1.217l-.162.085.407 3.859.284-.162c1.418-.933 3.57-1.464 5.39-1.464 2.59 0 3.642.897 3.642 3.289v.981c-6.841.04-10.853 2.561-10.853 6.914 0 3.05 2.066 4.92 5.347 4.92 2.47 0 4.701-1.144 5.87-3.05a24.01 24.01 0 00-.118 2.482v.196h4.25v-.235c-.117-1.47-.117-3.298-.117-5.048v-7.567zm-4.38 4.64c0 2.44-1.66 5.037-4.331 5.037-1.415 0-2.313-.817-2.313-2.07 0-2.036 2.233-3.094 6.645-3.134v.168zm-17.81-5.207c1.913-.725 3.893-2.478 3.893-5.609 0-4.06-3.237-6.543-8.22-6.543h-8.183v25.533h7.778c6.03 0 9.474-3.094 9.474-7.563 0-3.298-2.104-5.166-4.743-5.816v-.002zm-8.055-8.575h3.48c2.393 0 3.732.98 3.732 3.371 0 2.393-1.818 3.662-4.14 3.662h-3.072v-7.033zm3.647 18.248h-3.647v-7.644h3.436c2.761 0 4.58 1.301 4.58 3.785 0 2.725-1.904 3.86-4.371 3.86h.002z' fill='%23fff'/%3E%3Cg fill-rule='nonzero'%3E%3Cpath d='M71.994 35.998C71.994 69.388 69.382 72 35.999 72 2.615 72 0 69.388 0 35.998S2.612 0 35.999 0s35.995 2.612 35.995 35.998z' fill='%233C1053'/%3E%3Cpath d='M28.3 23.108v-5.331a3.433 3.433 0 016.786-.817c.065.311.096.628.092.945v6.726a3.42 3.42 0 01-2.35 3.503 8.389 8.389 0 00-4.637 4.637c-.772 1.753-1.818 2.493-3.71 2.493h-6.693a3.489 3.489 0 01-3.242-2.756 3.362 3.362 0 011.887-3.875 4.85 4.85 0 011.62-.323c1.726-.036 3.457 0 5.298 0-.15-.189-.234-.316-.34-.423-2.478-2.538-4.964-5.07-7.457-7.6a3.525 3.525 0 01-.5-4.461 3.424 3.424 0 015.37-.46c2.393 2.387 4.761 4.794 7.14 7.2.19.22.366.45.528.69.068-.05.139-.092.207-.139M48.483 43.693c1.51 1.525 2.883 2.907 4.25 4.293l3.674 3.719a3.504 3.504 0 01.135 4.952l-.041.041a3.384 3.384 0 01-4.784.119c-.05-.047-.097-.097-.142-.142-2.47-2.466-4.912-4.954-7.345-7.429-.139-.142-.29-.275-.537-.51v5.62a3.379 3.379 0 01-3.277 3.402c-1.81.083-3.517-1.258-3.586-3.072a105.79 105.79 0 010-7.926 3.02 3.02 0 012.131-2.791 8.183 8.183 0 004.808-4.684c.734-1.717 1.915-2.567 3.769-2.534 2.185.036 4.375 0 6.566 0a3.277 3.277 0 013.08 2.016 3.352 3.352 0 01-.38 3.636 2.949 2.949 0 01-2.36 1.249c-1.708.036-3.416 0-5.128 0l-.826.036-.007.005zM28.25 48.717c-.917.95-1.803 1.91-2.726 2.848a501.377 501.377 0 01-4.959 5.018c-1.226 1.217-2.755 1.498-4.132.8a3.521 3.521 0 01-1.525-4.742v-.002c.178-.34.409-.65.684-.918 2.007-2.06 4.035-4.104 6.06-6.149.542-.55 1.102-1.092 1.744-1.726-.27-.032-.417-.065-.568-.065h-4.876a3.51 3.51 0 01-1.255-6.764c.474-.16.97-.24 1.469-.234h6.566c1.836 0 2.85.758 3.522 2.466a8.263 8.263 0 004.591 4.678c1.611.693 2.378 1.782 2.387 3.559v6.791a3.411 3.411 0 01-2.214 3.215 3.568 3.568 0 01-3.815-.85 3.093 3.093 0 01-.794-2.14v-5.69l-.166-.091M48.731 28.192h5.284a3.514 3.514 0 013.215 5.05 3.393 3.393 0 01-3.215 2.02c-2.25.032-4.5.047-6.753 0a3.411 3.411 0 01-3.407-2.383 8.323 8.323 0 00-4.71-4.808 3.586 3.586 0 01-2.342-3.595V17.81a3.425 3.425 0 013.348-3.494h.092a3.323 3.323 0 013.438 3.206v5.661s0 .036.11.19c.615-.658 1.198-1.304 1.803-1.925 2.016-2.057 4.022-4.133 6.056-6.149a3.443 3.443 0 015.749 3.393 5.171 5.171 0 01-1.184 1.887c-2.295 2.377-4.619 4.701-6.933 7.048-.16.157-.308.323-.56.593' fill='%23FFF'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 261px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-dark-ulsterbank {
        height:32px;
        width: 116px
    }
}

.zb-champion-standard-theme .zb-brand-logo-dark-natwestGroup {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='308' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.974%25' y1='41.675%25' x2='75.006%25' y2='58.313%25' id='a'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.65%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.33%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.05%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='99.97%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.105%25' y1='35.504%25' x2='36.235%25' y2='59.456%25' id='b'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.66%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.34%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.07%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.44%25' y1='29.336%25' x2='63.811%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%235A287D' stop-opacity='0' offset='0%25'/%3E%3Cstop stop-color='%235C297F' stop-opacity='.013' offset='1.42%25'/%3E%3Cstop stop-color='%23863FB6' stop-opacity='.312' offset='34.66%25'/%3E%3Cstop stop-color='%23A54FDE' stop-opacity='.57' offset='63.34%25'/%3E%3Cstop stop-color='%23B858F6' stop-opacity='.775' offset='86.07%25'/%3E%3Cstop stop-color='%23BF5CFF' stop-opacity='.9' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cg%3E%3Cellipse fill='%233C1053' cx='36.035' cy='36' rx='36.035' ry='36'/%3E%3Cpath d='M72.069 36c0 33.387-2.615 36-36.035 36C2.615 72 0 69.387 0 36S2.615 0 36.034 0C69.454 0 72.07 2.613 72.07 36' fill='%233C1053'/%3E%3Cg fill='%23BF5CFF'%3E%3Cpath d='M46.798 43.914h-14.34l7.166 12.414h14.345zM43.206 12.894h-14.34l-7.167 12.402h14.339zM25.278 43.912l7.166-12.401h-14.34l-7.166 12.401-.005.005z'/%3E%3C/g%3E%3Cg fill='%238F52D1'%3E%3Cpath d='M53.966 31.512l7.17 12.407-7.167 12.409-7.171-12.414zM36.038 25.296l7.171 12.411 7.168-12.405-7.171-12.408zM32.45 56.328H18.103l-7.171-12.41.005-.006h14.34z'/%3E%3C/g%3E%3Cpath fill='url(%23a)' d='M46.786 31.512l-3.577 6.194h-7.173l-3.58 6.208h14.342l7.168-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.298 31.51h7.146l3.592 6.196h7.173l-7.171-12.41h-14.34z'/%3E%3Cpath fill='url(%23c)' d='M36.031 50.106l-3.575-6.192 3.58-6.208-3.592-6.195-7.166 12.401 7.17 12.416z'/%3E%3C/g%3E%3Cpath d='M237.538 34.799v11.575c-.586.354-1.25.664-1.994.93-.743.264-1.516.491-2.315.68a21.727 21.727 0 01-4.749.547c-2.028 0-3.835-.332-5.424-.995-1.588-.664-2.93-1.559-4.022-2.687a11.532 11.532 0 01-2.5-3.93 13.044 13.044 0 01-.863-4.727c0-1.746.31-3.371.93-4.875a12.013 12.013 0 012.568-3.914c1.093-1.105 2.405-1.973 3.938-2.604 1.532-.63 3.21-.945 5.036-.945 2.208 0 4.01.27 5.407.813 1.398.542 2.57 1.288 3.516 2.238l-2.299 2.422c-.991-.93-2.04-1.576-3.143-1.94a11.02 11.02 0 00-3.481-.548c-1.375 0-2.614.248-3.718.746a8.824 8.824 0 00-2.84 2.006 8.806 8.806 0 00-1.824 2.97 10.224 10.224 0 00-.642 3.63c0 1.327.236 2.56.71 3.699a9.144 9.144 0 001.96 2.969 9.036 9.036 0 002.94 1.973c1.127.476 2.354.713 3.684.713 1.15 0 2.237-.104 3.262-.315 1.025-.21 1.897-.514 2.62-.912v-6.533h-5.307V34.8h8.55v-.001zm4.552-2.587h3.041v2.421h.068c.203-.42.474-.802.812-1.144a5.424 5.424 0 011.132-.88 6.074 6.074 0 011.368-.58 5.31 5.31 0 011.488-.215c.495 0 .946.067 1.352.2l-.135 3.217a8.854 8.854 0 00-.744-.166 4.23 4.23 0 00-.743-.067c-1.487 0-2.625.41-3.413 1.227-.79.818-1.184 2.09-1.184 3.814v7.894h-3.041V32.212h-.001zm11.104 7.86c0-1.15.22-2.222.659-3.217a8.39 8.39 0 011.808-2.62 8.479 8.479 0 012.704-1.776 8.624 8.624 0 013.346-.646c1.194 0 2.31.215 3.346.646a8.458 8.458 0 012.704 1.775 8.385 8.385 0 011.808 2.62c.44.996.66 2.068.66 3.218a7.99 7.99 0 01-.66 3.234 8.124 8.124 0 01-1.808 2.62 8.595 8.595 0 01-2.704 1.757 8.627 8.627 0 01-3.346.647 8.62 8.62 0 01-3.346-.647 8.595 8.595 0 01-2.704-1.758 8.124 8.124 0 01-1.808-2.62 7.99 7.99 0 01-.66-3.233zm3.245 0c0 .796.124 1.531.372 2.206.247.675.596 1.25 1.048 1.725.45.476 1.002.852 1.656 1.127.653.277 1.386.415 2.197.415.811 0 1.543-.137 2.197-.415a4.85 4.85 0 001.656-1.127c.45-.475.8-1.05 1.048-1.725.247-.674.372-1.41.372-2.206s-.124-1.53-.372-2.205a4.977 4.977 0 00-1.048-1.725 4.862 4.862 0 00-1.656-1.128c-.654-.276-1.386-.415-2.197-.415-.811 0-1.544.139-2.197.415a4.853 4.853 0 00-1.656 1.128 4.96 4.96 0 00-1.048 1.725 6.337 6.337 0 00-.372 2.205zm30.93 7.86h-3.04v-2.42h-.068c-.384.84-1.048 1.52-1.995 2.04-.946.519-2.04.779-3.278.779a7.04 7.04 0 01-2.23-.348 5.023 5.023 0 01-1.843-1.079c-.529-.486-.952-1.111-1.267-1.873-.316-.764-.474-1.664-.474-2.704V32.211h3.042v9.287c0 .73.101 1.355.304 1.874.203.519.473.94.81 1.26.338.32.728.553 1.167.696.44.144.896.215 1.368.215.631 0 1.217-.1 1.758-.298.54-.2 1.014-.514 1.42-.946.405-.43.72-.978.946-1.642.225-.663.337-1.448.337-2.354V32.21h3.042v15.723h.002zm3.8-15.72h3.04v2.256h.069c.586-.84 1.391-1.493 2.416-1.958a7.591 7.591 0 013.16-.696c1.24 0 2.36.21 3.364.63a7.459 7.459 0 012.569 1.758 7.877 7.877 0 011.639 2.637 9.026 9.026 0 01.574 3.234c0 1.173-.192 2.261-.574 3.267a7.708 7.708 0 01-1.64 2.62 7.556 7.556 0 01-2.568 1.742c-1.003.42-2.124.63-3.363.63a7.556 7.556 0 01-3.245-.713c-1.014-.475-1.791-1.121-2.332-1.94h-.068v11.607h-3.042V32.212zm8.314 2.387c-.811 0-1.544.139-2.197.415a4.853 4.853 0 00-1.656 1.128 4.96 4.96 0 00-1.048 1.725 6.337 6.337 0 00-.372 2.205c0 .796.123 1.531.372 2.206.247.675.596 1.25 1.048 1.725.45.476 1.002.852 1.656 1.127.653.277 1.386.415 2.197.415.811 0 1.543-.137 2.197-.415a4.85 4.85 0 001.656-1.127c.45-.475.8-1.05 1.048-1.725.247-.674.372-1.41.372-2.206s-.124-1.53-.372-2.205a4.977 4.977 0 00-1.048-1.725 4.862 4.862 0 00-1.656-1.128c-.654-.276-1.387-.415-2.197-.415zm-161.916-5v3.573h-4.356v9.31c0 1.662.787 1.984 2.24 1.984.849 0 1.488-.163 1.792-.257l.324-.102v3.582l-.181.044c-1.223.31-2.245.413-3.752.413-1.044 0-4.447-.343-4.447-4.806V33.172h-2.557v-.24c-.003-.173-.003-1.402 0-2.546V29.6h2.557v-4.583l4.025-1.408v5.99h4.355zm27.023-5.841l-4.818 24.003h-4.089l-4.256-16.714-4.326 16.714h-4.023l-5.036-24.003h4.2l3.263 16.286 4.057-16.286h3.915c.388 1.522 4.064 16.085 4.112 16.266.028-.195 2.891-15.734 2.992-16.266h4.009zm4.17 13.042c.104-1.713 1.314-3.948 3.873-3.948 2.778 0 3.623 2.452 3.716 3.948h-7.589zm4.054-7.526c-3.068 0-8.248 1.998-8.248 9.493 0 8.924 7.106 9.379 8.53 9.379 3.016 0 4.364-.629 5.688-1.248l.144-.068v-3.796l-.382.23c-.961.6-2.823 1.274-4.911 1.274-4.224 0-4.82-3.018-4.875-4.289h11.611l.023-.205c.368-2.504.006-6.012-2.054-8.4-1.36-1.57-3.223-2.37-5.526-2.37m22.456 13.432c0 2.723-2.37 5.476-6.883 5.476-1.979 0-4.165-.49-5.736-1.248l-.141-.071v-3.897l.373.202c1.307.704 3.4 1.45 5.398 1.45 1.823 0 2.871-.652 2.871-1.794 0-1.072-.642-1.402-2.228-2.103l-.625-.266c-.768-.33-1.439-.626-2.543-1.133-1.06-.476-3.533-1.592-3.533-4.907 0-1.783 1.369-5.136 6.508-5.136 2.12 0 4.218.573 5.046.973l.147.072v3.829l-.373-.176c-1.64-.81-3.08-1.186-4.678-1.186-.588 0-2.53.117-2.53 1.475 0 1.03 1.224 1.57 2.212 2.013l.19.08c.712.314 1.273.575 1.755.766l.517.222c3.094 1.353 4.253 2.798 4.253 5.359m-90.888-18.948h3.927V47.76h-3.818L94.008 31.043v16.718h-3.921V23.758h3.917l10.38 16.844V23.758h-.002zm97.864 9.414v9.31c0 1.669.785 1.984 2.246 1.984a6.65 6.65 0 001.784-.25l.329-.109v3.582l-.207.044c-1.204.31-2.225.413-3.736.413-1.044 0-4.428-.343-4.428-4.806V33.172h-2.57v-.24c-.01-.173-.01-1.402 0-2.546V29.6h2.57v-4.583l4.012-1.408v5.99h4.359v3.574h-4.36zM121.47 42.46c-.463.568-1.907 2.07-4.159 2.07-1.729 0-2.835-.993-2.835-2.53 0-1.575 1.262-2.482 3.457-2.482h3.535v2.942h.002zm-3.098-13.242c-2.042 0-4.026.355-5.422.962l-.16.058v3.697l.365-.182c.944-.457 3.203-.873 4.62-.873 3.53 0 3.685 1.342 3.696 3.089h-3.773c-4.997 0-7.272 3.13-7.272 6.031 0 4.045 3.22 6.147 6.417 6.147 2.178 0 3.549-.815 4.654-1.794v1.409h3.98V35.489c-.001-5.66-4.972-6.271-7.105-6.271' fill='%23FFF'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 308px;
    height: 50px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-brand-logo-dark-natwestGroup {
        height:32px;
        width: 142px
    }
}

.zb-champion-standard-theme .zb-button-group input:focus+.zb-button-group-item {
    outline-color: #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-button-group .zb-button-group-item {
    position: relative
}

.zb-champion-standard-theme .zb-button-group .zb-button-group-item:after {
    height: 56%;
    width: 1px;
    border-right: 1px solid #cccfd0;
    content: "";
    position: absolute;
    top: 10px;
    right: 0
}

.zb-champion-standard-theme .zb-button-group .zb-button-group-item-is-selected:after {
    display: none
}

.zb-champion-standard-theme .zb-button-group .zb-button-group-item:last-of-type:after {
    border-right: none
}

.zb-champion-standard-theme .zb-button-group .zb-button-group-item-is-selected {
    color: #fff
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-alternate .zb-button-group-item:hover {
    background: hsla(0,0%,100%,.15)
}

.zb-champion-standard-theme .zb-button-group.zb-button-group-alternate .zb-button-group-item.zb-button-group-item-is-selected:hover {
    background: #fff
}

.zb-champion-standard-theme .zb-date-picker-trigger {
    border-radius: 8px
}

.zb-champion-standard-theme .zb-date-picker-trigger:hover {
    border: 1px solid #5e10b1;
    -webkit-box-shadow: 0 0 0 1px #5e10b1;
    box-shadow: 0 0 0 1px #5e10b1
}

.zb-champion-standard-theme .zb-date-picker-trigger:hover.zb-date-picker-trigger-is-disabled {
    border: 1px solid #e0e2e3;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-focused {
    -webkit-box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #fff,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-is-error,.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-is-error:hover {
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-is-error.zb-date-picker-trigger-is-focused {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-date-picker-trigger.zb-date-picker-trigger-is-disabled .zb-date-picker-input {
    color: #999
}

.zb-champion-standard-theme .zb-date-picker-trigger .zb-date-picker-icon {
    outline-offset: -1px
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused,.zb-champion-standard-theme .zb-month-calendar-month-is-focused {
    border-width: 1px
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused:focus,.zb-champion-standard-theme .zb-month-calendar-month-is-focused:focus {
    border-width: 2px
}

.zb-champion-standard-theme .zb-date-picker .pika-button.zb-date-picker-day-is-focused:hover,.zb-champion-standard-theme .zb-month-calendar-month-is-focused:hover {
    background-color: #f2eaf9
}

.zb-champion-standard-theme .is-selected .pika-button:focus,.zb-champion-standard-theme .zb-date-picker .is-selected .pika-button:hover {
    background-color: #5e10b1
}

.zb-champion-standard-theme .zb-date-picker .is-disabled.is-outside-current-month .pika-button,.zb-champion-standard-theme .zb-date-picker .is-disabled .pika-button {
    cursor: default;
    color: #646068
}

.zb-champion-standard-theme .zb-date-picker .is-disabled.is-outside-current-month .pika-button:hover,.zb-champion-standard-theme .zb-date-picker .is-disabled .pika-button:hover,.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:focus,.zb-champion-standard-theme .zb-month-calendar-month-is-disabled.zb-month-calendar-month:hover {
    background-color: #fff;
    border: none;
    color: #646068
}

.zb-champion-standard-theme .zb-date-picker .pika-button {
    border-radius: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-date-picker .pika-button {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-month-calendar-month {
    border-radius: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-month-calendar-month {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-year-calendar-year {
    border-radius: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-year-calendar-year {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-date-stepper-label {
    text-decoration: none;
    color: #646068
}

.zb-champion-standard-theme .zb-date-stepper-button:hover .zb-icon {
    color: #3c1053
}

.zb-champion-standard-theme .zb-date-stepper-button.zb-date-stepper-button-is-disabled:hover .zb-icon {
    color: #bf9fe0
}

.zb-champion-standard-theme .zb-lookup-input-wrapper:hover {
    border: 1px solid #5e10b1;
    -webkit-box-shadow: 0 0 0 1px #5e10b1;
    box-shadow: 0 0 0 1px #5e10b1
}

.zb-champion-standard-theme .zb-lookup-input-wrapper.zb-lookup-input-wrapper-is-disabled:hover {
    border: 1px solid #e0e2e3;
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-lookup-input-wrapper.zb-lookup-input-wrapper-is-error:hover {
    -webkit-box-shadow: 0 0 0 1px #cf223f;
    box-shadow: 0 0 0 1px #cf223f
}

.zb-champion-standard-theme .zb-lookup-input-wrapper.zb-lookup-input-wrapper-is-error.zb-lookup-input-wrapper-is-focused {
    -webkit-box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #cf223f,0 0 0 3px #5e10b1
}

.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper-is-disabled .zb-icon,.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper-is-disabled .zb-lookup-input,.zb-champion-standard-theme .zb-combobox .zb-lookup-input-wrapper-is-disabled .zb-select-input-wrapper,.zb-champion-standard-theme .zb-select .zb-lookup-input-wrapper-is-disabled .zb-icon,.zb-champion-standard-theme .zb-select .zb-lookup-input-wrapper-is-disabled .zb-lookup-input,.zb-champion-standard-theme .zb-select .zb-lookup-input-wrapper-is-disabled .zb-select-input-wrapper {
    cursor: auto
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused .zb-multiselect-dropdown-list-item-icon {
    border-color: #5e10b1
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-focused.zb-multiselect-dropdown-list-item-is-selected .zb-multiselect-dropdown-list-item-icon {
    border-color: #3c1053;
    background-color: #3c1053
}

.zb-champion-standard-theme .zb-dropdown-list-group-label:hover .zb-multiselect-dropdown-list-group-label-icon {
    border-color: #5e10b1
}

.zb-champion-standard-theme .zb-dropdown-list-group-label:hover .zb-multiselect-dropdown-list-group-is-selected .zb-multiselect-dropdown-list-group-label-icon {
    border-color: #3c1053;
    background-color: #3c1053
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon,.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
    padding: 2px
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-is-selected .zb-multiselect-dropdown-list-group-label-icon {
    background: #5e10b1;
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-multiselect-dropdown-list-group-is-selected .zb-multiselect-dropdown-list-group-label-icon .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-dropdown-list-item-is-disabled {
    background-color: #f2f2f8
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-multiselect-dropdown-list-group-label-icon,.zb-champion-standard-theme .zb-multiselect-dropdown-list-item-icon {
        padding:1px
    }
}

.zb-champion-standard-theme .zb-flyout-close-button:hover .zb-icon {
    color: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-input+label {
    color: #333
}

.zb-champion-standard-theme .zb-checkbox-input:disabled+label {
    color: #c1bfc3
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error {
    border-color: transparent
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error .zb-checkbox {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error .zb-checkbox.zb-checkbox-is-checked {
    background: #cf223f;
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-error .zb-checkbox.zb-checkbox-is-checked .zb-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-checkbox-labelled-is-focused {
    border-radius: 8px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-checkbox-labelled-is-focused {
        border-radius:4px
    }
}

.zb-champion-standard-theme .zb-checkbox-is-focused {
    border: 2px solid #646068;
    -webkit-box-shadow: 0 0 0 2px #f2f2f8,0 0 0 4px #5e10b1;
    box-shadow: 0 0 0 2px #f2f2f8,0 0 0 4px #5e10b1
}

.zb-champion-standard-theme .zb-checkbox-is-focused.zb-checkbox-is-checked {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-checkbox-is-focused.zb-checkbox-is-error {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-checkbox-labelled .zb-checkbox-is-focused {
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-checkbox-labelled:hover .zb-checkbox {
    border-color: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-labelled:hover .zb-checkbox.zb-checkbox-is-checked {
    background: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-labelled:hover .zb-checkbox.zb-checkbox-is-disabled {
    border: 2px solid #ebecec
}

.zb-champion-standard-theme .zb-checkbox-labelled:hover .zb-checkbox.zb-checkbox-is-disabled.zb-checkbox-is-checked {
    border: 2px solid #bf9fe0
}

.zb-champion-standard-theme .zb-radio-button-input+label {
    color: #333
}

.zb-champion-standard-theme .zb-radio-button-input:disabled+label {
    color: #adadad
}

.zb-champion-standard-theme .zb-radio-button-labelled:hover .zb-radio-button {
    border-color: #3c1053
}

.zb-champion-standard-theme .zb-radio-button-labelled:hover .zb-radio-button.zb-radio-button-is-checked .zb-radio-button-circle {
    background: #3c1053
}

.zb-champion-standard-theme .zb-radio-button-labelled:hover .zb-radio-button.zb-radio-button-is-disabled {
    border: 2px solid #c1bfc3
}

.zb-champion-standard-theme .zb-radio-button-labelled:hover .zb-radio-button.zb-radio-button-is-disabled.zb-radio-button-is-checked {
    border: 2px solid #bf9fe0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-labelled:hover .zb-radio-button.zb-radio-button-is-disabled.zb-radio-button-is-checked {
        border:2px solid #8e58c8
    }
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error {
    border-color: transparent
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button .zb-radio-button-circle {
    background: #cf223f
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-error .zb-radio-button-is-error {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-radio-button-labelled-is-focused {
    border-radius: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-labelled-is-focused {
        border-radius:8px
    }
}

.zb-champion-standard-theme .zb-radio-button-is-focused {
    border: 2px solid #646068;
    -webkit-box-shadow: 0 0 0 2px #f2f2f8,0 0 0 4px #5e10b1;
    box-shadow: 0 0 0 2px #f2f2f8,0 0 0 4px #5e10b1
}

.zb-champion-standard-theme .zb-radio-button-is-focused.zb-radio-button.zb-radio-button-is-checked {
    border: 2px solid #5e10b1
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-focused.zb-radio-button.zb-radio-button-is-checked {
        border:2px solid #5e10b1
    }
}

.zb-champion-standard-theme .zb-radio-button-is-focused.zb-radio-button.zb-radio-button-is-error {
    border: 2px solid #cf223f
}

.zb-champion-standard-theme .zb-radio-button-labelled .zb-radio-button-is-focused {
    -webkit-box-shadow: none;
    box-shadow: none
}

.zb-champion-standard-theme .zb-checkbox:hover,.zb-champion-standard-theme .zb-radio-button:hover {
    border-color: #5e10b1
}

.zb-champion-standard-theme .zb-radio-button-is-checked:hover {
    border-color: #3c1053
}

.zb-champion-standard-theme .zb-radio-button-is-checked:hover .zb-radio-button-circle {
    background: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-is-checked:hover {
    background: #3c1053;
    border-color: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-is-checked:hover .zb-checkbox-tick {
    background: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-is-checked.zb-checkbox-is-error {
    background: #cf223f
}

.zb-champion-standard-theme .zb-checkbox-is-checked.zb-checkbox-is-error:hover {
    background: #3c1053
}

.zb-champion-standard-theme .zb-checkbox-is-disabled:hover {
    border: 2px solid #ebecec
}

.zb-champion-standard-theme .zb-checkbox-is-disabled:hover.zb-checkbox-is-checked {
    border: 2px solid #bf9fe0;
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-checkbox-is-disabled:hover.zb-checkbox-is-checked .zb-checkbox-tick {
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-radio-button-is-disabled:hover {
    border: 2px solid #c1bfc3
}

.zb-champion-standard-theme .zb-radio-button-is-disabled:hover.zb-radio-button-is-checked {
    border: 2px solid #bf9fe0
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-button-is-disabled:hover.zb-radio-button-is-checked {
        border:2px solid #8e58c8
    }
}

.zb-champion-standard-theme .zb-radio-button-is-disabled:hover.zb-radio-button-is-checked .zb-radio-button-circle {
    background: #bf9fe0
}

.zb-champion-standard-theme .zb-masthead-container {
    padding-left: 20px;
    padding-right: 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-container {
        padding-right:0;
        padding-left: 0
    }
}

.zb-champion-standard-theme .zb-masthead-container .zb-masthead .zb-masthead-brand-logo {
    background-size: cover
}

.zb-champion-standard-theme .zb-masthead-container .zb-masthead .zb-masthead-nav-list>li {
    padding: 12px 0 12px 20px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-container .zb-masthead .zb-masthead-nav-list>li {
        padding:0
    }
}

.zb-champion-standard-theme .zb-masthead-container .zb-masthead+.zb-nav>ul {
    border-top: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-masthead-logout,.zb-champion-standard-theme .zb-masthead-toggler {
    background: #5e10b1
}

.zb-champion-standard-theme .zb-masthead-dark-container,.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead {
    background: #5a287d
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead a:focus {
    outline: 2px solid #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-brand-name,.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-item {
    color: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-brand-logo {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3' fill='%23fff'/%3E%3Cpath d='M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    height: 50px;
    width: 144px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-brand-logo {
        width:92px;
        height: 32px
    }
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-brand-logo.zb-masthead-brand-logo-asset {
    background-image: url(/CWSLogon/resources/images/zb-champion-standard/natwest_dark_brand_logo.svg)
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-brand-logo {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3' fill='%23fff'/%3E%3Cpath d='M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    height: 50px;
    width: 144px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-brand-logo {
        width:92px;
        height: 32px
    }
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-brand-logo.zb-masthead-brand-logo-asset {
    background-image: url(/CWSLogon/resources/images/zb-champion-standard/natwest_dark_brand_logo.svg)
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead+.zb-nav {
    background: #5a287d;
    border-top: 1px solid #5a287d
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead+.zb-nav>ul {
    border-top: 1px solid #3c1053
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item-label {
    color: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item {
    background: #5a287d
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label {
    color: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label:after {
    background: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item:hover .zb-nav-item-label {
    color: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item.zb-nav-item-is-open,.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item:hover .zb-nav-item-label:after {
    background: #fff
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-item.zb-nav-item-is-open:hover .zb-nav-item-label {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav a:focus,.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav span.zb-nav-item-label:focus,.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav span.zb-nav-menu-item-label:focus {
    outline: 2px solid #fff
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label {
        padding:0 15px 0 12px
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-submenu {
        padding-left: 0
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label {
        padding-left: 30px
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-item {
        color: #5e10b1
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-logout {
        background: transparent
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-toggler {
        background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='14'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M0 0h18v2H0zm0 6h18v2H0zm0 6h18v2H0z'/%3E%3C/svg%3E")
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav.zb-masthead-nav-focus-within+.zb-masthead-toggler,.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav[focus-within]+.zb-masthead-toggler,.zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-toggle:checked+.zb-masthead-nav+.zb-masthead-toggler {
        background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M2.343.929L8 6.586 13.657.929l1.414 1.414L9.414 8l5.657 5.657-1.414 1.414L8 9.414l-5.657 5.657L.93 13.657 6.586 8 .929 2.343 2.343.93z'/%3E%3C/svg%3E")
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-masthead .zb-masthead-nav-toggle+.zb-masthead-nav:focus-within+.zb-masthead-toggler {
        background: no-repeat 50% url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='%23FFF' fill-rule='evenodd' d='M2.343.929L8 6.586 13.657.929l1.414 1.414L9.414 8l5.657 5.657-1.414 1.414L8 9.414l-5.657 5.657L.93 13.657 6.586 8 .929 2.343 2.343.93z'/%3E%3C/svg%3E")
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-item {
        background: #fff
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-item:hover {
        background: #f2eaf9
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-item-label:hover,.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-menu-item-label:hover {
        color: #5e10b1
    }

    .zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label,.zb-champion-standard-theme .zb-masthead-dark-container .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open:hover .zb-nav-item-label {
        color: #fff
    }
}

.zb-champion-standard-theme .zb-modal-card,.zb-champion-standard-theme .zb-modal-content {
    width: 31.25rem
}

.zb-champion-standard-theme .zb-modal-card {
    border-radius: 16px
}

.zb-champion-standard-theme .zb-modal-card-head {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px
}

.zb-champion-standard-theme .zb-modal-card-foot {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px
}

.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-button,.zb-champion-standard-theme .zb-modal-card-foot-actions .zb-modal-action {
    line-height: 1.5
}

.zb-champion-standard-theme .zb-modal-body .zb-list {
    margin-bottom: 16px
}

.zb-champion-standard-theme .zb-modal-body .zb-list:last-child {
    margin-bottom: 0
}

.zb-champion-standard-theme .zb-modal-body .zb-list>li {
    margin-bottom: 16px;
    padding-top: 0;
    padding-bottom: 0
}

.zb-champion-standard-theme .zb-modal-body .zb-list>li:last-child {
    margin-bottom: 0
}

.zb-champion-standard-theme .zb-modal-close {
    outline-color: #5e10b1;
    cursor: pointer
}

.zb-champion-standard-theme .zb-modal-close:hover {
    color: #3c1053
}

.zb-champion-standard-theme .zb-modal-close:hover .zb-icon {
    color: inherit
}

@media screen and (min-width: 599px) {
    .zb-champion-standard-theme .zb-modal-card,.zb-champion-standard-theme .zb-modal-content {
        width:37.5rem
    }
}

.zb-champion-standard-theme .zb-nav-item {
    margin-right: 8px
}

.zb-champion-standard-theme .zb-nav-item .zb-nav-item-label {
    padding-left: 8px;
    padding-right: 8px;
    text-decoration: none
}

.zb-champion-standard-theme .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label:after,.zb-champion-standard-theme .zb-nav-item:hover .zb-nav-item-label:after {
    left: 1px;
    right: 1px;
    border-radius: 2px
}

.zb-champion-standard-theme .zb-nav-item:hover .zb-nav-item-label:after {
    background: #5e10b1
}

.zb-champion-standard-theme .zb-nav {
    background: #fff
}

.zb-champion-standard-theme .zb-nav a:focus,.zb-champion-standard-theme .zb-nav span.zb-nav-item-label:focus,.zb-champion-standard-theme .zb-nav span.zb-nav-menu-item-label:focus {
    text-decoration: none
}

.zb-champion-standard-theme .zb-nav-menu-item:hover>.zb-nav-menu-item-label {
    text-decoration: underline
}

.zb-champion-standard-theme .zb-nav-item-right-icon {
    display: inline-block
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item {
    margin-right: 0
}

.zb-champion-standard-theme .zb-nav-vertical .zb-left-hand-nav-header,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label {
    padding: 11px 15px
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item-label:hover,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label:hover {
    text-decoration: underline
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-submenu .zb-nav-menu-item-label {
    background: #f2f2f8
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label:hover {
    color: #5e10b1
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected.zb-nav-item-is-open .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label[aria-expanded=true] {
    background-color: #3c1053;
    color: #fff;
    border-color: #3c1053
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected.zb-nav-item-is-open .zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-selected.zb-nav-item-is-open .zb-nav-item-label .zb-nav-menu-item-open-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label[aria-expanded=true] .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu .zb-nav-menu-item-label[aria-expanded=true] .zb-nav-menu-item-open-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] {
    background-color: #3c1053;
    color: #fff;
    border-left-color: #3c1053
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open .zb-nav-item-label .zb-nav-menu-item-open-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] .zb-nav-item-right-icon,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] .zb-nav-menu-item-open-icon {
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true] {
    background: #3c1053
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-item-label[aria-expanded=true]:hover {
    background: #3c1053;
    color: #fff
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label {
    background: #f9f9fc
}

.zb-champion-standard-theme .zb-nav-vertical .zb-nav-menu-group-label:before {
    display: none
}

.zb-champion-standard-theme .zb-left-hand-nav.zb-nav .zb-left-hand-nav-header,.zb-champion-standard-theme .zb-nav-vertical.zb-nav .zb-left-hand-nav-header {
    padding: 11px 15px
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-item.zb-nav-item-is-open,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-item.zb-nav-item-is-open {
    border-bottom: 1px solid #3c1053
}

.zb-champion-standard-theme .zb-left-hand-nav .zb-nav-submenu,.zb-champion-standard-theme .zb-nav-vertical .zb-nav-submenu {
    border-left: none
}

@media (max-width: 840px) {
    @media (max-width:840px) {
        .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item {
            padding:5px 16px
        }
    }

    .zb-champion-standard-theme .zb-masthead .zb-nav-menu-group-label,.zb-champion-standard-theme .zb-masthead .zb-nav-menu-item-label,.zb-champion-standard-theme .zb-masthead .zb-nav-vertical .zb-nav-item-label {
        padding: 0 15px 0 12px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-list li:first-of-type {
        padding-top: 15px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-list li:last-of-type {
        padding-bottom: 15px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:first-of-type {
        padding-top: 5px
    }

    .zb-champion-standard-theme .zb-masthead .zb-masthead-nav-item:last-of-type {
        padding-bottom: 5px
    }
}

.zb-champion-standard-theme .zb-notification.zb-notification-without-title {
    color: #333
}

.zb-champion-standard-theme .zb-notification-has-border .zb-notification-inner {
    border-radius: 8px
}

.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-error .zb-notification-inner,.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-info .zb-notification-inner,.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-success .zb-notification-inner,.zb-champion-standard-theme .zb-notification-has-border.zb-notification-status-warning .zb-notification-inner {
    border-left-width: 4px
}

.zb-champion-standard-theme .zb-notification.zb-notification-large {
    padding-bottom: inherit
}

.zb-champion-standard-theme .zb-notification.zb-notification-large .zb-notification-inner:after {
    display: none
}

.zb-champion-standard-theme .zb-card .zb-notification-has-border.zb-notification-to-edges.zb-notification-status-error .zb-notification-inner,.zb-champion-standard-theme .zb-card .zb-notification-has-border.zb-notification-to-edges.zb-notification-status-info .zb-notification-inner,.zb-champion-standard-theme .zb-card .zb-notification-has-border.zb-notification-to-edges.zb-notification-status-success .zb-notification-inner,.zb-champion-standard-theme .zb-card .zb-notification-has-border.zb-notification-to-edges.zb-notification-status-warning .zb-notification-inner {
    border-left-width: 2px
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges {
    margin-top: -30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card .zb-notification-to-edges {
        margin-top:-20px
    }
}

.zb-champion-standard-theme .zb-card .zb-notification-to-edges .zb-notification-inner {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top-width: 2px
}

.zb-champion-standard-theme .zb-card .zb-card-body {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.zb-champion-standard-theme .zb-card .zb-card-body:last-of-type {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px
}

.zb-champion-standard-theme .zb-card .zb-card-description {
    background: #f2f2f8;
    border-bottom: 1px solid #cccfd0
}

.zb-champion-standard-theme .zb-card-wrapper {
    border: 30px solid #e0e0e0;
    border-top: none;
    border-bottom: none
}

.zb-champion-standard-theme .zb-card-description {
    padding: 17px 30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-description {
        padding:12px 20px
    }
}

.zb-champion-standard-theme .zb-step .zb-step-inner {
    border-color: #cccfd0
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button {
    width: 30px;
    height: 30px;
    background: #fff
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:hover,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:hover {
    background-color: #f2eaf9
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:hover .zb-icon,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:hover .zb-icon {
    margin-top: 4px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button:focus,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button:focus {
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-slider .zb-slider-decrease-button .zb-icon,.zb-champion-standard-theme .zb-slider .zb-slider-increase-button .zb-icon {
    margin-top: 4px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-webkit-slider-thumb {
    width: 30px;
    height: 30px;
    margin-top: -14px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input::-moz-range-thumb {
    margin-top: -14px;
    width: 30px;
    height: 30px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus::-webkit-slider-thumb {
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-slider .zb-slider-input:focus::-moz-range-thumb {
    outline-offset: 1px
}

.zb-champion-standard-theme .zb-slider .zb-slider-container {
    height: 30px;
    width: calc(100% - 96px)
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-input {
    height: 30px
}

.zb-champion-standard-theme .zb-slider .zb-slider-container .zb-slider-progress-bar-wrapper {
    top: 13px;
    right: 32px
}

.zb-champion-standard-theme .zb-slider-labeller .zb-slider-max-label,.zb-champion-standard-theme .zb-slider-labeller .zb-slider-min-label,.zb-champion-standard-theme .zb-slider-labeller .zb-slider-value-label {
    color: #333
}

.zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-focused {
    border-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-primary.zb-split-button-is-focused {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-focused {
    border-radius: 22px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-focused {
        border-radius:20px
    }
}

.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-secondary.zb-split-button-is-disabled .zb-split-button-label:hover,.zb-champion-standard-theme .zb-split-button-secondary .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled).zb-split-button-is-focused .zb-split-button-label,.zb-champion-standard-theme .zb-split-button-secondary:not(.zb-split-button-is-disabled):hover .zb-split-button-label {
    border-right-width: 2px
}

.zb-champion-standard-theme .zb-tab-bar-item,.zb-champion-standard-theme .zb-tab-bar-item:first-child,.zb-champion-standard-theme .zb-tab-bar-item:last-child {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px
}

.zb-champion-standard-theme .zb-card+.zb-card,.zb-champion-standard-theme .zb-card+.zb-card-body,.zb-champion-standard-theme .zb-tab-bar+.zb-card,.zb-champion-standard-theme .zb-tab-bar+.zb-card-body {
    border-radius: 0
}

.zb-champion-standard-theme .zb-card .zb-tab-bar .zb-tab-bar-item-is-selected,.zb-champion-standard-theme .zb-tab-bar .zb-tab-bar .zb-tab-bar-item-is-selected {
    border-left-color: #646068;
    border-right-color: #646068;
    border-top-color: #646068
}

.zb-champion-standard-theme .zb-card .zb-tab-bar+.zb-card,.zb-champion-standard-theme .zb-card .zb-tab-bar+.zb-card-body,.zb-champion-standard-theme .zb-tab-bar .zb-tab-bar+.zb-card,.zb-champion-standard-theme .zb-tab-bar .zb-tab-bar+.zb-card-body {
    border: 1px solid #646068;
    border-top: none;
    border-radius: 0
}

.zb-champion-standard-theme .zb-tab-bar-shadow .zb-tab-bar-item-is-focused {
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-tab-bar-shadow .zb-tab-bar-item:focus {
    outline: 2px solid #5e10b1;
    outline-offset: 2px
}

.zb-champion-standard-theme .zb-tab-bar-shadow .zb-tab-bar-item-is-selected {
    -webkit-box-shadow: 0 -1px 2px 0 rgba(0,0,0,.1);
    box-shadow: 0 -1px 2px 0 rgba(0,0,0,.1);
    border-color: transparent
}

.zb-champion-standard-theme .zb-tab-bar-shadow+.zb-card,.zb-champion-standard-theme .zb-tab-bar-shadow+.zb-card-body {
    border: none
}

.zb-champion-standard-theme .zb-table-toolbar-container {
    color: #333
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-table-toolbar-container {
        color:#5e10b1
    }
}

.zb-champion-standard-theme .zb-table-toolbar-container .zb-button-primary,.zb-champion-standard-theme .zb-table-toolbar-container .zb-button-secondary {
    line-height: 1.25
}

.zb-champion-standard-theme .zb-table-dropdown:focus {
    -webkit-box-shadow: 0 0 0 1px #f2f2f8,0 0 0 3px #5e10b1;
    box-shadow: 0 0 0 1px #f2f2f8,0 0 0 3px #5e10b1;
    outline: none
}

.zb-champion-standard-theme .zb-card-body .zb-flushed-table:first-child {
    margin-top: -30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-body .zb-flushed-table:first-child {
        margin-top:-20px
    }
}

.zb-champion-standard-theme .zb-card-body .zb-flushed-table:last-child {
    margin-bottom: -30px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-card-body .zb-flushed-table:last-child {
        margin-bottom:-20px
    }
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner {
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner .zb-sidepanel-header:first-child {
    border-top-left-radius: 16px
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner .zb-sidepanel-footer {
    border-bottom-left-radius: 16px
}

.zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner .zb-sidepanel-body {
    max-height: 500px
}

.zb-champion-standard-theme .zb-sidepanel-header-content {
    font-weight: 400
}

.zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel {
    bottom: auto;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel-wrapper .zb-sidepanel {
        -webkit-transform:translateY(0);
        transform: translateY(0);
        top: 0;
        bottom: 0
    }
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-sidepanel .zb-sidepanel-inner .zb-sidepanel-body {
        max-height:100%
    }
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-label {
    color: #3c1053
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed .zb-toggle-switch-label-invert {
    color: #646068
}

.zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label {
    color: #3c1053
}

.zb-champion-standard-theme .zb-toggle-switch .zb-toggle-switch-label-invert {
    color: #646068
}

.zb-champion-standard-theme .zb-toggle-switch-button-inner {
    border-radius: 12px
}

.zb-champion-standard-theme .zb-toggle-switch.zb-toggle-is-pressed:hover .zb-toggle-switch-button-inner,.zb-champion-standard-theme .zb-toggle-switch:hover .zb-toggle-switch-button-inner {
    border: 2px solid #5e10b1
}

.zb-champion-standard-theme .zb-toggle-star {
    line-height: 16px;
    padding: 2px
}

.zb-champion-standard-theme .zb-toggle-star .zb-icon {
    stroke: #5e10b1;
    stroke-width: 1px;
    margin-top: 0
}

.zb-champion-standard-theme .zb-toggle-star:hover .zb-icon {
    stroke: #3c1053;
    stroke-width: 2px
}

.zb-champion-standard-theme.zb-champion-premier-theme .zb-heading1,.zb-champion-standard-theme.zb-champion-premier-theme .zb-heading2-alternate,.zb-champion-standard-theme.zb-champion-premier-theme .zb-heading3-alternate {
    font-family: knilesemibold,Arial,sans-serif
}

.zb-champion-standard-theme .zb-has-scrollbar::-webkit-scrollbar-thumb {
    background-color: #646068
}

.zb-champion-standard-theme .zb-file-field-inner {
    border-color: #cccfd0;
    border-width: 1px 1px 0
}

.zb-champion-standard-theme .zb-file-field-inner .zb-file-field-body small {
    font-size: .875rem
}

.zb-champion-standard-theme .zb-file-field-inner .zb-file-field-body .zb-file-input-files {
    margin-top: 0
}

.zb-champion-standard-theme .zb-file-field-inner:first-child {
    background: #f2f2f8
}

.zb-champion-standard-theme .zb-file-field-inner .zb-notification-title {
    font-size: 1.125rem
}

.zb-champion-standard-theme .zb-file-field-inner.zb-file-input-is-hover {
    border-color: #5e10b1;
    border-width: 2px
}

.zb-champion-standard-theme .zb-file-field-inner .zb-file-field-icon-error {
    display: inline-block
}

.zb-champion-standard-theme .zb-file-input.zb-file-field-inner .zb-file-field-body p {
    margin-top: 0
}

.zb-champion-standard-theme .dark-background {
    background: #5a287d
}

.zb-champion-standard-theme .zb-error-message {
    font-size: .813rem;
    color: #cf223f
}

.zb-champion-standard-theme .zb-radio-margin {
    margin-right: 16px
}

@media (max-width: 840px) {
    .zb-champion-standard-theme .zb-radio-margin {
        margin-right:10px
    }
}

.zb-champion-standard-theme .zb-checkbox-is-error,.zb-champion-standard-theme .zb-radio-button-is-error {
    margin-right: 8px
}