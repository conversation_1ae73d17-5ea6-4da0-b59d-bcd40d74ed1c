{"version": 3, "file": "NestedAppAuthAdapter.mjs", "sources": ["../../../src/naa/mapping/NestedAppAuthAdapter.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;MAqCU,oBAAoB,CAAA;AAM7B,IAAA,WAAA,CACI,QAAgB,EAChB,kBAA4B,EAC5B,MAAe,EACf,MAAc,EAAA;AAEd,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAEM,IAAA,iBAAiB,CACpB,OAIsB,EAAA;AAEtB,QAAA,IAAI,WAAgC,CAAC;AACrC,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC5C,YAAA,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;AAC3C,SAAA;AAAM,aAAA;AACH,YAAA,WAAW,GAAG,IAAI,GAAG,CACjB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,MAAM,cAAc,GAAG,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAClE,QAAA,MAAM,MAAM,GAAG,cAAc,CAAC,6BAA6B,CACvD,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,kBAAkB,CAC1B,CAAC;AACF,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,mBAAmB,CAAC;AACrD,QAAA,MAAM,YAAY,GAAiB;AAC/B,YAAA,gBAAgB,EAAE,OAAO,CAAC,OAAO,EAAE,aAAa;YAChD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,OAAO,CAAC,SAAS;AAC5B,YAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,aAAa;AACb,YAAA,MAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,SAAS;YAC5D,KAAK,EAAE,OAAO,CAAC,KAAK;AACpB,YAAA,oBAAoB,EAChB,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM;AAC/D,YAAA,eAAe,EAAE,WAAW;SAC/B,CAAC;AAEF,QAAA,OAAO,YAAY,CAAC;KACvB;AAEM,IAAA,oBAAoB,CACvB,OAAqB,EACrB,QAAoB,EACpB,YAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE;AAC1D,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;AACtE,SAAA;QAED,MAAM,SAAS,GAAG,IAAI,IAAI,CACtB,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,IAAI,CAC3D,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAC9C,QAAQ,CAAC,KAAK,CAAC,QAAQ,EACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAC3B,CAAC;AACF,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CACnC,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,KAAK,CAAC,QAAQ,EACvB,aAAa,CAChB,CAAC;QACF,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC;AAErD,QAAA,MAAM,oBAAoB,GAAyB;YAC/C,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,IAAI,OAAO,CAAC,WAAW;YAC1D,QAAQ,EAAE,OAAO,CAAC,cAAc;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC1B,YAAA,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YACzB,OAAO;AACP,YAAA,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ;YAChC,aAAa;AACb,YAAA,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;AACxC,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,SAAS,EACL,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM;YAC/D,aAAa,EAAE,OAAO,CAAC,aAAa;AACpC,YAAA,YAAY,EAAE,SAAS;YACvB,KAAK,EAAE,OAAO,CAAC,KAAK;SACvB,CAAC;AAEF,QAAA,OAAO,oBAAoB,CAAC;KAC/B;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACI,IAAA,kBAAkB,CACrB,WAA2B,EAC3B,OAAgB,EAChB,aAA2B,EAAA;AAE3B,QAAA,MAAM,sBAAsB,GACxB,aAAa,IAAK,WAAW,CAAC,aAA6B,CAAC;AAEhE,QAAA,MAAM,cAAc,GAChB,WAAW,CAAC,cAAc;AAC1B,YAAA,sBAAsB,EAAE,GAAG;AAC3B,YAAA,sBAAsB,EAAE,GAAG;AAC3B,YAAA,EAAE,CAAC;QAEP,MAAM,QAAQ,GACV,WAAW,CAAC,QAAQ,IAAI,sBAAsB,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9D,MAAM,aAAa,GACf,WAAW,CAAC,aAAa,IAAI,CAAA,EAAG,cAAc,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAE,CAAC;AAEjE,QAAA,MAAM,QAAQ,GACV,WAAW,CAAC,QAAQ;AACpB,YAAA,sBAAsB,EAAE,kBAAkB;AAC1C,YAAA,EAAE,CAAC;QAEP,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,sBAAsB,EAAE,IAAI,CAAC;AAE9D,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;AAExD,QAAA,MAAM,aAAa,GAAG,kBAAkB,CACpC,aAAa,EACb,cAAc,EACd,QAAQ,EACR,sBAAsB,CACzB,CAAC;AACF,QAAA,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AAE5C,QAAA,MAAM,OAAO,GAAoB;YAC7B,aAAa;YACb,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,IAAI;AACJ,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,aAAa,EAAE,sBAAsB;YACrC,cAAc;SACjB,CAAC;AAEF,QAAA,OAAO,OAAO,CAAC;KAClB;AAED;;;;AAIG;AACI,IAAA,eAAe,CAClB,KAAc,EAAA;AAOd,QAAA,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;YACtB,QAAQ,KAAK,CAAC,MAAM;gBAChB,KAAK,gBAAgB,CAAC,UAAU;AAC5B,oBAAA,OAAO,IAAI,eAAe,CACtB,oBAAoB,CAAC,YAAY,CACpC,CAAC;gBACN,KAAK,gBAAgB,CAAC,SAAS;AAC3B,oBAAA,OAAO,IAAI,eAAe,CACtB,oBAAoB,CAAC,qBAAqB,CAC7C,CAAC;gBACN,KAAK,gBAAgB,CAAC,kBAAkB;AACpC,oBAAA,OAAO,IAAI,eAAe,CACtB,oBAAoB,CAAC,cAAc,CACtC,CAAC;gBACN,KAAK,gBAAgB,CAAC,QAAQ;AAC1B,oBAAA,OAAO,IAAI,eAAe,CACtB,oBAAoB,CAAC,2BAA2B,CACnD,CAAC;gBACN,KAAK,gBAAgB,CAAC,wBAAwB;AAC1C,oBAAA,OAAO,IAAI,eAAe,CACtB,KAAK,CAAC,IAAI;AACN,wBAAA,oBAAoB,CAAC,2BAA2B,EACpD,KAAK,CAAC,WAAW,CACpB,CAAC;gBACN,KAAK,gBAAgB,CAAC,cAAc,CAAC;gBACrC,KAAK,gBAAgB,CAAC,eAAe;oBACjC,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC1D,KAAK,gBAAgB,CAAC,uBAAuB;oBACzC,OAAO,IAAI,4BAA4B,CACnC,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,WAAW,CACpB,CAAC;AACN,gBAAA;oBACI,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AAC3D,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,SAAS,CAAC,eAAe,EAAE,2BAA2B,CAAC,CAAC;AACtE,SAAA;KACJ;AAED;;;;;;;;AAQG;IACI,+BAA+B,CAClC,OAAoB,EACpB,OAAsB,EACtB,WAA8B,EAC9B,OAAsB,EACtB,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,EAAE;AAC1B,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;AACtE,SAAA;AAED,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAC9C,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAC3B,CAAC;AAEF,QAAA,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAE9D,QAAA,MAAM,oBAAoB,GAAyB;AAC/C,YAAA,SAAS,EAAE,WAAW,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW;YACzD,QAAQ,EAAE,OAAO,CAAC,cAAc;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC1B,YAAA,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YACzB,OAAO;YACP,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,aAAa,EAAE,aAAa,IAAI,EAAE;YAClC,WAAW,EAAE,WAAW,CAAC,MAAM;AAC/B,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AACzD,YAAA,SAAS,EACL,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM;YAC/D,aAAa;AACb,YAAA,YAAY,EAAE,IAAI,IAAI,CAClB,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAC/C;YACD,KAAK,EAAE,OAAO,CAAC,KAAK;SACvB,CAAC;AAEF,QAAA,OAAO,oBAAoB,CAAC;KAC/B;AACJ;;;;"}