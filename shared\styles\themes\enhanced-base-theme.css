/**
 * Enhanced Base Theme - Comprehensive CSS Custom Properties
 * 
 * This file defines all structural styling extracted from ZB Champion theme
 * that will be shared between both CRM and MFE themes
 */

:root {
  /* === SPACING SYSTEM === */
  --zb-spacing-xs: 4px;
  --zb-spacing-sm: 8px;
  --zb-spacing-md: 16px;
  --zb-spacing-lg: 24px;
  --zb-spacing-xl: 32px;
  --zb-spacing-2xl: 48px;
  --zb-spacing-3xl: 64px;

  /* === DIMENSIONS === */
  /* Button dimensions */
  --zb-button-height: 44px;
  --zb-button-height-mobile: 44px;
  --zb-button-padding-x: 32px;
  --zb-button-padding-y: 12px;
  --zb-button-padding-y-secondary: 10px;
  --zb-button-padding-y-secondary-mobile: 11px;
  --zb-button-min-width: 96px;
  --zb-button-border-radius: 25px;

  /* Input dimensions */
  --zb-input-height: 44px;
  --zb-input-height-mobile: 40px;
  --zb-input-padding-x: 12px;
  --zb-input-padding-y: 0;
  --zb-input-border-width: 1px;
  --zb-input-border-radius: 8px;
  --zb-input-line-height: 42px;

  /* Container dimensions */
  --zb-container-max-width: 1200px;
  --zb-container-padding: var(--zb-spacing-lg);
  --zb-container-padding-mobile: var(--zb-spacing-md);

  /* Card dimensions */
  --zb-card-padding: var(--zb-spacing-lg);
  --zb-card-border-radius: var(--zb-input-border-radius);
  --zb-card-border-width: 1px;

  /* Header dimensions */
  --zb-header-height: 64px;
  --zb-masthead-height: 60px;

  /* === TYPOGRAPHY SCALE === */
  --zb-font-size-xs: 0.75rem;      /* 12px */
  --zb-font-size-sm: 0.8125rem;    /* 13px */
  --zb-font-size-base: 1rem;       /* 16px */
  --zb-font-size-lg: 1.125rem;     /* 18px */
  --zb-font-size-xl: 1.25rem;      /* 20px */
  --zb-font-size-2xl: 1.5rem;      /* 24px */
  --zb-font-size-3xl: 2rem;        /* 32px */

  /* Button font sizes */
  --zb-button-font-size: 18px;
  --zb-button-font-size-mobile: var(--zb-font-size-sm);

  /* Input font sizes */
  --zb-input-font-size: var(--zb-font-size-base);
  --zb-input-font-size-mobile: var(--zb-font-size-sm);

  /* Line heights */
  --zb-line-height-tight: 1.15;
  --zb-line-height-normal: 1.25;
  --zb-line-height-relaxed: 1.5;
  --zb-line-height-loose: 1.6;

  /* Button line heights */
  --zb-button-line-height: 1.45;
  --zb-button-line-height-mobile: 1.3;

  /* Font weights */
  --zb-font-weight-normal: 400;
  --zb-font-weight-medium: 500;
  --zb-font-weight-semibold: 600;
  --zb-font-weight-bold: 700;

  /* === VISUAL EFFECTS === */
  /* Shadows */
  --zb-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --zb-shadow-base: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  --zb-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --zb-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --zb-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Focus rings */
  --zb-focus-ring-width: 3px;
  --zb-focus-ring-offset: 1px;

  /* Transitions */
  --zb-transition-fast: 150ms ease-in-out;
  --zb-transition-base: 200ms ease-in-out;
  --zb-transition-slow: 300ms ease-in-out;

  /* === LAYOUT === */
  /* Grid system */
  --zb-grid-columns: 12;
  --zb-grid-gap: var(--zb-spacing-md);
  --zb-grid-gap-mobile: var(--zb-spacing-sm);

  /* Flexbox utilities */
  --zb-flex-gap: var(--zb-spacing-md);
  --zb-flex-gap-sm: var(--zb-spacing-sm);
  --zb-flex-gap-lg: var(--zb-spacing-lg);

  /* === Z-INDEX SCALE === */
  --zb-z-dropdown: 1000;
  --zb-z-sticky: 1020;
  --zb-z-fixed: 1030;
  --zb-z-modal-backdrop: 1040;
  --zb-z-modal: 1050;
  --zb-z-popover: 1060;
  --zb-z-tooltip: 1070;

  /* === BREAKPOINTS === */
  --zb-breakpoint-mobile: 840px;
  --zb-breakpoint-tablet: 768px;
  --zb-breakpoint-desktop: 1024px;
  --zb-breakpoint-wide: 1200px;

  /* === COMPONENT SPECIFIC === */
  /* Accordion */
  --zb-accordion-header-padding: var(--zb-spacing-md);
  --zb-accordion-content-padding: var(--zb-spacing-md);

  /* Table */
  --zb-table-cell-padding: var(--zb-spacing-sm) var(--zb-spacing-md);
  --zb-table-header-padding: var(--zb-spacing-md);

  /* Form groups */
  --zb-form-group-margin: var(--zb-spacing-md);
  --zb-form-label-margin: var(--zb-spacing-xs);

  /* Navigation */
  --zb-nav-item-padding: var(--zb-spacing-sm) var(--zb-spacing-md);
  --zb-nav-item-margin: var(--zb-spacing-xs);

  /* === ICON SIZES === */
  --zb-icon-xs: 12px;
  --zb-icon-sm: 16px;
  --zb-icon-base: 20px;
  --zb-icon-lg: 24px;
  --zb-icon-xl: 32px;

  /* === BORDER STYLES === */
  --zb-border-width-thin: 1px;
  --zb-border-width-base: 2px;
  --zb-border-width-thick: 3px;
  --zb-border-style: solid;

  /* === ANIMATION === */
  --zb-animation-duration-fast: 150ms;
  --zb-animation-duration-base: 200ms;
  --zb-animation-duration-slow: 300ms;
  --zb-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* === STRUCTURAL BASE STYLES === */

/* Reset and base */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: var(--zb-line-height-normal);
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  margin: 0;
  padding: 0;
  font-size: var(--zb-font-size-base);
  line-height: var(--zb-line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === LAYOUT COMPONENTS === */

.zb-container {
  width: 100%;
  max-width: var(--zb-container-max-width);
  margin: 0 auto;
  padding: 0 var(--zb-container-padding);
}

@media (max-width: 840px) {
  .zb-container {
    padding: 0 var(--zb-container-padding-mobile);
  }
}

/* === BUTTON SYSTEM === */

.zb-button {
  display: inline-block;
  text-align: center;
  padding: var(--zb-button-padding-y) var(--zb-button-padding-x);
  cursor: pointer;
  text-decoration: none;
  min-width: var(--zb-button-min-width);
  border-radius: var(--zb-button-border-radius);
  font-size: var(--zb-button-font-size);
  line-height: var(--zb-button-line-height);
  font-weight: var(--zb-font-weight-normal);
  border: none;
  transition: all var(--zb-transition-base);
  vertical-align: middle;
  white-space: nowrap;
  user-select: none;
}

.zb-button:disabled,
.zb-button.zb-button-is-disabled {
  cursor: auto;
  pointer-events: none;
}

.zb-button:hover {
  text-decoration: none;
}

.zb-button:focus {
  outline: none;
}

.zb-button-secondary {
  padding: var(--zb-button-padding-y-secondary) var(--zb-button-padding-x);
  border: var(--zb-border-width-base) solid transparent;
  line-height: var(--zb-button-line-height);
}

@media (max-width: 840px) {
  .zb-button {
    font-size: var(--zb-button-font-size-mobile);
    line-height: var(--zb-button-line-height-mobile);
  }
  
  .zb-button-secondary {
    padding: var(--zb-button-padding-y-secondary-mobile) var(--zb-button-padding-x);
    line-height: var(--zb-button-line-height-mobile);
  }
}

/* === INPUT SYSTEM === */

.zb-input {
  box-sizing: border-box;
  height: var(--zb-input-height);
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
  border: var(--zb-input-border-width) solid transparent;
  border-radius: var(--zb-input-border-radius);
  font-size: var(--zb-input-font-size);
  transition: all var(--zb-transition-base);
  width: 100%;
}

.zb-input:focus {
  outline: none;
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
}

.zb-input:hover {
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
}

.zb-input:disabled {
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
  cursor: not-allowed;
}

@media (max-width: 840px) {
  .zb-input {
    height: var(--zb-input-height-mobile);
    font-size: var(--zb-input-font-size-mobile);
  }
}

/* === CARD SYSTEM === */

.zb-card {
  background-color: var(--theme-bg-primary);
  border: var(--zb-card-border-width) solid var(--theme-border-primary);
  border-radius: var(--zb-card-border-radius);
  box-shadow: var(--zb-shadow-base);
  transition: all var(--zb-transition-base);
}

.zb-card-body {
  padding: var(--zb-card-padding);
}

.zb-card-header {
  padding: var(--zb-card-padding);
  border-bottom: var(--zb-border-width-thin) solid var(--theme-border-tertiary);
  margin-bottom: var(--zb-spacing-md);
}

/* === UTILITY CLASSES === */

.zb-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.zb-transition {
  transition: all var(--zb-transition-base);
}

/* === RESPONSIVE UTILITIES === */

@media (max-width: 840px) {
  .zb-mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 841px) {
  .zb-desktop-hidden {
    display: none !important;
  }
}
