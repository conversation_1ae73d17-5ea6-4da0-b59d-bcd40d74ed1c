/*! @azure/msal-common v14.16.1 2025-08-05 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const redirectUriEmpty = "redirect_uri_empty";
const claimsRequestParsingError = "claims_request_parsing_error";
const authorityUriInsecure = "authority_uri_insecure";
const urlParseError = "url_parse_error";
const urlEmptyError = "empty_url_error";
const emptyInputScopesError = "empty_input_scopes_error";
const invalidPromptValue = "invalid_prompt_value";
const invalidClaims = "invalid_claims";
const tokenRequestEmpty = "token_request_empty";
const logoutRequestEmpty = "logout_request_empty";
const invalidCodeChallengeMethod = "invalid_code_challenge_method";
const pkceParamsMissing = "pkce_params_missing";
const invalidCloudDiscoveryMetadata = "invalid_cloud_discovery_metadata";
const invalidAuthorityMetadata = "invalid_authority_metadata";
const untrustedAuthority = "untrusted_authority";
const missingSshJwk = "missing_ssh_jwk";
const missingSshKid = "missing_ssh_kid";
const missingNonceAuthenticationHeader = "missing_nonce_authentication_header";
const invalidAuthenticationHeader = "invalid_authentication_header";
const cannotSetOIDCOptions = "cannot_set_OIDCOptions";
const cannotAllowNativeBroker = "cannot_allow_native_broker";
const authorityMismatch = "authority_mismatch";

export { authorityMismatch, authorityUriInsecure, cannotAllowNativeBroker, cannotSetOIDCOptions, claimsRequestParsingError, emptyInputScopesError, invalidAuthenticationHeader, invalidAuthorityMetadata, invalidClaims, invalidCloudDiscoveryMetadata, invalidCodeChallengeMethod, invalidPromptValue, logoutRequestEmpty, missingNonceAuthenticationHeader, missingSshJwk, missingSshKid, pkceParamsMissing, redirectUriEmpty, tokenRequestEmpty, untrustedAuthority, urlEmptyError, urlParseError };
//# sourceMappingURL=ClientConfigurationErrorCodes.mjs.map
