/*! @azure/msal-common v14.16.1 2025-08-05 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
// Codes defined by MSAL
const noTokensFound = "no_tokens_found";
const nativeAccountUnavailable = "native_account_unavailable";
const refreshTokenExpired = "refresh_token_expired";
// Codes potentially returned by server
const interactionRequired = "interaction_required";
const consentRequired = "consent_required";
const loginRequired = "login_required";
const badToken = "bad_token";

export { badToken, consentRequired, interactionRequired, loginRequired, nativeAccountUnavailable, noTokensFound, refreshTokenExpired };
//# sourceMappingURL=InteractionRequiredAuthErrorCodes.mjs.map
