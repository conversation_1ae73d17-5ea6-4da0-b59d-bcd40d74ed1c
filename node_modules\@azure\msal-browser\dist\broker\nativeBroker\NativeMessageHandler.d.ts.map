{"version": 3, "file": "NativeMessageHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/broker/nativeBroker/NativeMessageHandler.ts"], "names": [], "mappings": "AASA,OAAO,EACH,MAAM,EAIN,oBAAoB,EAGpB,kBAAkB,EACrB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAEH,0BAA0B,EAC7B,MAAM,oBAAoB,CAAC;AAM5B,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AAUrE,qBAAa,oBAAoB;IAC7B,OAAO,CAAC,WAAW,CAAqB;IACxC,OAAO,CAAC,gBAAgB,CAAqB;IAC7C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAS;IAC5C,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,SAAS,CAAyC;IAC1D,OAAO,CAAC,kBAAkB,CAAuC;IACjE,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgC;IAC/D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAqB;IACvD,OAAO,CAAC,QAAQ,CAAC,cAAc,CAA6B;gBAGxD,MAAM,EAAE,MAAM,EACd,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,kBAAkB,EACrC,WAAW,CAAC,EAAE,MAAM;IAexB;;;OAGG;IACG,WAAW,CAAC,IAAI,EAAE,0BAA0B,GAAG,OAAO,CAAC,MAAM,CAAC;IAwBpE;;;;;;OAMG;WACU,cAAc,CACvB,MAAM,EAAE,MAAM,EACd,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,kBAAkB,GACtC,OAAO,CAAC,oBAAoB,CAAC;IAuBhC;;OAEG;YACW,oBAAoB;IAsDlC;;;OAGG;IACH,OAAO,CAAC,eAAe;IAyDvB;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAiGxB;;;OAGG;IACH,cAAc,IAAI,MAAM,GAAG,SAAS;IAIpC;;;OAGG;IACH,mBAAmB,IAAI,MAAM,GAAG,SAAS;IAIzC;;;;;;OAMG;IACH,MAAM,CAAC,iBAAiB,CACpB,MAAM,EAAE,oBAAoB,EAC5B,MAAM,EAAE,MAAM,EACd,uBAAuB,CAAC,EAAE,oBAAoB,EAC9C,oBAAoB,CAAC,EAAE,oBAAoB,GAC5C,OAAO;CAoCb"}