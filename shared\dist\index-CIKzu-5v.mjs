var u = Object.defineProperty;
var f = (r, e, n) => e in r ? u(r, e, { enumerable: !0, configurable: !0, writable: !0, value: n }) : r[e] = n;
var o = (r, e, n) => f(r, typeof e != "symbol" ? e + "" : e, n);
const a = { BASE_URL: "/", DEV: !1, MODE: "production", PROD: !0, SSR: !1 };
var h = /* @__PURE__ */ ((r) => (r.WEB_RESOURCE = "web_resource", r.EMBEDDED_SPA = "embedded_spa", r.STANDALONE_MFE = "standalone_mfe", r))(h || {}), c = /* @__PURE__ */ ((r) => (r.CRM = "crm", r.MFE = "mfe", r))(c || {});
function l(r) {
  switch (r) {
    case "standalone":
      return "standalone_mfe";
    case "web_resource":
      return "web_resource";
    case "embedded_spa":
      return "embedded_spa";
    case "standalone_mfe":
      return "standalone_mfe";
    default:
      return "web_resource";
  }
}
const i = class i {
  constructor() {
    o(this, "_detectedMode", null);
    o(this, "_config", null);
  }
  static getInstance() {
    return i._instance || (i._instance = new i()), i._instance;
  }
  /**
   * Detects the deployment mode based on runtime environment
   */
  detectDeploymentMode() {
    if (this._detectedMode)
      return this._detectedMode;
    const e = this.getConfigurationOverride();
    if (e)
      return this._detectedMode = e, this._detectedMode;
    const n = this.isDynamics365Environment(), t = this.isEmbeddedSPAEnvironment();
    return n ? this._detectedMode = "web_resource" : t ? this._detectedMode = "embedded_spa" : this._detectedMode = "standalone_mfe", this._detectedMode;
  }
  /**
   * Detects the appropriate theme mode based on deployment context
   */
  detectThemeMode() {
    const e = this.detectDeploymentMode(), n = this.getThemeOverride();
    if (n)
      return n;
    switch (e) {
      case "web_resource":
      case "embedded_spa":
        return "crm";
      case "standalone_mfe":
        return "mfe";
      default:
        return "crm";
    }
  }
  /**
   * Checks if running within Dynamics 365 context (web resource)
   */
  isDynamics365Environment() {
    try {
      if (typeof window < "u") {
        if (window.Xrm && window.Xrm.WebApi)
          return !0;
        if (window.parent && window.parent !== window)
          try {
            if (window.parent.Xrm)
              return !0;
          } catch {
          }
        const e = window.location.href;
        if (e.includes("/WebResources/") || e.includes("/_static/"))
          return !0;
      }
      return !1;
    } catch (e) {
      return console.warn("Error detecting Dynamics 365 environment:", e), !1;
    }
  }
  /**
   * Checks if running as embedded SPA within Dynamics 365 interface
   */
  isEmbeddedSPAEnvironment() {
    try {
      if (typeof window < "u") {
        const e = window.location.href;
        if ((e.includes(".dynamics.com") || e.includes(".crm.dynamics.com")) && !e.includes("/WebResources/") && !e.includes("/_static/"))
          return !0;
        const n = new URLSearchParams(window.location.search);
        if (n.has("embedded") || n.has("spa_mode") || document.referrer && (document.referrer.includes(".dynamics.com") || document.referrer.includes(".crm.dynamics.com")))
          return !0;
      }
      return !1;
    } catch (e) {
      return console.warn("Error detecting embedded SPA environment:", e), !1;
    }
  }
  /**
   * Gets configuration override from environment variables or URL parameters
   */
  getConfigurationOverride() {
    try {
      const e = this.getEnvironmentVariable("VITE_DEPLOYMENT_MODE");
      if (e)
        return l(e);
      if (typeof window < "u") {
        const t = new URLSearchParams(window.location.search).get("deploymentMode");
        if (t)
          return l(t);
      }
      return null;
    } catch (e) {
      return console.warn("Error getting configuration override:", e), null;
    }
  }
  /**
   * Gets theme override from environment variables or URL parameters
   */
  getThemeOverride() {
    try {
      const e = this.getEnvironmentVariable("VITE_THEME_MODE");
      if (e && Object.values(c).includes(e))
        return e;
      if (typeof window < "u") {
        const t = new URLSearchParams(window.location.search).get("themeMode");
        if (t && Object.values(c).includes(t))
          return t;
      }
      return null;
    } catch (e) {
      return console.warn("Error getting theme override:", e), null;
    }
  }
  /**
   * Gets environment variable with fallback
   */
  getEnvironmentVariable(e) {
    try {
      return (a == null ? void 0 : a[e]) || null;
    } catch {
      return null;
    }
  }
  /**
   * Gets the deployment configuration for the detected mode
   */
  getDeploymentConfig() {
    if (this._config)
      return this._config;
    const e = this.detectDeploymentMode();
    return this._config = this.createConfigForMode(e), this._config;
  }
  /**
   * Creates configuration object for the specified deployment mode
   */
  createConfigForMode(e) {
    const n = this.detectThemeMode(), t = this.createThemeConfig(n), s = {
      mode: e,
      theme: t,
      features: {
        enableLogging: this.getEnvironmentVariable("VITE_ENABLE_LOGGING") === "true" || e !== "web_resource",
        enableOfflineMode: this.getEnvironmentVariable("VITE_ENABLE_OFFLINE") === "true" || !1,
        enableTelemetry: this.getEnvironmentVariable("VITE_ENABLE_TELEMETRY") === "true" || !1,
        enableThemeSwitching: this.getEnvironmentVariable("VITE_ENABLE_THEME_SWITCHING") === "true" || !1
      }
    };
    switch (e) {
      case "web_resource":
        return {
          ...s,
          apiBaseUrl: "",
          // Will use relative URLs within D365
          authMethod: "dynamics365",
          dynamicsConfig: {
            serverUrl: this.getEnvironmentVariable("VITE_DYNAMICS_SERVER_URL") || "",
            version: this.getEnvironmentVariable("VITE_DYNAMICS_API_VERSION") || "9.2"
          }
        };
      case "embedded_spa":
        return {
          ...s,
          apiBaseUrl: this.getEnvironmentVariable("VITE_API_BASE_URL") || "https://your-org.api.crm.dynamics.com/api/data/v9.2",
          authMethod: "msal",
          msalConfig: {
            clientId: this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID") || "",
            authority: this.getEnvironmentVariable("VITE_MSAL_AUTHORITY") || "https://login.microsoftonline.com/common",
            redirectUri: this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI") || window.location.origin,
            scopes: (this.getEnvironmentVariable("VITE_MSAL_SCOPES") || "https://your-org.crm.dynamics.com/.default").split(",")
          }
        };
      case "standalone_mfe":
        return {
          ...s,
          apiBaseUrl: this.getEnvironmentVariable("VITE_API_BASE_URL") || "https://your-org.api.crm.dynamics.com/api/data/v9.2",
          authMethod: "msal",
          msalConfig: {
            clientId: this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID") || "",
            authority: this.getEnvironmentVariable("VITE_MSAL_AUTHORITY") || "https://login.microsoftonline.com/common",
            redirectUri: this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI") || window.location.origin,
            scopes: (this.getEnvironmentVariable("VITE_MSAL_SCOPES") || "https://your-org.crm.dynamics.com/.default").split(",")
          }
        };
      default:
        throw new Error(`Unsupported deployment mode: ${e}`);
    }
  }
  /**
   * Creates theme configuration for the specified theme mode
   */
  createThemeConfig(e) {
    switch (e) {
      case "crm":
        return {
          mode: "crm",
          primaryColor: "#0078d4",
          secondaryColor: "#106ebe",
          backgroundColor: "#ffffff",
          textColor: "#323130",
          borderColor: "#8a8886",
          fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
          customProperties: {
            "--crm-header-bg": "#0078d4",
            "--crm-sidebar-bg": "#f3f2f1",
            "--crm-card-bg": "#ffffff",
            "--crm-border-radius": "2px",
            "--crm-shadow": "0 2px 4px rgba(0, 0, 0, 0.1)",
            "--crm-spacing-xs": "4px",
            "--crm-spacing-sm": "8px",
            "--crm-spacing-md": "16px",
            "--crm-spacing-lg": "24px",
            "--crm-spacing-xl": "32px"
          }
        };
      case "mfe":
        return {
          mode: "mfe",
          primaryColor: "#5e10b1",
          secondaryColor: "#646068",
          backgroundColor: "#f2f2f8",
          textColor: "#333",
          borderColor: "#646068",
          fontFamily: '"RNHouseSans", Arial, sans-serif',
          customProperties: {
            "--mfe-header-bg": "#333",
            "--mfe-sidebar-bg": "#f9f9fc",
            "--mfe-card-bg": "#ffffff",
            "--mfe-border-radius": "16px",
            "--mfe-shadow": "0 2px 2px 0 rgba(0, 0, 0, 0.1)",
            "--mfe-spacing-xs": "4px",
            "--mfe-spacing-sm": "8px",
            "--mfe-spacing-md": "16px",
            "--mfe-spacing-lg": "24px",
            "--mfe-spacing-xl": "32px",
            "--theme-mobile-breakpoint": "840px"
          }
        };
      default:
        throw new Error(`Unsupported theme mode: ${e}`);
    }
  }
  /**
   * Forces a specific deployment mode (useful for testing)
   */
  forceDeploymentMode(e) {
    this._detectedMode = e, this._config = null;
  }
  /**
   * Resets the detector state
   */
  reset() {
    this._detectedMode = null, this._config = null;
  }
};
o(i, "_instance");
let d = i;
function m() {
  return d.getInstance().getDeploymentConfig();
}
function E() {
  return m().mode === "web_resource";
}
function _() {
  const r = m().mode;
  return r === "embedded_spa" || r === "standalone_mfe";
}
function p() {
  return m().theme;
}
export {
  h as D,
  c as T,
  d as a,
  _ as b,
  p as c,
  m as g,
  E as i
};
