import { a as cr, L as fr, l as mr } from "../logger-CcuSVRbq.mjs";
function F(t) {
  "@babel/helpers - typeof";
  return F = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(e) {
    return typeof e;
  } : function(e) {
    return e && typeof Symbol == "function" && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e;
  }, F(t);
}
function O(t) {
  if (t === null || t === !0 || t === !1)
    return NaN;
  var e = Number(t);
  return isNaN(e) ? e : e < 0 ? Math.ceil(e) : Math.floor(e);
}
function v(t, e) {
  if (e.length < t)
    throw new TypeError(t + " argument" + (t > 1 ? "s" : "") + " required, but only " + e.length + " present");
}
function h(t) {
  v(1, arguments);
  var e = Object.prototype.toString.call(t);
  return t instanceof Date || F(t) === "object" && e === "[object Date]" ? new Date(t.getTime()) : typeof t == "number" || e === "[object Number]" ? new Date(t) : ((typeof t == "string" || e === "[object String]") && typeof console < "u" && (console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"), console.warn(new Error().stack)), /* @__PURE__ */ new Date(NaN));
}
function ce(t, e) {
  v(2, arguments);
  var r = h(t).getTime(), a = O(e);
  return new Date(r + a);
}
var fe = {};
function k() {
  return fe;
}
function V(t) {
  var e = new Date(Date.UTC(t.getFullYear(), t.getMonth(), t.getDate(), t.getHours(), t.getMinutes(), t.getSeconds(), t.getMilliseconds()));
  return e.setUTCFullYear(t.getFullYear()), t.getTime() - e.getTime();
}
function R(t, e) {
  v(2, arguments);
  var r = h(t), a = h(e), n = r.getTime() - a.getTime();
  return n < 0 ? -1 : n > 0 ? 1 : n;
}
var K = 6e4, ee = 36e5;
function me(t) {
  return v(1, arguments), t instanceof Date || F(t) === "object" && Object.prototype.toString.call(t) === "[object Date]";
}
function _(t) {
  if (v(1, arguments), !me(t) && typeof t != "number")
    return !1;
  var e = h(t);
  return !isNaN(Number(e));
}
function he(t, e) {
  v(2, arguments);
  var r = h(t), a = h(e), n = r.getFullYear() - a.getFullYear(), i = r.getMonth() - a.getMonth();
  return n * 12 + i;
}
function ve(t, e) {
  return v(2, arguments), h(t).getTime() - h(e).getTime();
}
var ge = {
  ceil: Math.ceil,
  round: Math.round,
  floor: Math.floor,
  trunc: function(e) {
    return e < 0 ? Math.ceil(e) : Math.floor(e);
  }
  // Math.trunc is not supported by IE
}, we = "trunc";
function ye(t) {
  return ge[we];
}
function be(t) {
  v(1, arguments);
  var e = h(t);
  return e.setHours(23, 59, 59, 999), e;
}
function pe(t) {
  v(1, arguments);
  var e = h(t), r = e.getMonth();
  return e.setFullYear(e.getFullYear(), r + 1, 0), e.setHours(23, 59, 59, 999), e;
}
function Te(t) {
  v(1, arguments);
  var e = h(t);
  return be(e).getTime() === pe(e).getTime();
}
function Me(t, e) {
  v(2, arguments);
  var r = h(t), a = h(e), n = R(r, a), i = Math.abs(he(r, a)), o;
  if (i < 1)
    o = 0;
  else {
    r.getMonth() === 1 && r.getDate() > 27 && r.setDate(30), r.setMonth(r.getMonth() - n * i);
    var s = R(r, a) === -n;
    Te(h(t)) && i === 1 && R(t, a) === 1 && (s = !1), o = n * (i - Number(s));
  }
  return o === 0 ? 0 : o;
}
function De(t, e, r) {
  v(2, arguments);
  var a = ve(t, e) / 1e3;
  return ye()(a);
}
function Oe(t, e) {
  v(2, arguments);
  var r = O(e);
  return ce(t, -r);
}
var Ce = 864e5;
function Se(t) {
  v(1, arguments);
  var e = h(t), r = e.getTime();
  e.setUTCMonth(0, 1), e.setUTCHours(0, 0, 0, 0);
  var a = e.getTime(), n = r - a;
  return Math.floor(n / Ce) + 1;
}
function H(t) {
  v(1, arguments);
  var e = 1, r = h(t), a = r.getUTCDay(), n = (a < e ? 7 : 0) + a - e;
  return r.setUTCDate(r.getUTCDate() - n), r.setUTCHours(0, 0, 0, 0), r;
}
function te(t) {
  v(1, arguments);
  var e = h(t), r = e.getUTCFullYear(), a = /* @__PURE__ */ new Date(0);
  a.setUTCFullYear(r + 1, 0, 4), a.setUTCHours(0, 0, 0, 0);
  var n = H(a), i = /* @__PURE__ */ new Date(0);
  i.setUTCFullYear(r, 0, 4), i.setUTCHours(0, 0, 0, 0);
  var o = H(i);
  return e.getTime() >= n.getTime() ? r + 1 : e.getTime() >= o.getTime() ? r : r - 1;
}
function Pe(t) {
  v(1, arguments);
  var e = te(t), r = /* @__PURE__ */ new Date(0);
  r.setUTCFullYear(e, 0, 4), r.setUTCHours(0, 0, 0, 0);
  var a = H(r);
  return a;
}
var xe = 6048e5;
function Ue(t) {
  v(1, arguments);
  var e = h(t), r = H(e).getTime() - Pe(e).getTime();
  return Math.round(r / xe) + 1;
}
function L(t, e) {
  var r, a, n, i, o, s, u, d;
  v(1, arguments);
  var f = k(), l = O((r = (a = (n = (i = e == null ? void 0 : e.weekStartsOn) !== null && i !== void 0 ? i : e == null || (o = e.locale) === null || o === void 0 || (s = o.options) === null || s === void 0 ? void 0 : s.weekStartsOn) !== null && n !== void 0 ? n : f.weekStartsOn) !== null && a !== void 0 ? a : (u = f.locale) === null || u === void 0 || (d = u.options) === null || d === void 0 ? void 0 : d.weekStartsOn) !== null && r !== void 0 ? r : 0);
  if (!(l >= 0 && l <= 6))
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  var w = h(t), c = w.getUTCDay(), g = (c < l ? 7 : 0) + c - l;
  return w.setUTCDate(w.getUTCDate() - g), w.setUTCHours(0, 0, 0, 0), w;
}
function re(t, e) {
  var r, a, n, i, o, s, u, d;
  v(1, arguments);
  var f = h(t), l = f.getUTCFullYear(), w = k(), c = O((r = (a = (n = (i = e == null ? void 0 : e.firstWeekContainsDate) !== null && i !== void 0 ? i : e == null || (o = e.locale) === null || o === void 0 || (s = o.options) === null || s === void 0 ? void 0 : s.firstWeekContainsDate) !== null && n !== void 0 ? n : w.firstWeekContainsDate) !== null && a !== void 0 ? a : (u = w.locale) === null || u === void 0 || (d = u.options) === null || d === void 0 ? void 0 : d.firstWeekContainsDate) !== null && r !== void 0 ? r : 1);
  if (!(c >= 1 && c <= 7))
    throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");
  var g = /* @__PURE__ */ new Date(0);
  g.setUTCFullYear(l + 1, 0, c), g.setUTCHours(0, 0, 0, 0);
  var C = L(g, e), b = /* @__PURE__ */ new Date(0);
  b.setUTCFullYear(l, 0, c), b.setUTCHours(0, 0, 0, 0);
  var U = L(b, e);
  return f.getTime() >= C.getTime() ? l + 1 : f.getTime() >= U.getTime() ? l : l - 1;
}
function Ne(t, e) {
  var r, a, n, i, o, s, u, d;
  v(1, arguments);
  var f = k(), l = O((r = (a = (n = (i = e == null ? void 0 : e.firstWeekContainsDate) !== null && i !== void 0 ? i : e == null || (o = e.locale) === null || o === void 0 || (s = o.options) === null || s === void 0 ? void 0 : s.firstWeekContainsDate) !== null && n !== void 0 ? n : f.firstWeekContainsDate) !== null && a !== void 0 ? a : (u = f.locale) === null || u === void 0 || (d = u.options) === null || d === void 0 ? void 0 : d.firstWeekContainsDate) !== null && r !== void 0 ? r : 1), w = re(t, e), c = /* @__PURE__ */ new Date(0);
  c.setUTCFullYear(w, 0, l), c.setUTCHours(0, 0, 0, 0);
  var g = L(c, e);
  return g;
}
var Ee = 6048e5;
function We(t, e) {
  v(1, arguments);
  var r = h(t), a = L(r, e).getTime() - Ne(r, e).getTime();
  return Math.round(a / Ee) + 1;
}
function m(t, e) {
  for (var r = t < 0 ? "-" : "", a = Math.abs(t).toString(); a.length < e; )
    a = "0" + a;
  return r + a;
}
var D = {
  // Year
  y: function(e, r) {
    var a = e.getUTCFullYear(), n = a > 0 ? a : 1 - a;
    return m(r === "yy" ? n % 100 : n, r.length);
  },
  // Month
  M: function(e, r) {
    var a = e.getUTCMonth();
    return r === "M" ? String(a + 1) : m(a + 1, 2);
  },
  // Day of the month
  d: function(e, r) {
    return m(e.getUTCDate(), r.length);
  },
  // AM or PM
  a: function(e, r) {
    var a = e.getUTCHours() / 12 >= 1 ? "pm" : "am";
    switch (r) {
      case "a":
      case "aa":
        return a.toUpperCase();
      case "aaa":
        return a;
      case "aaaaa":
        return a[0];
      case "aaaa":
      default:
        return a === "am" ? "a.m." : "p.m.";
    }
  },
  // Hour [1-12]
  h: function(e, r) {
    return m(e.getUTCHours() % 12 || 12, r.length);
  },
  // Hour [0-23]
  H: function(e, r) {
    return m(e.getUTCHours(), r.length);
  },
  // Minute
  m: function(e, r) {
    return m(e.getUTCMinutes(), r.length);
  },
  // Second
  s: function(e, r) {
    return m(e.getUTCSeconds(), r.length);
  },
  // Fraction of second
  S: function(e, r) {
    var a = r.length, n = e.getUTCMilliseconds(), i = Math.floor(n * Math.pow(10, a - 3));
    return m(i, r.length);
  }
}, P = {
  midnight: "midnight",
  noon: "noon",
  morning: "morning",
  afternoon: "afternoon",
  evening: "evening",
  night: "night"
}, ke = {
  // Era
  G: function(e, r, a) {
    var n = e.getUTCFullYear() > 0 ? 1 : 0;
    switch (r) {
      case "G":
      case "GG":
      case "GGG":
        return a.era(n, {
          width: "abbreviated"
        });
      case "GGGGG":
        return a.era(n, {
          width: "narrow"
        });
      case "GGGG":
      default:
        return a.era(n, {
          width: "wide"
        });
    }
  },
  // Year
  y: function(e, r, a) {
    if (r === "yo") {
      var n = e.getUTCFullYear(), i = n > 0 ? n : 1 - n;
      return a.ordinalNumber(i, {
        unit: "year"
      });
    }
    return D.y(e, r);
  },
  // Local week-numbering year
  Y: function(e, r, a, n) {
    var i = re(e, n), o = i > 0 ? i : 1 - i;
    if (r === "YY") {
      var s = o % 100;
      return m(s, 2);
    }
    return r === "Yo" ? a.ordinalNumber(o, {
      unit: "year"
    }) : m(o, r.length);
  },
  // ISO week-numbering year
  R: function(e, r) {
    var a = te(e);
    return m(a, r.length);
  },
  // Extended year. This is a single number designating the year of this calendar system.
  // The main difference between `y` and `u` localizers are B.C. years:
  // | Year | `y` | `u` |
  // |------|-----|-----|
  // | AC 1 |   1 |   1 |
  // | BC 1 |   1 |   0 |
  // | BC 2 |   2 |  -1 |
  // Also `yy` always returns the last two digits of a year,
  // while `uu` pads single digit years to 2 characters and returns other years unchanged.
  u: function(e, r) {
    var a = e.getUTCFullYear();
    return m(a, r.length);
  },
  // Quarter
  Q: function(e, r, a) {
    var n = Math.ceil((e.getUTCMonth() + 1) / 3);
    switch (r) {
      case "Q":
        return String(n);
      case "QQ":
        return m(n, 2);
      case "Qo":
        return a.ordinalNumber(n, {
          unit: "quarter"
        });
      case "QQQ":
        return a.quarter(n, {
          width: "abbreviated",
          context: "formatting"
        });
      case "QQQQQ":
        return a.quarter(n, {
          width: "narrow",
          context: "formatting"
        });
      case "QQQQ":
      default:
        return a.quarter(n, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Stand-alone quarter
  q: function(e, r, a) {
    var n = Math.ceil((e.getUTCMonth() + 1) / 3);
    switch (r) {
      case "q":
        return String(n);
      case "qq":
        return m(n, 2);
      case "qo":
        return a.ordinalNumber(n, {
          unit: "quarter"
        });
      case "qqq":
        return a.quarter(n, {
          width: "abbreviated",
          context: "standalone"
        });
      case "qqqqq":
        return a.quarter(n, {
          width: "narrow",
          context: "standalone"
        });
      case "qqqq":
      default:
        return a.quarter(n, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  // Month
  M: function(e, r, a) {
    var n = e.getUTCMonth();
    switch (r) {
      case "M":
      case "MM":
        return D.M(e, r);
      case "Mo":
        return a.ordinalNumber(n + 1, {
          unit: "month"
        });
      case "MMM":
        return a.month(n, {
          width: "abbreviated",
          context: "formatting"
        });
      case "MMMMM":
        return a.month(n, {
          width: "narrow",
          context: "formatting"
        });
      case "MMMM":
      default:
        return a.month(n, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Stand-alone month
  L: function(e, r, a) {
    var n = e.getUTCMonth();
    switch (r) {
      case "L":
        return String(n + 1);
      case "LL":
        return m(n + 1, 2);
      case "Lo":
        return a.ordinalNumber(n + 1, {
          unit: "month"
        });
      case "LLL":
        return a.month(n, {
          width: "abbreviated",
          context: "standalone"
        });
      case "LLLLL":
        return a.month(n, {
          width: "narrow",
          context: "standalone"
        });
      case "LLLL":
      default:
        return a.month(n, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  // Local week of year
  w: function(e, r, a, n) {
    var i = We(e, n);
    return r === "wo" ? a.ordinalNumber(i, {
      unit: "week"
    }) : m(i, r.length);
  },
  // ISO week of year
  I: function(e, r, a) {
    var n = Ue(e);
    return r === "Io" ? a.ordinalNumber(n, {
      unit: "week"
    }) : m(n, r.length);
  },
  // Day of the month
  d: function(e, r, a) {
    return r === "do" ? a.ordinalNumber(e.getUTCDate(), {
      unit: "date"
    }) : D.d(e, r);
  },
  // Day of year
  D: function(e, r, a) {
    var n = Se(e);
    return r === "Do" ? a.ordinalNumber(n, {
      unit: "dayOfYear"
    }) : m(n, r.length);
  },
  // Day of week
  E: function(e, r, a) {
    var n = e.getUTCDay();
    switch (r) {
      case "E":
      case "EE":
      case "EEE":
        return a.day(n, {
          width: "abbreviated",
          context: "formatting"
        });
      case "EEEEE":
        return a.day(n, {
          width: "narrow",
          context: "formatting"
        });
      case "EEEEEE":
        return a.day(n, {
          width: "short",
          context: "formatting"
        });
      case "EEEE":
      default:
        return a.day(n, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Local day of week
  e: function(e, r, a, n) {
    var i = e.getUTCDay(), o = (i - n.weekStartsOn + 8) % 7 || 7;
    switch (r) {
      case "e":
        return String(o);
      case "ee":
        return m(o, 2);
      case "eo":
        return a.ordinalNumber(o, {
          unit: "day"
        });
      case "eee":
        return a.day(i, {
          width: "abbreviated",
          context: "formatting"
        });
      case "eeeee":
        return a.day(i, {
          width: "narrow",
          context: "formatting"
        });
      case "eeeeee":
        return a.day(i, {
          width: "short",
          context: "formatting"
        });
      case "eeee":
      default:
        return a.day(i, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Stand-alone local day of week
  c: function(e, r, a, n) {
    var i = e.getUTCDay(), o = (i - n.weekStartsOn + 8) % 7 || 7;
    switch (r) {
      case "c":
        return String(o);
      case "cc":
        return m(o, r.length);
      case "co":
        return a.ordinalNumber(o, {
          unit: "day"
        });
      case "ccc":
        return a.day(i, {
          width: "abbreviated",
          context: "standalone"
        });
      case "ccccc":
        return a.day(i, {
          width: "narrow",
          context: "standalone"
        });
      case "cccccc":
        return a.day(i, {
          width: "short",
          context: "standalone"
        });
      case "cccc":
      default:
        return a.day(i, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  // ISO day of week
  i: function(e, r, a) {
    var n = e.getUTCDay(), i = n === 0 ? 7 : n;
    switch (r) {
      case "i":
        return String(i);
      case "ii":
        return m(i, r.length);
      case "io":
        return a.ordinalNumber(i, {
          unit: "day"
        });
      case "iii":
        return a.day(n, {
          width: "abbreviated",
          context: "formatting"
        });
      case "iiiii":
        return a.day(n, {
          width: "narrow",
          context: "formatting"
        });
      case "iiiiii":
        return a.day(n, {
          width: "short",
          context: "formatting"
        });
      case "iiii":
      default:
        return a.day(n, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // AM or PM
  a: function(e, r, a) {
    var n = e.getUTCHours(), i = n / 12 >= 1 ? "pm" : "am";
    switch (r) {
      case "a":
      case "aa":
        return a.dayPeriod(i, {
          width: "abbreviated",
          context: "formatting"
        });
      case "aaa":
        return a.dayPeriod(i, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "aaaaa":
        return a.dayPeriod(i, {
          width: "narrow",
          context: "formatting"
        });
      case "aaaa":
      default:
        return a.dayPeriod(i, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // AM, PM, midnight, noon
  b: function(e, r, a) {
    var n = e.getUTCHours(), i;
    switch (n === 12 ? i = P.noon : n === 0 ? i = P.midnight : i = n / 12 >= 1 ? "pm" : "am", r) {
      case "b":
      case "bb":
        return a.dayPeriod(i, {
          width: "abbreviated",
          context: "formatting"
        });
      case "bbb":
        return a.dayPeriod(i, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "bbbbb":
        return a.dayPeriod(i, {
          width: "narrow",
          context: "formatting"
        });
      case "bbbb":
      default:
        return a.dayPeriod(i, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // in the morning, in the afternoon, in the evening, at night
  B: function(e, r, a) {
    var n = e.getUTCHours(), i;
    switch (n >= 17 ? i = P.evening : n >= 12 ? i = P.afternoon : n >= 4 ? i = P.morning : i = P.night, r) {
      case "B":
      case "BB":
      case "BBB":
        return a.dayPeriod(i, {
          width: "abbreviated",
          context: "formatting"
        });
      case "BBBBB":
        return a.dayPeriod(i, {
          width: "narrow",
          context: "formatting"
        });
      case "BBBB":
      default:
        return a.dayPeriod(i, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  // Hour [1-12]
  h: function(e, r, a) {
    if (r === "ho") {
      var n = e.getUTCHours() % 12;
      return n === 0 && (n = 12), a.ordinalNumber(n, {
        unit: "hour"
      });
    }
    return D.h(e, r);
  },
  // Hour [0-23]
  H: function(e, r, a) {
    return r === "Ho" ? a.ordinalNumber(e.getUTCHours(), {
      unit: "hour"
    }) : D.H(e, r);
  },
  // Hour [0-11]
  K: function(e, r, a) {
    var n = e.getUTCHours() % 12;
    return r === "Ko" ? a.ordinalNumber(n, {
      unit: "hour"
    }) : m(n, r.length);
  },
  // Hour [1-24]
  k: function(e, r, a) {
    var n = e.getUTCHours();
    return n === 0 && (n = 24), r === "ko" ? a.ordinalNumber(n, {
      unit: "hour"
    }) : m(n, r.length);
  },
  // Minute
  m: function(e, r, a) {
    return r === "mo" ? a.ordinalNumber(e.getUTCMinutes(), {
      unit: "minute"
    }) : D.m(e, r);
  },
  // Second
  s: function(e, r, a) {
    return r === "so" ? a.ordinalNumber(e.getUTCSeconds(), {
      unit: "second"
    }) : D.s(e, r);
  },
  // Fraction of second
  S: function(e, r) {
    return D.S(e, r);
  },
  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)
  X: function(e, r, a, n) {
    var i = n._originalDate || e, o = i.getTimezoneOffset();
    if (o === 0)
      return "Z";
    switch (r) {
      case "X":
        return B(o);
      case "XXXX":
      case "XX":
        return S(o);
      case "XXXXX":
      case "XXX":
      default:
        return S(o, ":");
    }
  },
  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)
  x: function(e, r, a, n) {
    var i = n._originalDate || e, o = i.getTimezoneOffset();
    switch (r) {
      case "x":
        return B(o);
      case "xxxx":
      case "xx":
        return S(o);
      case "xxxxx":
      case "xxx":
      default:
        return S(o, ":");
    }
  },
  // Timezone (GMT)
  O: function(e, r, a, n) {
    var i = n._originalDate || e, o = i.getTimezoneOffset();
    switch (r) {
      case "O":
      case "OO":
      case "OOO":
        return "GMT" + G(o, ":");
      case "OOOO":
      default:
        return "GMT" + S(o, ":");
    }
  },
  // Timezone (specific non-location)
  z: function(e, r, a, n) {
    var i = n._originalDate || e, o = i.getTimezoneOffset();
    switch (r) {
      case "z":
      case "zz":
      case "zzz":
        return "GMT" + G(o, ":");
      case "zzzz":
      default:
        return "GMT" + S(o, ":");
    }
  },
  // Seconds timestamp
  t: function(e, r, a, n) {
    var i = n._originalDate || e, o = Math.floor(i.getTime() / 1e3);
    return m(o, r.length);
  },
  // Milliseconds timestamp
  T: function(e, r, a, n) {
    var i = n._originalDate || e, o = i.getTime();
    return m(o, r.length);
  }
};
function G(t, e) {
  var r = t > 0 ? "-" : "+", a = Math.abs(t), n = Math.floor(a / 60), i = a % 60;
  if (i === 0)
    return r + String(n);
  var o = e;
  return r + String(n) + o + m(i, 2);
}
function B(t, e) {
  if (t % 60 === 0) {
    var r = t > 0 ? "-" : "+";
    return r + m(Math.abs(t) / 60, 2);
  }
  return S(t, e);
}
function S(t, e) {
  var r = e || "", a = t > 0 ? "-" : "+", n = Math.abs(t), i = m(Math.floor(n / 60), 2), o = m(n % 60, 2);
  return a + i + r + o;
}
var z = function(e, r) {
  switch (e) {
    case "P":
      return r.date({
        width: "short"
      });
    case "PP":
      return r.date({
        width: "medium"
      });
    case "PPP":
      return r.date({
        width: "long"
      });
    case "PPPP":
    default:
      return r.date({
        width: "full"
      });
  }
}, ae = function(e, r) {
  switch (e) {
    case "p":
      return r.time({
        width: "short"
      });
    case "pp":
      return r.time({
        width: "medium"
      });
    case "ppp":
      return r.time({
        width: "long"
      });
    case "pppp":
    default:
      return r.time({
        width: "full"
      });
  }
}, _e = function(e, r) {
  var a = e.match(/(P+)(p+)?/) || [], n = a[1], i = a[2];
  if (!i)
    return z(e, r);
  var o;
  switch (n) {
    case "P":
      o = r.dateTime({
        width: "short"
      });
      break;
    case "PP":
      o = r.dateTime({
        width: "medium"
      });
      break;
    case "PPP":
      o = r.dateTime({
        width: "long"
      });
      break;
    case "PPPP":
    default:
      o = r.dateTime({
        width: "full"
      });
      break;
  }
  return o.replace("{{date}}", z(n, r)).replace("{{time}}", ae(i, r));
}, Ye = {
  p: ae,
  P: _e
}, Ie = ["D", "DD"], $e = ["YY", "YYYY"];
function Re(t) {
  return Ie.indexOf(t) !== -1;
}
function Fe(t) {
  return $e.indexOf(t) !== -1;
}
function J(t, e, r) {
  if (t === "YYYY")
    throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e, "`) for formatting years to the input `").concat(r, "`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));
  if (t === "YY")
    throw new RangeError("Use `yy` instead of `YY` (in `".concat(e, "`) for formatting years to the input `").concat(r, "`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));
  if (t === "D")
    throw new RangeError("Use `d` instead of `D` (in `".concat(e, "`) for formatting days of the month to the input `").concat(r, "`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));
  if (t === "DD")
    throw new RangeError("Use `dd` instead of `DD` (in `".concat(e, "`) for formatting days of the month to the input `").concat(r, "`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));
}
var He = {
  lessThanXSeconds: {
    one: "less than a second",
    other: "less than {{count}} seconds"
  },
  xSeconds: {
    one: "1 second",
    other: "{{count}} seconds"
  },
  halfAMinute: "half a minute",
  lessThanXMinutes: {
    one: "less than a minute",
    other: "less than {{count}} minutes"
  },
  xMinutes: {
    one: "1 minute",
    other: "{{count}} minutes"
  },
  aboutXHours: {
    one: "about 1 hour",
    other: "about {{count}} hours"
  },
  xHours: {
    one: "1 hour",
    other: "{{count}} hours"
  },
  xDays: {
    one: "1 day",
    other: "{{count}} days"
  },
  aboutXWeeks: {
    one: "about 1 week",
    other: "about {{count}} weeks"
  },
  xWeeks: {
    one: "1 week",
    other: "{{count}} weeks"
  },
  aboutXMonths: {
    one: "about 1 month",
    other: "about {{count}} months"
  },
  xMonths: {
    one: "1 month",
    other: "{{count}} months"
  },
  aboutXYears: {
    one: "about 1 year",
    other: "about {{count}} years"
  },
  xYears: {
    one: "1 year",
    other: "{{count}} years"
  },
  overXYears: {
    one: "over 1 year",
    other: "over {{count}} years"
  },
  almostXYears: {
    one: "almost 1 year",
    other: "almost {{count}} years"
  }
}, Le = function(e, r, a) {
  var n, i = He[e];
  return typeof i == "string" ? n = i : r === 1 ? n = i.one : n = i.other.replace("{{count}}", r.toString()), a != null && a.addSuffix ? a.comparison && a.comparison > 0 ? "in " + n : n + " ago" : n;
};
function j(t) {
  return function() {
    var e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, r = e.width ? String(e.width) : t.defaultWidth, a = t.formats[r] || t.formats[t.defaultWidth];
    return a;
  };
}
var qe = {
  full: "EEEE, MMMM do, y",
  long: "MMMM do, y",
  medium: "MMM d, y",
  short: "MM/dd/yyyy"
}, Xe = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
}, je = {
  full: "{{date}} 'at' {{time}}",
  long: "{{date}} 'at' {{time}}",
  medium: "{{date}}, {{time}}",
  short: "{{date}}, {{time}}"
}, Ae = {
  date: j({
    formats: qe,
    defaultWidth: "full"
  }),
  time: j({
    formats: Xe,
    defaultWidth: "full"
  }),
  dateTime: j({
    formats: je,
    defaultWidth: "full"
  })
}, Qe = {
  lastWeek: "'last' eeee 'at' p",
  yesterday: "'yesterday at' p",
  today: "'today at' p",
  tomorrow: "'tomorrow at' p",
  nextWeek: "eeee 'at' p",
  other: "P"
}, Ve = function(e, r, a, n) {
  return Qe[e];
};
function N(t) {
  return function(e, r) {
    var a = r != null && r.context ? String(r.context) : "standalone", n;
    if (a === "formatting" && t.formattingValues) {
      var i = t.defaultFormattingWidth || t.defaultWidth, o = r != null && r.width ? String(r.width) : i;
      n = t.formattingValues[o] || t.formattingValues[i];
    } else {
      var s = t.defaultWidth, u = r != null && r.width ? String(r.width) : t.defaultWidth;
      n = t.values[u] || t.values[s];
    }
    var d = t.argumentCallback ? t.argumentCallback(e) : e;
    return n[d];
  };
}
var Ge = {
  narrow: ["B", "A"],
  abbreviated: ["BC", "AD"],
  wide: ["Before Christ", "Anno Domini"]
}, Be = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["Q1", "Q2", "Q3", "Q4"],
  wide: ["1st quarter", "2nd quarter", "3rd quarter", "4th quarter"]
}, ze = {
  narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"],
  abbreviated: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
  wide: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
}, Je = {
  narrow: ["S", "M", "T", "W", "T", "F", "S"],
  short: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
  abbreviated: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  wide: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
}, Ze = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  }
}, Ke = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  }
}, et = function(e, r) {
  var a = Number(e), n = a % 100;
  if (n > 20 || n < 10)
    switch (n % 10) {
      case 1:
        return a + "st";
      case 2:
        return a + "nd";
      case 3:
        return a + "rd";
    }
  return a + "th";
}, tt = {
  ordinalNumber: et,
  era: N({
    values: Ge,
    defaultWidth: "wide"
  }),
  quarter: N({
    values: Be,
    defaultWidth: "wide",
    argumentCallback: function(e) {
      return e - 1;
    }
  }),
  month: N({
    values: ze,
    defaultWidth: "wide"
  }),
  day: N({
    values: Je,
    defaultWidth: "wide"
  }),
  dayPeriod: N({
    values: Ze,
    defaultWidth: "wide",
    formattingValues: Ke,
    defaultFormattingWidth: "wide"
  })
};
function E(t) {
  return function(e) {
    var r = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, a = r.width, n = a && t.matchPatterns[a] || t.matchPatterns[t.defaultMatchWidth], i = e.match(n);
    if (!i)
      return null;
    var o = i[0], s = a && t.parsePatterns[a] || t.parsePatterns[t.defaultParseWidth], u = Array.isArray(s) ? at(s, function(l) {
      return l.test(o);
    }) : rt(s, function(l) {
      return l.test(o);
    }), d;
    d = t.valueCallback ? t.valueCallback(u) : u, d = r.valueCallback ? r.valueCallback(d) : d;
    var f = e.slice(o.length);
    return {
      value: d,
      rest: f
    };
  };
}
function rt(t, e) {
  for (var r in t)
    if (t.hasOwnProperty(r) && e(t[r]))
      return r;
}
function at(t, e) {
  for (var r = 0; r < t.length; r++)
    if (e(t[r]))
      return r;
}
function nt(t) {
  return function(e) {
    var r = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, a = e.match(t.matchPattern);
    if (!a) return null;
    var n = a[0], i = e.match(t.parsePattern);
    if (!i) return null;
    var o = t.valueCallback ? t.valueCallback(i[0]) : i[0];
    o = r.valueCallback ? r.valueCallback(o) : o;
    var s = e.slice(n.length);
    return {
      value: o,
      rest: s
    };
  };
}
var it = /^(\d+)(th|st|nd|rd)?/i, ot = /\d+/i, ut = {
  narrow: /^(b|a)/i,
  abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,
  wide: /^(before christ|before common era|anno domini|common era)/i
}, st = {
  any: [/^b/i, /^(a|c)/i]
}, dt = {
  narrow: /^[1234]/i,
  abbreviated: /^q[1234]/i,
  wide: /^[1234](th|st|nd|rd)? quarter/i
}, lt = {
  any: [/1/i, /2/i, /3/i, /4/i]
}, ct = {
  narrow: /^[jfmasond]/i,
  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i
}, ft = {
  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],
  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]
}, mt = {
  narrow: /^[smtwf]/i,
  short: /^(su|mo|tu|we|th|fr|sa)/i,
  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,
  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i
}, ht = {
  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]
}, vt = {
  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,
  any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i
}, gt = {
  any: {
    am: /^a/i,
    pm: /^p/i,
    midnight: /^mi/i,
    noon: /^no/i,
    morning: /morning/i,
    afternoon: /afternoon/i,
    evening: /evening/i,
    night: /night/i
  }
}, wt = {
  ordinalNumber: nt({
    matchPattern: it,
    parsePattern: ot,
    valueCallback: function(e) {
      return parseInt(e, 10);
    }
  }),
  era: E({
    matchPatterns: ut,
    defaultMatchWidth: "wide",
    parsePatterns: st,
    defaultParseWidth: "any"
  }),
  quarter: E({
    matchPatterns: dt,
    defaultMatchWidth: "wide",
    parsePatterns: lt,
    defaultParseWidth: "any",
    valueCallback: function(e) {
      return e + 1;
    }
  }),
  month: E({
    matchPatterns: ct,
    defaultMatchWidth: "wide",
    parsePatterns: ft,
    defaultParseWidth: "any"
  }),
  day: E({
    matchPatterns: mt,
    defaultMatchWidth: "wide",
    parsePatterns: ht,
    defaultParseWidth: "any"
  }),
  dayPeriod: E({
    matchPatterns: vt,
    defaultMatchWidth: "any",
    parsePatterns: gt,
    defaultParseWidth: "any"
  })
}, ne = {
  code: "en-US",
  formatDistance: Le,
  formatLong: Ae,
  formatRelative: Ve,
  localize: tt,
  match: wt,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1
  }
}, yt = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g, bt = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g, pt = /^'([^]*?)'?$/, Tt = /''/g, Mt = /[a-zA-Z]/;
function Dt(t, e, r) {
  var a, n, i, o, s, u, d, f, l, w, c, g, C, b;
  v(2, arguments);
  var U = String(e), T = k(), p = (a = (n = void 0) !== null && n !== void 0 ? n : T.locale) !== null && a !== void 0 ? a : ne, q = O((i = (o = (s = (u = void 0) !== null && u !== void 0 ? u : void 0) !== null && s !== void 0 ? s : T.firstWeekContainsDate) !== null && o !== void 0 ? o : (d = T.locale) === null || d === void 0 || (f = d.options) === null || f === void 0 ? void 0 : f.firstWeekContainsDate) !== null && i !== void 0 ? i : 1);
  if (!(q >= 1 && q <= 7))
    throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");
  var X = O((l = (w = (c = (g = void 0) !== null && g !== void 0 ? g : void 0) !== null && c !== void 0 ? c : T.weekStartsOn) !== null && w !== void 0 ? w : (C = T.locale) === null || C === void 0 || (b = C.options) === null || b === void 0 ? void 0 : b.weekStartsOn) !== null && l !== void 0 ? l : 0);
  if (!(X >= 0 && X <= 6))
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  if (!p.localize)
    throw new RangeError("locale must contain localize property");
  if (!p.formatLong)
    throw new RangeError("locale must contain formatLong property");
  var Y = h(t);
  if (!_(Y))
    throw new RangeError("Invalid time value");
  var ue = V(Y), se = Oe(Y, ue), de = {
    firstWeekContainsDate: q,
    weekStartsOn: X,
    locale: p,
    _originalDate: Y
  }, le = U.match(bt).map(function(y) {
    var M = y[0];
    if (M === "p" || M === "P") {
      var I = Ye[M];
      return I(y, p.formatLong);
    }
    return y;
  }).join("").match(yt).map(function(y) {
    if (y === "''")
      return "'";
    var M = y[0];
    if (M === "'")
      return Ot(y);
    var I = ke[M];
    if (I)
      return Fe(y) && J(y, e, String(t)), Re(y) && J(y, e, String(t)), I(se, y, p.localize, de);
    if (M.match(Mt))
      throw new RangeError("Format string contains an unescaped latin alphabet character `" + M + "`");
    return y;
  }).join("");
  return le;
}
function Ot(t) {
  var e = t.match(pt);
  return e ? e[1].replace(Tt, "'") : t;
}
function ie(t, e) {
  if (t == null)
    throw new TypeError("assign requires that input parameter not be null or undefined");
  for (var r in e)
    Object.prototype.hasOwnProperty.call(e, r) && (t[r] = e[r]);
  return t;
}
function Ct(t) {
  return ie({}, t);
}
var Z = 1440, St = 2520, A = 43200, Pt = 86400;
function xt(t, e, r) {
  var a, n;
  v(2, arguments);
  var i = k(), o = (a = (n = r == null ? void 0 : r.locale) !== null && n !== void 0 ? n : i.locale) !== null && a !== void 0 ? a : ne;
  if (!o.formatDistance)
    throw new RangeError("locale must contain formatDistance property");
  var s = R(t, e);
  if (isNaN(s))
    throw new RangeError("Invalid time value");
  var u = ie(Ct(r), {
    addSuffix: !!(r != null && r.addSuffix),
    comparison: s
  }), d, f;
  s > 0 ? (d = h(e), f = h(t)) : (d = h(t), f = h(e));
  var l = De(f, d), w = (V(f) - V(d)) / 1e3, c = Math.round((l - w) / 60), g;
  if (c < 2)
    return r != null && r.includeSeconds ? l < 5 ? o.formatDistance("lessThanXSeconds", 5, u) : l < 10 ? o.formatDistance("lessThanXSeconds", 10, u) : l < 20 ? o.formatDistance("lessThanXSeconds", 20, u) : l < 40 ? o.formatDistance("halfAMinute", 0, u) : l < 60 ? o.formatDistance("lessThanXMinutes", 1, u) : o.formatDistance("xMinutes", 1, u) : c === 0 ? o.formatDistance("lessThanXMinutes", 1, u) : o.formatDistance("xMinutes", c, u);
  if (c < 45)
    return o.formatDistance("xMinutes", c, u);
  if (c < 90)
    return o.formatDistance("aboutXHours", 1, u);
  if (c < Z) {
    var C = Math.round(c / 60);
    return o.formatDistance("aboutXHours", C, u);
  } else {
    if (c < St)
      return o.formatDistance("xDays", 1, u);
    if (c < A) {
      var b = Math.round(c / Z);
      return o.formatDistance("xDays", b, u);
    } else if (c < Pt)
      return g = Math.round(c / A), o.formatDistance("aboutXMonths", g, u);
  }
  if (g = Me(f, d), g < 12) {
    var U = Math.round(c / A);
    return o.formatDistance("xMonths", U, u);
  } else {
    var T = g % 12, p = Math.floor(g / 12);
    return T < 3 ? o.formatDistance("aboutXYears", p, u) : T < 9 ? o.formatDistance("overXYears", p, u) : o.formatDistance("almostXYears", p + 1, u);
  }
}
function Ut(t, e) {
  return v(1, arguments), xt(t, Date.now(), e);
}
function Nt(t, e) {
  var r;
  v(1, arguments);
  var a = O((r = void 0) !== null && r !== void 0 ? r : 2);
  if (a !== 2 && a !== 1 && a !== 0)
    throw new RangeError("additionalDigits must be 0, 1 or 2");
  if (!(typeof t == "string" || Object.prototype.toString.call(t) === "[object String]"))
    return /* @__PURE__ */ new Date(NaN);
  var n = _t(t), i;
  if (n.date) {
    var o = Yt(n.date, a);
    i = It(o.restDateString, o.year);
  }
  if (!i || isNaN(i.getTime()))
    return /* @__PURE__ */ new Date(NaN);
  var s = i.getTime(), u = 0, d;
  if (n.time && (u = $t(n.time), isNaN(u)))
    return /* @__PURE__ */ new Date(NaN);
  if (n.timezone) {
    if (d = Rt(n.timezone), isNaN(d))
      return /* @__PURE__ */ new Date(NaN);
  } else {
    var f = new Date(s + u), l = /* @__PURE__ */ new Date(0);
    return l.setFullYear(f.getUTCFullYear(), f.getUTCMonth(), f.getUTCDate()), l.setHours(f.getUTCHours(), f.getUTCMinutes(), f.getUTCSeconds(), f.getUTCMilliseconds()), l;
  }
  return new Date(s + u + d);
}
var $ = {
  dateTimeDelimiter: /[T ]/,
  timeZoneDelimiter: /[Z ]/i,
  timezone: /([Z+-].*)$/
}, Et = /^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/, Wt = /^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/, kt = /^([+-])(\d{2})(?::?(\d{2}))?$/;
function _t(t) {
  var e = {}, r = t.split($.dateTimeDelimiter), a;
  if (r.length > 2)
    return e;
  if (/:/.test(r[0]) ? a = r[0] : (e.date = r[0], a = r[1], $.timeZoneDelimiter.test(e.date) && (e.date = t.split($.timeZoneDelimiter)[0], a = t.substr(e.date.length, t.length))), a) {
    var n = $.timezone.exec(a);
    n ? (e.time = a.replace(n[1], ""), e.timezone = n[1]) : e.time = a;
  }
  return e;
}
function Yt(t, e) {
  var r = new RegExp("^(?:(\\d{4}|[+-]\\d{" + (4 + e) + "})|(\\d{2}|[+-]\\d{" + (2 + e) + "})$)"), a = t.match(r);
  if (!a) return {
    year: NaN,
    restDateString: ""
  };
  var n = a[1] ? parseInt(a[1]) : null, i = a[2] ? parseInt(a[2]) : null;
  return {
    year: i === null ? n : i * 100,
    restDateString: t.slice((a[1] || a[2]).length)
  };
}
function It(t, e) {
  if (e === null) return /* @__PURE__ */ new Date(NaN);
  var r = t.match(Et);
  if (!r) return /* @__PURE__ */ new Date(NaN);
  var a = !!r[4], n = W(r[1]), i = W(r[2]) - 1, o = W(r[3]), s = W(r[4]), u = W(r[5]) - 1;
  if (a)
    return Xt(e, s, u) ? Ft(e, s, u) : /* @__PURE__ */ new Date(NaN);
  var d = /* @__PURE__ */ new Date(0);
  return !Lt(e, i, o) || !qt(e, n) ? /* @__PURE__ */ new Date(NaN) : (d.setUTCFullYear(e, i, Math.max(n, o)), d);
}
function W(t) {
  return t ? parseInt(t) : 1;
}
function $t(t) {
  var e = t.match(Wt);
  if (!e) return NaN;
  var r = Q(e[1]), a = Q(e[2]), n = Q(e[3]);
  return jt(r, a, n) ? r * ee + a * K + n * 1e3 : NaN;
}
function Q(t) {
  return t && parseFloat(t.replace(",", ".")) || 0;
}
function Rt(t) {
  if (t === "Z") return 0;
  var e = t.match(kt);
  if (!e) return 0;
  var r = e[1] === "+" ? -1 : 1, a = parseInt(e[2]), n = e[3] && parseInt(e[3]) || 0;
  return At(a, n) ? r * (a * ee + n * K) : NaN;
}
function Ft(t, e, r) {
  var a = /* @__PURE__ */ new Date(0);
  a.setUTCFullYear(t, 0, 4);
  var n = a.getUTCDay() || 7, i = (e - 1) * 7 + r + 1 - n;
  return a.setUTCDate(a.getUTCDate() + i), a;
}
var Ht = [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function oe(t) {
  return t % 400 === 0 || t % 4 === 0 && t % 100 !== 0;
}
function Lt(t, e, r) {
  return e >= 0 && e <= 11 && r >= 1 && r <= (Ht[e] || (oe(t) ? 29 : 28));
}
function qt(t, e) {
  return e >= 1 && e <= (oe(t) ? 366 : 365);
}
function Xt(t, e, r) {
  return e >= 1 && e <= 53 && r >= 0 && r <= 6;
}
function jt(t, e, r) {
  return t === 24 ? e === 0 && r === 0 : r >= 0 && r < 60 && e >= 0 && e < 60 && t >= 0 && t < 25;
}
function At(t, e) {
  return e >= 0 && e <= 59;
}
const Qt = (t, e = "MMM dd, yyyy") => {
  try {
    const r = x(t);
    return _(r) ? Dt(r, e) : "Invalid Date";
  } catch (r) {
    return console.error("Error formatting date:", r), "Invalid Date";
  }
}, Gt = (t, e = "MMM dd, yyyy h:mm a") => Qt(t, e), Bt = (t) => {
  try {
    const e = x(t);
    return _(e) ? Ut(e, { addSuffix: !0 }) : "Invalid Date";
  } catch (e) {
    return console.error("Error formatting relative time:", e), "Invalid Date";
  }
}, zt = (t) => {
  try {
    const e = x(t);
    if (!_(e))
      throw new Error("Invalid date");
    return e.toISOString();
  } catch (e) {
    throw console.error("Error formatting date for API:", e), e;
  }
}, x = (t) => {
  if (t instanceof Date)
    return t;
  if (typeof t == "string")
    return t.includes("T") || t.includes("Z") ? Nt(t) : new Date(t);
  if (typeof t == "number")
    return new Date(t);
  throw new Error("Invalid date input");
}, Jt = (t) => {
  try {
    const e = x(t);
    return _(e);
  } catch {
    return !1;
  }
}, Zt = (t) => {
  const e = x(t);
  return e.setHours(0, 0, 0, 0), e;
}, Kt = (t) => {
  const e = x(t);
  return e.setHours(23, 59, 59, 999), e;
}, er = {
  SHORT: "MM/dd/yyyy",
  MEDIUM: "MMM dd, yyyy",
  LONG: "MMMM dd, yyyy",
  ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
  TIME_12: "h:mm a",
  TIME_24: "HH:mm",
  DATETIME_SHORT: "MM/dd/yyyy h:mm a",
  DATETIME_MEDIUM: "MMM dd, yyyy h:mm a",
  DATETIME_LONG: "MMMM dd, yyyy h:mm a"
}, tr = (t) => {
  const e = [];
  return t ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t) || e.push("Please enter a valid email address") : e.push("Email is required"), {
    isValid: e.length === 0,
    errors: e
  };
}, rr = (t) => {
  const e = [];
  return t ? (t.length < 8 && e.push("Password must be at least 8 characters long"), /(?=.*[a-z])/.test(t) || e.push("Password must contain at least one lowercase letter"), /(?=.*[A-Z])/.test(t) || e.push("Password must contain at least one uppercase letter"), /(?=.*\d)/.test(t) || e.push("Password must contain at least one number"), /(?=.*[@$!%*?&])/.test(t) || e.push("Password must contain at least one special character (@$!%*?&)")) : e.push("Password is required"), {
    isValid: e.length === 0,
    errors: e
  };
}, ar = (t) => {
  const e = [];
  return t ? t.replace(/\D/g, "").length !== 10 && e.push("Phone number must be 10 digits") : e.push("Phone number is required"), {
    isValid: e.length === 0,
    errors: e
  };
}, nr = (t, e) => {
  const r = [];
  return (t == null || t === "") && r.push(`${e} is required`), {
    isValid: r.length === 0,
    errors: r
  };
}, ir = (t, e, r, a) => {
  const n = [];
  return t.length < e && n.push(`${a} must be at least ${e} characters long`), t.length > r && n.push(`${a} must be no more than ${r} characters long`), {
    isValid: n.length === 0,
    errors: n
  };
}, or = (t, e, r, a) => {
  const n = [];
  return isNaN(t) ? n.push(`${a} must be a valid number`) : (t < e && n.push(`${a} must be at least ${e}`), t > r && n.push(`${a} must be no more than ${r}`)), {
    isValid: n.length === 0,
    errors: n
  };
}, ur = (t) => {
  const e = [];
  if (!t)
    e.push("URL is required");
  else
    try {
      new URL(t);
    } catch {
      e.push("Please enter a valid URL");
    }
  return {
    isValid: e.length === 0,
    errors: e
  };
}, Vt = (...t) => {
  const e = t.flatMap((r) => r.errors);
  return {
    isValid: e.length === 0,
    errors: e
  };
}, sr = (t, e) => {
  const r = Object.keys(e).map((a) => {
    const n = e[a];
    return n(t[a]);
  });
  return Vt(...r);
};
export {
  er as DATE_FORMATS,
  cr as LogLevel,
  fr as Logger,
  Vt as combineValidationResults,
  Qt as formatDate,
  zt as formatDateForApi,
  Gt as formatDateTime,
  Bt as formatRelativeTime,
  Kt as getEndOfDay,
  Zt as getStartOfDay,
  Jt as isValidDate,
  mr as logger,
  x as parseDate,
  tr as validateEmail,
  ir as validateLength,
  or as validateNumberRange,
  sr as validateObject,
  rr as validatePassword,
  ar as validatePhoneNumber,
  nr as validateRequired,
  ur as validateUrl
};
