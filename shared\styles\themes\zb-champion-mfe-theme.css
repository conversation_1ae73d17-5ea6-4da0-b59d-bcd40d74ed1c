/**
 * ZB Champion MFE Theme - Exact Replica of Original ZB Champion Standard Theme
 *
 * This theme is used for standalone MFE deployments.
 * Contains the EXACT styling from zb-champion-standard-theme.css
 * with data-theme="mfe" selectors for theme switching integration.
 */

/* === ZB CHAMPION BRAND LOGO === */
[data-theme="mfe"] .zb-brand-logo {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='207' height='72' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient x1='24.97%25' y1='41.659%25' x2='75.003%25' y2='58.334%25' id='a'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='24%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='72%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.6' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='71.101%25' y1='35.489%25' x2='36.228%25' y2='59.492%25' id='b'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3ClinearGradient x1='42.462%25' y1='29.337%25' x2='63.793%25' y2='70.663%25' id='c'%3E%3Cstop stop-color='%23C20000' stop-opacity='.1' offset='0%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.2' offset='20%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.44' offset='61%25'/%3E%3Cstop stop-color='%23C20000' stop-opacity='.7' offset='100%25'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill-rule='nonzero' fill='none'%3E%3Cpath d='M137.678 31.388v3.59h-4.375v9.355c0 1.67.79 1.993 2.25 1.993.852 0 1.494-.164 1.8-.258l.325-.103v3.598l-.183.045c-1.227.311-2.254.415-3.767.415-1.048 0-4.466-.346-4.466-4.829V34.978h-2.567v-.242c-.003-.175-.003-1.407 0-2.558v-.79h2.567v-4.605l4.041-1.415v6.02h4.375zm27.135-5.87l-4.837 24.119h-4.106l-4.274-16.795-4.345 16.795h-4.04l-5.056-24.119h4.216l3.277 16.366 4.074-16.366h3.931c.39 1.53 4.081 16.163 4.129 16.346.029-.195 2.903-15.81 3.004-16.346h4.027zm4.188 13.106c.104-1.721 1.32-3.966 3.89-3.966 2.789 0 3.638 2.464 3.73 3.966h-7.62zm4.07-7.563c-3.08 0-8.28 2.007-8.28 9.538 0 8.969 7.135 9.424 8.565 9.424 3.03 0 4.382-.632 5.71-1.253l.146-.068v-3.814l-.384.23c-.965.603-2.836 1.28-4.93 1.28-4.242 0-4.84-3.032-4.896-4.31h11.659l.023-.206c.368-2.517.004-6.04-2.062-8.44-1.365-1.58-3.236-2.383-5.549-2.383m22.548 13.497c0 2.736-2.38 5.503-6.91 5.503-1.99 0-4.184-.494-5.76-1.255l-.143-.07v-3.917l.374.203c1.312.706 3.414 1.456 5.42 1.456 1.83 0 2.882-.655 2.882-1.801 0-1.078-.645-1.41-2.238-2.114l-.628-.268c-.77-.333-1.444-.63-2.554-1.138-1.064-.48-3.548-1.6-3.548-4.93 0-1.792 1.375-5.163 6.536-5.163 2.13 0 4.236.575 5.067.977l.147.072v3.847l-.374-.175c-1.648-.814-3.094-1.191-4.696-1.191-.59 0-2.542.118-2.542 1.481 0 1.036 1.23 1.577 2.222 2.024l.192.08c.715.316 1.277.58 1.76.77l.52.221c3.108 1.36 4.27 2.81 4.27 5.386m-91.263-19.042h3.944v24.119h-3.835L93.938 32.833v16.798h-3.939V25.512h3.933l10.421 16.926V25.512h.002zm98.269 9.461v9.356c0 1.675.787 1.993 2.255 1.993.832 0 1.47-.164 1.791-.252l.33-.11v3.599l-.207.045c-1.209.31-2.234.414-3.75.414-1.048 0-4.447-.345-4.447-4.828V34.973h-2.582v-.242c-.01-.174-.01-1.407 0-2.557v-.79h2.582v-4.606l4.028-1.414v6.02H207v3.59h-4.376zm-81.109 9.333c-.463.57-1.915 2.08-4.174 2.08-1.737 0-2.847-.999-2.847-2.544s1.266-2.494 3.472-2.494h3.55V44.306zM118.403 31c-2.05 0-4.042.356-5.444.967l-.16.059v3.715l.366-.185c.947-.459 3.217-.876 4.64-.876 3.543 0 3.7 1.349 3.71 3.103h-3.788c-5.017 0-7.301 3.145-7.301 6.06 0 4.066 3.233 6.177 6.442 6.177 2.186 0 3.563-.82 4.673-1.803v1.415h3.996V37.3c0-5.686-4.99-6.3-7.134-6.3M72 36c0 33.386-2.613 36-36 36C2.614 72 0 69.386 0 36S2.614 0 36 0c33.387 0 36 2.614 36 36z' fill='%233C1053'/%3E%3Cpath fill='%23E90000' d='M46.754 43.914H32.426l7.16 12.414h14.332z'/%3E%3Cpath fill='%23C20000' d='M53.914 31.513l7.164 12.407-7.16 12.408-7.164-12.414z'/%3E%3Cpath fill='%23E90000' d='M43.164 12.895l.002-.001H28.838l-7.16 12.402h14.327z'/%3E%3Cpath fill='%23C20000' d='M36.005 25.296l7.164 12.412 7.16-12.406-7.165-12.408z'/%3E%3Cpath fill='%23E90000' d='M25.255 43.913l7.16-12.401H18.087l-7.16 12.4-.005.007z'/%3E%3Cpath fill='%23C20000' d='M32.418 56.328H18.087l-7.165-12.41.005-.005h14.328z'/%3E%3Cpath fill='url(%23a)' d='M46.743 31.512l-3.576 6.194h-7.164l-3.577 6.208h14.328l7.16-12.402z'/%3E%3Cpath fill='url(%23b)' d='M25.274 31.512h7.14l3.59 6.194h7.163l-7.162-12.411H21.678z'/%3E%3Cpath fill='url(%23c)' d='M35.997 50.106l-3.57-6.192 3.576-6.208-3.59-6.194-7.158 12.4 7.163 12.416z'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    width: 207px;
    height: 50px;
}

@media (max-width: 840px) {
    [data-theme="mfe"] .zb-brand-logo {
        height: 32px;
    }
}

  /* Background Colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f2f2f8;
  --theme-bg-tertiary: #f9f9fc;
  --theme-bg-quaternary: #cccfd0;
  --theme-bg-overlay: rgba(248, 248, 248, 0.9);

  /* Text Colors */
  --theme-text-primary: #333;
  --theme-text-secondary: #646068;
  --theme-text-tertiary: #747077;
  --theme-text-disabled: #999;
  --theme-text-inverse: #ffffff;

  /* Border Colors */
  --theme-border-primary: #646068;
  --theme-border-secondary: #cccfd0;
  --theme-border-tertiary: #d3d3d3;
  --theme-border-focus: #5e10b1;
  --theme-border-error: #cf223f;
  --theme-border-success: #107c10;
  --theme-border-warning: #ff8c00;

  /* Status Colors */
  --theme-success: #107c10;
  --theme-success-light: #dff6dd;
  --theme-error: #cf223f;
  --theme-error-light: #fdf2f2;
  --theme-warning: #ff8c00;
  --theme-warning-light: #fff4ce;
  --theme-info: #5e10b1;
  --theme-info-light: #f2eaf9;

  /* === TYPOGRAPHY === */
  /* Font families - ZB Champion brand fonts */
  --theme-font-family: "RNHouseSans", Arial, sans-serif;
  --theme-font-family-heading: "knileblack", Arial, sans-serif;

  /* Font sizes - inherit from base theme */
  --theme-font-size-xs: var(--zb-font-size-xs);
  --theme-font-size-sm: var(--zb-font-size-sm);
  --theme-font-size-base: var(--zb-font-size-base);
  --theme-font-size-lg: var(--zb-font-size-lg);
  --theme-font-size-xl: var(--zb-font-size-xl);
  --theme-font-size-2xl: var(--zb-font-size-2xl);
  --theme-font-size-3xl: var(--zb-font-size-3xl);

  /* Font weights - inherit from base theme */
  --theme-font-weight-normal: var(--zb-font-weight-normal);
  --theme-font-weight-medium: var(--zb-font-weight-medium);
  --theme-font-weight-semibold: var(--zb-font-weight-semibold);
  --theme-font-weight-bold: var(--zb-font-weight-bold);

  /* Line heights - inherit from base theme */
  --theme-line-height-tight: var(--zb-line-height-tight);
  --theme-line-height-normal: var(--zb-line-height-normal);
  --theme-line-height-relaxed: var(--zb-line-height-relaxed);

  /* === SPACING === */
  /* Inherit spacing from base theme */
  --theme-spacing-xs: var(--zb-spacing-xs);
  --theme-spacing-sm: var(--zb-spacing-sm);
  --theme-spacing-md: var(--zb-spacing-md);
  --theme-spacing-lg: var(--zb-spacing-lg);
  --theme-spacing-xl: var(--zb-spacing-xl);
  --theme-spacing-2xl: var(--zb-spacing-2xl);
  --theme-spacing-3xl: var(--zb-spacing-3xl);

  /* === BORDER RADIUS === */
  /* ZB Champion rounded corners - use base theme values */
  --theme-radius-none: 0;
  --theme-radius-sm: var(--zb-input-border-radius);
  --theme-radius-base: var(--zb-input-border-radius);
  --theme-radius-lg: var(--zb-input-border-radius);
  --theme-radius-xl: var(--zb-button-border-radius);
  --theme-radius-full: 9999px;

  /* === SHADOWS === */
  /* ZB Champion shadows - inherit from base */
  --theme-shadow-sm: var(--zb-shadow-sm);
  --theme-shadow-base: var(--zb-shadow-base);
  --theme-shadow-md: var(--zb-shadow-md);
  --theme-shadow-lg: var(--zb-shadow-lg);
  --theme-shadow-xl: var(--zb-shadow-xl);

  /* Z-Index */
  --theme-z-dropdown: 1000;
  --theme-z-sticky: 1020;
  --theme-z-fixed: 1030;
  --theme-z-modal-backdrop: 1040;
  --theme-z-modal: 1050;
  --theme-z-popover: 1060;
  --theme-z-tooltip: 1070;

  /* Component Specific - ZB Champion standards */
  --theme-header-height: 64px;
  --theme-sidebar-width: 320px;
  --theme-sidebar-collapsed-width: 80px;
  --theme-content-max-width: 1400px;

  /* Form Controls - ZB Champion style */
  --theme-input-height: 44px;
  --theme-input-padding: 0 12px;
  --theme-input-border-width: 1px;
  --theme-input-focus-ring: 0 0 0 1px #fff, 0 0 0 3px #5e10b1;

  /* Buttons - ZB Champion style */
  --theme-button-height-sm: 32px;
  --theme-button-height-base: 44px;
  --theme-button-height-lg: 52px;
  --theme-button-padding-sm: 9px 32px;
  --theme-button-padding-base: 12px 32px;
  --theme-button-padding-lg: 16px 32px;

  /* Transitions */
  --theme-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* MFE Specific Variables */
  --mfe-header-bg: var(--theme-secondary-dark);
  --mfe-sidebar-bg: var(--theme-bg-tertiary);
  --mfe-card-bg: var(--theme-bg-primary);
  --mfe-border-radius: var(--theme-radius-lg);
  --mfe-shadow: var(--theme-shadow-base);
  --mfe-spacing-xs: var(--theme-spacing-xs);
  --mfe-spacing-sm: var(--theme-spacing-sm);
  --mfe-spacing-md: var(--theme-spacing-md);
  --mfe-spacing-lg: var(--theme-spacing-lg);
  --mfe-spacing-xl: var(--theme-spacing-xl);

  /* Mobile breakpoint */
  --theme-mobile-breakpoint: 840px;
}

/* MFE Theme Body Styles */
body[data-theme="mfe"] {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  line-height: var(--theme-line-height-normal);
  color: var(--theme-text-secondary);
  background-color: var(--theme-bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === MFE THEME COMPONENT OVERRIDES === */

/* Button System - MFE Theme (ZB Champion) */
[data-theme="mfe"] .zb-button,
[data-theme="mfe"] .theme-button {
  /* Inherit structural styles from base theme */
  height: var(--zb-button-height);
  padding: var(--zb-button-padding-y) var(--zb-button-padding-x);
  border-radius: var(--zb-button-border-radius);
  font-size: var(--zb-button-font-size);
  line-height: var(--zb-button-line-height);
  min-width: var(--zb-button-min-width);
  font-weight: var(--theme-font-weight-normal);
  transition: all var(--zb-transition-base);
}

[data-theme="mfe"] .zb-button-primary,
[data-theme="mfe"] .theme-button-primary {
  background-color: var(--theme-primary);
  border: var(--zb-border-width-thin) solid var(--theme-primary);
  color: var(--theme-text-inverse);
}

[data-theme="mfe"] .zb-button-primary:hover,
[data-theme="mfe"] .theme-button-primary:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

[data-theme="mfe"] .zb-button-primary:disabled,
[data-theme="mfe"] .theme-button-primary:disabled {
  background-color: #bf9fe0;
  border-color: #bf9fe0;
  color: var(--theme-text-inverse);
}

[data-theme="mfe"] .zb-button-secondary,
[data-theme="mfe"] .theme-button-secondary {
  background-color: var(--theme-bg-primary);
  border: var(--zb-border-width-base) solid var(--theme-primary);
  color: var(--theme-primary);
  padding: var(--zb-button-padding-y-secondary) var(--zb-button-padding-x);
}

[data-theme="mfe"] .zb-button-secondary:hover,
[data-theme="mfe"] .theme-button-secondary:hover {
  background-color: var(--theme-primary-light);
  border-color: var(--theme-primary-hover);
  color: var(--theme-primary-hover);
}

[data-theme="mfe"] .zb-button-secondary:disabled,
[data-theme="mfe"] .theme-button-secondary:disabled {
  background-color: var(--theme-bg-primary);
  border-color: #bf9fe0;
  color: #bf9fe0;
}

/* Mobile button adjustments */
@media (max-width: 840px) {
  [data-theme="mfe"] .zb-button,
  [data-theme="mfe"] .theme-button {
    font-size: var(--zb-button-font-size-mobile);
    line-height: var(--zb-button-line-height-mobile);
  }

  [data-theme="mfe"] .zb-button-secondary,
  [data-theme="mfe"] .theme-button-secondary {
    padding: var(--zb-button-padding-y-secondary-mobile) var(--zb-button-padding-x);
  }
}

[data-theme="mfe"] .theme-card {
  background-color: var(--mfe-card-bg);
  border: none;
  border-radius: var(--mfe-border-radius);
  box-shadow: var(--mfe-shadow);
  transition: all var(--theme-transition-base);
}

[data-theme="mfe"] .theme-card:hover {
  box-shadow: var(--theme-shadow-lg);
}

[data-theme="mfe"] .theme-header {
  background-color: var(--mfe-header-bg);
  color: var(--theme-text-inverse);
  height: var(--theme-header-height);
}

[data-theme="mfe"] .theme-sidebar {
  background-color: var(--mfe-sidebar-bg);
  width: var(--theme-sidebar-width);
  border-right: 1px solid var(--theme-border-tertiary);
}

/* Input System - MFE Theme (ZB Champion) */
[data-theme="mfe"] .zb-input,
[data-theme="mfe"] .theme-input {
  /* Inherit structural styles from base theme */
  height: var(--zb-input-height);
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
  border: var(--zb-input-border-width) solid var(--theme-border-primary);
  border-radius: var(--zb-input-border-radius);
  font-family: var(--theme-font-family);
  font-size: var(--zb-input-font-size);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  transition: all var(--zb-transition-base);
}

[data-theme="mfe"] .zb-input:hover,
[data-theme="mfe"] .theme-input:hover {
  border-color: var(--theme-primary);
}

[data-theme="mfe"] .zb-input:focus,
[data-theme="mfe"] .theme-input:focus {
  outline: none;
  border-color: var(--theme-border-primary);
  box-shadow: 0 0 0 var(--zb-focus-ring-offset) var(--theme-bg-primary),
              0 0 0 var(--zb-focus-ring-width) var(--theme-primary);
}

[data-theme="mfe"] .zb-input:disabled,
[data-theme="mfe"] .theme-input:disabled {
  border-color: #c1bfc3;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-disabled);
  cursor: not-allowed;
}

/* Mobile input adjustments */
@media (max-width: 840px) {
  [data-theme="mfe"] .zb-input,
  [data-theme="mfe"] .theme-input {
    height: var(--zb-input-height-mobile);
    font-size: var(--zb-input-font-size-mobile);
  }
}

/* MFE Theme Loading Spinner */
[data-theme="mfe"] .theme-spinner {
  border-color: var(--theme-border-secondary);
  border-top-color: var(--theme-primary);
  animation: spin 1s linear infinite;
}

/* MFE Theme Typography */
[data-theme="mfe"] h1 {
  font-family: var(--theme-font-family-heading);
  font-size: var(--theme-font-size-3xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h2 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-2xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h3 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: 1.6;
  color: var(--theme-text-primary);
}

/* MFE Theme Utilities */
[data-theme="mfe"] .theme-text-primary { color: var(--theme-text-primary); }
[data-theme="mfe"] .theme-text-secondary { color: var(--theme-text-secondary); }
[data-theme="mfe"] .theme-text-success { color: var(--theme-success); }
[data-theme="mfe"] .theme-text-error { color: var(--theme-error); }
[data-theme="mfe"] .theme-text-warning { color: var(--theme-warning); }

[data-theme="mfe"] .theme-bg-primary { background-color: var(--theme-bg-primary); }
[data-theme="mfe"] .theme-bg-secondary { background-color: var(--theme-bg-secondary); }
[data-theme="mfe"] .theme-bg-success { background-color: var(--theme-success-light); }
[data-theme="mfe"] .theme-bg-error { background-color: var(--theme-error-light); }
[data-theme="mfe"] .theme-bg-warning { background-color: var(--theme-warning-light); }
