/**
 * ZB Champion MFE Theme - Modern Micro Frontend Styling
 *
 * This theme is used for standalone MFE deployments.
 * Based on ZB Champion Standard theme with enhanced base theme structure.
 * Uses the same structural styling as CRM theme but with ZB Champion colors.
 */

:root[data-theme="mfe"] {
  /* === BRAND COLORS === */
  /* Primary Colors - ZB Champion Purple */
  --theme-primary: #5e10b1;
  --theme-primary-hover: #3c1053;
  --theme-primary-active: #5a287d;
  --theme-primary-light: #f2eaf9;
  --theme-primary-dark: #3c1053;

  /* Secondary Colors - ZB Champion Gray */
  --theme-secondary: #646068;
  --theme-secondary-hover: #333;
  --theme-secondary-active: #000;
  --theme-secondary-light: #f2f2f8;
  --theme-secondary-dark: #333;

  /* Background Colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f2f2f8;
  --theme-bg-tertiary: #f9f9fc;
  --theme-bg-quaternary: #cccfd0;
  --theme-bg-overlay: rgba(248, 248, 248, 0.9);

  /* Text Colors */
  --theme-text-primary: #333;
  --theme-text-secondary: #646068;
  --theme-text-tertiary: #747077;
  --theme-text-disabled: #999;
  --theme-text-inverse: #ffffff;

  /* Border Colors */
  --theme-border-primary: #646068;
  --theme-border-secondary: #cccfd0;
  --theme-border-tertiary: #d3d3d3;
  --theme-border-focus: #5e10b1;
  --theme-border-error: #cf223f;
  --theme-border-success: #107c10;
  --theme-border-warning: #ff8c00;

  /* Status Colors */
  --theme-success: #107c10;
  --theme-success-light: #dff6dd;
  --theme-error: #cf223f;
  --theme-error-light: #fdf2f2;
  --theme-warning: #ff8c00;
  --theme-warning-light: #fff4ce;
  --theme-info: #5e10b1;
  --theme-info-light: #f2eaf9;

  /* === TYPOGRAPHY === */
  /* Font families - ZB Champion brand fonts */
  --theme-font-family: "RNHouseSans", Arial, sans-serif;
  --theme-font-family-heading: "knileblack", Arial, sans-serif;

  /* Font sizes - inherit from base theme */
  --theme-font-size-xs: var(--zb-font-size-xs);
  --theme-font-size-sm: var(--zb-font-size-sm);
  --theme-font-size-base: var(--zb-font-size-base);
  --theme-font-size-lg: var(--zb-font-size-lg);
  --theme-font-size-xl: var(--zb-font-size-xl);
  --theme-font-size-2xl: var(--zb-font-size-2xl);
  --theme-font-size-3xl: var(--zb-font-size-3xl);

  /* Font weights - inherit from base theme */
  --theme-font-weight-normal: var(--zb-font-weight-normal);
  --theme-font-weight-medium: var(--zb-font-weight-medium);
  --theme-font-weight-semibold: var(--zb-font-weight-semibold);
  --theme-font-weight-bold: var(--zb-font-weight-bold);

  /* Line heights - inherit from base theme */
  --theme-line-height-tight: var(--zb-line-height-tight);
  --theme-line-height-normal: var(--zb-line-height-normal);
  --theme-line-height-relaxed: var(--zb-line-height-relaxed);

  /* === SPACING === */
  /* Inherit spacing from base theme */
  --theme-spacing-xs: var(--zb-spacing-xs);
  --theme-spacing-sm: var(--zb-spacing-sm);
  --theme-spacing-md: var(--zb-spacing-md);
  --theme-spacing-lg: var(--zb-spacing-lg);
  --theme-spacing-xl: var(--zb-spacing-xl);
  --theme-spacing-2xl: var(--zb-spacing-2xl);
  --theme-spacing-3xl: var(--zb-spacing-3xl);

  /* === BORDER RADIUS === */
  /* ZB Champion rounded corners - use base theme values */
  --theme-radius-none: 0;
  --theme-radius-sm: var(--zb-input-border-radius);
  --theme-radius-base: var(--zb-input-border-radius);
  --theme-radius-lg: var(--zb-input-border-radius);
  --theme-radius-xl: var(--zb-button-border-radius);
  --theme-radius-full: 9999px;

  /* === SHADOWS === */
  /* ZB Champion shadows - inherit from base */
  --theme-shadow-sm: var(--zb-shadow-sm);
  --theme-shadow-base: var(--zb-shadow-base);
  --theme-shadow-md: var(--zb-shadow-md);
  --theme-shadow-lg: var(--zb-shadow-lg);
  --theme-shadow-xl: var(--zb-shadow-xl);

  /* Z-Index */
  --theme-z-dropdown: 1000;
  --theme-z-sticky: 1020;
  --theme-z-fixed: 1030;
  --theme-z-modal-backdrop: 1040;
  --theme-z-modal: 1050;
  --theme-z-popover: 1060;
  --theme-z-tooltip: 1070;

  /* Component Specific - ZB Champion standards */
  --theme-header-height: 64px;
  --theme-sidebar-width: 320px;
  --theme-sidebar-collapsed-width: 80px;
  --theme-content-max-width: 1400px;

  /* Form Controls - ZB Champion style */
  --theme-input-height: 44px;
  --theme-input-padding: 0 12px;
  --theme-input-border-width: 1px;
  --theme-input-focus-ring: 0 0 0 1px #fff, 0 0 0 3px #5e10b1;

  /* Buttons - ZB Champion style */
  --theme-button-height-sm: 32px;
  --theme-button-height-base: 44px;
  --theme-button-height-lg: 52px;
  --theme-button-padding-sm: 9px 32px;
  --theme-button-padding-base: 12px 32px;
  --theme-button-padding-lg: 16px 32px;

  /* Transitions */
  --theme-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* MFE Specific Variables */
  --mfe-header-bg: var(--theme-secondary-dark);
  --mfe-sidebar-bg: var(--theme-bg-tertiary);
  --mfe-card-bg: var(--theme-bg-primary);
  --mfe-border-radius: var(--theme-radius-lg);
  --mfe-shadow: var(--theme-shadow-base);
  --mfe-spacing-xs: var(--theme-spacing-xs);
  --mfe-spacing-sm: var(--theme-spacing-sm);
  --mfe-spacing-md: var(--theme-spacing-md);
  --mfe-spacing-lg: var(--theme-spacing-lg);
  --mfe-spacing-xl: var(--theme-spacing-xl);

  /* Mobile breakpoint */
  --theme-mobile-breakpoint: 840px;
}

/* MFE Theme Body Styles */
body[data-theme="mfe"] {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  line-height: var(--theme-line-height-normal);
  color: var(--theme-text-secondary);
  background-color: var(--theme-bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === MFE THEME COMPONENT OVERRIDES === */

/* Button System - MFE Theme (ZB Champion) */
[data-theme="mfe"] .zb-button,
[data-theme="mfe"] .theme-button {
  /* Inherit structural styles from base theme */
  height: var(--zb-button-height);
  padding: var(--zb-button-padding-y) var(--zb-button-padding-x);
  border-radius: var(--zb-button-border-radius);
  font-size: var(--zb-button-font-size);
  line-height: var(--zb-button-line-height);
  min-width: var(--zb-button-min-width);
  font-weight: var(--theme-font-weight-normal);
  transition: all var(--zb-transition-base);
}

[data-theme="mfe"] .zb-button-primary,
[data-theme="mfe"] .theme-button-primary {
  background-color: var(--theme-primary);
  border: var(--zb-border-width-thin) solid var(--theme-primary);
  color: var(--theme-text-inverse);
}

[data-theme="mfe"] .zb-button-primary:hover,
[data-theme="mfe"] .theme-button-primary:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

[data-theme="mfe"] .zb-button-primary:disabled,
[data-theme="mfe"] .theme-button-primary:disabled {
  background-color: #bf9fe0;
  border-color: #bf9fe0;
  color: var(--theme-text-inverse);
}

[data-theme="mfe"] .zb-button-secondary,
[data-theme="mfe"] .theme-button-secondary {
  background-color: var(--theme-bg-primary);
  border: var(--zb-border-width-base) solid var(--theme-primary);
  color: var(--theme-primary);
  padding: var(--zb-button-padding-y-secondary) var(--zb-button-padding-x);
}

[data-theme="mfe"] .zb-button-secondary:hover,
[data-theme="mfe"] .theme-button-secondary:hover {
  background-color: var(--theme-primary-light);
  border-color: var(--theme-primary-hover);
  color: var(--theme-primary-hover);
}

[data-theme="mfe"] .zb-button-secondary:disabled,
[data-theme="mfe"] .theme-button-secondary:disabled {
  background-color: var(--theme-bg-primary);
  border-color: #bf9fe0;
  color: #bf9fe0;
}

/* Mobile button adjustments */
@media (max-width: 840px) {
  [data-theme="mfe"] .zb-button,
  [data-theme="mfe"] .theme-button {
    font-size: var(--zb-button-font-size-mobile);
    line-height: var(--zb-button-line-height-mobile);
  }

  [data-theme="mfe"] .zb-button-secondary,
  [data-theme="mfe"] .theme-button-secondary {
    padding: var(--zb-button-padding-y-secondary-mobile) var(--zb-button-padding-x);
  }
}

[data-theme="mfe"] .theme-card {
  background-color: var(--mfe-card-bg);
  border: none;
  border-radius: var(--mfe-border-radius);
  box-shadow: var(--mfe-shadow);
  transition: all var(--theme-transition-base);
}

[data-theme="mfe"] .theme-card:hover {
  box-shadow: var(--theme-shadow-lg);
}

[data-theme="mfe"] .theme-header {
  background-color: var(--mfe-header-bg);
  color: var(--theme-text-inverse);
  height: var(--theme-header-height);
}

[data-theme="mfe"] .theme-sidebar {
  background-color: var(--mfe-sidebar-bg);
  width: var(--theme-sidebar-width);
  border-right: 1px solid var(--theme-border-tertiary);
}

/* Input System - MFE Theme (ZB Champion) */
[data-theme="mfe"] .zb-input,
[data-theme="mfe"] .theme-input {
  /* Inherit structural styles from base theme */
  height: var(--zb-input-height);
  padding: var(--zb-input-padding-y) var(--zb-input-padding-x);
  border: var(--zb-input-border-width) solid var(--theme-border-primary);
  border-radius: var(--zb-input-border-radius);
  font-family: var(--theme-font-family);
  font-size: var(--zb-input-font-size);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  transition: all var(--zb-transition-base);
}

[data-theme="mfe"] .zb-input:hover,
[data-theme="mfe"] .theme-input:hover {
  border-color: var(--theme-primary);
}

[data-theme="mfe"] .zb-input:focus,
[data-theme="mfe"] .theme-input:focus {
  outline: none;
  border-color: var(--theme-border-primary);
  box-shadow: 0 0 0 var(--zb-focus-ring-offset) var(--theme-bg-primary),
              0 0 0 var(--zb-focus-ring-width) var(--theme-primary);
}

[data-theme="mfe"] .zb-input:disabled,
[data-theme="mfe"] .theme-input:disabled {
  border-color: #c1bfc3;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-disabled);
  cursor: not-allowed;
}

/* Mobile input adjustments */
@media (max-width: 840px) {
  [data-theme="mfe"] .zb-input,
  [data-theme="mfe"] .theme-input {
    height: var(--zb-input-height-mobile);
    font-size: var(--zb-input-font-size-mobile);
  }
}

/* MFE Theme Loading Spinner */
[data-theme="mfe"] .theme-spinner {
  border-color: var(--theme-border-secondary);
  border-top-color: var(--theme-primary);
  animation: spin 1s linear infinite;
}

/* MFE Theme Typography */
[data-theme="mfe"] h1 {
  font-family: var(--theme-font-family-heading);
  font-size: var(--theme-font-size-3xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h2 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-2xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h3 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: 1.6;
  color: var(--theme-text-primary);
}

/* MFE Theme Utilities */
[data-theme="mfe"] .theme-text-primary { color: var(--theme-text-primary); }
[data-theme="mfe"] .theme-text-secondary { color: var(--theme-text-secondary); }
[data-theme="mfe"] .theme-text-success { color: var(--theme-success); }
[data-theme="mfe"] .theme-text-error { color: var(--theme-error); }
[data-theme="mfe"] .theme-text-warning { color: var(--theme-warning); }

[data-theme="mfe"] .theme-bg-primary { background-color: var(--theme-bg-primary); }
[data-theme="mfe"] .theme-bg-secondary { background-color: var(--theme-bg-secondary); }
[data-theme="mfe"] .theme-bg-success { background-color: var(--theme-success-light); }
[data-theme="mfe"] .theme-bg-error { background-color: var(--theme-error-light); }
[data-theme="mfe"] .theme-bg-warning { background-color: var(--theme-warning-light); }
