/**
 * ZB Champion MFE Theme - Modern Micro Frontend Styling
 * 
 * This theme is used for standalone MFE deployments
 * Based on ZB Champion Standard theme with CSS custom properties integration
 */

:root[data-theme="mfe"] {
  /* Primary Colors - ZB Champion Purple */
  --theme-primary: #5e10b1;
  --theme-primary-hover: #3c1053;
  --theme-primary-active: #5a287d;
  --theme-primary-light: #f2eaf9;
  --theme-primary-dark: #3c1053;

  /* Secondary Colors - ZB Champion Gray */
  --theme-secondary: #646068;
  --theme-secondary-hover: #333;
  --theme-secondary-active: #000;
  --theme-secondary-light: #f2f2f8;
  --theme-secondary-dark: #333;

  /* Background Colors */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f2f2f8;
  --theme-bg-tertiary: #f9f9fc;
  --theme-bg-quaternary: #cccfd0;
  --theme-bg-overlay: rgba(248, 248, 248, 0.9);

  /* Text Colors */
  --theme-text-primary: #333;
  --theme-text-secondary: #646068;
  --theme-text-tertiary: #747077;
  --theme-text-disabled: #999;
  --theme-text-inverse: #ffffff;

  /* Border Colors */
  --theme-border-primary: #646068;
  --theme-border-secondary: #cccfd0;
  --theme-border-tertiary: #d3d3d3;
  --theme-border-focus: #5e10b1;
  --theme-border-error: #cf223f;
  --theme-border-success: #107c10;
  --theme-border-warning: #ff8c00;

  /* Status Colors */
  --theme-success: #107c10;
  --theme-success-light: #dff6dd;
  --theme-error: #cf223f;
  --theme-error-light: #fdf2f2;
  --theme-warning: #ff8c00;
  --theme-warning-light: #fff4ce;
  --theme-info: #5e10b1;
  --theme-info-light: #f2eaf9;

  /* Typography - ZB Champion fonts */
  --theme-font-family: "RNHouseSans", Arial, sans-serif;
  --theme-font-family-heading: "knileblack", Arial, sans-serif;
  --theme-font-size-xs: 0.75rem;    /* 12px */
  --theme-font-size-sm: 0.8125rem;  /* 13px */
  --theme-font-size-base: 1rem;     /* 16px */
  --theme-font-size-lg: 1.125rem;   /* 18px */
  --theme-font-size-xl: 1.25rem;    /* 20px */
  --theme-font-size-2xl: 1.5rem;    /* 24px */
  --theme-font-size-3xl: 2rem;      /* 32px */

  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-semibold: 600;
  --theme-font-weight-bold: 700;

  --theme-line-height-tight: 1.15;
  --theme-line-height-normal: 1.25;
  --theme-line-height-relaxed: 1.5;

  /* Spacing - ZB Champion spacing */
  --theme-spacing-xs: 4px;
  --theme-spacing-sm: 8px;
  --theme-spacing-md: 16px;
  --theme-spacing-lg: 24px;
  --theme-spacing-xl: 32px;
  --theme-spacing-2xl: 48px;
  --theme-spacing-3xl: 64px;

  /* Border Radius - ZB Champion rounded corners */
  --theme-radius-none: 0;
  --theme-radius-sm: 8px;
  --theme-radius-base: 8px;
  --theme-radius-lg: 16px;
  --theme-radius-xl: 25px;
  --theme-radius-full: 9999px;

  /* Shadows - ZB Champion shadows */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --theme-shadow-base: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Z-Index */
  --theme-z-dropdown: 1000;
  --theme-z-sticky: 1020;
  --theme-z-fixed: 1030;
  --theme-z-modal-backdrop: 1040;
  --theme-z-modal: 1050;
  --theme-z-popover: 1060;
  --theme-z-tooltip: 1070;

  /* Component Specific - ZB Champion standards */
  --theme-header-height: 64px;
  --theme-sidebar-width: 320px;
  --theme-sidebar-collapsed-width: 80px;
  --theme-content-max-width: 1400px;

  /* Form Controls - ZB Champion style */
  --theme-input-height: 44px;
  --theme-input-padding: 0 12px;
  --theme-input-border-width: 1px;
  --theme-input-focus-ring: 0 0 0 1px #fff, 0 0 0 3px #5e10b1;

  /* Buttons - ZB Champion style */
  --theme-button-height-sm: 32px;
  --theme-button-height-base: 44px;
  --theme-button-height-lg: 52px;
  --theme-button-padding-sm: 9px 32px;
  --theme-button-padding-base: 12px 32px;
  --theme-button-padding-lg: 16px 32px;

  /* Transitions */
  --theme-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --theme-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* MFE Specific Variables */
  --mfe-header-bg: var(--theme-secondary-dark);
  --mfe-sidebar-bg: var(--theme-bg-tertiary);
  --mfe-card-bg: var(--theme-bg-primary);
  --mfe-border-radius: var(--theme-radius-lg);
  --mfe-shadow: var(--theme-shadow-base);
  --mfe-spacing-xs: var(--theme-spacing-xs);
  --mfe-spacing-sm: var(--theme-spacing-sm);
  --mfe-spacing-md: var(--theme-spacing-md);
  --mfe-spacing-lg: var(--theme-spacing-lg);
  --mfe-spacing-xl: var(--theme-spacing-xl);

  /* Mobile breakpoint */
  --theme-mobile-breakpoint: 840px;
}

/* MFE Theme Body Styles */
body[data-theme="mfe"] {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  line-height: var(--theme-line-height-normal);
  color: var(--theme-text-secondary);
  background-color: var(--theme-bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* MFE Theme Component Overrides */
[data-theme="mfe"] .theme-button-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: var(--theme-text-inverse);
  border-radius: var(--theme-radius-xl);
  font-weight: var(--theme-font-weight-normal);
  font-size: 18px;
  line-height: 1.45;
  min-width: 96px;
  transition: all var(--theme-transition-base);
}

@media (max-width: 840px) {
  [data-theme="mfe"] .theme-button-primary {
    font-size: var(--theme-font-size-sm);
    line-height: 1.3;
  }
}

[data-theme="mfe"] .theme-button-primary:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

[data-theme="mfe"] .theme-button-primary:disabled {
  background-color: #bf9fe0;
  border-color: #bf9fe0;
  color: var(--theme-text-inverse);
}

[data-theme="mfe"] .theme-button-secondary {
  background-color: var(--theme-bg-primary);
  border: 2px solid var(--theme-primary);
  color: var(--theme-primary);
  border-radius: var(--theme-radius-xl);
  font-weight: var(--theme-font-weight-normal);
  font-size: 18px;
  line-height: 1.45;
  min-width: 96px;
  padding: 10px 32px;
  transition: all var(--theme-transition-base);
}

@media (max-width: 840px) {
  [data-theme="mfe"] .theme-button-secondary {
    font-size: var(--theme-font-size-sm);
    line-height: 1.3;
    padding: 11px 32px;
  }
}

[data-theme="mfe"] .theme-button-secondary:hover {
  background-color: var(--theme-primary-light);
  border-color: var(--theme-primary-hover);
  color: var(--theme-primary-hover);
}

[data-theme="mfe"] .theme-button-secondary:disabled {
  background-color: var(--theme-bg-primary);
  border-color: #bf9fe0;
  color: #bf9fe0;
}

[data-theme="mfe"] .theme-card {
  background-color: var(--mfe-card-bg);
  border: none;
  border-radius: var(--mfe-border-radius);
  box-shadow: var(--mfe-shadow);
  transition: all var(--theme-transition-base);
}

[data-theme="mfe"] .theme-card:hover {
  box-shadow: var(--theme-shadow-lg);
}

[data-theme="mfe"] .theme-header {
  background-color: var(--mfe-header-bg);
  color: var(--theme-text-inverse);
  height: var(--theme-header-height);
}

[data-theme="mfe"] .theme-sidebar {
  background-color: var(--mfe-sidebar-bg);
  width: var(--theme-sidebar-width);
  border-right: 1px solid var(--theme-border-tertiary);
}

/* MFE Theme Form Controls */
[data-theme="mfe"] .theme-input {
  height: var(--theme-input-height);
  padding: var(--theme-input-padding);
  border: var(--theme-input-border-width) solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-secondary);
  transition: all var(--theme-transition-base);
}

@media (max-width: 840px) {
  [data-theme="mfe"] .theme-input {
    height: 40px;
    font-size: var(--theme-font-size-sm);
  }
}

[data-theme="mfe"] .theme-input:hover {
  border-color: var(--theme-primary);
}

[data-theme="mfe"] .theme-input:focus {
  outline: none;
  border-color: var(--theme-border-primary);
  box-shadow: var(--theme-input-focus-ring);
}

[data-theme="mfe"] .theme-input:disabled {
  border-color: #c1bfc3;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-disabled);
}

/* MFE Theme Loading Spinner */
[data-theme="mfe"] .theme-spinner {
  border-color: var(--theme-border-secondary);
  border-top-color: var(--theme-primary);
  animation: spin 1s linear infinite;
}

/* MFE Theme Typography */
[data-theme="mfe"] h1 {
  font-family: var(--theme-font-family-heading);
  font-size: var(--theme-font-size-3xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h2 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-2xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-relaxed);
  color: var(--theme-text-primary);
}

[data-theme="mfe"] h3 {
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-xl);
  font-weight: var(--theme-font-weight-normal);
  line-height: 1.6;
  color: var(--theme-text-primary);
}

/* MFE Theme Utilities */
[data-theme="mfe"] .theme-text-primary { color: var(--theme-text-primary); }
[data-theme="mfe"] .theme-text-secondary { color: var(--theme-text-secondary); }
[data-theme="mfe"] .theme-text-success { color: var(--theme-success); }
[data-theme="mfe"] .theme-text-error { color: var(--theme-error); }
[data-theme="mfe"] .theme-text-warning { color: var(--theme-warning); }

[data-theme="mfe"] .theme-bg-primary { background-color: var(--theme-bg-primary); }
[data-theme="mfe"] .theme-bg-secondary { background-color: var(--theme-bg-secondary); }
[data-theme="mfe"] .theme-bg-success { background-color: var(--theme-success-light); }
[data-theme="mfe"] .theme-bg-error { background-color: var(--theme-error-light); }
[data-theme="mfe"] .theme-bg-warning { background-color: var(--theme-warning-light); }
