/**
 * Theme System Tests
 *
 * Basic tests for theme switching functionality
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../services/theme';
import { ThemeMode } from '../config/deploymentContext';

// Simple test component
const TestComponent: React.FC = () => {
  return (
    <div data-testid="test-component">
      <h1>Test Component</h1>
    </div>
  );
};

describe('Theme System', () => {
  test('renders component with ThemeProvider', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.CRM}>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  test('renders component with MFE theme', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.MFE}>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

});
