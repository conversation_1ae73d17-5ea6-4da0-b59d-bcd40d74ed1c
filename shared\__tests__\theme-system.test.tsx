/**
 * Theme System Tests
 * 
 * Tests for theme switching, detection, and theme-aware components
 */

import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, useTheme, useThemeStyles, ThemeMode } from '../services/theme';
import { DeploymentMode } from '../config/deploymentContext';
import {
  renderWithTheme,
  testInBothThemes,
  mockThemeDetection,
  expectThemeAttributes,
  themeTestHelpers,
} from '../testing/theme-test-utils';
import { setupTestMocks, cleanupTestMocks } from '../testing/mock-utils';

// Test component that uses theme hooks
const TestThemeComponent: React.FC = () => {
  const { currentTheme, switchTheme } = useTheme();
  const { getThemeClass, getCSSVariable } = useThemeStyles();

  return (
    <div data-testid="theme-component" className={getThemeClass('test-component')}>
      <span data-testid="current-theme">{currentTheme}</span>
      <span data-testid="primary-color">{getCSSVariable('--theme-primary')}</span>
      <button
        data-testid="switch-to-crm"
        onClick={() => switchTheme(ThemeMode.CRM)}
      >
        Switch to CRM
      </button>
      <button
        data-testid="switch-to-mfe"
        onClick={() => switchTheme(ThemeMode.MFE)}
      >
        Switch to MFE
      </button>
    </div>
  );
};

describe('Theme System', () => {
  beforeEach(() => {
    setupTestMocks();
  });

  afterEach(() => {
    cleanupTestMocks();
  });

  describe('ThemeProvider', () => {
    test('provides default CRM theme', () => {
      renderWithTheme(<TestThemeComponent />);
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('crm');
      expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
    });

    test('provides specified theme', () => {
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.MFE });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
      expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
    });

    test('enables auto-detection when specified', () => {
      mockThemeDetection(ThemeMode.MFE);
      
      renderWithTheme(<TestThemeComponent />, {
        enableAutoDetection: true,
        theme: ThemeMode.CRM, // This should be overridden by auto-detection
      });
      
      // Auto-detection should override the default theme
      expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
    });
  });

  describe('Theme Switching', () => {
    test('switches from CRM to MFE theme', async () => {
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.CRM });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('crm');
      
      fireEvent.click(screen.getByTestId('switch-to-mfe'));
      
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
        expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
      });
    });

    test('switches from MFE to CRM theme', async () => {
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.MFE });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
      
      fireEvent.click(screen.getByTestId('switch-to-crm'));
      
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('crm');
        expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
      });
    });
  });

  describe('Theme Detection', () => {
    test('detects CRM theme for web resource deployment', () => {
      mockThemeDetection(ThemeMode.CRM);
      process.env.VITE_DEPLOYMENT_MODE = DeploymentMode.WEB_RESOURCE;
      
      renderWithTheme(<TestThemeComponent />, { enableAutoDetection: true });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('crm');
    });

    test('detects MFE theme for standalone deployment', () => {
      mockThemeDetection(ThemeMode.MFE);
      process.env.VITE_DEPLOYMENT_MODE = DeploymentMode.STANDALONE_MFE;
      
      renderWithTheme(<TestThemeComponent />, { enableAutoDetection: true });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
    });
  });

  describe('Theme Persistence', () => {
    test('persists theme choice to localStorage', async () => {
      const storageKey = 'test-theme-persistence';
      renderWithTheme(<TestThemeComponent />, {
        enablePersistence: true,
        storageKey,
      });
      
      fireEvent.click(screen.getByTestId('switch-to-mfe'));
      
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith(storageKey, 'mfe');
      });
    });

    test('loads persisted theme on initialization', () => {
      const storageKey = 'test-theme-load';
      (localStorage.getItem as jest.Mock).mockReturnValue('mfe');
      
      renderWithTheme(<TestThemeComponent />, {
        enablePersistence: true,
        storageKey,
      });
      
      expect(localStorage.getItem).toHaveBeenCalledWith(storageKey);
      expect(screen.getByTestId('current-theme')).toHaveTextContent('mfe');
    });
  });

  describe('useThemeStyles Hook', () => {
    testInBothThemes('provides theme-aware class names', (theme) => {
      renderWithTheme(<TestThemeComponent />, { theme });
      
      const component = screen.getByTestId('theme-component');
      expect(component).toHaveClass(`test-component theme-${theme}`);
    });

    test('provides CSS custom properties', () => {
      themeTestHelpers.setupThemeProperties(ThemeMode.CRM);
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.CRM });
      
      const primaryColor = screen.getByTestId('primary-color');
      expect(primaryColor).toHaveTextContent('#0078d4');
    });
  });

  describe('Theme-Specific Styling', () => {
    test('applies CRM theme styles correctly', () => {
      themeTestHelpers.setupThemeProperties(ThemeMode.CRM);
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.CRM });
      
      const component = screen.getByTestId('theme-component');
      themeTestHelpers.verifyThemeStyling(component, ThemeMode.CRM);
    });

    test('applies MFE theme styles correctly', () => {
      themeTestHelpers.setupThemeProperties(ThemeMode.MFE);
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.MFE });
      
      const component = screen.getByTestId('theme-component');
      themeTestHelpers.verifyThemeStyling(component, ThemeMode.MFE);
    });
  });

  describe('Error Handling', () => {
    test('throws error when useTheme is used outside ThemeProvider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        renderWithTheme(<TestThemeComponent />, {});
      }).not.toThrow(); // Should not throw because we're using renderWithTheme
      
      consoleSpy.mockRestore();
    });

    test('handles invalid theme gracefully', async () => {
      renderWithTheme(<TestThemeComponent />);
      
      // Try to switch to an invalid theme (this should be handled gracefully)
      const { switchTheme } = useTheme();
      
      // This test would need to be implemented based on your error handling strategy
      expect(screen.getByTestId('current-theme')).toHaveTextContent('crm');
    });
  });

  describe('Theme Configuration', () => {
    test('provides correct CRM theme configuration', () => {
      const expectedConfig = themeTestHelpers.getThemeData(ThemeMode.CRM);
      themeTestHelpers.setupThemeProperties(ThemeMode.CRM);
      
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.CRM });
      
      expect(document.documentElement.style.getPropertyValue('--theme-primary')).toBe(expectedConfig.primaryColor);
    });

    test('provides correct MFE theme configuration', () => {
      const expectedConfig = themeTestHelpers.getThemeData(ThemeMode.MFE);
      themeTestHelpers.setupThemeProperties(ThemeMode.MFE);
      
      renderWithTheme(<TestThemeComponent />, { theme: ThemeMode.MFE });
      
      expect(document.documentElement.style.getPropertyValue('--theme-primary')).toBe(expectedConfig.primaryColor);
    });
  });
});
