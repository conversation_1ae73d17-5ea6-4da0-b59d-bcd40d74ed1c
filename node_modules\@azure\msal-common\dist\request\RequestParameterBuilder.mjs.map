{"version": 3, "file": "RequestParameterBuilder.mjs", "sources": ["../../src/request/RequestParameterBuilder.ts"], "sourcesContent": [null], "names": ["AADServerParamKeys.CLIENT_ID", "AADServerParamKeys.BROKER_CLIENT_ID", "AADServerParamKeys.REDIRECT_URI", "AADServerParamKeys.RESPONSE_TYPE", "AADServerParamKeys.RESPONSE_MODE", "AADServerParamKeys.NATIVE_BROKER", "AADServerParamKeys.SCOPE", "AADServerParamKeys.POST_LOGOUT_URI", "AADServerParamKeys.ID_TOKEN_HINT", "AADServerParamKeys.DOMAIN_HINT", "AADServerParamKeys.LOGIN_HINT", "AADServerParamKeys.SID", "AADServerParamKeys.CLAIMS", "AADServerParamKeys.CLIENT_REQUEST_ID", "AADServerParamKeys.X_CLIENT_SKU", "AADServerParamKeys.X_CLIENT_VER", "AADServerParamKeys.X_CLIENT_OS", "AADServerParamKeys.X_CLIENT_CPU", "AADServerParamKeys.X_APP_NAME", "AADServerParamKeys.X_APP_VER", "AADServerParamKeys.PROMPT", "AADServerParamKeys.STATE", "AADServerParamKeys.NONCE", "AADServerParamKeys.CODE_CHALLENGE", "AADServerParamKeys.CODE_CHALLENGE_METHOD", "ClientConfigurationErrorCodes.pkceParamsMissing", "AADServerParamKeys.CODE", "AADServerParamKeys.DEVICE_CODE", "AADServerParamKeys.REFRESH_TOKEN", "AADServerParamKeys.CODE_VERIFIER", "AADServerParamKeys.CLIENT_SECRET", "AADServerParamKeys.CLIENT_ASSERTION", "AADServerParamKeys.CLIENT_ASSERTION_TYPE", "AADServerParamKeys.OBO_ASSERTION", "AADServerParamKeys.REQUESTED_TOKEN_USE", "AADServerParamKeys.GRANT_TYPE", "ClientConfigurationErrorCodes.invalidClaims", "AADServerParamKeys.TOKEN_TYPE", "AADServerParamKeys.REQ_CNF", "AADServerParamKeys.X_CLIENT_CURR_TELEM", "AADServerParamKeys.X_CLIENT_LAST_TELEM", "AADServerParamKeys.X_MS_LIB_CAPABILITY", "AADServerParamKeys.LOGOUT_HINT", "AADServerParamKeys.BROKER_REDIRECT_URI"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AA6BH,SAAS,sBAAsB,CAC3B,UAA+B,EAC/B,aAAsB,EACtB,iBAAsC,EAAA;IAEtC,IAAI,CAAC,aAAa,EAAE;QAChB,OAAO;AACV,KAAA;IAED,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAACA,SAA4B,CAAC,CAAC;IAC9D,IAAI,QAAQ,IAAI,UAAU,CAAC,GAAG,CAACC,gBAAmC,CAAC,EAAE;QACjE,iBAAiB,EAAE,SAAS,CACxB;AACI,YAAA,gBAAgB,EAAE,QAAQ;YAC1B,mBAAmB,EAAE,UAAU,CAAC,GAAG,CAC/BC,YAA+B,CAClC;SACJ,EACD,aAAa,CAChB,CAAC;AACL,KAAA;AACL,CAAC;AAED;MACa,uBAAuB,CAAA;IAKhC,WACI,CAAA,aAAsB,EACtB,iBAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAC5C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AAED;;AAEG;IACH,mBAAmB,GAAA;AACf,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,CAAC,CACnD,CAAC;KACL;AAED;;AAEG;IACH,iCAAiC,GAAA;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CACfA,aAAgC,EAChC,kBAAkB,CACd,GAAG,SAAS,CAAC,mBAAmB,CAAI,CAAA,EAAA,SAAS,CAAC,sBAAsB,CAAA,CAAE,CACzE,CACJ,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,YAA2B,EAAA;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CACvE,CAAC;KACL;AAED;;AAEG;IACH,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,GAAG,CAAC,CAC1B,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,SAAS,CACL,MAAgB,EAChB,gBAAyB,IAAI,EAC7B,gBAA+B,mBAAmB,EAAA;;AAGlD,QAAA,IACI,aAAa;AACb,YAAA,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACjC,YAAA,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAC5B;AACE,YAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAA;QACD,MAAM,aAAa,GAAG,aAAa;cAC7B,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC;AACvC,cAAE,MAAM,IAAI,EAAE,CAAC;AACnB,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,KAAwB,EACxB,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAC7C,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfN,SAA4B,EAC5B,kBAAkB,CAAC,QAAQ,CAAC,CAC/B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAAmB,EAAA;AAC9B,QAAA,gBAAgB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfE,YAA+B,EAC/B,kBAAkB,CAAC,WAAW,CAAC,CAClC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,wBAAwB,CAAC,WAAmB,EAAA;AACxC,QAAA,gBAAgB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfK,eAAkC,EAClC,kBAAkB,CAAC,WAAW,CAAC,CAClC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,WAAW,CAAC,CAClC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,UAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,WAA8B,EAC9B,kBAAkB,CAAC,UAAU,CAAC,CACjC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,SAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,UAA6B,EAC7B,kBAAkB,CAAC,SAAS,CAAC,CAChC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,SAAiB,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,WAAW,CAAC,UAAU,EACtB,kBAAkB,CAAC,CAAO,IAAA,EAAA,SAAS,CAAE,CAAA,CAAC,CACzC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,UAAsB,EAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,WAAW,CAAC,UAAU,EACtB,kBAAkB,CAAC,OAAO,UAAU,CAAC,GAAG,CAAI,CAAA,EAAA,UAAU,CAAC,IAAI,CAAA,CAAE,CAAC,CACjE,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,GAAW,EAAA;AACd,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAACC,GAAsB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;KACxE;AAED;;;AAGG;IACH,SAAS,CAAC,MAAe,EAAE,kBAAkC,EAAA;QACzD,MAAM,YAAY,GAAG,IAAI,CAAC,6BAA6B,CACnD,MAAM,EACN,kBAAkB,CACrB,CAAC;AACF,QAAA,gBAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,MAAyB,EACzB,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,aAAqB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,iBAAoC,EACpC,kBAAkB,CAAC,aAAa,CAAC,CACpC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAAwB,EAAA;;AAEnC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAACC,YAA+B,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,YAA+B,EAC/B,WAAW,CAAC,OAAO,CACtB,CAAC;QACF,IAAI,WAAW,CAAC,EAAE,EAAE;AAChB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAACC,WAA8B,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AACvE,SAAA;QACD,IAAI,WAAW,CAAC,GAAG,EAAE;AACjB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,YAA+B,EAC/B,WAAW,CAAC,GAAG,CAClB,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,uBAAuB,CAAC,YAAkC,EAAA;QACtD,IAAI,YAAY,EAAE,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,UAA6B,EAC7B,YAAY,CAAC,OAAO,CACvB,CAAC;AACL,SAAA;QAED,IAAI,YAAY,EAAE,UAAU,EAAE;AAC1B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,SAA4B,EAC5B,YAAY,CAAC,UAAU,CAC1B,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACpB,QAAA,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,GAAGC,MAAyB,CAAA,CAAE,EAC9B,kBAAkB,CAAC,MAAM,CAAC,CAC7B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AAClB,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,KAAwB,EACxB,kBAAkB,CAAC,KAAK,CAAC,CAC5B,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,KAAwB,EACxB,kBAAkB,CAAC,KAAK,CAAC,CAC5B,CAAC;KACL;AAED;;;;;AAKG;IACH,sBAAsB,CAClB,aAAqB,EACrB,mBAA2B,EAAA;AAE3B,QAAA,gBAAgB,CAAC,2BAA2B,CACxC,aAAa,EACb,mBAAmB,CACtB,CAAC;QACF,IAAI,aAAa,IAAI,mBAAmB,EAAE;AACtC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,cAAiC,EACjC,kBAAkB,CAAC,aAAa,CAAC,CACpC,CAAC;AACF,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,qBAAwC,EACxC,kBAAkB,CAAC,mBAAmB,CAAC,CAC1C,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,8BAA8B,CAChCC,iBAA+C,CAClD,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,IAAY,EAAA;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAACC,IAAuB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1E;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,IAAY,EAAA;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,WAA8B,EAC9B,kBAAkB,CAAC,IAAI,CAAC,CAC3B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,eAAuB,EAAA;AACtC,QAAA,IAAI,eAAe,EAAE;AACjB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,gBAAmC,EACnC,kBAAkB,CAAC,eAAe,CAAC,CACtC,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,sBAAsB,CAAC,mBAA2B,EAAA;AAC9C,QAAA,IAAI,mBAAmB,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,qBAAwC,EACxC,kBAAkB,CAAC,mBAAmB,CAAC,CAC1C,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,aAAgC,EAChC,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,QAAgB,EAAA;AAC/B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,mBAAsC,EACtC,kBAAkB,CAAC,QAAQ,CAAC,CAC/B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,SAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,UAA6B,EAC7B,kBAAkB,CAAC,SAAS,CAAC,CAChC,CAAC;KACL;AAED;;;AAGG;IACH,aAAa,GAAA;QACT,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;KACzC;AAED;;;AAGG;AACH,IAAA,uBAAuB,CAAC,QAAoB,EAAA;AACxC,QAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;YAC9C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE;gBACpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACnC,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,6BAA6B,CACzB,MAAe,EACf,kBAAkC,EAAA;AAElC,QAAA,IAAI,YAAoB,CAAC;;QAGzB,IAAI,CAAC,MAAM,EAAE;YACT,YAAY,GAAG,EAAE,CAAC;AACrB,SAAA;AAAM,aAAA;YACH,IAAI;AACA,gBAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACrC,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;;AAE9D,gBAAA,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACrD,aAAA;;YAGD,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CACxC,iBAAiB,CAAC,MAAM,CAC3B,GAAG;AACA,gBAAA,MAAM,EAAE,kBAAkB;aAC7B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,sBAAsB,CAAC,QAAQ,EAC/B,kBAAkB,CAAC,QAAQ,CAAC,CAC/B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,sBAAsB,CAAC,QAAQ,EAC/B,kBAAkB,CAAC,QAAQ,CAAC,CAC/B,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,SAAiB,EAAA;AACzB,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,UAA6B,EAC7B,oBAAoB,CAAC,GAAG,CAC3B,CAAC;AACF,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,OAA0B,EAC1B,kBAAkB,CAAC,SAAS,CAAC,CAChC,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,SAAS,CAAC,YAAoB,EAAA;AAC1B,QAAA,IAAI,YAAY,EAAE;AACd,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfD,UAA6B,EAC7B,oBAAoB,CAAC,GAAG,CAC3B,CAAC;AACF,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,OAA0B,EAC1B,kBAAkB,CAAC,YAAY,CAAC,CACnC,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,sBAA8C,EAAA;AAC7D,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,mBAAsC,EACtC,sBAAsB,CAAC,iCAAiC,EAAE,CAC7D,CAAC;AACF,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,mBAAsC,EACtC,sBAAsB,CAAC,8BAA8B,EAAE,CAC1D,CAAC;KACL;AAED;;AAEG;IACH,aAAa,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,mBAAsC,EACtC,mBAAmB,CAAC,yBAAyB,CAChD,CAAC;KACL;AAED;;AAEG;AACH,IAAA,aAAa,CAAC,UAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACfC,WAA8B,EAC9B,kBAAkB,CAAC,UAAU,CAAC,CACjC,CAAC;KACL;AAED,IAAA,mBAAmB,CAAC,MAGnB,EAAA;QACG,MAAM,YAAY,GAAe,EAAE,CAAC;AACpC,QAAA,YAAY,CAACzC,gBAAmC,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC;AAC1B,QAAA,YAAY,CAAC0C,mBAAsC,CAAC;YAChD,MAAM,CAAC,iBAAiB,CAAC;AAE7B,QAAA,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;KAC9C;AAED;;AAEG;IACH,iBAAiB,GAAA;AACb,QAAA,MAAM,mBAAmB,GAAkB,IAAI,KAAK,EAAU,CAAC;QAE/D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;YACnC,mBAAmB,CAAC,IAAI,CAAC,CAAA,EAAG,GAAG,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AAChD,SAAC,CAAC,CAAC;AAEH,QAAA,sBAAsB,CAClB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,QAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACxC;AACJ;;;;"}