/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

import { IWindowStorage } from "./IWindowStorage.js";

// Cookie life calculation (hours * minutes * seconds * ms)
const COOKIE_LIFE_MULTIPLIER = 24 * 60 * 60 * 1000;

export class CookieStorage implements IWindowStorage<string> {
    getItem(key: string): string | null {
        const name = `${encodeURIComponent(key)}`;
        const cookieList = document.cookie.split(";");
        for (let i = 0; i < cookieList.length; i++) {
            const cookie = cookieList[i];
            const [key, ...rest] = decodeURIComponent(cookie).trim().split("=");
            const value = rest.join("=");

            if (key === name) {
                return value;
            }
        }
        return "";
    }

    setItem(
        key: string,
        value: string,
        cookieLifeDays?: number,
        secure: boolean = true
    ): void {
        let cookieStr = `${encodeURIComponent(key)}=${encodeURIComponent(
            value
        )};path=/;SameSite=Lax;`;

        if (cookieLifeDays) {
            const expireTime = getCookieExpirationTime(cookieLifeDays);
            cookieStr += `expires=${expireTime};`;
        }

        if (secure) {
            cookieStr += "Secure;";
        }

        document.cookie = cookieStr;
    }

    removeItem(key: string): void {
        // Setting expiration to -1 removes it
        this.setItem(key, "", -1);
    }

    getKeys(): string[] {
        const cookieList = document.cookie.split(";");
        const keys: Array<string> = [];
        cookieList.forEach((cookie) => {
            const cookieParts = decodeURIComponent(cookie).trim().split("=");
            keys.push(cookieParts[0]);
        });

        return keys;
    }

    containsKey(key: string): boolean {
        return this.getKeys().includes(key);
    }
}

/**
 * Get cookie expiration time
 * @param cookieLifeDays
 */
export function getCookieExpirationTime(cookieLifeDays: number): string {
    const today = new Date();
    const expr = new Date(
        today.getTime() + cookieLifeDays * COOKIE_LIFE_MULTIPLIER
    );
    return expr.toUTCString();
}
