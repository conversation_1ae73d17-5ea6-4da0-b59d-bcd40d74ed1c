/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

export const redirectUriEmpty = "redirect_uri_empty";
export const claimsRequestParsingError = "claims_request_parsing_error";
export const authorityUriInsecure = "authority_uri_insecure";
export const urlParseError = "url_parse_error";
export const urlEmptyError = "empty_url_error";
export const emptyInputScopesError = "empty_input_scopes_error";
export const invalidPromptValue = "invalid_prompt_value";
export const invalidClaims = "invalid_claims";
export const tokenRequestEmpty = "token_request_empty";
export const logoutRequestEmpty = "logout_request_empty";
export const invalidCodeChallengeMethod = "invalid_code_challenge_method";
export const pkceParamsMissing = "pkce_params_missing";
export const invalidCloudDiscoveryMetadata = "invalid_cloud_discovery_metadata";
export const invalidAuthorityMetadata = "invalid_authority_metadata";
export const untrustedAuthority = "untrusted_authority";
export const missingSshJwk = "missing_ssh_jwk";
export const missingSshKid = "missing_ssh_kid";
export const missingNonceAuthenticationHeader =
    "missing_nonce_authentication_header";
export const invalidAuthenticationHeader = "invalid_authentication_header";
export const cannotSetOIDCOptions = "cannot_set_OIDCOptions";
export const cannotAllowNativeBroker = "cannot_allow_native_broker";
export const authorityMismatch = "authority_mismatch";
