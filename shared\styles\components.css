/**
 * Component Styles
 *
 * Theme-aware component styles using CSS custom properties
 * Enhanced with ZB Champion structural styling
 */

/* === ENHANCED COMPONENT STYLES === */

/* Enhanced Button Styles */
.zb-button,
.theme-button {
  /* Base button styles are defined in enhanced-base-theme.css */
  /* Theme-specific colors are applied in theme files */
}

/* Enhanced Input Styles */
.zb-input,
.theme-input {
  /* Base input styles are defined in enhanced-base-theme.css */
  /* Theme-specific colors are applied in theme files */
}

/* Enhanced Card Styles */
.zb-card,
.theme-card {
  /* Base card styles are defined in enhanced-base-theme.css */
  /* Theme-specific colors are applied in theme files */
}

/* Enhanced Container Styles */
.zb-container {
  /* Base container styles are defined in enhanced-base-theme.css */
}

/* Theme Switcher Component Styles */
.theme-switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  font-family: var(--theme-font-family);
}

.theme-switcher-sm {
  font-size: var(--theme-font-size-sm);
}

.theme-switcher-md {
  font-size: var(--theme-font-size-base);
}

.theme-switcher-lg {
  font-size: var(--theme-font-size-lg);
}

/* Toggle variant */
.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-xs);
  padding: var(--theme-spacing-sm) var(--theme-spacing-md);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  font-family: var(--theme-font-family);
  font-size: inherit;
}

.theme-toggle-button:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

.theme-toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-primary);
}

/* Buttons variant */
.theme-switcher-buttons {
  display: flex;
  gap: var(--theme-spacing-xs);
  padding: 2px;
  background-color: var(--theme-bg-tertiary);
  border-radius: var(--theme-radius-lg);
}

.theme-button {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-xs);
  padding: var(--theme-spacing-xs) var(--theme-spacing-md);
  border: none;
  border-radius: var(--theme-radius-base);
  background-color: transparent;
  color: var(--theme-text-secondary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  font-family: var(--theme-font-family);
  font-size: inherit;
}

.theme-button:hover:not(.disabled) {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.theme-button.active {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

.theme-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-primary);
}

/* Dropdown variant */
.theme-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm);
  padding: var(--theme-spacing-sm) var(--theme-spacing-md);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  font-family: var(--theme-font-family);
  font-size: inherit;
  min-width: 140px;
}

.theme-dropdown-trigger:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

.theme-dropdown-trigger:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-primary);
}

.theme-dropdown-arrow {
  font-size: 0.75em;
  transition: transform var(--theme-transition-base);
  margin-left: auto;
}

.theme-dropdown-trigger.open .theme-dropdown-arrow {
  transform: rotate(180deg);
}

.theme-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: var(--theme-spacing-xs);
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-lg);
  box-shadow: var(--theme-shadow-lg);
  z-index: var(--theme-z-dropdown);
  overflow: hidden;
  min-width: 200px;
}

.theme-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-md);
  width: 100%;
  padding: var(--theme-spacing-md) var(--theme-spacing-lg);
  border: none;
  background-color: transparent;
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  text-align: left;
  font-family: var(--theme-font-family);
  font-size: inherit;
}

.theme-dropdown-item:hover {
  background-color: var(--theme-bg-secondary);
}

.theme-dropdown-item.active {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.theme-dropdown-item:focus {
  outline: none;
  background-color: var(--theme-bg-secondary);
}

.theme-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.theme-label {
  font-weight: var(--theme-font-weight-medium);
}

.theme-description {
  font-size: var(--theme-font-size-sm);
  color: var(--theme-text-secondary);
  line-height: var(--theme-line-height-tight);
}

.theme-check {
  color: var(--theme-primary);
  font-weight: var(--theme-font-weight-bold);
  font-size: 1.2em;
}

.theme-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: calc(var(--theme-z-dropdown) - 1);
  background: transparent;
}

/* Disabled state */
.theme-switcher .disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* Error indicator */
.theme-error {
  color: var(--theme-error);
  font-size: 1.2em;
  cursor: help;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading state */
.theme-switcher.loading {
  opacity: 0.7;
}

.theme-switcher.loading * {
  pointer-events: none;
}

/* Icon styles */
.theme-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1em;
  line-height: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-switcher-dropdown .theme-dropdown-menu {
    position: fixed;
    top: auto;
    bottom: var(--theme-spacing-lg);
    left: var(--theme-spacing-md);
    right: var(--theme-spacing-md);
    margin-top: 0;
  }
  
  .theme-switcher-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .theme-button {
    justify-content: flex-start;
    width: 100%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .theme-switcher button {
    border-width: 2px;
  }
  
  .theme-dropdown-menu {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .theme-switcher *,
  .theme-switcher *::before,
  .theme-switcher *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
