import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig(({ mode }) => {
  const deploymentMode = process.env.VITE_DEPLOYMENT_MODE || 'standalone_mfe';

  return {
    plugins: [
      react(),
      dts({
        insertTypesEntry: true,
        include: ['components/**/*', 'services/**/*', 'utils/**/*', 'config/**/*', 'styles/**/*', 'index.ts'],
      }),
    ],
    css: {
      preprocessorOptions: {
        css: {
          // Ensure CSS custom properties are preserved
          charset: false,
        },
      },
    },
  build: {
    lib: {
      entry: {
        index: resolve(__dirname, 'index.ts'),
        'components/index': resolve(__dirname, 'components/index.ts'),
        'services/index': resolve(__dirname, 'services/index.ts'),
        'utils/index': resolve(__dirname, 'utils/index.ts'),
        'config/index': resolve(__dirname, 'config/index.ts'),
      },
      name: 'CrmShared',
      formats: ['es', 'cjs'],
      fileName: (format, entryName) => {
        const extension = format === 'es' ? 'esm.js' : 'js';
        return `${entryName}.${extension}`;
      },
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
        // Include theme CSS files as separate assets
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            if (assetInfo.name.includes('theme')) {
              return 'themes/[name].[ext]';
            }
            return 'styles/[name].[ext]';
          }
          return 'assets/[name].[ext]';
        },
      },
      // Ensure CSS is extracted
      cssCodeSplit: true,
    },
    define: {
      // Inject deployment mode for theme detection
      'import.meta.env.VITE_DEPLOYMENT_MODE': JSON.stringify(deploymentMode),
      'import.meta.env.VITE_THEME_MODE': JSON.stringify(process.env.VITE_THEME_MODE),
    },
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '.'),
      },
    },
  };
});
