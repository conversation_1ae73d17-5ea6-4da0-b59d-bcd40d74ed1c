# Custom Theme Integration Guide

This guide explains how your custom `zb-champion-standard-theme.css` has been integrated into the theme switching system and how to test the new themes.

## 🎯 What Was Done

### 1. **Theme File Integration**

Your original `zb-champion-standard-theme.css` has been converted and integrated as:

- **`dynamics-crm-theme.css`** - Enhanced Dynamics 365 theme (replaces old CRM theme)
- **`zb-champion-mfe-theme.css`** - Your ZB Champion theme converted to CSS custom properties (replaces old MFE theme)

### 2. **Theme Mode Mapping**

The existing `ThemeMode` enum values now map to your custom themes:

```typescript
enum ThemeMode {
  CRM = 'crm',    // → dynamics-crm-theme.css
  MFE = 'mfe'     // → zb-champion-mfe-theme.css
}
```

### 3. **Deployment Context Integration**

- **Web Resource Mode** → Dynamics CRM Theme
- **Embedded SPA Mode** → Dynamics CRM Theme  
- **Standalone MFE Mode** → ZB Champion Theme

## 🎨 Theme Characteristics

### Dynamics CRM Theme (`ThemeMode.CRM`)

```css
/* Key characteristics */
--theme-primary: #0078d4;           /* Dynamics 365 blue */
--theme-font-family: "Segoe UI", "Segoe UI Web (West European)", ...;
--theme-radius-base: 4px;           /* Conservative radius */
--theme-input-height: 32px;         /* Standard enterprise height */
--theme-button-height-base: 32px;   /* Accessibility-focused */
```

### ZB Champion Theme (`ThemeMode.MFE`)

```css
/* Key characteristics */
--theme-primary: #5e10b1;           /* ZB Champion purple */
--theme-font-family: "RNHouseSans", Arial, sans-serif;
--theme-radius-base: 16px;          /* Modern rounded corners */
--theme-input-height: 44px;         /* Touch-friendly height */
--theme-button-height-base: 44px;   /* Mobile-optimized */
--theme-mobile-breakpoint: 840px;   /* ZB Champion breakpoint */
```

## 🚀 Testing the New Themes

### 1. **Build and Run Tests**

```bash
# Test shared package build
npm run build:shared

# Test web resource build (Dynamics CRM theme)
npm run build:transcript-and-summary:webresource

# Test standalone build (ZB Champion theme)
npm run build:transcript-and-summary:standalone

# Run development with specific themes
npm run dev:transcript-and-summary:webresource   # CRM theme
npm run dev:transcript-and-summary:standalone     # ZB Champion theme
```

### 2. **Manual Theme Testing**

Use URL parameters to test themes in development:

```bash
# Test ZB Champion theme
http://localhost:5173?themeMode=mfe

# Test Dynamics CRM theme
http://localhost:5173?themeMode=crm

# Test deployment mode detection
http://localhost:5173?deploymentMode=standalone_mfe&themeMode=mfe
```

### 3. **Theme Switcher Testing**

The `ThemeSwitcher` component now shows:

- **"Dynamics CRM"** 🏢 - Dynamics 365 enterprise styling
- **"ZB Champion"** 🎯 - ZB Champion standard theme

```tsx
import { ThemeSwitcher } from '@shared/components';

// Test different variants
<ThemeSwitcher variant="dropdown" showLabels={true} showIcons={true} />
<ThemeSwitcher variant="toggle" />
<ThemeSwitcher variant="buttons" />
```

### 4. **Component Testing**

Test theme-aware components:

```tsx
import { Button, useTheme, useThemeStyles } from '@shared/services';

function TestComponent() {
  const { currentTheme, switchTheme } = useTheme();
  const { getThemeClass } = useThemeStyles();

  return (
    <div className={getThemeClass('theme-card')}>
      <h2 className="theme-text-primary">Current: {currentTheme}</h2>
      
      {/* Test buttons with different themes */}
      <Button variant="primary">Primary Button</Button>
      <Button variant="secondary">Secondary Button</Button>
      
      {/* Test form controls */}
      <input className="theme-input" placeholder="Test input" />
      <select className="theme-input theme-select">
        <option>Test select</option>
      </select>
    </div>
  );
}
```

## 🔧 Customization Options

### 1. **Modify ZB Champion Colors**

Edit `shared/styles/themes/zb-champion-mfe-theme.css`:

```css
:root[data-theme="mfe"] {
  /* Update primary color */
  --theme-primary: #your-new-color;
  
  /* Update secondary color */
  --theme-secondary: #your-secondary-color;
  
  /* Add custom properties */
  --your-custom-property: value;
}
```

### 2. **Modify Dynamics CRM Theme**

Edit `shared/styles/themes/dynamics-crm-theme.css`:

```css
:root[data-theme="crm"] {
  /* Customize CRM theme */
  --theme-primary: #your-crm-color;
}
```

### 3. **Add New CSS Custom Properties**

Both themes support custom properties:

```css
/* In your theme file */
:root[data-theme="mfe"] {
  --your-custom-spacing: 20px;
  --your-custom-color: #123456;
}

/* Use in components */
.my-component {
  padding: var(--your-custom-spacing);
  color: var(--your-custom-color);
}
```

## 📱 Mobile Responsiveness

The ZB Champion theme includes mobile-specific styles:

```css
@media (max-width: 840px) {
  [data-theme="mfe"] .theme-button-primary {
    font-size: 0.8125rem;  /* Smaller font on mobile */
    line-height: 1.3;
  }
  
  [data-theme="mfe"] .theme-input {
    height: 40px;          /* Smaller input height */
  }
}
```

## 🎯 Key Features Preserved

### 1. **ZB Champion Brand Elements**

- ✅ RNHouseSans font family
- ✅ Purple primary color (#5e10b1)
- ✅ 840px mobile breakpoint
- ✅ 44px touch-friendly controls
- ✅ Rounded corners (16px)

### 2. **Dynamics 365 Integration**

- ✅ Segoe UI font family
- ✅ Enterprise blue (#0078d4)
- ✅ Conservative styling
- ✅ Accessibility standards
- ✅ 32px standard controls

### 3. **Runtime Switching**

- ✅ No page reload required
- ✅ Automatic deployment detection
- ✅ Theme persistence
- ✅ CSS custom properties

## 🧪 Example Usage

### Complete Example Component

```tsx
import React from 'react';
import { 
  ThemeProvider, 
  useTheme, 
  ThemeMode 
} from '@shared/services/theme';
import { Button, ThemeSwitcher } from '@shared/components';

function MyApp() {
  return (
    <ThemeProvider enableAutoDetection={true}>
      <AppContent />
    </ThemeProvider>
  );
}

function AppContent() {
  const { currentTheme, switchTheme } = useTheme();
  
  return (
    <div className="theme-card">
      <h1 className="theme-text-primary">
        Current Theme: {currentTheme === ThemeMode.CRM ? 'Dynamics CRM' : 'ZB Champion'}
      </h1>
      
      <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
        <Button 
          variant="primary" 
          onClick={() => switchTheme(ThemeMode.CRM)}
        >
          Switch to CRM
        </Button>
        <Button 
          variant="secondary" 
          onClick={() => switchTheme(ThemeMode.MFE)}
        >
          Switch to ZB Champion
        </Button>
      </div>
      
      <ThemeSwitcher variant="dropdown" showLabels={true} />
    </div>
  );
}
```

## 🔍 Troubleshooting

### Theme Not Loading

1. **Check CSS imports**:
   ```tsx
   import '@shared/styles/index.css';
   ```

2. **Verify ThemeProvider**:
   ```tsx
   <ThemeProvider>
     <App />
   </ThemeProvider>
   ```

3. **Check data-theme attribute**:
   ```html
   <html data-theme="mfe">
   <body data-theme="mfe">
   ```

### CSS Variables Not Working

1. **Use correct variable names**:
   ```css
   /* ✅ Correct */
   color: var(--theme-primary);
   
   /* ❌ Incorrect */
   color: var(--primary-color);
   ```

2. **Check theme-aware classes**:
   ```tsx
   // ✅ Use theme-aware classes
   <div className="theme-card">
   
   // ❌ Avoid hardcoded styles
   <div style={{ backgroundColor: '#ffffff' }}>
   ```

### Font Loading Issues

For ZB Champion theme, ensure fonts are available:

```css
/* Add to your main CSS if fonts aren't loading */
@font-face {
  font-family: 'RNHouseSans';
  src: url('./fonts/RNHouseSans.woff2') format('woff2');
}
```

## 📚 Next Steps

1. **Test thoroughly** in both deployment modes
2. **Customize colors** to match your exact brand requirements
3. **Add custom CSS properties** for specific components
4. **Test mobile responsiveness** at 840px breakpoint
5. **Validate accessibility** with new color schemes

Your ZB Champion theme is now fully integrated into the theme switching system while maintaining all the existing functionality! 🎉
