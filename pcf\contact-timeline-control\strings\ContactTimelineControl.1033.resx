<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ContactTimelineControl_Display_Key" xml:space="preserve">
    <value>Contact Timeline Control</value>
    <comment>Display name for the control</comment>
  </data>
  <data name="ContactTimelineControl_Desc_Key" xml:space="preserve">
    <value>A timeline control that displays contact activities and interactions</value>
    <comment>Description for the control</comment>
  </data>
  <data name="ContactId_Display_Key" xml:space="preserve">
    <value>Contact ID</value>
    <comment>Display name for the contact ID property</comment>
  </data>
  <data name="ContactId_Desc_Key" xml:space="preserve">
    <value>The ID of the contact to display timeline for</value>
    <comment>Description for the contact ID property</comment>
  </data>
  <data name="TimelineData_Display_Key" xml:space="preserve">
    <value>Timeline Data</value>
    <comment>Display name for the timeline data property</comment>
  </data>
  <data name="TimelineData_Desc_Key" xml:space="preserve">
    <value>Additional timeline data configuration</value>
    <comment>Description for the timeline data property</comment>
  </data>
  <data name="MaxItems_Display_Key" xml:space="preserve">
    <value>Max Items</value>
    <comment>Display name for the max items property</comment>
  </data>
  <data name="MaxItems_Desc_Key" xml:space="preserve">
    <value>Maximum number of timeline items to display</value>
    <comment>Description for the max items property</comment>
  </data>
  <data name="ShowFilters_Display_Key" xml:space="preserve">
    <value>Show Filters</value>
    <comment>Display name for the show filters property</comment>
  </data>
  <data name="ShowFilters_Desc_Key" xml:space="preserve">
    <value>Whether to show filter controls</value>
    <comment>Description for the show filters property</comment>
  </data>
</root>
