import { AuthError } from "@azure/msal-common/browser";
import * as BrowserAuthErrorCodes from "./BrowserAuthErrorCodes.js";
export { BrowserAuthErrorCodes };
/**
 * BrowserAuthErrorMessage class containing string constants used by error codes and messages.
 */
export declare const BrowserAuthErrorMessages: {
    pkce_not_created: string;
    crypto_nonexistent: string;
    empty_navigate_uri: string;
    hash_empty_error: string;
    no_state_in_hash: string;
    hash_does_not_contain_known_properties: string;
    unable_to_parse_state: string;
    state_interaction_type_mismatch: string;
    interaction_in_progress: string;
    popup_window_error: string;
    empty_window_error: string;
    user_cancelled: string;
    monitor_popup_timeout: string;
    monitor_window_timeout: string;
    redirect_in_iframe: string;
    block_iframe_reload: string;
    block_nested_popups: string;
    iframe_closed_prematurely: string;
    silent_logout_unsupported: string;
    no_account_error: string;
    silent_prompt_value_error: string;
    no_token_request_cache_error: string;
    unable_to_parse_token_request_cache_error: string;
    no_cached_authority_error: string;
    auth_request_not_set_error: string;
    invalid_cache_type: string;
    non_browser_environment: string;
    database_not_open: string;
    no_network_connectivity: string;
    post_request_failed: string;
    get_request_failed: string;
    failed_to_parse_response: string;
    unable_to_load_token: string;
    crypto_key_not_found: string;
    auth_code_required: string;
    auth_code_or_nativeAccountId_required: string;
    spa_code_and_nativeAccountId_present: string;
    database_unavailable: string;
    unable_to_acquire_token_from_native_platform: string;
    native_handshake_timeout: string;
    native_extension_not_installed: string;
    native_connection_not_established: string;
    uninitialized_public_client_application: string;
    native_prompt_not_supported: string;
    invalid_base64_string: string;
    invalid_pop_token_request: string;
    failed_to_build_headers: string;
    failed_to_parse_headers: string;
};
/**
 * BrowserAuthErrorMessage class containing string constants used by error codes and messages.
 * @deprecated Use exported BrowserAuthErrorCodes instead.
 * In your app you can do :
 * ```
 * import { BrowserAuthErrorCodes } from "@azure/msal-browser";
 * ```
 */
export declare const BrowserAuthErrorMessage: {
    pkceNotGenerated: {
        code: string;
        desc: string;
    };
    cryptoDoesNotExist: {
        code: string;
        desc: string;
    };
    emptyNavigateUriError: {
        code: string;
        desc: string;
    };
    hashEmptyError: {
        code: string;
        desc: string;
    };
    hashDoesNotContainStateError: {
        code: string;
        desc: string;
    };
    hashDoesNotContainKnownPropertiesError: {
        code: string;
        desc: string;
    };
    unableToParseStateError: {
        code: string;
        desc: string;
    };
    stateInteractionTypeMismatchError: {
        code: string;
        desc: string;
    };
    interactionInProgress: {
        code: string;
        desc: string;
    };
    popupWindowError: {
        code: string;
        desc: string;
    };
    emptyWindowError: {
        code: string;
        desc: string;
    };
    userCancelledError: {
        code: string;
        desc: string;
    };
    monitorPopupTimeoutError: {
        code: string;
        desc: string;
    };
    monitorIframeTimeoutError: {
        code: string;
        desc: string;
    };
    redirectInIframeError: {
        code: string;
        desc: string;
    };
    blockTokenRequestsInHiddenIframeError: {
        code: string;
        desc: string;
    };
    blockAcquireTokenInPopupsError: {
        code: string;
        desc: string;
    };
    iframeClosedPrematurelyError: {
        code: string;
        desc: string;
    };
    silentLogoutUnsupportedError: {
        code: string;
        desc: string;
    };
    noAccountError: {
        code: string;
        desc: string;
    };
    silentPromptValueError: {
        code: string;
        desc: string;
    };
    noTokenRequestCacheError: {
        code: string;
        desc: string;
    };
    unableToParseTokenRequestCacheError: {
        code: string;
        desc: string;
    };
    noCachedAuthorityError: {
        code: string;
        desc: string;
    };
    authRequestNotSet: {
        code: string;
        desc: string;
    };
    invalidCacheType: {
        code: string;
        desc: string;
    };
    notInBrowserEnvironment: {
        code: string;
        desc: string;
    };
    databaseNotOpen: {
        code: string;
        desc: string;
    };
    noNetworkConnectivity: {
        code: string;
        desc: string;
    };
    postRequestFailed: {
        code: string;
        desc: string;
    };
    getRequestFailed: {
        code: string;
        desc: string;
    };
    failedToParseNetworkResponse: {
        code: string;
        desc: string;
    };
    unableToLoadTokenError: {
        code: string;
        desc: string;
    };
    signingKeyNotFoundInStorage: {
        code: string;
        desc: string;
    };
    authCodeRequired: {
        code: string;
        desc: string;
    };
    authCodeOrNativeAccountRequired: {
        code: string;
        desc: string;
    };
    spaCodeAndNativeAccountPresent: {
        code: string;
        desc: string;
    };
    databaseUnavailable: {
        code: string;
        desc: string;
    };
    unableToAcquireTokenFromNativePlatform: {
        code: string;
        desc: string;
    };
    nativeHandshakeTimeout: {
        code: string;
        desc: string;
    };
    nativeExtensionNotInstalled: {
        code: string;
        desc: string;
    };
    nativeConnectionNotEstablished: {
        code: string;
        desc: string;
    };
    uninitializedPublicClientApplication: {
        code: string;
        desc: string;
    };
    nativePromptNotSupported: {
        code: string;
        desc: string;
    };
    invalidBase64StringError: {
        code: string;
        desc: string;
    };
    invalidPopTokenRequest: {
        code: string;
        desc: string;
    };
};
/**
 * Browser library error class thrown by the MSAL.js library for SPAs
 */
export declare class BrowserAuthError extends AuthError {
    constructor(errorCode: string, subError?: string);
}
export declare function createBrowserAuthError(errorCode: string, subError?: string): BrowserAuthError;
//# sourceMappingURL=BrowserAuthError.d.ts.map