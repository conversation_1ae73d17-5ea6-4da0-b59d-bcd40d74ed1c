"use strict";var st=Object.defineProperty;var ot=(t,e,r)=>e in t?st(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var we=(t,e,r)=>ot(t,typeof e!="symbol"?e+"":e,r);const k=require("react"),x=require("./logger-DOG_vMcL.js");function _e(t,e){return function(){return t.apply(e,arguments)}}const{toString:it}=Object.prototype,{getPrototypeOf:pe}=Object,{iterator:Y,toStringTag:Le}=Symbol,ee=(t=>e=>{const r=it.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),N=t=>(t=t.toLowerCase(),e=>ee(e)===t),te=t=>e=>typeof e===t,{isArray:I}=Array,$=te("undefined");function z(t){return t!==null&&!$(t)&&t.constructor!==null&&!$(t.constructor)&&O(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Fe=N("ArrayBuffer");function at(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&Fe(t.buffer),e}const ct=te("string"),O=te("function"),Be=te("number"),J=t=>t!==null&&typeof t=="object",lt=t=>t===!0||t===!1,K=t=>{if(ee(t)!=="object")return!1;const e=pe(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Le in t)&&!(Y in t)},ut=t=>{if(!J(t)||z(t))return!1;try{return Object.keys(t).length===0&&Object.getPrototypeOf(t)===Object.prototype}catch{return!1}},ft=N("Date"),dt=N("File"),ht=N("Blob"),pt=N("FileList"),mt=t=>J(t)&&O(t.pipe),yt=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||O(t.append)&&((e=ee(t))==="formdata"||e==="object"&&O(t.toString)&&t.toString()==="[object FormData]"))},gt=N("URLSearchParams"),[wt,bt,Et,St]=["ReadableStream","Request","Response","Headers"].map(N),Rt=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function V(t,e,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let n,s;if(typeof t!="object"&&(t=[t]),I(t))for(n=0,s=t.length;n<s;n++)e.call(null,t[n],n,t);else{if(z(t))return;const o=r?Object.getOwnPropertyNames(t):Object.keys(t),i=o.length;let c;for(n=0;n<i;n++)c=o[n],e.call(null,t[c],c,t)}}function De(t,e){if(z(t))return null;e=e.toLowerCase();const r=Object.keys(t);let n=r.length,s;for(;n-- >0;)if(s=r[n],e===s.toLowerCase())return s;return null}const B=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,je=t=>!$(t)&&t!==B;function ce(){const{caseless:t}=je(this)&&this||{},e={},r=(n,s)=>{const o=t&&De(e,s)||s;K(e[o])&&K(n)?e[o]=ce(e[o],n):K(n)?e[o]=ce({},n):I(n)?e[o]=n.slice():e[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&V(arguments[n],r);return e}const At=(t,e,r,{allOwnKeys:n}={})=>(V(e,(s,o)=>{r&&O(s)?t[o]=_e(s,r):t[o]=s},{allOwnKeys:n}),t),Tt=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Ot=(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},Ct=(t,e,r,n)=>{let s,o,i;const c={};if(e=e||{},t==null)return e;do{for(s=Object.getOwnPropertyNames(t),o=s.length;o-- >0;)i=s[o],(!n||n(i,t,e))&&!c[i]&&(e[i]=t[i],c[i]=!0);t=r!==!1&&pe(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},xt=(t,e,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=e.length;const n=t.indexOf(e,r);return n!==-1&&n===r},Nt=t=>{if(!t)return null;if(I(t))return t;let e=t.length;if(!Be(e))return null;const r=new Array(e);for(;e-- >0;)r[e]=t[e];return r},Pt=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&pe(Uint8Array)),Ut=(t,e)=>{const n=(t&&t[Y]).call(t);let s;for(;(s=n.next())&&!s.done;){const o=s.value;e.call(t,o[0],o[1])}},kt=(t,e)=>{let r;const n=[];for(;(r=t.exec(e))!==null;)n.push(r);return n},_t=N("HTMLFormElement"),Lt=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),be=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),Ft=N("RegExp"),Ie=(t,e)=>{const r=Object.getOwnPropertyDescriptors(t),n={};V(r,(s,o)=>{let i;(i=e(s,o,t))!==!1&&(n[o]=i||s)}),Object.defineProperties(t,n)},Bt=t=>{Ie(t,(e,r)=>{if(O(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=t[r];if(O(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Dt=(t,e)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return I(t)?n(t):n(String(t).split(e)),r},jt=()=>{},It=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function qt(t){return!!(t&&O(t.append)&&t[Le]==="FormData"&&t[Y])}const Ht=t=>{const e=new Array(10),r=(n,s)=>{if(J(n)){if(e.indexOf(n)>=0)return;if(z(n))return n;if(!("toJSON"in n)){e[s]=n;const o=I(n)?[]:{};return V(n,(i,c)=>{const f=r(i,s+1);!$(f)&&(o[c]=f)}),e[s]=void 0,o}}return n};return r(t,0)},Mt=N("AsyncFunction"),$t=t=>t&&(J(t)||O(t))&&O(t.then)&&O(t.catch),qe=((t,e)=>t?setImmediate:e?((r,n)=>(B.addEventListener("message",({source:s,data:o})=>{s===B&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),B.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",O(B.postMessage)),zt=typeof queueMicrotask<"u"?queueMicrotask.bind(B):typeof process<"u"&&process.nextTick||qe,Jt=t=>t!=null&&O(t[Y]),a={isArray:I,isArrayBuffer:Fe,isBuffer:z,isFormData:yt,isArrayBufferView:at,isString:ct,isNumber:Be,isBoolean:lt,isObject:J,isPlainObject:K,isEmptyObject:ut,isReadableStream:wt,isRequest:bt,isResponse:Et,isHeaders:St,isUndefined:$,isDate:ft,isFile:dt,isBlob:ht,isRegExp:Ft,isFunction:O,isStream:mt,isURLSearchParams:gt,isTypedArray:Pt,isFileList:pt,forEach:V,merge:ce,extend:At,trim:Rt,stripBOM:Tt,inherits:Ot,toFlatObject:Ct,kindOf:ee,kindOfTest:N,endsWith:xt,toArray:Nt,forEachEntry:Ut,matchAll:kt,isHTMLForm:_t,hasOwnProperty:be,hasOwnProp:be,reduceDescriptors:Ie,freezeMethods:Bt,toObjectSet:Dt,toCamelCase:Lt,noop:jt,toFiniteNumber:It,findKey:De,global:B,isContextDefined:je,isSpecCompliantForm:qt,toJSONObject:Ht,isAsyncFn:Mt,isThenable:$t,setImmediate:qe,asap:zt,isIterable:Jt};function m(t,e,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(m,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const He=m.prototype,Me={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Me[t]={value:t}});Object.defineProperties(m,Me);Object.defineProperty(He,"isAxiosError",{value:!0});m.from=(t,e,r,n,s,o)=>{const i=Object.create(He);return a.toFlatObject(t,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),m.call(i,t.message,e,r,n,s),i.cause=t,i.name=t.name,o&&Object.assign(i,o),i};const Vt=null;function le(t){return a.isPlainObject(t)||a.isArray(t)}function $e(t){return a.endsWith(t,"[]")?t.slice(0,-2):t}function Ee(t,e,r){return t?t.concat(e).map(function(s,o){return s=$e(s),!r&&o?"["+s+"]":s}).join(r?".":""):e}function vt(t){return a.isArray(t)&&!t.some(le)}const Wt=a.toFlatObject(a,{},null,function(e){return/^is[A-Z]/.test(e)});function re(t,e,r){if(!a.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,r=a.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!a.isUndefined(p[y])});const n=r.metaTokens,s=r.visitor||u,o=r.dots,i=r.indexes,f=(r.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(e);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(a.isDate(h))return h.toISOString();if(a.isBoolean(h))return h.toString();if(!f&&a.isBlob(h))throw new m("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(h)||a.isTypedArray(h)?f&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function u(h,y,p){let w=h;if(h&&!p&&typeof h=="object"){if(a.endsWith(y,"{}"))y=n?y:y.slice(0,-2),h=JSON.stringify(h);else if(a.isArray(h)&&vt(h)||(a.isFileList(h)||a.endsWith(y,"[]"))&&(w=a.toArray(h)))return y=$e(y),w.forEach(function(R,U){!(a.isUndefined(R)||R===null)&&e.append(i===!0?Ee([y],U,o):i===null?y:y+"[]",l(R))}),!1}return le(h)?!0:(e.append(Ee(p,y,o),l(h)),!1)}const d=[],g=Object.assign(Wt,{defaultVisitor:u,convertValue:l,isVisitable:le});function E(h,y){if(!a.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(h),a.forEach(h,function(w,S){(!(a.isUndefined(w)||w===null)&&s.call(e,w,a.isString(S)?S.trim():S,y,g))===!0&&E(w,y?y.concat(S):[S])}),d.pop()}}if(!a.isObject(t))throw new TypeError("data must be an object");return E(t),e}function Se(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function me(t,e){this._pairs=[],t&&re(t,this,e)}const ze=me.prototype;ze.append=function(e,r){this._pairs.push([e,r])};ze.toString=function(e){const r=e?function(n){return e.call(this,n,Se)}:Se;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function Kt(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Je(t,e,r){if(!e)return t;const n=r&&r.encode||Kt;a.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(e,r):o=a.isURLSearchParams(e)?e.toString():new me(e,r).toString(n),o){const i=t.indexOf("#");i!==-1&&(t=t.slice(0,i)),t+=(t.indexOf("?")===-1?"?":"&")+o}return t}class Re{constructor(){this.handlers=[]}use(e,r,n){return this.handlers.push({fulfilled:e,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){a.forEach(this.handlers,function(n){n!==null&&e(n)})}}const Ve={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xt=typeof URLSearchParams<"u"?URLSearchParams:me,Gt=typeof FormData<"u"?FormData:null,Qt=typeof Blob<"u"?Blob:null,Zt={isBrowser:!0,classes:{URLSearchParams:Xt,FormData:Gt,Blob:Qt},protocols:["http","https","file","blob","url","data"]},ye=typeof window<"u"&&typeof document<"u",ue=typeof navigator=="object"&&navigator||void 0,Yt=ye&&(!ue||["ReactNative","NativeScript","NS"].indexOf(ue.product)<0),er=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",tr=ye&&window.location.href||"http://localhost",rr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ye,hasStandardBrowserEnv:Yt,hasStandardBrowserWebWorkerEnv:er,navigator:ue,origin:tr},Symbol.toStringTag,{value:"Module"})),A={...rr,...Zt};function nr(t,e){return re(t,new A.classes.URLSearchParams,{visitor:function(r,n,s,o){return A.isNode&&a.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...e})}function sr(t){return a.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function or(t){const e={},r=Object.keys(t);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],e[o]=t[o];return e}function ve(t){function e(r,n,s,o){let i=r[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=r.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],n]:s[i]=n,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),e(r,n,s[i],o)&&a.isArray(s[i])&&(s[i]=or(s[i])),!c)}if(a.isFormData(t)&&a.isFunction(t.entries)){const r={};return a.forEachEntry(t,(n,s)=>{e(sr(n),s,r,0)}),r}return null}function ir(t,e,r){if(a.isString(t))try{return(e||JSON.parse)(t),a.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(t)}const v={transitional:Ve,adapter:["xhr","http","fetch"],transformRequest:[function(e,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=a.isObject(e);if(o&&a.isHTMLForm(e)&&(e=new FormData(e)),a.isFormData(e))return s?JSON.stringify(ve(e)):e;if(a.isArrayBuffer(e)||a.isBuffer(e)||a.isStream(e)||a.isFile(e)||a.isBlob(e)||a.isReadableStream(e))return e;if(a.isArrayBufferView(e))return e.buffer;if(a.isURLSearchParams(e))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return nr(e,this.formSerializer).toString();if((c=a.isFileList(e))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return re(c?{"files[]":e}:e,f&&new f,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),ir(e)):e}],transformResponse:[function(e){const r=this.transitional||v.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(e)||a.isReadableStream(e))return e;if(e&&a.isString(e)&&(n&&!this.responseType||s)){const i=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(c){if(i)throw c.name==="SyntaxError"?m.from(c,m.ERR_BAD_RESPONSE,this,null,this.response):c}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:A.classes.FormData,Blob:A.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],t=>{v.headers[t]={}});const ar=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),cr=t=>{const e={};let r,n,s;return t&&t.split(`
`).forEach(function(i){s=i.indexOf(":"),r=i.substring(0,s).trim().toLowerCase(),n=i.substring(s+1).trim(),!(!r||e[r]&&ar[r])&&(r==="set-cookie"?e[r]?e[r].push(n):e[r]=[n]:e[r]=e[r]?e[r]+", "+n:n)}),e},Ae=Symbol("internals");function H(t){return t&&String(t).trim().toLowerCase()}function X(t){return t===!1||t==null?t:a.isArray(t)?t.map(X):String(t)}function lr(t){const e=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(t);)e[n[1]]=n[2];return e}const ur=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function oe(t,e,r,n,s){if(a.isFunction(n))return n.call(this,e,r);if(s&&(e=r),!!a.isString(e)){if(a.isString(n))return e.indexOf(n)!==-1;if(a.isRegExp(n))return n.test(e)}}function fr(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,r,n)=>r.toUpperCase()+n)}function dr(t,e){const r=a.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(s,o,i){return this[n].call(this,e,s,o,i)},configurable:!0})})}let C=class{constructor(e){e&&this.set(e)}set(e,r,n){const s=this;function o(c,f,l){const u=H(f);if(!u)throw new Error("header name must be a non-empty string");const d=a.findKey(s,u);(!d||s[d]===void 0||l===!0||l===void 0&&s[d]!==!1)&&(s[d||f]=X(c))}const i=(c,f)=>a.forEach(c,(l,u)=>o(l,u,f));if(a.isPlainObject(e)||e instanceof this.constructor)i(e,r);else if(a.isString(e)&&(e=e.trim())&&!ur(e))i(cr(e),r);else if(a.isObject(e)&&a.isIterable(e)){let c={},f,l;for(const u of e){if(!a.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(f=c[l])?a.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}i(c,r)}else e!=null&&o(r,e,n);return this}get(e,r){if(e=H(e),e){const n=a.findKey(this,e);if(n){const s=this[n];if(!r)return s;if(r===!0)return lr(s);if(a.isFunction(r))return r.call(this,s,n);if(a.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,r){if(e=H(e),e){const n=a.findKey(this,e);return!!(n&&this[n]!==void 0&&(!r||oe(this,this[n],n,r)))}return!1}delete(e,r){const n=this;let s=!1;function o(i){if(i=H(i),i){const c=a.findKey(n,i);c&&(!r||oe(n,n[c],c,r))&&(delete n[c],s=!0)}}return a.isArray(e)?e.forEach(o):o(e),s}clear(e){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!e||oe(this,this[o],o,e,!0))&&(delete this[o],s=!0)}return s}normalize(e){const r=this,n={};return a.forEach(this,(s,o)=>{const i=a.findKey(n,o);if(i){r[i]=X(s),delete r[o];return}const c=e?fr(o):String(o).trim();c!==o&&delete r[o],r[c]=X(s),n[c]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const r=Object.create(null);return a.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=e&&a.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,r])=>e+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...r){const n=new this(e);return r.forEach(s=>n.set(s)),n}static accessor(e){const n=(this[Ae]=this[Ae]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=H(i);n[c]||(dr(s,i),n[c]=!0)}return a.isArray(e)?e.forEach(o):o(e),this}};C.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(C.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[r]=n}}});a.freezeMethods(C);function ie(t,e){const r=this||v,n=e||r,s=C.from(n.headers);let o=n.data;return a.forEach(t,function(c){o=c.call(r,o,s.normalize(),e?e.status:void 0)}),s.normalize(),o}function We(t){return!!(t&&t.__CANCEL__)}function q(t,e,r){m.call(this,t??"canceled",m.ERR_CANCELED,e,r),this.name="CanceledError"}a.inherits(q,m,{__CANCEL__:!0});function Ke(t,e,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new m("Request failed with status code "+r.status,[m.ERR_BAD_REQUEST,m.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function hr(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function pr(t,e){t=t||10;const r=new Array(t),n=new Array(t);let s=0,o=0,i;return e=e!==void 0?e:1e3,function(f){const l=Date.now(),u=n[o];i||(i=l),r[s]=f,n[s]=l;let d=o,g=0;for(;d!==s;)g+=r[d++],d=d%t;if(s=(s+1)%t,s===o&&(o=(o+1)%t),l-i<e)return;const E=u&&l-u;return E?Math.round(g*1e3/E):void 0}}function mr(t,e){let r=0,n=1e3/e,s,o;const i=(l,u=Date.now())=>{r=u,s=null,o&&(clearTimeout(o),o=null),t(...l)};return[(...l)=>{const u=Date.now(),d=u-r;d>=n?i(l,u):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},n-d)))},()=>s&&i(s)]}const Q=(t,e,r=3)=>{let n=0;const s=pr(50,250);return mr(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-n,l=s(f),u=i<=c;n=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:l||void 0,estimated:l&&c&&u?(c-i)/l:void 0,event:o,lengthComputable:c!=null,[e?"download":"upload"]:!0};t(d)},r)},Te=(t,e)=>{const r=t!=null;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},Oe=t=>(...e)=>a.asap(()=>t(...e)),yr=A.hasStandardBrowserEnv?((t,e)=>r=>(r=new URL(r,A.origin),t.protocol===r.protocol&&t.host===r.host&&(e||t.port===r.port)))(new URL(A.origin),A.navigator&&/(msie|trident)/i.test(A.navigator.userAgent)):()=>!0,gr=A.hasStandardBrowserEnv?{write(t,e,r,n,s,o){const i=[t+"="+encodeURIComponent(e)];a.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),a.isString(n)&&i.push("path="+n),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function wr(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function br(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function Xe(t,e,r){let n=!wr(e);return t&&(n||r==!1)?br(t,e):e}const Ce=t=>t instanceof C?{...t}:t;function j(t,e){e=e||{};const r={};function n(l,u,d,g){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:g},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function s(l,u,d,g){if(a.isUndefined(u)){if(!a.isUndefined(l))return n(void 0,l,d,g)}else return n(l,u,d,g)}function o(l,u){if(!a.isUndefined(u))return n(void 0,u)}function i(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return n(void 0,l)}else return n(void 0,u)}function c(l,u,d){if(d in e)return n(l,u);if(d in t)return n(void 0,l)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(l,u,d)=>s(Ce(l),Ce(u),d,!0)};return a.forEach(Object.keys({...t,...e}),function(u){const d=f[u]||s,g=d(t[u],e[u],u);a.isUndefined(g)&&d!==c||(r[u]=g)}),r}const Ge=t=>{const e=j({},t);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=e;e.headers=i=C.from(i),e.url=Je(Xe(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(r)){if(A.hasStandardBrowserEnv||A.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[l,...u]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(A.hasStandardBrowserEnv&&(n&&a.isFunction(n)&&(n=n(e)),n||n!==!1&&yr(e.url))){const l=s&&o&&gr.read(o);l&&i.set(s,l)}return e},Er=typeof XMLHttpRequest<"u",Sr=Er&&function(t){return new Promise(function(r,n){const s=Ge(t);let o=s.data;const i=C.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:l}=s,u,d,g,E,h;function y(){E&&E(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function w(){if(!p)return;const R=C.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),T={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:R,config:t,request:p};Ke(function(L){r(L),y()},function(L){n(L),y()},T),p=null}"onloadend"in p?p.onloadend=w:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(w)},p.onabort=function(){p&&(n(new m("Request aborted",m.ECONNABORTED,t,p)),p=null)},p.onerror=function(){n(new m("Network Error",m.ERR_NETWORK,t,p)),p=null},p.ontimeout=function(){let U=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const T=s.transitional||Ve;s.timeoutErrorMessage&&(U=s.timeoutErrorMessage),n(new m(U,T.clarifyTimeoutError?m.ETIMEDOUT:m.ECONNABORTED,t,p)),p=null},o===void 0&&i.setContentType(null),"setRequestHeader"in p&&a.forEach(i.toJSON(),function(U,T){p.setRequestHeader(T,U)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),l&&([g,h]=Q(l,!0),p.addEventListener("progress",g)),f&&p.upload&&([d,E]=Q(f),p.upload.addEventListener("progress",d),p.upload.addEventListener("loadend",E)),(s.cancelToken||s.signal)&&(u=R=>{p&&(n(!R||R.type?new q(null,t,p):R),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const S=hr(s.url);if(S&&A.protocols.indexOf(S)===-1){n(new m("Unsupported protocol "+S+":",m.ERR_BAD_REQUEST,t));return}p.send(o||null)})},Rr=(t,e)=>{const{length:r}=t=t?t.filter(Boolean):[];if(e||r){let n=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;n.abort(u instanceof m?u:new q(u instanceof Error?u.message:u))}};let i=e&&setTimeout(()=>{i=null,o(new m(`timeout ${e} of ms exceeded`,m.ETIMEDOUT))},e);const c=()=>{t&&(i&&clearTimeout(i),i=null,t.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),t=null)};t.forEach(l=>l.addEventListener("abort",o));const{signal:f}=n;return f.unsubscribe=()=>a.asap(c),f}},Ar=function*(t,e){let r=t.byteLength;if(r<e){yield t;return}let n=0,s;for(;n<r;)s=n+e,yield t.slice(n,s),n=s},Tr=async function*(t,e){for await(const r of Or(t))yield*Ar(r,e)},Or=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:r,value:n}=await e.read();if(r)break;yield n}}finally{await e.cancel()}},xe=(t,e,r,n)=>{const s=Tr(t,e);let o=0,i,c=f=>{i||(i=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:l,value:u}=await s.next();if(l){c(),f.close();return}let d=u.byteLength;if(r){let g=o+=d;r(g)}f.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},ne=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qe=ne&&typeof ReadableStream=="function",Cr=ne&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Ze=(t,...e)=>{try{return!!t(...e)}catch{return!1}},xr=Qe&&Ze(()=>{let t=!1;const e=new Request(A.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),Ne=64*1024,fe=Qe&&Ze(()=>a.isReadableStream(new Response("").body)),Z={stream:fe&&(t=>t.body)};ne&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Z[e]&&(Z[e]=a.isFunction(t[e])?r=>r[e]():(r,n)=>{throw new m(`Response type '${e}' is not supported`,m.ERR_NOT_SUPPORT,n)})})})(new Response);const Nr=async t=>{if(t==null)return 0;if(a.isBlob(t))return t.size;if(a.isSpecCompliantForm(t))return(await new Request(A.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(a.isArrayBufferView(t)||a.isArrayBuffer(t))return t.byteLength;if(a.isURLSearchParams(t)&&(t=t+""),a.isString(t))return(await Cr(t)).byteLength},Pr=async(t,e)=>{const r=a.toFiniteNumber(t.getContentLength());return r??Nr(e)},Ur=ne&&(async t=>{let{url:e,method:r,data:n,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:g}=Ge(t);l=l?(l+"").toLowerCase():"text";let E=Rr([s,o&&o.toAbortSignal()],i),h;const y=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let p;try{if(f&&xr&&r!=="get"&&r!=="head"&&(p=await Pr(u,n))!==0){let T=new Request(e,{method:"POST",body:n,duplex:"half"}),_;if(a.isFormData(n)&&(_=T.headers.get("content-type"))&&u.setContentType(_),T.body){const[L,W]=Te(p,Q(Oe(f)));n=xe(T.body,Ne,L,W)}}a.isString(d)||(d=d?"include":"omit");const w="credentials"in Request.prototype;h=new Request(e,{...g,signal:E,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:w?d:void 0});let S=await fetch(h,g);const R=fe&&(l==="stream"||l==="response");if(fe&&(c||R&&y)){const T={};["status","statusText","headers"].forEach(ge=>{T[ge]=S[ge]});const _=a.toFiniteNumber(S.headers.get("content-length")),[L,W]=c&&Te(_,Q(Oe(c),!0))||[];S=new Response(xe(S.body,Ne,L,()=>{W&&W(),y&&y()}),T)}l=l||"text";let U=await Z[a.findKey(Z,l)||"text"](S,t);return!R&&y&&y(),await new Promise((T,_)=>{Ke(T,_,{data:U,headers:C.from(S.headers),status:S.status,statusText:S.statusText,config:t,request:h})})}catch(w){throw y&&y(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new m("Network Error",m.ERR_NETWORK,t,h),{cause:w.cause||w}):m.from(w,w&&w.code,t,h)}}),de={http:Vt,xhr:Sr,fetch:Ur};a.forEach(de,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const Pe=t=>`- ${t}`,kr=t=>a.isFunction(t)||t===null||t===!1,Ye={getAdapter:t=>{t=a.isArray(t)?t:[t];const{length:e}=t;let r,n;const s={};for(let o=0;o<e;o++){r=t[o];let i;if(n=r,!kr(r)&&(n=de[(i=String(r)).toLowerCase()],n===void 0))throw new m(`Unknown adapter '${i}'`);if(n)break;s[i||"#"+o]=n}if(!n){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=e?o.length>1?`since :
`+o.map(Pe).join(`
`):" "+Pe(o[0]):"as no adapter specified";throw new m("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:de};function ae(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new q(null,t)}function Ue(t){return ae(t),t.headers=C.from(t.headers),t.data=ie.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),Ye.getAdapter(t.adapter||v.adapter)(t).then(function(n){return ae(t),n.data=ie.call(t,t.transformResponse,n),n.headers=C.from(n.headers),n},function(n){return We(n)||(ae(t),n&&n.response&&(n.response.data=ie.call(t,t.transformResponse,n.response),n.response.headers=C.from(n.response.headers))),Promise.reject(n)})}const et="1.11.0",se={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{se[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const ke={};se.transitional=function(e,r,n){function s(o,i){return"[Axios v"+et+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,c)=>{if(e===!1)throw new m(s(i," has been removed"+(r?" in "+r:"")),m.ERR_DEPRECATED);return r&&!ke[i]&&(ke[i]=!0,console.warn(s(i," has been deprecated since v"+r+" and will be removed in the near future"))),e?e(o,i,c):!0}};se.spelling=function(e){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function _r(t,e,r){if(typeof t!="object")throw new m("options must be an object",m.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let s=n.length;for(;s-- >0;){const o=n[s],i=e[o];if(i){const c=t[o],f=c===void 0||i(c,o,t);if(f!==!0)throw new m("option "+o+" must be "+f,m.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new m("Unknown option "+o,m.ERR_BAD_OPTION)}}const G={assertOptions:_r,validators:se},P=G.validators;let D=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Re,response:new Re}}async request(e,r){try{return await this._request(e,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(e,r){typeof e=="string"?(r=r||{},r.url=e):r=e||{},r=j(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&G.assertOptions(n,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),s!=null&&(a.isFunction(s)?r.paramsSerializer={serialize:s}:G.assertOptions(s,{encode:P.function,serialize:P.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),G.assertOptions(r,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[r.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),r.headers=C.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,d=0,g;if(!f){const h=[Ue.bind(this),void 0];for(h.unshift(...c),h.push(...l),g=h.length,u=Promise.resolve(r);d<g;)u=u.then(h[d++],h[d++]);return u}g=c.length;let E=r;for(d=0;d<g;){const h=c[d++],y=c[d++];try{E=h(E)}catch(p){y.call(this,p);break}}try{u=Ue.call(this,E)}catch(h){return Promise.reject(h)}for(d=0,g=l.length;d<g;)u=u.then(l[d++],l[d++]);return u}getUri(e){e=j(this.defaults,e);const r=Xe(e.baseURL,e.url,e.allowAbsoluteUrls);return Je(r,e.params,e.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(e){D.prototype[e]=function(r,n){return this.request(j(n||{},{method:e,url:r,data:(n||{}).data}))}});a.forEach(["post","put","patch"],function(e){function r(n){return function(o,i,c){return this.request(j(c||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}D.prototype[e]=r(),D.prototype[e+"Form"]=r(!0)});let Lr=class tt{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{n.subscribe(c),o=c}).then(s);return i.cancel=function(){n.unsubscribe(o)},i},e(function(o,i,c){n.reason||(n.reason=new q(o,i,c),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const r=this._listeners.indexOf(e);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const e=new AbortController,r=n=>{e.abort(n)};return this.subscribe(r),e.signal.unsubscribe=()=>this.unsubscribe(r),e.signal}static source(){let e;return{token:new tt(function(s){e=s}),cancel:e}}};function Fr(t){return function(r){return t.apply(null,r)}}function Br(t){return a.isObject(t)&&t.isAxiosError===!0}const he={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(he).forEach(([t,e])=>{he[e]=t});function rt(t){const e=new D(t),r=_e(D.prototype.request,e);return a.extend(r,D.prototype,e,{allOwnKeys:!0}),a.extend(r,e,null,{allOwnKeys:!0}),r.create=function(s){return rt(j(t,s))},r}const b=rt(v);b.Axios=D;b.CanceledError=q;b.CancelToken=Lr;b.isCancel=We;b.VERSION=et;b.toFormData=re;b.AxiosError=m;b.Cancel=b.CanceledError;b.all=function(e){return Promise.all(e)};b.spread=Fr;b.isAxiosError=Br;b.mergeConfig=j;b.AxiosHeaders=C;b.formToJSON=t=>ve(a.isHTMLForm(t)?new FormData(t):t);b.getAdapter=Ye.getAdapter;b.HttpStatusCode=he;b.default=b;const{Axios:Mr,AxiosError:$r,CanceledError:zr,isCancel:Jr,CancelToken:Vr,VERSION:vr,all:Wr,Cancel:Kr,isAxiosError:Xr,spread:Gr,toFormData:Qr,AxiosHeaders:Zr,HttpStatusCode:Yr,formToJSON:en,getAdapter:tn,mergeConfig:rn}=b;class nt{constructor(e){we(this,"client");this.client=b.create({baseURL:e.baseURL,timeout:e.timeout||1e4,headers:{"Content-Type":"application/json",...e.headers}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{var n;x.logger.info(`API Request: ${(n=e.method)==null?void 0:n.toUpperCase()} ${e.url}`);const r=this.getAuthToken();return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>(x.logger.error("API Request Error:",e),Promise.reject(e))),this.client.interceptors.response.use(e=>(x.logger.info(`API Response: ${e.status} ${e.config.url}`),e),e=>{var r;return x.logger.error("API Response Error:",e),((r=e.response)==null?void 0:r.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){return localStorage.getItem("authToken")}handleUnauthorized(){localStorage.removeItem("authToken"),window.location.href="/login"}async get(e,r){try{return{data:(await this.client.get(e,r)).data,success:!0}}catch(n){return this.handleError(n)}}async post(e,r,n){try{return{data:(await this.client.post(e,r,n)).data,success:!0}}catch(s){return this.handleError(s)}}async put(e,r,n){try{return{data:(await this.client.put(e,r,n)).data,success:!0}}catch(s){return this.handleError(s)}}async delete(e,r){try{return{data:(await this.client.delete(e,r)).data,success:!0}}catch(n){return this.handleError(n)}}handleError(e){var s,o,i,c;const r=((o=(s=e.response)==null?void 0:s.data)==null?void 0:o.message)||e.message||"An error occurred",n=((c=(i=e.response)==null?void 0:i.data)==null?void 0:c.errors)||[r];return{data:null,success:!1,message:r,errors:n}}}const M=new nt({baseURL:process.env.REACT_APP_API_BASE_URL||"http://localhost:3001/api"});class Dr{async login(e){try{const r=await M.post("/auth/login",e);return r.success&&r.data?(localStorage.setItem("authToken",r.data.token),localStorage.setItem("user",JSON.stringify(r.data.user)),x.logger.info("User logged in successfully"),!0):(x.logger.error("Login failed:",r.message),!1)}catch(r){return x.logger.error("Login error:",r),!1}}async logout(){try{await M.post("/auth/logout")}catch(e){x.logger.error("Logout error:",e)}finally{localStorage.removeItem("authToken"),localStorage.removeItem("user"),x.logger.info("User logged out")}}async refreshToken(){try{const e=await M.post("/auth/refresh");return e.success&&e.data?(localStorage.setItem("authToken",e.data.token),!0):!1}catch(e){return x.logger.error("Token refresh error:",e),!1}}async getCurrentUser(){try{const e=await M.get("/auth/me");return e.success&&e.data?(localStorage.setItem("user",JSON.stringify(e.data)),e.data):null}catch(e){return x.logger.error("Get current user error:",e),null}}}const F=new Dr,jr=()=>{const[t,e]=k.useState({user:null,isAuthenticated:!1,isLoading:!0,error:null}),r=k.useCallback(l=>{e(u=>({...u,user:l,isAuthenticated:!!l,isLoading:!1,error:null}))},[]),n=k.useCallback(l=>{e(u=>({...u,error:l,isLoading:!1}))},[]),s=k.useCallback(l=>{e(u=>({...u,isLoading:l}))},[]),o=k.useCallback(async l=>{s(!0),n("");const u=await F.login(l);if(u){const d=await F.getCurrentUser();r(d)}else n("Invalid credentials"),s(!1);return u},[r,n,s]),i=k.useCallback(async()=>{s(!0),await F.logout(),r(null)},[r,s]),c=k.useCallback(async()=>await F.refreshToken(),[]),f=k.useCallback(async()=>{const l=await F.getCurrentUser();return r(l),l},[r]);return k.useEffect(()=>{(async()=>{const u=localStorage.getItem("authToken"),d=localStorage.getItem("user");if(u&&d)try{const g=JSON.parse(d);r(g),await F.getCurrentUser()||(localStorage.removeItem("authToken"),localStorage.removeItem("user"),r(null))}catch(g){x.logger.error("Auth initialization error:",g),r(null)}else s(!1)})()},[r,s]),{...t,login:o,logout:i,refreshToken:c,getCurrentUser:f}};exports.ApiClient=nt;exports.apiClient=M;exports.authService=F;exports.useAuth=jr;
