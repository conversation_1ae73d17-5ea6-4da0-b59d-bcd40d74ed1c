/**
 * Theme Testing Utilities
 * 
 * Utilities for testing theme switching and theme-aware components
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ReactElement } from 'react';
import { ThemeProvider, ThemeMode } from '../services/theme';

export interface ThemeRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  theme?: ThemeMode;
  enableAutoDetection?: boolean;
  enablePersistence?: boolean;
  storageKey?: string;
}

/**
 * Render component with ThemeProvider wrapper
 */
export function renderWithTheme(
  ui: ReactElement,
  options: ThemeRenderOptions = {}
): RenderResult {
  const {
    theme = ThemeMode.CRM,
    enableAutoDetection = false,
    enablePersistence = false,
    storageKey = 'test-theme',
    ...renderOptions
  } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider
      defaultTheme={theme}
      enableAutoDetection={enableAutoDetection}
      enablePersistence={enablePersistence}
      storageKey={storageKey}
    >
      {children}
    </ThemeProvider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

/**
 * Test component in both CRM and MFE themes
 */
export function testInBothThemes(
  testName: string,
  testFn: (theme: ThemeMode) => void
): void {
  describe(testName, () => {
    test('in CRM theme', () => {
      testFn(ThemeMode.CRM);
    });

    test('in MFE theme', () => {
      testFn(ThemeMode.MFE);
    });
  });
}

/**
 * Mock theme detection for testing
 */
export function mockThemeDetection(theme: ThemeMode): void {
  // Mock deployment mode environment variable
  const deploymentMode = theme === ThemeMode.CRM ? 'web_resource' : 'standalone_mfe';
  process.env.VITE_DEPLOYMENT_MODE = deploymentMode;
  process.env.VITE_THEME_MODE = theme;
}

/**
 * Assert that element has correct theme attributes
 */
export function expectThemeAttributes(element: HTMLElement, theme: ThemeMode): void {
  expect(element).toHaveAttribute('data-theme', theme);
}

/**
 * Assert that CSS custom properties are applied
 */
export function expectCSSCustomProperties(properties: Record<string, string>): void {
  const root = document.documentElement;
  Object.entries(properties).forEach(([property, expectedValue]) => {
    const actualValue = getComputedStyle(root).getPropertyValue(property);
    expect(actualValue.trim()).toBe(expectedValue);
  });
}

/**
 * Mock CSS custom property values for testing
 */
export function mockCSSCustomProperties(properties: Record<string, string>): void {
  const root = document.documentElement;
  Object.entries(properties).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
}

/**
 * Test theme switching behavior
 */
export function createThemeSwitchingTest(
  componentFactory: (theme: ThemeMode) => ReactElement
) {
  return {
    testThemeSwitch: (fromTheme: ThemeMode, toTheme: ThemeMode) => {
      const { rerender } = renderWithTheme(componentFactory(fromTheme), {
        theme: fromTheme,
      });

      // Verify initial theme
      expect(document.documentElement).toHaveAttribute('data-theme', fromTheme);

      // Switch theme
      rerender(componentFactory(toTheme));

      // Verify theme switched
      expect(document.documentElement).toHaveAttribute('data-theme', toTheme);
    },
  };
}

/**
 * Mock responsive breakpoints for theme testing
 */
export function mockResponsiveBreakpoint(
  breakpoint: 'mobile' | 'tablet' | 'desktop',
  theme: ThemeMode = ThemeMode.MFE
): void {
  const breakpoints = {
    mobile: theme === ThemeMode.MFE ? 840 : 768,
    tablet: theme === ThemeMode.MFE ? 1200 : 1024,
    desktop: 1920,
  };

  const width = breakpoints[breakpoint] - 1; // Just below breakpoint

  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => {
      const matches = query.includes(`max-width: ${breakpoints[breakpoint]}px`);
      return {
        matches,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    }),
  });
}

/**
 * Theme test helpers
 */
export const themeTestHelpers = {
  /**
   * Get theme-specific test data
   */
  getThemeData: (theme: ThemeMode) => ({
    primaryColor: theme === ThemeMode.CRM ? '#0078d4' : '#5e10b1',
    fontFamily: theme === ThemeMode.CRM 
      ? '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif'
      : '"RNHouseSans", Arial, sans-serif',
    borderRadius: theme === ThemeMode.CRM ? '2px' : '16px',
    controlHeight: theme === ThemeMode.CRM ? '32px' : '44px',
    mobileBreakpoint: theme === ThemeMode.CRM ? '768px' : '840px',
  }),

  /**
   * Setup theme-specific CSS custom properties for testing
   */
  setupThemeProperties: (theme: ThemeMode) => {
    const data = themeTestHelpers.getThemeData(theme);
    mockCSSCustomProperties({
      '--theme-primary': data.primaryColor,
      '--theme-font-family': data.fontFamily,
      '--theme-border-radius': data.borderRadius,
      '--theme-control-height': data.controlHeight,
      '--theme-mobile-breakpoint': data.mobileBreakpoint,
    });
  },

  /**
   * Verify theme-specific styling
   */
  verifyThemeStyling: (element: HTMLElement, theme: ThemeMode) => {
    const data = themeTestHelpers.getThemeData(theme);
    expectThemeAttributes(element, theme);
    
    // You can add more specific style assertions here
    const computedStyle = getComputedStyle(element);
    
    // Example assertions (customize based on your components)
    if (element.classList.contains('theme-button-primary')) {
      expect(computedStyle.backgroundColor).toBe(data.primaryColor);
    }
    
    if (element.classList.contains('theme-text')) {
      expect(computedStyle.fontFamily).toContain(data.fontFamily.split(',')[0].replace(/"/g, ''));
    }
  },
};
