/**
 * Deployment Context Detection and Configuration Management
 * 
 * This module provides runtime detection of the deployment environment
 * and manages configuration switching between web resource and standalone deployments.
 */

export enum DeploymentMode {
  WEB_RESOURCE = 'web_resource',
  EMBEDDED_SPA = 'embedded_spa',
  STANDALONE_MFE = 'standalone_mfe'
}

export enum ThemeMode {
  CRM = 'crm',
  MFE = 'mfe'
}

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  fontFamily: string;
  customProperties: Record<string, string>;
}

/**
 * Legacy compatibility - maps old STANDALONE mode to new modes
 */
function mapLegacyDeploymentMode(mode: string): DeploymentMode {
  switch (mode) {
    case 'standalone':
      return DeploymentMode.STANDALONE_MFE;
    case 'web_resource':
      return DeploymentMode.WEB_RESOURCE;
    case 'embedded_spa':
      return DeploymentMode.EMBEDDED_SPA;
    case 'standalone_mfe':
      return DeploymentMode.STANDALONE_MFE;
    default:
      return DeploymentMode.WEB_RESOURCE;
  }
}

export interface DeploymentConfig {
  mode: DeploymentMode;
  theme: ThemeConfig;
  apiBaseUrl: string;
  authMethod: 'dynamics365' | 'msal';
  msalConfig?: {
    clientId: string;
    authority: string;
    redirectUri: string;
    scopes: string[];
  };
  dynamicsConfig?: {
    serverUrl: string;
    version: string;
  };
  features: {
    enableLogging: boolean;
    enableOfflineMode: boolean;
    enableTelemetry: boolean;
    enableThemeSwitching: boolean;
  };
}

/**
 * Detects the current deployment context
 */
export class DeploymentContextDetector {
  private static _instance: DeploymentContextDetector;
  private _detectedMode: DeploymentMode | null = null;
  private _config: DeploymentConfig | null = null;

  public static getInstance(): DeploymentContextDetector {
    if (!DeploymentContextDetector._instance) {
      DeploymentContextDetector._instance = new DeploymentContextDetector();
    }
    return DeploymentContextDetector._instance;
  }

  /**
   * Detects the deployment mode based on runtime environment
   */
  public detectDeploymentMode(): DeploymentMode {
    if (this._detectedMode) {
      return this._detectedMode;
    }

    // Check for explicit configuration override
    const configOverride = this.getConfigurationOverride();
    if (configOverride) {
      this._detectedMode = configOverride;
      return this._detectedMode;
    }

    // Check for Dynamics 365 context indicators
    const isDynamics365Context = this.isDynamics365Environment();
    const isEmbeddedSPA = this.isEmbeddedSPAEnvironment();

    if (isDynamics365Context) {
      this._detectedMode = DeploymentMode.WEB_RESOURCE;
    } else if (isEmbeddedSPA) {
      this._detectedMode = DeploymentMode.EMBEDDED_SPA;
    } else {
      this._detectedMode = DeploymentMode.STANDALONE_MFE;
    }

    return this._detectedMode;
  }

  /**
   * Detects the appropriate theme mode based on deployment context
   */
  public detectThemeMode(): ThemeMode {
    const deploymentMode = this.detectDeploymentMode();

    // Check for explicit theme override
    const themeOverride = this.getThemeOverride();
    if (themeOverride) {
      return themeOverride;
    }

    // Map deployment mode to theme mode
    switch (deploymentMode) {
      case DeploymentMode.WEB_RESOURCE:
      case DeploymentMode.EMBEDDED_SPA:
        return ThemeMode.CRM;
      case DeploymentMode.STANDALONE_MFE:
        return ThemeMode.MFE;
      default:
        return ThemeMode.CRM;
    }
  }

  /**
   * Checks if running within Dynamics 365 context (web resource)
   */
  private isDynamics365Environment(): boolean {
    try {
      // Check for Dynamics 365 global objects
      if (typeof window !== 'undefined') {
        // Check for Xrm object (Dynamics 365 Client API)
        if ((window as any).Xrm && (window as any).Xrm.WebApi) {
          return true;
        }

        // Check for parent window with Dynamics context
        if (window.parent && window.parent !== window) {
          try {
            if ((window.parent as any).Xrm) {
              return true;
            }
          } catch (e) {
            // Cross-origin access might be blocked, but this could still be D365
          }
        }

        // Check URL patterns that indicate Dynamics 365 web resource
        const url = window.location.href;
        if (url.includes('/WebResources/') || url.includes('/_static/')) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn('Error detecting Dynamics 365 environment:', error);
      return false;
    }
  }

  /**
   * Checks if running as embedded SPA within Dynamics 365 interface
   */
  private isEmbeddedSPAEnvironment(): boolean {
    try {
      if (typeof window !== 'undefined') {
        const url = window.location.href;

        // Check URL patterns that indicate embedded SPA
        if ((url.includes('.dynamics.com') || url.includes('.crm.dynamics.com')) &&
            !url.includes('/WebResources/') && !url.includes('/_static/')) {
          return true;
        }

        // Check for embedded SPA specific query parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('embedded') || urlParams.has('spa_mode')) {
          return true;
        }

        // Check for specific referrer patterns
        if (document.referrer &&
            (document.referrer.includes('.dynamics.com') ||
             document.referrer.includes('.crm.dynamics.com'))) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn('Error detecting embedded SPA environment:', error);
      return false;
    }
  }

  /**
   * Gets configuration override from environment variables or URL parameters
   */
  private getConfigurationOverride(): DeploymentMode | null {
    try {
      // Check environment variable override
      const envOverride = this.getEnvironmentVariable('VITE_DEPLOYMENT_MODE');
      if (envOverride) {
        return mapLegacyDeploymentMode(envOverride);
      }

      // Check URL parameter override (useful for testing)
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlOverride = urlParams.get('deploymentMode');
        if (urlOverride) {
          return mapLegacyDeploymentMode(urlOverride);
        }
      }

      return null;
    } catch (error) {
      console.warn('Error getting configuration override:', error);
      return null;
    }
  }

  /**
   * Gets theme override from environment variables or URL parameters
   */
  private getThemeOverride(): ThemeMode | null {
    try {
      // Check environment variable override
      const envOverride = this.getEnvironmentVariable('VITE_THEME_MODE');
      if (envOverride && Object.values(ThemeMode).includes(envOverride as ThemeMode)) {
        return envOverride as ThemeMode;
      }

      // Check URL parameter override (useful for testing)
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlOverride = urlParams.get('themeMode');
        if (urlOverride && Object.values(ThemeMode).includes(urlOverride as ThemeMode)) {
          return urlOverride as ThemeMode;
        }
      }

      return null;
    } catch (error) {
      console.warn('Error getting theme override:', error);
      return null;
    }
  }

  /**
   * Gets environment variable with fallback
   */
  private getEnvironmentVariable(key: string): string | null {
    try {
      // @ts-ignore - Vite will replace this at build time
      return import.meta.env?.[key] || null;
    } catch {
      return null;
    }
  }

  /**
   * Gets the deployment configuration for the detected mode
   */
  public getDeploymentConfig(): DeploymentConfig {
    if (this._config) {
      return this._config;
    }

    const mode = this.detectDeploymentMode();
    this._config = this.createConfigForMode(mode);
    return this._config;
  }

  /**
   * Creates configuration object for the specified deployment mode
   */
  private createConfigForMode(mode: DeploymentMode): DeploymentConfig {
    const themeMode = this.detectThemeMode();
    const themeConfig = this.createThemeConfig(themeMode);

    const baseConfig = {
      mode,
      theme: themeConfig,
      features: {
        enableLogging: this.getEnvironmentVariable('VITE_ENABLE_LOGGING') === 'true' || mode !== DeploymentMode.WEB_RESOURCE,
        enableOfflineMode: this.getEnvironmentVariable('VITE_ENABLE_OFFLINE') === 'true' || false,
        enableTelemetry: this.getEnvironmentVariable('VITE_ENABLE_TELEMETRY') === 'true' || false,
        enableThemeSwitching: this.getEnvironmentVariable('VITE_ENABLE_THEME_SWITCHING') === 'true' || false,
      }
    };

    switch (mode) {
      case DeploymentMode.WEB_RESOURCE:
        return {
          ...baseConfig,
          apiBaseUrl: '', // Will use relative URLs within D365
          authMethod: 'dynamics365',
          dynamicsConfig: {
            serverUrl: this.getEnvironmentVariable('VITE_DYNAMICS_SERVER_URL') || '',
            version: this.getEnvironmentVariable('VITE_DYNAMICS_API_VERSION') || '9.2',
          }
        };

      case DeploymentMode.EMBEDDED_SPA:
        return {
          ...baseConfig,
          apiBaseUrl: this.getEnvironmentVariable('VITE_API_BASE_URL') || 'https://your-org.api.crm.dynamics.com/api/data/v9.2',
          authMethod: 'msal',
          msalConfig: {
            clientId: this.getEnvironmentVariable('VITE_MSAL_CLIENT_ID') || '',
            authority: this.getEnvironmentVariable('VITE_MSAL_AUTHORITY') || 'https://login.microsoftonline.com/common',
            redirectUri: this.getEnvironmentVariable('VITE_MSAL_REDIRECT_URI') || window.location.origin,
            scopes: (this.getEnvironmentVariable('VITE_MSAL_SCOPES') || 'https://your-org.crm.dynamics.com/.default').split(','),
          }
        };

      case DeploymentMode.STANDALONE_MFE:
        return {
          ...baseConfig,
          apiBaseUrl: this.getEnvironmentVariable('VITE_API_BASE_URL') || 'https://your-org.api.crm.dynamics.com/api/data/v9.2',
          authMethod: 'msal',
          msalConfig: {
            clientId: this.getEnvironmentVariable('VITE_MSAL_CLIENT_ID') || '',
            authority: this.getEnvironmentVariable('VITE_MSAL_AUTHORITY') || 'https://login.microsoftonline.com/common',
            redirectUri: this.getEnvironmentVariable('VITE_MSAL_REDIRECT_URI') || window.location.origin,
            scopes: (this.getEnvironmentVariable('VITE_MSAL_SCOPES') || 'https://your-org.crm.dynamics.com/.default').split(','),
          }
        };

      default:
        throw new Error(`Unsupported deployment mode: ${mode}`);
    }
  }

  /**
   * Creates theme configuration for the specified theme mode
   */
  private createThemeConfig(themeMode: ThemeMode): ThemeConfig {
    switch (themeMode) {
      case ThemeMode.CRM:
        return {
          mode: ThemeMode.CRM,
          primaryColor: '#0078d4',
          secondaryColor: '#106ebe',
          backgroundColor: '#ffffff',
          textColor: '#323130',
          borderColor: '#8a8886',
          fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
          customProperties: {
            '--crm-header-bg': '#0078d4',
            '--crm-sidebar-bg': '#f3f2f1',
            '--crm-card-bg': '#ffffff',
            '--crm-border-radius': '2px',
            '--crm-shadow': '0 2px 4px rgba(0, 0, 0, 0.1)',
            '--crm-spacing-xs': '4px',
            '--crm-spacing-sm': '8px',
            '--crm-spacing-md': '16px',
            '--crm-spacing-lg': '24px',
            '--crm-spacing-xl': '32px',
          }
        };

      case ThemeMode.MFE:
        return {
          mode: ThemeMode.MFE,
          primaryColor: '#5e10b1',
          secondaryColor: '#646068',
          backgroundColor: '#f2f2f8',
          textColor: '#333',
          borderColor: '#646068',
          fontFamily: '"RNHouseSans", Arial, sans-serif',
          customProperties: {
            '--mfe-header-bg': '#333',
            '--mfe-sidebar-bg': '#f9f9fc',
            '--mfe-card-bg': '#ffffff',
            '--mfe-border-radius': '16px',
            '--mfe-shadow': '0 2px 2px 0 rgba(0, 0, 0, 0.1)',
            '--mfe-spacing-xs': '4px',
            '--mfe-spacing-sm': '8px',
            '--mfe-spacing-md': '16px',
            '--mfe-spacing-lg': '24px',
            '--mfe-spacing-xl': '32px',
            '--theme-mobile-breakpoint': '840px',
          }
        };

      default:
        throw new Error(`Unsupported theme mode: ${themeMode}`);
    }
  }

  /**
   * Forces a specific deployment mode (useful for testing)
   */
  public forceDeploymentMode(mode: DeploymentMode): void {
    this._detectedMode = mode;
    this._config = null; // Reset config to regenerate
  }

  /**
   * Resets the detector state
   */
  public reset(): void {
    this._detectedMode = null;
    this._config = null;
  }
}

/**
 * Convenience function to get the current deployment configuration
 */
export function getDeploymentConfig(): DeploymentConfig {
  return DeploymentContextDetector.getInstance().getDeploymentConfig();
}

/**
 * Convenience function to check if running in web resource mode
 */
export function isWebResourceMode(): boolean {
  return getDeploymentConfig().mode === DeploymentMode.WEB_RESOURCE;
}

/**
 * Convenience function to check if running in embedded SPA mode
 */
export function isEmbeddedSPAMode(): boolean {
  return getDeploymentConfig().mode === DeploymentMode.EMBEDDED_SPA;
}

/**
 * Convenience function to check if running in standalone MFE mode
 */
export function isStandaloneMFEMode(): boolean {
  return getDeploymentConfig().mode === DeploymentMode.STANDALONE_MFE;
}

/**
 * Convenience function to check if running in standalone mode (legacy compatibility)
 * Maps new deployment modes to legacy standalone concept
 */
export function isStandaloneMode(): boolean {
  const mode = getDeploymentConfig().mode;
  return mode === DeploymentMode.EMBEDDED_SPA || mode === DeploymentMode.STANDALONE_MFE;
}



/**
 * Convenience function to check if using CRM theme
 */
export function isCRMTheme(): boolean {
  return getDeploymentConfig().theme.mode === ThemeMode.CRM;
}

/**
 * Convenience function to check if using MFE theme
 */
export function isMFETheme(): boolean {
  return getDeploymentConfig().theme.mode === ThemeMode.MFE;
}

/**
 * Convenience function to get current theme configuration
 */
export function getThemeConfig(): ThemeConfig {
  return getDeploymentConfig().theme;
}
