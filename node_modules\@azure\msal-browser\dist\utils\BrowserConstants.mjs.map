{"version": 3, "file": "BrowserConstants.mjs", "sources": ["../../src/utils/BrowserConstants.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAMH;;AAEG;AACU,MAAA,gBAAgB,GAAG;AAC5B;;AAEG;AACH,IAAA,6BAA6B,EAAE,yBAAyB;AACxD;;AAEG;AACH,IAAA,mBAAmB,EAAE,eAAe;AACpC;;AAEG;AACH,IAAA,WAAW,EAAE,GAAG;AAChB;;AAEG;AACH,IAAA,YAAY,EAAE,GAAG;AACjB;;AAEG;AACH,IAAA,iBAAiB,EAAE,MAAM;AACzB;;AAEG;AACH,IAAA,wBAAwB,EAAE,EAAE;AAC5B;;AAEG;AACH,IAAA,QAAQ,EAAE,iBAAiB;EAC7B;AAEW,MAAA,eAAe,GAAG;AAC3B,IAAA,UAAU,EAAE,sCAAsC;AAClD,IAAA,sBAAsB,EAAE,kCAAkC;AAC1D,IAAA,cAAc,EAAE,MAAM;EACxB;AAEW,MAAA,qBAAqB,GAAG;AACjC,IAAA,gBAAgB,EAAE,WAAW;AAC7B,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,QAAQ,EAAE,UAAU;EACb;AAIE,MAAA,oBAAoB,GAAG;AAChC,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,aAAa,EAAE,eAAe;EACvB;AAIX;;AAEG;AACU,MAAA,iBAAiB,GAAG;AAC7B,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,IAAI,EAAE,MAAM;EACL;AAIX;;AAEG;AACU,MAAA,kBAAkB,GAAG;AAC9B,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,qBAAqB,EAAE,sBAAsB;AAC7C,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,aAAa,EAAE,gBAAgB;AAC/B,IAAA,UAAU,EAAE,gBAAgB;AAC5B,IAAA,YAAY,EAAE,oBAAoB;AAClC,IAAA,QAAQ,EAAE,SAAS;AACnB,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,sBAAsB,EAAE,oBAAoB;AAC5C,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,cAAc,EAAE,uBAAuB;AACvC,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,gBAAgB,EAAE,0BAA0B;EACrC;AAIE,MAAA,eAAe,GAAG;AAC3B,IAAA,YAAY,EAAE,mBAAmB;AACjC,IAAA,UAAU,EAAE,iBAAiB;AAC7B,IAAA,OAAO,EAAE,cAAc;EAChB;AAIX;;AAEG;AACU,MAAA,iBAAiB,GAAG;AAC7B,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,WAAW,EAAE,iBAAiB;EACvB;AAIX;;;;;AAKG;AACU,MAAA,KAAK,GAAG;AACjB,IAAA,oBAAoB,EAAE,GAAG;AACzB,IAAA,iBAAiB,EAAE,GAAG;AACtB,IAAA,SAAS,EAAE,GAAG;AACd,IAAA,2BAA2B,EAAE,GAAG;AAChC,IAAA,qBAAqB,EAAE,GAAG;AAC1B,IAAA,kBAAkB,EAAE,GAAG;AACvB,IAAA,6BAA6B,EAAE,EAAE;AACjC,IAAA,MAAM,EAAE,GAAG;AACX,IAAA,WAAW,EAAE,GAAG;EACT;AAGX;;AAEG;IACS,gBAKX;AALD,CAAA,UAAY,eAAe,EAAA;AACvB,IAAA,eAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EALW,eAAe,KAAf,eAAe,GAK1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;AACU,MAAA,iBAAiB,GAAG;AAC7B;;AAEG;AACH,IAAA,OAAO,EAAE,SAAS;AAClB;;AAEG;AACH,IAAA,KAAK,EAAE,OAAO;AACd;;AAEG;AACH,IAAA,MAAM,EAAE,QAAQ;AAChB;;AAEG;AACH,IAAA,YAAY,EAAE,cAAc;AAC5B;;AAEG;AACH,IAAA,SAAS,EAAE,WAAW;AACtB;;AAEG;AACH,IAAA,cAAc,EAAE,gBAAgB;AAChC;;AAEG;AACH,IAAA,IAAI,EAAE,MAAM;EACL;AAIE,MAAA,eAAe,GAAmC;AAC3D,IAAA,MAAM,EAAE,mBAAmB;EAC7B;AAEF;;AAEG;AACI,MAAM,cAAc,GAAG,MAAM;AAEpC;AACa,MAAA,UAAU,GAAG;AACtB,IAAA,KAAK,EAAE,mBAAmB;AAC1B,IAAA,OAAO,EAAE,qBAAqB;EACvB;AAGX;AACO,MAAM,OAAO,GAAG,UAAU;AAC1B,MAAM,UAAU,GAAG,EAAE;AACf,MAAA,aAAa,GAAG,CAAG,EAAA,OAAO,QAAQ;AAElC,MAAA,iBAAiB,GAAG;AAC7B;;;;AAIG;AACH,IAAA,OAAO,EAAE,CAAC;AACV;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;;AAIG;AACH,IAAA,0BAA0B,EAAE,CAAC;AAC7B;;;;AAIG;AACH,IAAA,YAAY,EAAE,CAAC;AACf;;;;AAIG;AACH,IAAA,sBAAsB,EAAE,CAAC;AACzB;;;AAGG;AACH,IAAA,IAAI,EAAE,CAAC;EACA;AAIE,MAAA,qBAAqB,GAAwB;AACtD,IAAA,iBAAiB,CAAC,OAAO;AACzB,IAAA,iBAAiB,CAAC,IAAI;AACtB,IAAA,iBAAiB,CAAC,sBAAsB;EAC1C;AAEK,MAAM,mBAAmB,GAAG,yBAAyB;AACrD,MAAM,iBAAiB,GAAG,uBAAuB;AAEjD,MAAM,wBAAwB,GAAG;;;;"}