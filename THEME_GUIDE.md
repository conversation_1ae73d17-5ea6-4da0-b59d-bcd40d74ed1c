# Theme System Guide

This guide explains how to use the runtime theme switching system in the CRM React monorepo.

## 🎯 Overview

The theme system provides automatic theme detection based on deployment context and supports manual theme switching for development and testing. It uses CSS custom properties (CSS variables) for efficient runtime theme switching without page reloads.

## 🏗️ Architecture

### Deployment Context-Based Theming

The system automatically detects the deployment context and applies the appropriate theme:

- **Web Resource Mode**: Uses CRM theme (Dynamics 365 branded styling)
- **Embedded SPA Mode**: Uses CRM theme (Dynamics 365 branded styling)
- **Standalone MFE Mode**: Uses MFE theme (modern micro frontend styling)

### Theme Modes

```typescript
enum ThemeMode {
  CRM = 'crm',      // Dynamics 365 branded theme
  MFE = 'mfe'       // Modern micro frontend theme
}
```

## 🚀 Quick Start

### 1. Wrap Your App with ThemeProvider

```tsx
import React from 'react';
import { ThemeProvider } from '@shared/services/theme';
import '@shared/styles/index.css'; // Import theme styles

function App() {
  return (
    <ThemeProvider enableAutoDetection={true} enablePersistence={true}>
      <YourAppContent />
    </ThemeProvider>
  );
}
```

### 2. Use Theme-Aware Components

```tsx
import React from 'react';
import { useTheme, useThemeStyles } from '@shared/services/theme';
import { Button, ThemeSwitcher } from '@shared/components';

function MyComponent() {
  const { currentTheme, switchTheme } = useTheme();
  const { getThemeClass } = useThemeStyles();

  return (
    <div className={getThemeClass('theme-card')}>
      <h1 className="theme-text-primary">Current Theme: {currentTheme}</h1>
      
      {/* Theme-aware button */}
      <Button variant="primary" onClick={() => console.log('Clicked!')}>
        Click Me
      </Button>
      
      {/* Theme switcher for development */}
      <ThemeSwitcher variant="dropdown" showLabels={true} />
    </div>
  );
}
```

## 🎨 Using Themes

### CSS Custom Properties

All themes use CSS custom properties that can be used in your styles:

```css
.my-component {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  padding: var(--theme-spacing-md);
  font-family: var(--theme-font-family);
}
```

### Available CSS Variables

#### Colors
- `--theme-primary` - Primary brand color
- `--theme-secondary` - Secondary color
- `--theme-bg-primary` - Primary background
- `--theme-bg-secondary` - Secondary background
- `--theme-text-primary` - Primary text color
- `--theme-text-secondary` - Secondary text color
- `--theme-border-primary` - Primary border color
- `--theme-success` - Success color
- `--theme-error` - Error color
- `--theme-warning` - Warning color

#### Typography
- `--theme-font-family` - Primary font family
- `--theme-font-size-xs` to `--theme-font-size-3xl` - Font sizes
- `--theme-font-weight-normal` to `--theme-font-weight-bold` - Font weights
- `--theme-line-height-tight` to `--theme-line-height-relaxed` - Line heights

#### Spacing
- `--theme-spacing-xs` to `--theme-spacing-3xl` - Spacing values

#### Border Radius
- `--theme-radius-none` to `--theme-radius-full` - Border radius values

#### Shadows
- `--theme-shadow-sm` to `--theme-shadow-xl` - Box shadow values

### Theme-Aware Classes

Use predefined theme-aware classes for common styling:

```tsx
<div className="theme-card">
  <h2 className="theme-text-primary">Title</h2>
  <p className="theme-text-secondary">Description</p>
  <button className="theme-button theme-button-primary">Action</button>
</div>
```

## 🔧 Hooks and Utilities

### useTheme Hook

```tsx
import { useTheme } from '@shared/services/theme';

function MyComponent() {
  const {
    currentTheme,     // Current theme mode
    themeConfig,      // Current theme configuration
    isLoading,        // Theme loading state
    error,            // Theme error state
    switchTheme,      // Function to switch themes
    resetTheme,       // Function to reset to default
    applyTheme        // Function to apply specific theme
  } = useTheme();

  const handleSwitchTheme = async () => {
    await switchTheme(ThemeMode.MFE);
  };

  return (
    <div>
      <p>Current theme: {currentTheme}</p>
      {isLoading && <p>Loading theme...</p>}
      {error && <p>Error: {error}</p>}
      <button onClick={handleSwitchTheme}>Switch to MFE Theme</button>
    </div>
  );
}
```

### useThemeStyles Hook

```tsx
import { useThemeStyles } from '@shared/services/theme';

function MyComponent() {
  const {
    currentTheme,
    getThemeClass,    // Get theme-aware class name
    getThemeStyle,    // Get theme-specific styles
    getCSSVariable    // Get CSS variable value
  } = useThemeStyles();

  const cardClass = getThemeClass('card');
  const primaryColor = getCSSVariable('--theme-primary');

  const themeSpecificStyles = getThemeStyle({
    [ThemeMode.CRM]: { backgroundColor: '#f3f2f1' },
    [ThemeMode.MFE]: { backgroundColor: '#f1f5f9' }
  });

  return (
    <div className={cardClass} style={themeSpecificStyles}>
      <p style={{ color: primaryColor }}>Themed content</p>
    </div>
  );
}
```

### useCurrentTheme Hook

```tsx
import { useCurrentTheme, ThemeMode } from '@shared/services/theme';

function MyComponent() {
  const currentTheme = useCurrentTheme();
  
  return (
    <div>
      {currentTheme === ThemeMode.CRM ? (
        <p>Using CRM theme</p>
      ) : (
        <p>Using MFE theme</p>
      )}
    </div>
  );
}
```

### useIsTheme Hook

```tsx
import { useIsTheme, ThemeMode } from '@shared/services/theme';

function MyComponent() {
  const isCRMTheme = useIsTheme(ThemeMode.CRM);
  
  return (
    <div>
      {isCRMTheme && <p>CRM-specific content</p>}
    </div>
  );
}
```

## 🎛️ Theme Switcher Component

The `ThemeSwitcher` component provides UI for switching between themes:

### Basic Usage

```tsx
import { ThemeSwitcher } from '@shared/components';

// Dropdown variant (default)
<ThemeSwitcher />

// Toggle variant
<ThemeSwitcher variant="toggle" />

// Buttons variant
<ThemeSwitcher variant="buttons" />
```

### Advanced Configuration

```tsx
<ThemeSwitcher
  variant="dropdown"
  size="lg"
  showLabels={true}
  showIcons={true}
  hideIfDisabled={true}
  className="my-theme-switcher"
/>
```

## 🔧 Configuration

### Environment Variables

Configure theme behavior using environment variables:

```bash
# .env.webresource
VITE_DEPLOYMENT_MODE=web_resource
VITE_THEME_MODE=crm
VITE_ENABLE_THEME_SWITCHING=false

# .env.standalone
VITE_DEPLOYMENT_MODE=standalone_mfe
VITE_THEME_MODE=mfe
VITE_ENABLE_THEME_SWITCHING=true
```

### ThemeProvider Props

```tsx
<ThemeProvider
  defaultTheme={ThemeMode.CRM}           // Default theme
  enableAutoDetection={true}             // Auto-detect from deployment context
  enablePersistence={true}               // Persist theme choice
  storageKey="my-app-theme"              // Storage key for persistence
>
  <App />
</ThemeProvider>
```

## 🎨 Creating Custom Themes

### 1. Define Theme Configuration

```typescript
import { ThemeConfig, ThemeMode } from '@shared/services/theme';

const customTheme: ThemeConfig = {
  mode: ThemeMode.MFE,
  primaryColor: '#6366f1',
  secondaryColor: '#4f46e5',
  backgroundColor: '#ffffff',
  textColor: '#1e293b',
  borderColor: '#e2e8f0',
  fontFamily: '"Inter", sans-serif',
  customProperties: {
    '--custom-header-bg': '#1e293b',
    '--custom-sidebar-bg': '#f1f5f9',
    '--custom-border-radius': '8px',
  }
};
```

### 2. Create Theme CSS File

```css
/* custom-theme.css */
:root[data-theme="custom"] {
  --theme-primary: #6366f1;
  --theme-secondary: #4f46e5;
  --theme-bg-primary: #ffffff;
  --theme-text-primary: #1e293b;
  --theme-border-primary: #e2e8f0;
  --theme-font-family: "Inter", sans-serif;
  
  /* Custom properties */
  --custom-header-bg: #1e293b;
  --custom-sidebar-bg: #f1f5f9;
  --custom-border-radius: 8px;
}

/* Component overrides */
[data-theme="custom"] .theme-button-primary {
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-secondary));
  border-radius: var(--custom-border-radius);
}
```

## 🧪 Testing Themes

### Manual Theme Switching

Use URL parameters for testing:

```
http://localhost:5173?themeMode=mfe
http://localhost:5173?deploymentMode=standalone_mfe&themeMode=mfe
```

### Programmatic Testing

```typescript
import { ThemeManager } from '@shared/services/theme';

const themeManager = new ThemeManager({
  enableAutoDetection: false,
  defaultTheme: ThemeMode.CRM
});

// Test theme switching
await themeManager.switchTheme(ThemeMode.MFE);
console.log('Current theme:', themeManager.getCurrentTheme());

// Test theme reset
await themeManager.resetTheme();
```

## 🚀 Build and Deployment

### Development

```bash
# Test with specific theme
VITE_THEME_MODE=crm npm run dev:transcript-and-summary
VITE_THEME_MODE=mfe npm run dev:transcript-and-summary

# Enable theme switching in development
VITE_ENABLE_THEME_SWITCHING=true npm run dev
```

### Production Builds

```bash
# Build with CRM theme (for web resource/embedded SPA)
npm run build:all:webresource

# Build with MFE theme (for standalone deployment)
npm run build:all:standalone
```

## 🎯 Best Practices

### 1. Use Theme-Aware Classes

```tsx
// ✅ Good - uses theme-aware classes
<div className="theme-card">
  <button className="theme-button theme-button-primary">Action</button>
</div>

// ❌ Avoid - hardcoded styles
<div style={{ backgroundColor: '#ffffff', border: '1px solid #ccc' }}>
  <button style={{ backgroundColor: '#0078d4', color: 'white' }}>Action</button>
</div>
```

### 2. Use CSS Custom Properties

```css
/* ✅ Good - uses CSS variables */
.my-component {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-primary);
}

/* ❌ Avoid - hardcoded values */
.my-component {
  background-color: #ffffff;
  color: #323130;
  border: 1px solid #8a8886;
}
```

### 3. Conditional Theme Logic

```tsx
// ✅ Good - uses theme hooks
const { currentTheme } = useTheme();
const showAdvancedFeatures = currentTheme === ThemeMode.MFE;

// ❌ Avoid - manual detection
const showAdvancedFeatures = window.location.href.includes('standalone');
```

### 4. Theme Persistence

```tsx
// ✅ Good - let ThemeProvider handle persistence
<ThemeProvider enablePersistence={true}>
  <App />
</ThemeProvider>

// ❌ Avoid - manual localStorage management
localStorage.setItem('theme', 'mfe');
```

## 🔍 Troubleshooting

### Theme Not Loading

1. Check that CSS files are imported:
   ```tsx
   import '@shared/styles/index.css';
   ```

2. Verify ThemeProvider is wrapping your app:
   ```tsx
   <ThemeProvider>
     <App />
   </ThemeProvider>
   ```

### CSS Variables Not Working

1. Ensure you're using the correct variable names
2. Check that the theme CSS is loaded
3. Verify the `data-theme` attribute is set on the root element

### Theme Switching Not Working

1. Check that `enableThemeSwitching` is true in deployment config
2. Verify environment variables are set correctly
3. Check browser console for errors

## 📚 API Reference

See the TypeScript definitions in `shared/services/theme/themeTypes.ts` for complete API documentation.

## 🤝 Contributing

When adding new theme features:

1. Update both CRM and MFE theme CSS files
2. Add corresponding CSS custom properties
3. Update this documentation
4. Add tests for new functionality
5. Ensure accessibility compliance
