/**
 * Basic Theme System Tests
 * 
 * Simple tests for theme functionality
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../services/theme';
import { ThemeMode } from '../config/deploymentContext';

// Simple test component
const TestComponent: React.FC = () => {
  return (
    <div data-testid="test-component">
      <h1>Test Component</h1>
    </div>
  );
};

describe('Basic Theme System', () => {
  test('renders component with CRM theme provider', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.CRM}>
        <TestComponent />
      </ThemeProvider>
    );

    const component = screen.getByTestId('test-component');
    const text = screen.getByText('Test Component');

    expect(component).toBeTruthy();
    expect(text).toBeTruthy();
  });

  test('renders component with MFE theme provider', () => {
    render(
      <ThemeProvider defaultTheme={ThemeMode.MFE}>
        <TestComponent />
      </ThemeProvider>
    );

    const component = screen.getByTestId('test-component');
    const text = screen.getByText('Test Component');

    expect(component).toBeTruthy();
    expect(text).toBeTruthy();
  });

  test('theme provider accepts children', () => {
    render(
      <ThemeProvider>
        <div data-testid="child-component">Child Content</div>
      </ThemeProvider>
    );

    const component = screen.getByTestId('child-component');
    const text = screen.getByText('Child Content');

    expect(component).toBeTruthy();
    expect(text).toBeTruthy();
  });
});
