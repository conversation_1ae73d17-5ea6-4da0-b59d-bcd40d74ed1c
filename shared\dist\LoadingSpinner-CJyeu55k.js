"use strict";const Ce=require("react");var H={exports:{}},W={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var we;function cr(){if(we)return W;we=1;var C=Ce,h=Symbol.for("react.element"),O=Symbol.for("react.fragment"),y=Object.prototype.hasOwnProperty,b=C.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,E={key:!0,ref:!0,__self:!0,__source:!0};function _(R,c,w){var f,m={},x=null,P=null;w!==void 0&&(x=""+w),c.key!==void 0&&(x=""+c.key),c.ref!==void 0&&(P=c.ref);for(f in c)y.call(c,f)&&!E.hasOwnProperty(f)&&(m[f]=c[f]);if(R&&R.defaultProps)for(f in c=R.defaultProps,c)m[f]===void 0&&(m[f]=c[f]);return{$$typeof:h,type:R,key:x,ref:P,props:m,_owner:b.current}}return W.Fragment=O,W.jsx=_,W.jsxs=_,W}var Y={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Te;function fr(){return Te||(Te=1,process.env.NODE_ENV!=="production"&&function(){var C=Ce,h=Symbol.for("react.element"),O=Symbol.for("react.portal"),y=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),R=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),P=Symbol.for("react.offscreen"),Z=Symbol.iterator,Oe="@@iterator";function Se(e){if(e===null||typeof e!="object")return null;var r=Z&&e[Z]||e[Oe];return typeof r=="function"?r:null}var k=C.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];je("error",e,t)}}function je(e,r,t){{var n=k.ReactDebugCurrentFrame,o=n.getStackAddendum();o!==""&&(r+="%s",t=t.concat([o]));var s=t.map(function(i){return String(i)});s.unshift("Warning: "+r),Function.prototype.apply.call(console[e],console,s)}}var Pe=!1,ke=!1,De=!1,Fe=!1,Ae=!1,Q;Q=Symbol.for("react.module.reference");function Ie(e){return!!(typeof e=="string"||typeof e=="function"||e===y||e===E||Ae||e===b||e===w||e===f||Fe||e===P||Pe||ke||De||typeof e=="object"&&e!==null&&(e.$$typeof===x||e.$$typeof===m||e.$$typeof===_||e.$$typeof===R||e.$$typeof===c||e.$$typeof===Q||e.getModuleId!==void 0))}function $e(e,r,t){var n=e.displayName;if(n)return n;var o=r.displayName||r.name||"";return o!==""?t+"("+o+")":t}function ee(e){return e.displayName||"Context"}function T(e){if(e==null)return null;if(typeof e.tag=="number"&&d("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case y:return"Fragment";case O:return"Portal";case E:return"Profiler";case b:return"StrictMode";case w:return"Suspense";case f:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case R:var r=e;return ee(r)+".Consumer";case _:var t=e;return ee(t._context)+".Provider";case c:return $e(e,e.render,"ForwardRef");case m:var n=e.displayName||null;return n!==null?n:T(e.type)||"Memo";case x:{var o=e,s=o._payload,i=o._init;try{return T(i(s))}catch{return null}}}return null}var S=Object.assign,I=0,re,te,ne,ae,ie,oe,se;function ue(){}ue.__reactDisabledLog=!0;function We(){{if(I===0){re=console.log,te=console.info,ne=console.warn,ae=console.error,ie=console.group,oe=console.groupCollapsed,se=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}I++}}function Ye(){{if(I--,I===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:S({},e,{value:re}),info:S({},e,{value:te}),warn:S({},e,{value:ne}),error:S({},e,{value:ae}),group:S({},e,{value:ie}),groupCollapsed:S({},e,{value:oe}),groupEnd:S({},e,{value:se})})}I<0&&d("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var U=k.ReactCurrentDispatcher,B;function L(e,r,t){{if(B===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\n( *(at )?)/);B=n&&n[1]||""}return`
`+B+e}}var q=!1,N;{var Le=typeof WeakMap=="function"?WeakMap:Map;N=new Le}function le(e,r){if(!e||q)return"";{var t=N.get(e);if(t!==void 0)return t}var n;q=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=U.current,U.current=null,We();try{if(r){var i=function(){throw Error()};if(Object.defineProperty(i.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(i,[])}catch(p){n=p}Reflect.construct(e,[],i)}else{try{i.call()}catch(p){n=p}e.call(i.prototype)}}else{try{throw Error()}catch(p){n=p}e()}}catch(p){if(p&&n&&typeof p.stack=="string"){for(var a=p.stack.split(`
`),v=n.stack.split(`
`),u=a.length-1,l=v.length-1;u>=1&&l>=0&&a[u]!==v[l];)l--;for(;u>=1&&l>=0;u--,l--)if(a[u]!==v[l]){if(u!==1||l!==1)do if(u--,l--,l<0||a[u]!==v[l]){var g=`
`+a[u].replace(" at new "," at ");return e.displayName&&g.includes("<anonymous>")&&(g=g.replace("<anonymous>",e.displayName)),typeof e=="function"&&N.set(e,g),g}while(u>=1&&l>=0);break}}}finally{q=!1,U.current=s,Ye(),Error.prepareStackTrace=o}var F=e?e.displayName||e.name:"",j=F?L(F):"";return typeof e=="function"&&N.set(e,j),j}function Ne(e,r,t){return le(e,!1)}function Ve(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function V(e,r,t){if(e==null)return"";if(typeof e=="function")return le(e,Ve(e));if(typeof e=="string")return L(e);switch(e){case w:return L("Suspense");case f:return L("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case c:return Ne(e.render);case m:return V(e.type,r,t);case x:{var n=e,o=n._payload,s=n._init;try{return V(s(o),r,t)}catch{}}}return""}var $=Object.prototype.hasOwnProperty,ce={},fe=k.ReactDebugCurrentFrame;function M(e){if(e){var r=e._owner,t=V(e.type,e._source,r?r.type:null);fe.setExtraStackFrame(t)}else fe.setExtraStackFrame(null)}function Me(e,r,t,n,o){{var s=Function.call.bind($);for(var i in e)if(s(e,i)){var a=void 0;try{if(typeof e[i]!="function"){var v=Error((n||"React class")+": "+t+" type `"+i+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[i]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw v.name="Invariant Violation",v}a=e[i](r,i,n,t,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(u){a=u}a&&!(a instanceof Error)&&(M(o),d("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",n||"React class",t,i,typeof a),M(null)),a instanceof Error&&!(a.message in ce)&&(ce[a.message]=!0,M(o),d("Failed %s type: %s",t,a.message),M(null))}}}var Ue=Array.isArray;function z(e){return Ue(e)}function Be(e){{var r=typeof Symbol=="function"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||"Object";return t}}function qe(e){try{return de(e),!1}catch{return!0}}function de(e){return""+e}function ve(e){if(qe(e))return d("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Be(e)),de(e)}var pe=k.ReactCurrentOwner,ze={key:!0,ref:!0,__self:!0,__source:!0},ge,ye;function Je(e){if($.call(e,"ref")){var r=Object.getOwnPropertyDescriptor(e,"ref").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Ke(e){if($.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Ge(e,r){typeof e.ref=="string"&&pe.current}function Xe(e,r){{var t=function(){ge||(ge=!0,d("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}}function He(e,r){{var t=function(){ye||(ye=!0,d("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"ref",{get:t,configurable:!0})}}var Ze=function(e,r,t,n,o,s,i){var a={$$typeof:h,type:e,key:r,ref:t,props:i,_owner:s};return a._store={},Object.defineProperty(a._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,"_self",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,"_source",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function Qe(e,r,t,n,o){{var s,i={},a=null,v=null;t!==void 0&&(ve(t),a=""+t),Ke(r)&&(ve(r.key),a=""+r.key),Je(r)&&(v=r.ref,Ge(r,o));for(s in r)$.call(r,s)&&!ze.hasOwnProperty(s)&&(i[s]=r[s]);if(e&&e.defaultProps){var u=e.defaultProps;for(s in u)i[s]===void 0&&(i[s]=u[s])}if(a||v){var l=typeof e=="function"?e.displayName||e.name||"Unknown":e;a&&Xe(i,l),v&&He(i,l)}return Ze(e,a,v,o,n,pe.current,i)}}var J=k.ReactCurrentOwner,me=k.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,t=V(e.type,e._source,r?r.type:null);me.setExtraStackFrame(t)}else me.setExtraStackFrame(null)}var K;K=!1;function G(e){return typeof e=="object"&&e!==null&&e.$$typeof===h}function he(){{if(J.current){var e=T(J.current.type);if(e)return`

Check the render method of \``+e+"`."}return""}}function er(e){return""}var be={};function rr(e){{var r=he();if(!r){var t=typeof e=="string"?e:e.displayName||e.name;t&&(r=`

Check the top-level render call using <`+t+">.")}return r}}function Ee(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=rr(r);if(be[t])return;be[t]=!0;var n="";e&&e._owner&&e._owner!==J.current&&(n=" It was passed a child from "+T(e._owner.type)+"."),D(e),d('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),D(null)}}function _e(e,r){{if(typeof e!="object")return;if(z(e))for(var t=0;t<e.length;t++){var n=e[t];G(n)&&Ee(n,r)}else if(G(e))e._store&&(e._store.validated=!0);else if(e){var o=Se(e);if(typeof o=="function"&&o!==e.entries)for(var s=o.call(e),i;!(i=s.next()).done;)G(i.value)&&Ee(i.value,r)}}}function tr(e){{var r=e.type;if(r==null||typeof r=="string")return;var t;if(typeof r=="function")t=r.propTypes;else if(typeof r=="object"&&(r.$$typeof===c||r.$$typeof===m))t=r.propTypes;else return;if(t){var n=T(r);Me(t,e.props,"prop",n,e)}else if(r.PropTypes!==void 0&&!K){K=!0;var o=T(r);d("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",o||"Unknown")}typeof r.getDefaultProps=="function"&&!r.getDefaultProps.isReactClassApproved&&d("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function nr(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!=="children"&&n!=="key"){D(e),d("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",n),D(null);break}}e.ref!==null&&(D(e),d("Invalid attribute `ref` supplied to `React.Fragment`."),D(null))}}var Re={};function xe(e,r,t,n,o,s){{var i=Ie(e);if(!i){var a="";(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(a+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var v=er();v?a+=v:a+=he();var u;e===null?u="null":z(e)?u="array":e!==void 0&&e.$$typeof===h?(u="<"+(T(e.type)||"Unknown")+" />",a=" Did you accidentally export a JSX literal instead of a component?"):u=typeof e,d("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",u,a)}var l=Qe(e,r,t,o,s);if(l==null)return l;if(i){var g=r.children;if(g!==void 0)if(n)if(z(g)){for(var F=0;F<g.length;F++)_e(g[F],e);Object.freeze&&Object.freeze(g)}else d("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else _e(g,e)}if($.call(r,"key")){var j=T(e),p=Object.keys(r).filter(function(lr){return lr!=="key"}),X=p.length>0?"{key: someKey, "+p.join(": ..., ")+": ...}":"{key: someKey}";if(!Re[j+X]){var ur=p.length>0?"{"+p.join(": ..., ")+": ...}":"{}";d(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,X,j,ur,j),Re[j+X]=!0}}return e===y?nr(l):tr(l),l}}function ar(e,r,t){return xe(e,r,t,!0)}function ir(e,r,t){return xe(e,r,t,!1)}var or=ir,sr=ar;Y.Fragment=y,Y.jsx=or,Y.jsxs=sr}()),Y}process.env.NODE_ENV==="production"?H.exports=cr():H.exports=fr();var A=H.exports;const dr=({children:C,onClick:h,variant:O="primary",size:y="medium",disabled:b=!1,type:E="button",className:_="",style:R,...c})=>{const w="inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors",f={primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"},m={small:"px-3 py-2 text-sm",medium:"px-4 py-2 text-base",large:"px-6 py-3 text-lg"},x=b?"opacity-50 cursor-not-allowed":"cursor-pointer",P=[w,f[O],m[y],x,_].join(" ");return A.jsx("button",{type:E,className:P,style:R,onClick:h,disabled:b,...c,children:C})},vr=({size:C="medium",color:h="primary",className:O="",text:y})=>{const b={small:"w-4 h-4",medium:"w-8 h-8",large:"w-12 h-12"},E={primary:"text-blue-600",secondary:"text-gray-600",white:"text-white"},_=["animate-spin",b[C],E[h],O].join(" ");return A.jsxs("div",{className:"flex flex-col items-center justify-center",children:[A.jsxs("svg",{className:_,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[A.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),A.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),y&&A.jsx("p",{className:`mt-2 text-sm ${E[h]}`,children:y})]})};exports.Button=dr;exports.LoadingSpinner=vr;
