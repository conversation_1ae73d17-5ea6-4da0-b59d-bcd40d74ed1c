/*! @azure/msal-browser v3.30.0 2025-08-05 */
"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).msal={})}(this,(function(e){
/*! @azure/msal-common v14.16.1 2025-08-05 */
const t={LIBRARY_NAME:"MSAL.JS",SKU:"msal.js.common",CACHE_PREFIX:"msal",DEFAULT_AUTHORITY:"https://login.microsoftonline.com/common/",DEFAULT_AUTHORITY_HOST:"login.microsoftonline.com",DEFAULT_COMMON_TENANT:"common",ADFS:"adfs",DSTS:"dstsv2",AAD_INSTANCE_DISCOVERY_ENDPT:"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",CIAM_AUTH_URL:".ciamlogin.com",AAD_TENANT_DOMAIN_SUFFIX:".onmicrosoft.com",RESOURCE_DELIM:"|",NO_ACCOUNT:"NO_ACCOUNT",CLAIMS:"claims",CONSUMER_UTID:"9188040d-6c67-4c5b-b112-36a304b66dad",OPENID_SCOPE:"openid",PROFILE_SCOPE:"profile",OFFLINE_ACCESS_SCOPE:"offline_access",EMAIL_SCOPE:"email",CODE_RESPONSE_TYPE:"code",CODE_GRANT_TYPE:"authorization_code",RT_GRANT_TYPE:"refresh_token",FRAGMENT_RESPONSE_MODE:"fragment",S256_CODE_CHALLENGE_METHOD:"S256",URL_FORM_CONTENT_TYPE:"application/x-www-form-urlencoded;charset=utf-8",AUTHORIZATION_PENDING:"authorization_pending",NOT_DEFINED:"not_defined",EMPTY_STRING:"",NOT_APPLICABLE:"N/A",NOT_AVAILABLE:"Not Available",FORWARD_SLASH:"/",IMDS_ENDPOINT:"http://***************/metadata/instance/compute/location",IMDS_VERSION:"2020-06-01",IMDS_TIMEOUT:2e3,AZURE_REGION_AUTO_DISCOVER_FLAG:"TryAutoDetect",REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX:"login.microsoft.com",KNOWN_PUBLIC_CLOUDS:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"],TOKEN_RESPONSE_TYPE:"token",ID_TOKEN_RESPONSE_TYPE:"id_token",SHR_NONCE_VALIDITY:240,INVALID_INSTANCE:"invalid_instance"},r=400,n=499,o=500,i=599,a=[t.OPENID_SCOPE,t.PROFILE_SCOPE,t.OFFLINE_ACCESS_SCOPE],s=[...a,t.EMAIL_SCOPE],c="Content-Type",l="Content-Length",d="Retry-After",h="X-AnchorMailbox",u="WWW-Authenticate",g="Authentication-Info",p="x-ms-request-id",m="x-ms-httpver",f="idtoken",C="client.info",y="adal.idtoken",v="error",I="error.description",T="active-account",w="active-account-filters",A="common",k="organizations",S="consumers",b="access_token",E="xms_cc",R={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none",CREATE:"create",NO_SESSION:"no_session"},_={PLAIN:"plain",S256:"S256"},P={QUERY:"query",FRAGMENT:"fragment"},M={...P,FORM_POST:"form_post"},N="authorization_code",O="refresh_token",q="MSSTS",U="ADFS",L="Generic",H="-",B=".",x={ID_TOKEN:"IdToken",ACCESS_TOKEN:"AccessToken",ACCESS_TOKEN_WITH_AUTH_SCHEME:"AccessToken_With_AuthScheme",REFRESH_TOKEN:"RefreshToken"},D="appmetadata",F="1",K="authority-metadata",z=86400,$="config",G="cache",Q="network",W="hardcoded_values",j={SCHEMA_VERSION:5,MAX_CUR_HEADER_BYTES:80,MAX_LAST_HEADER_BYTES:330,MAX_CACHED_ERRORS:50,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"},V={BEARER:"Bearer",POP:"pop",SSH:"ssh-cert"},Y=60,J=3600,X="throttling",Z="retry-after, h429",ee="invalid_grant",te="client_mismatch",re="username",ne="password",oe=200,ie=400,ae="1",se="3",ce="4",le="2",de="4",he="5",ue="0",ge="1",pe="2",me="3",fe="4",Ce={Jwt:"JWT",Jwk:"JWK",Pop:"pop"},ye="unexpected_error",ve="post_request_failed";var Ie=Object.freeze({__proto__:null,postRequestFailed:ve,unexpectedError:ye});
/*! @azure/msal-common v14.16.1 2025-08-05 */const Te={[ye]:"Unexpected error in authentication.",[ve]:"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."},we={unexpectedError:{code:ye,desc:Te[ye]},postRequestFailed:{code:ve,desc:Te[ve]}};class Ae extends Error{constructor(e,r,n){super(r?`${e}: ${r}`:e),Object.setPrototypeOf(this,Ae.prototype),this.errorCode=e||t.EMPTY_STRING,this.errorMessage=r||t.EMPTY_STRING,this.subError=n||t.EMPTY_STRING,this.name="AuthError"}setCorrelationId(e){this.correlationId=e}}function ke(e,t){return new Ae(e,t?`${Te[e]} ${t}`:Te[e])}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Se="client_info_decoding_error",be="client_info_empty_error",Ee="token_parsing_error",Re="null_or_empty_token",_e="endpoints_resolution_error",Pe="network_error",Me="openid_config_error",Ne="hash_not_deserialized",Oe="invalid_state",qe="state_mismatch",Ue="state_not_found",Le="nonce_mismatch",He="auth_time_not_found",Be="max_age_transpired",xe="multiple_matching_tokens",De="multiple_matching_accounts",Fe="multiple_matching_appMetadata",Ke="request_cannot_be_made",ze="cannot_remove_empty_scope",$e="cannot_append_scopeset",Ge="empty_input_scopeset",Qe="device_code_polling_cancelled",We="device_code_expired",je="device_code_unknown_error",Ve="no_account_in_silent_request",Ye="invalid_cache_record",Je="invalid_cache_environment",Xe="no_account_found",Ze="no_crypto_object",et="unexpected_credential_type",tt="invalid_assertion",rt="invalid_client_credential",nt="token_refresh_required",ot="user_timeout_reached",it="token_claims_cnf_required_for_signedjwt",at="authorization_code_missing_from_server_response",st="binding_key_not_removed",ct="end_session_endpoint_not_supported",lt="key_id_missing",dt="no_network_connectivity",ht="user_canceled",ut="missing_tenant_id_error",gt="method_not_implemented",pt="nested_app_auth_bridge_disabled";var mt=Object.freeze({__proto__:null,authTimeNotFound:He,authorizationCodeMissingFromServerResponse:at,bindingKeyNotRemoved:st,cannotAppendScopeSet:$e,cannotRemoveEmptyScope:ze,clientInfoDecodingError:Se,clientInfoEmptyError:be,deviceCodeExpired:We,deviceCodePollingCancelled:Qe,deviceCodeUnknownError:je,emptyInputScopeSet:Ge,endSessionEndpointNotSupported:ct,endpointResolutionError:_e,hashNotDeserialized:Ne,invalidAssertion:tt,invalidCacheEnvironment:Je,invalidCacheRecord:Ye,invalidClientCredential:rt,invalidState:Oe,keyIdMissing:lt,maxAgeTranspired:Be,methodNotImplemented:gt,missingTenantIdError:ut,multipleMatchingAccounts:De,multipleMatchingAppMetadata:Fe,multipleMatchingTokens:xe,nestedAppAuthBridgeDisabled:pt,networkError:Pe,noAccountFound:Xe,noAccountInSilentRequest:Ve,noCryptoObject:Ze,noNetworkConnectivity:dt,nonceMismatch:Le,nullOrEmptyToken:Re,openIdConfigError:Me,requestCannotBeMade:Ke,stateMismatch:qe,stateNotFound:Ue,tokenClaimsCnfRequiredForSignedJwt:it,tokenParsingError:Ee,tokenRefreshRequired:nt,unexpectedCredentialType:et,userCanceled:ht,userTimeoutReached:ot});
/*! @azure/msal-common v14.16.1 2025-08-05 */const ft={[Se]:"The client info could not be parsed/decoded correctly",[be]:"The client info was empty",[Ee]:"Token cannot be parsed",[Re]:"The token is null or empty",[_e]:"Endpoints cannot be resolved",[Pe]:"Network request failed",[Me]:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.",[Ne]:"The hash parameters could not be deserialized",[Oe]:"State was not the expected format",[qe]:"State mismatch error",[Ue]:"State not found",[Le]:"Nonce mismatch error",[He]:"Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information.",[Be]:"Max Age is set to 0, or too much time has elapsed since the last end-user authentication.",[xe]:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account.",[De]:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account",[Fe]:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata",[Ke]:"Token request cannot be made without authorization code or refresh token.",[ze]:"Cannot remove null or empty scope from ScopeSet",[$e]:"Cannot append ScopeSet",[Ge]:"Empty input ScopeSet cannot be processed",[Qe]:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.",[We]:"Device code is expired.",[je]:"Device code stopped polling for unknown reasons.",[Ve]:"Please pass an account object, silent flow is not supported without account information",[Ye]:"Cache record object was null or undefined.",[Je]:"Invalid environment when attempting to create cache entry",[Xe]:"No account found in cache for given key.",[Ze]:"No crypto object detected.",[et]:"Unexpected credential type.",[tt]:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515",[rt]:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential",[nt]:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.",[ot]:"User defined timeout for device code polling reached",[it]:"Cannot generate a POP jwt if the token_claims are not populated",[at]:"Server response does not contain an authorization code to proceed",[st]:"Could not remove the credential's binding key from storage.",[ct]:"The provided authority does not support logout",[lt]:"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.",[dt]:"No network connectivity. Check your internet connection.",[ht]:"User cancelled the flow.",[ut]:"A tenant id - not common, organizations, or consumers - must be specified when using the client_credentials flow.",[gt]:"This method has not been implemented",[pt]:"The nested app auth bridge is disabled"},Ct={clientInfoDecodingError:{code:Se,desc:ft[Se]},clientInfoEmptyError:{code:be,desc:ft[be]},tokenParsingError:{code:Ee,desc:ft[Ee]},nullOrEmptyToken:{code:Re,desc:ft[Re]},endpointResolutionError:{code:_e,desc:ft[_e]},networkError:{code:Pe,desc:ft[Pe]},unableToGetOpenidConfigError:{code:Me,desc:ft[Me]},hashNotDeserialized:{code:Ne,desc:ft[Ne]},invalidStateError:{code:Oe,desc:ft[Oe]},stateMismatchError:{code:qe,desc:ft[qe]},stateNotFoundError:{code:Ue,desc:ft[Ue]},nonceMismatchError:{code:Le,desc:ft[Le]},authTimeNotFoundError:{code:He,desc:ft[He]},maxAgeTranspired:{code:Be,desc:ft[Be]},multipleMatchingTokens:{code:xe,desc:ft[xe]},multipleMatchingAccounts:{code:De,desc:ft[De]},multipleMatchingAppMetadata:{code:Fe,desc:ft[Fe]},tokenRequestCannotBeMade:{code:Ke,desc:ft[Ke]},removeEmptyScopeError:{code:ze,desc:ft[ze]},appendScopeSetError:{code:$e,desc:ft[$e]},emptyInputScopeSetError:{code:Ge,desc:ft[Ge]},DeviceCodePollingCancelled:{code:Qe,desc:ft[Qe]},DeviceCodeExpired:{code:We,desc:ft[We]},DeviceCodeUnknownError:{code:je,desc:ft[je]},NoAccountInSilentRequest:{code:Ve,desc:ft[Ve]},invalidCacheRecord:{code:Ye,desc:ft[Ye]},invalidCacheEnvironment:{code:Je,desc:ft[Je]},noAccountFound:{code:Xe,desc:ft[Xe]},noCryptoObj:{code:Ze,desc:ft[Ze]},unexpectedCredentialType:{code:et,desc:ft[et]},invalidAssertion:{code:tt,desc:ft[tt]},invalidClientCredential:{code:rt,desc:ft[rt]},tokenRefreshRequired:{code:nt,desc:ft[nt]},userTimeoutReached:{code:ot,desc:ft[ot]},tokenClaimsRequired:{code:it,desc:ft[it]},noAuthorizationCodeFromServer:{code:at,desc:ft[at]},bindingKeyNotRemovedError:{code:st,desc:ft[st]},logoutNotSupported:{code:ct,desc:ft[ct]},keyIdMissing:{code:lt,desc:ft[lt]},noNetworkConnectivity:{code:dt,desc:ft[dt]},userCanceledError:{code:ht,desc:ft[ht]},missingTenantIdError:{code:ut,desc:ft[ut]},nestedAppAuthBridgeDisabled:{code:pt,desc:ft[pt]}};class yt extends Ae{constructor(e,t){super(e,t?`${ft[e]}: ${t}`:ft[e]),this.name="ClientAuthError",Object.setPrototypeOf(this,yt.prototype)}}function vt(e,t){return new yt(e,t)}
/*! @azure/msal-common v14.16.1 2025-08-05 */const It={createNewGuid:()=>{throw vt(gt)},base64Decode:()=>{throw vt(gt)},base64Encode:()=>{throw vt(gt)},base64UrlEncode:()=>{throw vt(gt)},encodeKid:()=>{throw vt(gt)},async getPublicKeyThumbprint(){throw vt(gt)},async removeTokenBindingKey(){throw vt(gt)},async clearKeystore(){throw vt(gt)},async signJwt(){throw vt(gt)},async hashString(){throw vt(gt)}};
/*! @azure/msal-common v14.16.1 2025-08-05 */var Tt;e.LogLevel=void 0,(Tt=e.LogLevel||(e.LogLevel={}))[Tt.Error=0]="Error",Tt[Tt.Warning=1]="Warning",Tt[Tt.Info=2]="Info",Tt[Tt.Verbose=3]="Verbose",Tt[Tt.Trace=4]="Trace";class wt{constructor(r,n,o){this.level=e.LogLevel.Info;const i=r||wt.createDefaultLoggerOptions();this.localCallback=i.loggerCallback||(()=>{}),this.piiLoggingEnabled=i.piiLoggingEnabled||!1,this.level="number"==typeof i.logLevel?i.logLevel:e.LogLevel.Info,this.correlationId=i.correlationId||t.EMPTY_STRING,this.packageName=n||t.EMPTY_STRING,this.packageVersion=o||t.EMPTY_STRING}static createDefaultLoggerOptions(){return{loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info}}clone(e,t,r){return new wt({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level,correlationId:r||this.correlationId},e,t)}logMessage(t,r){if(r.logLevel>this.level||!this.piiLoggingEnabled&&r.containsPii)return;const n=`${`[${(new Date).toUTCString()}] : [${r.correlationId||this.correlationId||""}]`} : ${this.packageName}@${this.packageVersion} : ${e.LogLevel[r.logLevel]} - ${t}`;this.executeCallback(r.logLevel,n,r.containsPii||!1)}executeCallback(e,t,r){this.localCallback&&this.localCallback(e,t,r)}error(r,n){this.logMessage(r,{logLevel:e.LogLevel.Error,containsPii:!1,correlationId:n||t.EMPTY_STRING})}errorPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Error,containsPii:!0,correlationId:n||t.EMPTY_STRING})}warning(r,n){this.logMessage(r,{logLevel:e.LogLevel.Warning,containsPii:!1,correlationId:n||t.EMPTY_STRING})}warningPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Warning,containsPii:!0,correlationId:n||t.EMPTY_STRING})}info(r,n){this.logMessage(r,{logLevel:e.LogLevel.Info,containsPii:!1,correlationId:n||t.EMPTY_STRING})}infoPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Info,containsPii:!0,correlationId:n||t.EMPTY_STRING})}verbose(r,n){this.logMessage(r,{logLevel:e.LogLevel.Verbose,containsPii:!1,correlationId:n||t.EMPTY_STRING})}verbosePii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Verbose,containsPii:!0,correlationId:n||t.EMPTY_STRING})}trace(r,n){this.logMessage(r,{logLevel:e.LogLevel.Trace,containsPii:!1,correlationId:n||t.EMPTY_STRING})}tracePii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Trace,containsPii:!0,correlationId:n||t.EMPTY_STRING})}isPiiLoggingEnabled(){return this.piiLoggingEnabled||!1}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const At="@azure/msal-common",kt="14.16.1",St={None:"none",AzurePublic:"https://login.microsoftonline.com",AzurePpe:"https://login.windows-ppe.net",AzureChina:"https://login.chinacloudapi.cn",AzureGermany:"https://login.microsoftonline.de",AzureUsGovernment:"https://login.microsoftonline.us"};
/*! @azure/msal-common v14.16.1 2025-08-05 */
function bt(e,t){const r=function(e){if(!e)throw vt(Re);const t=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(e);if(!t||t.length<4)throw vt(Ee);return t[2]}(e);try{const e=t(r);return JSON.parse(e)}catch(e){throw vt(Ee)}}function Et(e,t){if(0===t||Date.now()-3e5>e+t)throw vt(Be)}
/*! @azure/msal-common v14.16.1 2025-08-05 */function Rt(){return Math.round((new Date).getTime()/1e3)}function _t(e,t){const r=Number(e)||0;return Rt()+t>r}function Pt(e){return Number(e)>Rt()}
/*! @azure/msal-common v14.16.1 2025-08-05 */function Mt(e){return[xt(e),Dt(e),Ft(e),Kt(e),zt(e)].join(H).toLowerCase()}function Nt(e,t,r,n,o){return{credentialType:x.ID_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r,realm:o}}function Ot(e,t,r,n,o,i,a,s,c,l,d,h,u,g,p){const m={homeAccountId:e,credentialType:x.ACCESS_TOKEN,secret:r,cachedAt:Rt().toString(),expiresOn:a.toString(),extendedExpiresOn:s.toString(),environment:t,clientId:n,realm:o,target:i,tokenType:d||V.BEARER};if(h&&(m.userAssertionHash=h),l&&(m.refreshOn=l.toString()),g&&(m.requestedClaims=g,m.requestedClaimsHash=p),m.tokenType?.toLowerCase()!==V.BEARER.toLowerCase())switch(m.credentialType=x.ACCESS_TOKEN_WITH_AUTH_SCHEME,m.tokenType){case V.POP:const e=bt(r,c);if(!e?.cnf?.kid)throw vt(it);m.keyId=e.cnf.kid;break;case V.SSH:m.keyId=u}return m}function qt(e,t,r,n,o,i,a){const s={credentialType:x.REFRESH_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r};return i&&(s.userAssertionHash=i),o&&(s.familyId=o),a&&(s.expiresOn=a.toString()),s}function Ut(e){return e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")}function Lt(e){return!!e&&(Ut(e)&&e.hasOwnProperty("realm")&&e.hasOwnProperty("target")&&(e.credentialType===x.ACCESS_TOKEN||e.credentialType===x.ACCESS_TOKEN_WITH_AUTH_SCHEME))}function Ht(e){return!!e&&(Ut(e)&&e.hasOwnProperty("realm")&&e.credentialType===x.ID_TOKEN)}function Bt(e){return!!e&&(Ut(e)&&e.credentialType===x.REFRESH_TOKEN)}function xt(e){return[e.homeAccountId,e.environment].join(H).toLowerCase()}function Dt(e){const t=e.credentialType===x.REFRESH_TOKEN&&e.familyId||e.clientId;return[e.credentialType,t,e.realm||""].join(H).toLowerCase()}function Ft(e){return(e.target||"").toLowerCase()}function Kt(e){return(e.requestedClaimsHash||"").toLowerCase()}function zt(e){return e.tokenType&&e.tokenType.toLowerCase()!==V.BEARER.toLowerCase()?e.tokenType.toLowerCase():""}function $t(){return Rt()+z}function Gt(e,t,r){e.authorization_endpoint=t.authorization_endpoint,e.token_endpoint=t.token_endpoint,e.end_session_endpoint=t.end_session_endpoint,e.issuer=t.issuer,e.endpointsFromNetwork=r,e.jwks_uri=t.jwks_uri}function Qt(e,t,r){e.aliases=t.aliases,e.preferred_cache=t.preferred_cache,e.preferred_network=t.preferred_network,e.aliasesFromNetwork=r}function Wt(e){return e.expiresAt<=Rt()}
/*! @azure/msal-common v14.16.1 2025-08-05 */const jt="redirect_uri_empty",Vt="claims_request_parsing_error",Yt="authority_uri_insecure",Jt="url_parse_error",Xt="empty_url_error",Zt="empty_input_scopes_error",er="invalid_prompt_value",tr="invalid_claims",rr="token_request_empty",nr="logout_request_empty",or="invalid_code_challenge_method",ir="pkce_params_missing",ar="invalid_cloud_discovery_metadata",sr="invalid_authority_metadata",cr="untrusted_authority",lr="missing_ssh_jwk",dr="missing_ssh_kid",hr="missing_nonce_authentication_header",ur="invalid_authentication_header",gr="cannot_set_OIDCOptions",pr="cannot_allow_native_broker",mr="authority_mismatch";var fr=Object.freeze({__proto__:null,authorityMismatch:mr,authorityUriInsecure:Yt,cannotAllowNativeBroker:pr,cannotSetOIDCOptions:gr,claimsRequestParsingError:Vt,emptyInputScopesError:Zt,invalidAuthenticationHeader:ur,invalidAuthorityMetadata:sr,invalidClaims:tr,invalidCloudDiscoveryMetadata:ar,invalidCodeChallengeMethod:or,invalidPromptValue:er,logoutRequestEmpty:nr,missingNonceAuthenticationHeader:hr,missingSshJwk:lr,missingSshKid:dr,pkceParamsMissing:ir,redirectUriEmpty:jt,tokenRequestEmpty:rr,untrustedAuthority:cr,urlEmptyError:Xt,urlParseError:Jt});
/*! @azure/msal-common v14.16.1 2025-08-05 */const Cr={[jt]:"A redirect URI is required for all calls, and none has been set.",[Vt]:"Could not parse the given claims request object.",[Yt]:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options",[Jt]:"URL could not be parsed into appropriate segments.",[Xt]:"URL was empty or null.",[Zt]:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.",[er]:"Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest",[tr]:"Given claims parameter must be a stringified JSON object.",[rr]:"Token request was empty and not found in cache.",[nr]:"The logout request was null or undefined.",[or]:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".',[ir]:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request",[ar]:"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields",[sr]:"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.",[cr]:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.",[lr]:"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.",[dr]:"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.",[hr]:"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.",[ur]:"Invalid authentication header provided",[gr]:"Cannot set OIDCOptions parameter. Please change the protocol mode to OIDC or use a non-Microsoft authority.",[pr]:"Cannot set allowNativeBroker parameter to true when not in AAD protocol mode.",[mr]:"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."},yr={redirectUriNotSet:{code:jt,desc:Cr[jt]},claimsRequestParsingError:{code:Vt,desc:Cr[Vt]},authorityUriInsecure:{code:Yt,desc:Cr[Yt]},urlParseError:{code:Jt,desc:Cr[Jt]},urlEmptyError:{code:Xt,desc:Cr[Xt]},emptyScopesError:{code:Zt,desc:Cr[Zt]},invalidPrompt:{code:er,desc:Cr[er]},invalidClaimsRequest:{code:tr,desc:Cr[tr]},tokenRequestEmptyError:{code:rr,desc:Cr[rr]},logoutRequestEmptyError:{code:nr,desc:Cr[nr]},invalidCodeChallengeMethod:{code:or,desc:Cr[or]},invalidCodeChallengeParams:{code:ir,desc:Cr[ir]},invalidCloudDiscoveryMetadata:{code:ar,desc:Cr[ar]},invalidAuthorityMetadata:{code:sr,desc:Cr[sr]},untrustedAuthority:{code:cr,desc:Cr[cr]},missingSshJwk:{code:lr,desc:Cr[lr]},missingSshKid:{code:dr,desc:Cr[dr]},missingNonceAuthenticationHeader:{code:hr,desc:Cr[hr]},invalidAuthenticationHeader:{code:ur,desc:Cr[ur]},cannotSetOIDCOptions:{code:gr,desc:Cr[gr]},cannotAllowNativeBroker:{code:pr,desc:Cr[pr]},authorityMismatch:{code:mr,desc:Cr[mr]}};class vr extends Ae{constructor(e){super(e,Cr[e]),this.name="ClientConfigurationError",Object.setPrototypeOf(this,vr.prototype)}}function Ir(e){return new vr(e)}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Tr{static isEmptyObj(e){if(e)try{const t=JSON.parse(e);return 0===Object.keys(t).length}catch(e){}return!0}static startsWith(e,t){return 0===e.indexOf(t)}static endsWith(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length}static queryStringToObject(e){const t={},r=e.split("&"),n=e=>decodeURIComponent(e.replace(/\+/g," "));return r.forEach((e=>{if(e.trim()){const[r,o]=e.split(/=(.+)/g,2);r&&o&&(t[n(r)]=n(o))}})),t}static trimArrayEntries(e){return e.map((e=>e.trim()))}static removeEmptyStringsFromArray(e){return e.filter((e=>!!e))}static jsonParseHelper(e){try{return JSON.parse(e)}catch(e){return null}}static matchPattern(e,t){return new RegExp(e.replace(/\\/g,"\\\\").replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class wr{constructor(e){const t=e?Tr.trimArrayEntries([...e]):[],r=t?Tr.removeEmptyStringsFromArray(t):[];this.validateInputScopes(r),this.scopes=new Set,r.forEach((e=>this.scopes.add(e)))}static fromString(e){const r=(e||t.EMPTY_STRING).split(" ");return new wr(r)}static createSearchScopes(e){const r=new wr(e);return r.containsOnlyOIDCScopes()?r.removeScope(t.OFFLINE_ACCESS_SCOPE):r.removeOIDCScopes(),r}validateInputScopes(e){if(!e||e.length<1)throw Ir(Zt)}containsScope(e){const t=this.printScopesLowerCase().split(" "),r=new wr(t);return!!e&&r.scopes.has(e.toLowerCase())}containsScopeSet(e){return!(!e||e.scopes.size<=0)&&(this.scopes.size>=e.scopes.size&&e.asArray().every((e=>this.containsScope(e))))}containsOnlyOIDCScopes(){let e=0;return s.forEach((t=>{this.containsScope(t)&&(e+=1)})),this.scopes.size===e}appendScope(e){e&&this.scopes.add(e.trim())}appendScopes(e){try{e.forEach((e=>this.appendScope(e)))}catch(e){throw vt($e)}}removeScope(e){if(!e)throw vt(ze);this.scopes.delete(e.trim())}removeOIDCScopes(){s.forEach((e=>{this.scopes.delete(e)}))}unionScopeSets(e){if(!e)throw vt(Ge);const t=new Set;return e.scopes.forEach((e=>t.add(e.toLowerCase()))),this.scopes.forEach((e=>t.add(e.toLowerCase()))),t}intersectingScopeSets(e){if(!e)throw vt(Ge);e.containsOnlyOIDCScopes()||e.removeOIDCScopes();const t=this.unionScopeSets(e),r=e.getScopeCount(),n=this.getScopeCount();return t.size<n+r}getScopeCount(){return this.scopes.size}asArray(){const e=[];return this.scopes.forEach((t=>e.push(t))),e}printScopes(){if(this.scopes){return this.asArray().join(" ")}return t.EMPTY_STRING}printScopesLowerCase(){return this.printScopes().toLowerCase()}}
/*! @azure/msal-common v14.16.1 2025-08-05 */function Ar(e,t){if(!e)throw vt(be);try{const r=t(e);return JSON.parse(r)}catch(e){throw vt(Se)}}function kr(e){if(!e)throw vt(Se);const r=e.split(B,2);return{uid:r[0],utid:r.length<2?t.EMPTY_STRING:r[1]}}
/*! @azure/msal-common v14.16.1 2025-08-05 */function Sr(e,t){return!!e&&!!t&&e===t.split(".")[1]}function br(e,t,r,n){if(n){const{oid:t,sub:r,tid:o,name:i,tfp:a,acr:s}=n,c=o||a||s||"";return{tenantId:c,localAccountId:t||r||"",name:i,isHomeTenant:Sr(c,e)}}return{tenantId:r,localAccountId:t,isHomeTenant:Sr(r,e)}}function Er(e,t,r,n){let o=e;if(t){const{isHomeTenant:r,...n}=t;o={...e,...n}}if(r){const{isHomeTenant:t,...i}=br(e.homeAccountId,e.localAccountId,e.tenantId,r);return o={...o,...i,idTokenClaims:r,idToken:n},o}return o}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Rr=0,_r=1,Pr=2,Mr=3;
/*! @azure/msal-common v14.16.1 2025-08-05 */function Nr(e){if(e){return e.tid||e.tfp||e.acr||null}return null}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Or={AAD:"AAD",OIDC:"OIDC"};
/*! @azure/msal-common v14.16.1 2025-08-05 */class qr{generateAccountId(){return[this.homeAccountId,this.environment].join(H).toLowerCase()}generateAccountKey(){return qr.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})}getAccountInfo(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,nativeAccountId:this.nativeAccountId,authorityType:this.authorityType,tenantProfiles:new Map((this.tenantProfiles||[]).map((e=>[e.tenantId,e])))}}isSingleTenant(){return!this.tenantProfiles}static generateAccountCacheKey(e){const t=e.homeAccountId.split(".")[1];return[e.homeAccountId,e.environment||"",t||e.tenantId||""].join(H).toLowerCase()}static createAccount(e,t,r){const n=new qr;let o;t.authorityType===_r?n.authorityType=U:t.protocolMode===Or.AAD?n.authorityType=q:n.authorityType=L,e.clientInfo&&r&&(o=Ar(e.clientInfo,r)),n.clientInfo=e.clientInfo,n.homeAccountId=e.homeAccountId,n.nativeAccountId=e.nativeAccountId;const i=e.environment||t&&t.getPreferredCache();if(!i)throw vt(Je);n.environment=i,n.realm=o?.utid||Nr(e.idTokenClaims)||"",n.localAccountId=o?.uid||e.idTokenClaims?.oid||e.idTokenClaims?.sub||"";const a=e.idTokenClaims?.preferred_username||e.idTokenClaims?.upn,s=e.idTokenClaims?.emails?e.idTokenClaims.emails[0]:null;if(n.username=a||s||"",n.name=e.idTokenClaims?.name||"",n.cloudGraphHostName=e.cloudGraphHostName,n.msGraphHost=e.msGraphHost,e.tenantProfiles)n.tenantProfiles=e.tenantProfiles;else{const t=br(e.homeAccountId,n.localAccountId,n.realm,e.idTokenClaims);n.tenantProfiles=[t]}return n}static createFromAccountInfo(e,t,r){const n=new qr;return n.authorityType=e.authorityType||L,n.homeAccountId=e.homeAccountId,n.localAccountId=e.localAccountId,n.nativeAccountId=e.nativeAccountId,n.realm=e.tenantId,n.environment=e.environment,n.username=e.username,n.name=e.name,n.cloudGraphHostName=t,n.msGraphHost=r,n.tenantProfiles=Array.from(e.tenantProfiles?.values()||[]),n}static generateHomeAccountId(e,t,r,n,o){if(t!==_r&&t!==Pr){if(e)try{const t=Ar(e,n.base64Decode);if(t.uid&&t.utid)return`${t.uid}.${t.utid}`}catch(e){}r.warning("No client info in response")}return o?.sub||""}static isAccountEntity(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType"))}static accountInfoIsEqual(e,t,r){if(!e||!t)return!1;let n=!0;if(r){const r=e.idTokenClaims||{},o=t.idTokenClaims||{};n=r.iat===o.iat&&r.nonce===o.nonce}return e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment&&e.nativeAccountId===t.nativeAccountId&&n}}
/*! @azure/msal-common v14.16.1 2025-08-05 */function Ur(e){return e.startsWith("#/")?e.substring(2):e.startsWith("#")||e.startsWith("?")?e.substring(1):e}function Lr(e){if(!e||e.indexOf("=")<0)return null;try{const t=Ur(e),r=Object.fromEntries(new URLSearchParams(t));if(r.code||r.error||r.error_description||r.state)return r}catch(e){throw vt(Ne)}return null}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Hr{get urlString(){return this._urlString}constructor(e){if(this._urlString=e,!this._urlString)throw Ir(Xt);e.includes("#")||(this._urlString=Hr.canonicalizeUri(e))}static canonicalizeUri(e){if(e){let t=e.toLowerCase();return Tr.endsWith(t,"?")?t=t.slice(0,-1):Tr.endsWith(t,"?/")&&(t=t.slice(0,-2)),Tr.endsWith(t,"/")||(t+="/"),t}return e}validateAsUri(){let e;try{e=this.getUrlComponents()}catch(e){throw Ir(Jt)}if(!e.HostNameAndPort||!e.PathSegments)throw Ir(Jt);if(!e.Protocol||"https:"!==e.Protocol.toLowerCase())throw Ir(Yt)}static appendQueryString(e,t){return t?e.indexOf("?")<0?`${e}?${t}`:`${e}&${t}`:e}static removeHashFromUrl(e){return Hr.canonicalizeUri(e.split("#")[0])}replaceTenantPath(e){const t=this.getUrlComponents(),r=t.PathSegments;return!e||0===r.length||r[0]!==A&&r[0]!==k||(r[0]=e),Hr.constructAuthorityUriFromObject(t)}getUrlComponents(){const e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw Ir(Jt);const r={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]};let n=r.AbsolutePath.split("/");return n=n.filter((e=>e&&e.length>0)),r.PathSegments=n,r.QueryString&&r.QueryString.endsWith("/")&&(r.QueryString=r.QueryString.substring(0,r.QueryString.length-1)),r}static getDomainFromUrl(e){const t=RegExp("^([^:/?#]+://)?([^/?#]*)"),r=e.match(t);if(!r)throw Ir(Jt);return r[2]}static getAbsoluteUrl(e,r){if(e[0]===t.FORWARD_SLASH){const t=new Hr(r).getUrlComponents();return t.Protocol+"//"+t.HostNameAndPort+e}return e}static constructAuthorityUriFromObject(e){return new Hr(e.Protocol+"//"+e.HostNameAndPort+"/"+e.PathSegments.join("/"))}static hashContainsKnownProperties(e){return!!Lr(e)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Br={"login.microsoftonline.com":{token_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.com/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/logout"},"login.chinacloudapi.cn":{token_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.chinacloudapi.cn/{tenantid}/discovery/v2.0/keys",issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",authorization_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/logout"},"login.microsoftonline.us":{token_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.us/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/logout"}},xr={tenant_discovery_endpoint:"https://{canonicalAuthority}/v2.0/.well-known/openid-configuration",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},Dr=new Set;function Fr(e,t,r,n){if(n?.trace(`getAliasesFromMetadata called with source: ${r}`),e&&t){const o=Kr(t,e);if(o)return n?.trace(`getAliasesFromMetadata: found cloud discovery metadata in ${r}, returning aliases`),o.aliases;n?.trace(`getAliasesFromMetadata: did not find cloud discovery metadata in ${r}`)}return null}function Kr(e,t){for(let r=0;r<e.length;r++){const n=e[r];if(n.aliases.includes(t))return n}return null}
/*! @azure/msal-common v14.16.1 2025-08-05 */xr.metadata.forEach((e=>{e.aliases.forEach((e=>{Dr.add(e)}))}));const zr="cache_quota_exceeded",$r="cache_error_unknown",Gr={[zr]:"Exceeded cache storage capacity.",[$r]:"Unexpected error occurred when using cache storage."};class Qr extends Error{constructor(e,t){const r=t||(Gr[e]?Gr[e]:Gr[$r]);super(`${e}: ${r}`),Object.setPrototypeOf(this,Qr.prototype),this.name="CacheError",this.errorCode=e,this.errorMessage=r}}function Wr(e){return e instanceof Error?"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name||e.message.includes("exceeded the quota")?new Qr(zr):new Qr(e.name,e.message):new Qr($r)}
/*! @azure/msal-common v14.16.1 2025-08-05 */class jr{constructor(e,t,r,n){this.clientId=e,this.cryptoImpl=t,this.commonLogger=r.clone(At,kt),this.staticAuthorityOptions=n}getAllAccounts(e,t){return this.buildTenantProfiles(this.getAccountsFilteredBy(t||{},e),e,t)}getAccountInfoFilteredBy(e,t){const r=this.getAllAccounts(t,e);if(r.length>1){return r.sort((e=>e.idTokenClaims?-1:1))[0]}return 1===r.length?r[0]:null}getBaseAccountInfo(e,t){const r=this.getAccountsFilteredBy(e,t);return r.length>0?r[0].getAccountInfo():null}buildTenantProfiles(e,t,r){return e.flatMap((e=>this.getTenantProfilesFromAccountEntity(e,t,r?.tenantId,r)))}getTenantedAccountInfoByFilter(e,t,r,n,o){let i,a=null;if(o&&!this.tenantProfileMatchesFilter(r,o))return null;const s=this.getIdToken(e,n,t,r.tenantId);return s&&(i=bt(s.secret,this.cryptoImpl.base64Decode),!this.idTokenClaimsMatchTenantProfileFilter(i,o))?null:(a=Er(e,r,i,s?.secret),a)}getTenantProfilesFromAccountEntity(e,t,r,n){const o=e.getAccountInfo();let i=o.tenantProfiles||new Map;const a=this.getTokenKeys();if(r){const e=i.get(r);if(!e)return[];i=new Map([[r,e]])}const s=[];return i.forEach((e=>{const r=this.getTenantedAccountInfoByFilter(o,a,e,t,n);r&&s.push(r)})),s}tenantProfileMatchesFilter(e,t){return!(t.localAccountId&&!this.matchLocalAccountIdFromTenantProfile(e,t.localAccountId))&&((!t.name||e.name===t.name)&&(void 0===t.isHomeTenant||e.isHomeTenant===t.isHomeTenant))}idTokenClaimsMatchTenantProfileFilter(e,t){if(t){if(t.localAccountId&&!this.matchLocalAccountIdFromTokenClaims(e,t.localAccountId))return!1;if(t.loginHint&&!this.matchLoginHintFromTokenClaims(e,t.loginHint))return!1;if(t.username&&!this.matchUsername(e.preferred_username,t.username))return!1;if(t.name&&!this.matchName(e,t.name))return!1;if(t.sid&&!this.matchSid(e,t.sid))return!1}return!0}async saveCacheRecord(e,t,r){if(!e)throw vt(Ye);try{e.account&&this.setAccount(e.account,t),e.idToken&&!1!==r?.idToken&&this.setIdTokenCredential(e.idToken,t),e.accessToken&&!1!==r?.accessToken&&await this.saveAccessToken(e.accessToken,t),e.refreshToken&&!1!==r?.refreshToken&&this.setRefreshTokenCredential(e.refreshToken,t),e.appMetadata&&this.setAppMetadata(e.appMetadata,t)}catch(e){throw this.commonLogger?.error("CacheManager.saveCacheRecord: failed"),e instanceof Ae?e:Wr(e)}}async saveAccessToken(e,t){const r={clientId:e.clientId,credentialType:e.credentialType,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm,tokenType:e.tokenType,requestedClaimsHash:e.requestedClaimsHash},n=this.getTokenKeys(),o=wr.fromString(e.target);n.accessToken.forEach((e=>{if(!this.accessTokenKeyMatchesFilter(e,r,!1))return;const n=this.getAccessTokenCredential(e,t);if(n&&this.credentialMatchesFilter(n,r)){wr.fromString(n.target).intersectingScopeSets(o)&&this.removeAccessToken(e,t)}})),this.setAccessTokenCredential(e,t)}getAccountsFilteredBy(e,t){const r=this.getAccountKeys(),n=[];return r.forEach((r=>{if(!this.isAccountKey(r,e.homeAccountId))return;const o=this.getAccount(r,t,this.commonLogger);if(!o)return;if(e.homeAccountId&&!this.matchHomeAccountId(o,e.homeAccountId))return;if(e.username&&!this.matchUsername(o.username,e.username))return;if(e.environment&&!this.matchEnvironment(o,e.environment))return;if(e.realm&&!this.matchRealm(o,e.realm))return;if(e.nativeAccountId&&!this.matchNativeAccountId(o,e.nativeAccountId))return;if(e.authorityType&&!this.matchAuthorityType(o,e.authorityType))return;const i={localAccountId:e?.localAccountId,name:e?.name},a=o.tenantProfiles?.filter((e=>this.tenantProfileMatchesFilter(e,i)));a&&0===a.length||n.push(o)})),n}isAccountKey(e,t,r){return!(e.split(H).length<3)&&(!(t&&!e.toLowerCase().includes(t.toLowerCase()))&&!(r&&!e.toLowerCase().includes(r.toLowerCase())))}isCredentialKey(e){if(e.split(H).length<6)return!1;const t=e.toLowerCase();if(-1===t.indexOf(x.ID_TOKEN.toLowerCase())&&-1===t.indexOf(x.ACCESS_TOKEN.toLowerCase())&&-1===t.indexOf(x.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())&&-1===t.indexOf(x.REFRESH_TOKEN.toLowerCase()))return!1;if(t.indexOf(x.REFRESH_TOKEN.toLowerCase())>-1){const e=`${x.REFRESH_TOKEN}${H}${this.clientId}${H}`,r=`${x.REFRESH_TOKEN}${H}${F}${H}`;if(-1===t.indexOf(e.toLowerCase())&&-1===t.indexOf(r.toLowerCase()))return!1}else if(-1===t.indexOf(this.clientId.toLowerCase()))return!1;return!0}credentialMatchesFilter(e,t){if(t.clientId&&!this.matchClientId(e,t.clientId))return!1;if(t.userAssertionHash&&!this.matchUserAssertionHash(e,t.userAssertionHash))return!1;if("string"==typeof t.homeAccountId&&!this.matchHomeAccountId(e,t.homeAccountId))return!1;if(t.environment&&!this.matchEnvironment(e,t.environment))return!1;if(t.realm&&!this.matchRealm(e,t.realm))return!1;if(t.credentialType&&!this.matchCredentialType(e,t.credentialType))return!1;if(t.familyId&&!this.matchFamilyId(e,t.familyId))return!1;if(t.target&&!this.matchTarget(e,t.target))return!1;if((t.requestedClaimsHash||e.requestedClaimsHash)&&e.requestedClaimsHash!==t.requestedClaimsHash)return!1;if(e.credentialType===x.ACCESS_TOKEN_WITH_AUTH_SCHEME){if(t.tokenType&&!this.matchTokenType(e,t.tokenType))return!1;if(t.tokenType===V.SSH&&t.keyId&&!this.matchKeyId(e,t.keyId))return!1}return!0}getAppMetadataFilteredBy(e){const t=this.getKeys(),r={};return t.forEach((t=>{if(!this.isAppMetadata(t))return;const n=this.getAppMetadata(t);n&&(e.environment&&!this.matchEnvironment(n,e.environment)||e.clientId&&!this.matchClientId(n,e.clientId)||(r[t]=n))})),r}getAuthorityMetadataByAlias(e){const t=this.getAuthorityMetadataKeys();let r=null;return t.forEach((t=>{if(!this.isAuthorityMetadata(t)||-1===t.indexOf(this.clientId))return;const n=this.getAuthorityMetadata(t);n&&-1!==n.aliases.indexOf(e)&&(r=n)})),r}async removeAllAccounts(e){const t=this.getAccountKeys(),r=[];t.forEach((t=>{r.push(this.removeAccount(t,e))})),await Promise.all(r)}async removeAccount(e,t){const r=this.getAccount(e,t,this.commonLogger);r&&(await this.removeAccountContext(r,t),this.removeItem(e,t))}async removeAccountContext(e,t){const r=this.getTokenKeys(),n=e.generateAccountId();r.idToken.forEach((e=>{0===e.indexOf(n)&&this.removeIdToken(e,t)})),r.accessToken.forEach((e=>{0===e.indexOf(n)&&this.removeAccessToken(e,t)})),r.refreshToken.forEach((e=>{0===e.indexOf(n)&&this.removeRefreshToken(e,t)})),this.getKeys().forEach((e=>{e.includes(n)&&this.removeItem(e,t)}))}updateOutdatedCachedAccount(e,t,r,n){if(t&&t.isSingleTenant()){this.commonLogger?.verbose("updateOutdatedCachedAccount: Found a single-tenant (outdated) account entity in the cache, migrating to multi-tenant account entity");const o=this.getAccountKeys().filter((e=>e.startsWith(t.homeAccountId))),i=[];o.forEach((e=>{const t=this.getCachedAccountEntity(e,r);t&&i.push(t)}));const a=i.find((e=>Sr(e.realm,e.homeAccountId)))||i[0];a.tenantProfiles=i.map((e=>({tenantId:e.realm,localAccountId:e.localAccountId,name:e.name,isHomeTenant:Sr(e.realm,e.homeAccountId)})));const s=jr.toObject(new qr,{...a}),c=s.generateAccountKey();return o.forEach((t=>{t!==c&&this.removeOutdatedAccount(e,r)})),this.setAccount(s,r),n?.verbose("Updated an outdated account entity in the cache"),s}return t}removeAccessToken(e,t){const r=this.getAccessTokenCredential(e,t);if(this.removeItem(e,t),!r||r.credentialType.toLowerCase()!==x.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()||r.tokenType!==V.POP)return;const n=r.keyId;n&&this.cryptoImpl.removeTokenBindingKey(n).catch((()=>{this.commonLogger.error("Binding key could not be removed")}))}removeAppMetadata(e){return this.getKeys().forEach((t=>{this.isAppMetadata(t)&&this.removeItem(t,e)})),!0}readAccountFromCache(e,t){const r=qr.generateAccountCacheKey(e);return this.getAccount(r,t,this.commonLogger)}getIdToken(e,t,r,n,o){this.commonLogger.trace("CacheManager - getIdToken called");const i={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:x.ID_TOKEN,clientId:this.clientId,realm:n},a=this.getIdTokensByFilter(i,t,r),s=a.size;if(s<1)return this.commonLogger.info("CacheManager:getIdToken - No token found"),null;if(s>1){let r=a;if(!n){const t=new Map;a.forEach(((r,n)=>{r.realm===e.tenantId&&t.set(n,r)}));const n=t.size;if(n<1)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account but none match account entity tenant id, returning first result"),a.values().next().value;if(1===n)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account, defaulting to home tenant profile"),t.values().next().value;r=t}return this.commonLogger.info("CacheManager:getIdToken - Multiple matching ID tokens found, clearing them"),r.forEach(((e,r)=>{this.removeIdToken(r,t)})),o&&t&&o.addFields({multiMatchedID:a.size},t),null}return this.commonLogger.info("CacheManager:getIdToken - Returning ID token"),a.values().next().value}getIdTokensByFilter(e,t,r){const n=r&&r.idToken||this.getTokenKeys().idToken,o=new Map;return n.forEach((r=>{if(!this.idTokenKeyMatchesFilter(r,{clientId:this.clientId,...e}))return;const n=this.getIdTokenCredential(r,t);n&&this.credentialMatchesFilter(n,e)&&o.set(r,n)})),o}idTokenKeyMatchesFilter(e,t){const r=e.toLowerCase();return(!t.clientId||-1!==r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase()))}removeIdToken(e,t){this.removeItem(e,t)}removeRefreshToken(e,t){this.removeItem(e,t)}getAccessToken(e,t,r,n,o){this.commonLogger.trace("CacheManager - getAccessToken called");const i=wr.createSearchScopes(t.scopes),a=t.authenticationScheme||V.BEARER,s=a&&a.toLowerCase()!==V.BEARER.toLowerCase()?x.ACCESS_TOKEN_WITH_AUTH_SCHEME:x.ACCESS_TOKEN,c={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:s,clientId:this.clientId,realm:n||e.tenantId,target:i,tokenType:a,keyId:t.sshKid,requestedClaimsHash:t.requestedClaimsHash},l=r&&r.accessToken||this.getTokenKeys().accessToken,d=[];l.forEach((e=>{if(this.accessTokenKeyMatchesFilter(e,c,!0)){const r=this.getAccessTokenCredential(e,t.correlationId);r&&this.credentialMatchesFilter(r,c)&&d.push(r)}}));const h=d.length;return h<1?(this.commonLogger.info("CacheManager:getAccessToken - No token found"),null):h>1?(this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"),d.forEach((e=>{this.removeAccessToken(Mt(e),t.correlationId)})),o&&t.correlationId&&o.addFields({multiMatchedAT:d.length},t.correlationId),null):(this.commonLogger.info("CacheManager:getAccessToken - Returning access token"),d[0])}accessTokenKeyMatchesFilter(e,t,r){const n=e.toLowerCase();if(t.clientId&&-1===n.indexOf(t.clientId.toLowerCase()))return!1;if(t.homeAccountId&&-1===n.indexOf(t.homeAccountId.toLowerCase()))return!1;if(t.realm&&-1===n.indexOf(t.realm.toLowerCase()))return!1;if(t.requestedClaimsHash&&-1===n.indexOf(t.requestedClaimsHash.toLowerCase()))return!1;if(t.target){const e=t.target.asArray();for(let t=0;t<e.length;t++){if(r&&!n.includes(e[t].toLowerCase()))return!1;if(!r&&n.includes(e[t].toLowerCase()))return!0}}return!0}getAccessTokensByFilter(e,t){const r=this.getTokenKeys(),n=[];return r.accessToken.forEach((r=>{if(!this.accessTokenKeyMatchesFilter(r,e,!0))return;const o=this.getAccessTokenCredential(r,t);o&&this.credentialMatchesFilter(o,e)&&n.push(o)})),n}getRefreshToken(e,t,r,n,o){this.commonLogger.trace("CacheManager - getRefreshToken called");const i=t?F:void 0,a={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:x.REFRESH_TOKEN,clientId:this.clientId,familyId:i},s=n&&n.refreshToken||this.getTokenKeys().refreshToken,c=[];s.forEach((e=>{if(this.refreshTokenKeyMatchesFilter(e,a)){const t=this.getRefreshTokenCredential(e,r);t&&this.credentialMatchesFilter(t,a)&&c.push(t)}}));const l=c.length;return l<1?(this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."),null):(l>1&&o&&r&&o.addFields({multiMatchedRT:l},r),this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"),c[0])}refreshTokenKeyMatchesFilter(e,t){const r=e.toLowerCase();return(!t.familyId||-1!==r.indexOf(t.familyId.toLowerCase()))&&(!(!t.familyId&&t.clientId&&-1===r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase())))}readAppMetadataFromCache(e){const t={environment:e,clientId:this.clientId},r=this.getAppMetadataFilteredBy(t),n=Object.keys(r).map((e=>r[e])),o=n.length;if(o<1)return null;if(o>1)throw vt(Fe);return n[0]}isAppMetadataFOCI(e){const t=this.readAppMetadataFromCache(e);return!(!t||t.familyId!==F)}matchHomeAccountId(e,t){return!("string"!=typeof e.homeAccountId||t!==e.homeAccountId)}matchLocalAccountIdFromTokenClaims(e,t){return t===(e.oid||e.sub)}matchLocalAccountIdFromTenantProfile(e,t){return e.localAccountId===t}matchName(e,t){return!(t.toLowerCase()!==e.name?.toLowerCase())}matchUsername(e,t){return!(!e||"string"!=typeof e||t?.toLowerCase()!==e.toLowerCase())}matchUserAssertionHash(e,t){return!(!e.userAssertionHash||t!==e.userAssertionHash)}matchEnvironment(e,t){if(this.staticAuthorityOptions){const r=function(e,t){let r;const n=e.canonicalAuthority;if(n){const o=new Hr(n).getUrlComponents().HostNameAndPort;r=Fr(o,e.cloudDiscoveryMetadata?.metadata,$,t)||Fr(o,xr.metadata,W,t)||e.knownAuthorities}return r||[]}(this.staticAuthorityOptions,this.commonLogger);if(r.includes(t)&&r.includes(e.environment))return!0}const r=this.getAuthorityMetadataByAlias(t);return!!(r&&r.aliases.indexOf(e.environment)>-1)}matchCredentialType(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()}matchClientId(e,t){return!(!e.clientId||t!==e.clientId)}matchFamilyId(e,t){return!(!e.familyId||t!==e.familyId)}matchRealm(e,t){return!(e.realm?.toLowerCase()!==t.toLowerCase())}matchNativeAccountId(e,t){return!(!e.nativeAccountId||t!==e.nativeAccountId)}matchLoginHintFromTokenClaims(e,t){return e.login_hint===t||(e.preferred_username===t||e.upn===t)}matchSid(e,t){return e.sid===t}matchAuthorityType(e,t){return!(!e.authorityType||t.toLowerCase()!==e.authorityType.toLowerCase())}matchTarget(e,t){if(e.credentialType!==x.ACCESS_TOKEN&&e.credentialType!==x.ACCESS_TOKEN_WITH_AUTH_SCHEME||!e.target)return!1;return wr.fromString(e.target).containsScopeSet(t)}matchTokenType(e,t){return!(!e.tokenType||e.tokenType!==t)}matchKeyId(e,t){return!(!e.keyId||e.keyId!==t)}isAppMetadata(e){return-1!==e.indexOf(D)}isAuthorityMetadata(e){return-1!==e.indexOf(K)}generateAuthorityMetadataCacheKey(e){return`${K}-${this.clientId}-${e}`}static toObject(e,t){for(const r in t)e[r]=t[r];return e}}class Vr extends jr{setAccount(){throw vt(gt)}getAccount(){throw vt(gt)}getCachedAccountEntity(){throw vt(gt)}setIdTokenCredential(){throw vt(gt)}getIdTokenCredential(){throw vt(gt)}setAccessTokenCredential(){throw vt(gt)}getAccessTokenCredential(){throw vt(gt)}setRefreshTokenCredential(){throw vt(gt)}getRefreshTokenCredential(){throw vt(gt)}setAppMetadata(){throw vt(gt)}getAppMetadata(){throw vt(gt)}setServerTelemetry(){throw vt(gt)}getServerTelemetry(){throw vt(gt)}setAuthorityMetadata(){throw vt(gt)}getAuthorityMetadata(){throw vt(gt)}getAuthorityMetadataKeys(){throw vt(gt)}setThrottlingCache(){throw vt(gt)}getThrottlingCache(){throw vt(gt)}removeItem(){throw vt(gt)}getKeys(){throw vt(gt)}getAccountKeys(){throw vt(gt)}getTokenKeys(){throw vt(gt)}updateCredentialCacheKey(){throw vt(gt)}removeOutdatedAccount(){throw vt(gt)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Yr={tokenRenewalOffsetSeconds:300,preventCorsPreflight:!1},Jr={loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info,correlationId:t.EMPTY_STRING},Xr={claimsBasedCachingEnabled:!1},Zr={async sendGetRequestAsync(){throw vt(gt)},async sendPostRequestAsync(){throw vt(gt)}},en={sku:t.SKU,version:kt,cpu:t.EMPTY_STRING,os:t.EMPTY_STRING},tn={clientSecret:t.EMPTY_STRING,clientAssertion:void 0},rn={azureCloudInstance:St.None,tenant:`${t.DEFAULT_COMMON_TENANT}`},nn={application:{appName:"",appVersion:""}};function on(e){return e.authOptions.authority.options.protocolMode===Or.OIDC}
/*! @azure/msal-common v14.16.1 2025-08-05 */const an="home_account_id",sn="UPN",cn="client_id",ln="redirect_uri",dn="response_type",hn="token_type",un="req_cnf",gn="return_spa_code",pn="x-client-xtra-sku",mn="brk_client_id",fn="brk_redirect_uri";
/*! @azure/msal-common v14.16.1 2025-08-05 */
/*! @azure/msal-common v14.16.1 2025-08-05 */
class Cn{static validateRedirectUri(e){if(!e)throw Ir(jt)}static validatePrompt(e){const t=[];for(const e in R)t.push(R[e]);if(t.indexOf(e)<0)throw Ir(er)}static validateClaims(e){try{JSON.parse(e)}catch(e){throw Ir(tr)}}static validateCodeChallengeParams(e,t){if(!e||!t)throw Ir(ir);this.validateCodeChallengeMethod(t)}static validateCodeChallengeMethod(e){if([_.PLAIN,_.S256].indexOf(e)<0)throw Ir(or)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class yn{constructor(e,t){this.parameters=new Map,this.performanceClient=t,this.correlationId=e}addResponseTypeCode(){this.parameters.set(dn,encodeURIComponent(t.CODE_RESPONSE_TYPE))}addResponseTypeForTokenAndIdToken(){this.parameters.set(dn,encodeURIComponent(`${t.TOKEN_RESPONSE_TYPE} ${t.ID_TOKEN_RESPONSE_TYPE}`))}addResponseMode(e){this.parameters.set("response_mode",encodeURIComponent(e||M.QUERY))}addNativeBroker(){this.parameters.set("nativebroker",encodeURIComponent("1"))}addScopes(e,t=!0,r=a){!t||r.includes("openid")||e.includes("openid")||r.push("openid");const n=t?[...e||[],...r]:e||[],o=new wr(n);this.parameters.set("scope",encodeURIComponent(o.printScopes()))}addClientId(e){this.parameters.set(cn,encodeURIComponent(e))}addRedirectUri(e){Cn.validateRedirectUri(e),this.parameters.set(ln,encodeURIComponent(e))}addPostLogoutRedirectUri(e){Cn.validateRedirectUri(e),this.parameters.set("post_logout_redirect_uri",encodeURIComponent(e))}addIdTokenHint(e){this.parameters.set("id_token_hint",encodeURIComponent(e))}addDomainHint(e){this.parameters.set("domain_hint",encodeURIComponent(e))}addLoginHint(e){this.parameters.set("login_hint",encodeURIComponent(e))}addCcsUpn(e){this.parameters.set(h,encodeURIComponent(`UPN:${e}`))}addCcsOid(e){this.parameters.set(h,encodeURIComponent(`Oid:${e.uid}@${e.utid}`))}addSid(e){this.parameters.set("sid",encodeURIComponent(e))}addClaims(e,t){const r=this.addClientCapabilitiesToClaims(e,t);Cn.validateClaims(r),this.parameters.set("claims",encodeURIComponent(r))}addCorrelationId(e){this.parameters.set("client-request-id",encodeURIComponent(e))}addLibraryInfo(e){this.parameters.set("x-client-SKU",e.sku),this.parameters.set("x-client-VER",e.version),e.os&&this.parameters.set("x-client-OS",e.os),e.cpu&&this.parameters.set("x-client-CPU",e.cpu)}addApplicationTelemetry(e){e?.appName&&this.parameters.set("x-app-name",e.appName),e?.appVersion&&this.parameters.set("x-app-ver",e.appVersion)}addPrompt(e){Cn.validatePrompt(e),this.parameters.set("prompt",encodeURIComponent(e))}addState(e){e&&this.parameters.set("state",encodeURIComponent(e))}addNonce(e){this.parameters.set("nonce",encodeURIComponent(e))}addCodeChallengeParams(e,t){if(Cn.validateCodeChallengeParams(e,t),!e||!t)throw Ir(ir);this.parameters.set("code_challenge",encodeURIComponent(e)),this.parameters.set("code_challenge_method",encodeURIComponent(t))}addAuthorizationCode(e){this.parameters.set("code",encodeURIComponent(e))}addDeviceCode(e){this.parameters.set("device_code",encodeURIComponent(e))}addRefreshToken(e){this.parameters.set("refresh_token",encodeURIComponent(e))}addCodeVerifier(e){this.parameters.set("code_verifier",encodeURIComponent(e))}addClientSecret(e){this.parameters.set("client_secret",encodeURIComponent(e))}addClientAssertion(e){e&&this.parameters.set("client_assertion",encodeURIComponent(e))}addClientAssertionType(e){e&&this.parameters.set("client_assertion_type",encodeURIComponent(e))}addOboAssertion(e){this.parameters.set("assertion",encodeURIComponent(e))}addRequestTokenUse(e){this.parameters.set("requested_token_use",encodeURIComponent(e))}addGrantType(e){this.parameters.set("grant_type",encodeURIComponent(e))}addClientInfo(){this.parameters.set("client_info","1")}addExtraQueryParameters(e){Object.entries(e).forEach((([e,t])=>{!this.parameters.has(e)&&t&&this.parameters.set(e,t)}))}addClientCapabilitiesToClaims(e,t){let r;if(e)try{r=JSON.parse(e)}catch(e){throw Ir(tr)}else r={};return t&&t.length>0&&(r.hasOwnProperty(b)||(r[b]={}),r[b][E]={values:t}),JSON.stringify(r)}addUsername(e){this.parameters.set(re,encodeURIComponent(e))}addPassword(e){this.parameters.set(ne,encodeURIComponent(e))}addPopToken(e){e&&(this.parameters.set(hn,V.POP),this.parameters.set(un,encodeURIComponent(e)))}addSshJwk(e){e&&(this.parameters.set(hn,V.SSH),this.parameters.set(un,encodeURIComponent(e)))}addServerTelemetry(e){this.parameters.set("x-client-current-telemetry",e.generateCurrentRequestHeaderValue()),this.parameters.set("x-client-last-telemetry",e.generateLastRequestHeaderValue())}addThrottling(){this.parameters.set("x-ms-lib-capability",Z)}addLogoutHint(e){this.parameters.set("logout_hint",encodeURIComponent(e))}addBrokerParameters(e){const t={};t[mn]=e.brokerClientId,t[fn]=e.brokerRedirectUri,this.addExtraQueryParameters(t)}createQueryString(){const e=new Array;return this.parameters.forEach(((t,r)=>{e.push(`${r}=${t}`)})),function(e,t,r){if(!t)return;const n=e.get(cn);n&&e.has(mn)&&r?.addFields({embeddedClientId:n,embeddedRedirectUri:e.get(ln)},t)}(this.parameters,this.correlationId,this.performanceClient),e.join("&")}}
/*! @azure/msal-common v14.16.1 2025-08-05 */
/*! @azure/msal-common v14.16.1 2025-08-05 */
const vn={AcquireTokenByCode:"acquireTokenByCode",AcquireTokenByRefreshToken:"acquireTokenByRefreshToken",AcquireTokenSilent:"acquireTokenSilent",AcquireTokenSilentAsync:"acquireTokenSilentAsync",AcquireTokenPopup:"acquireTokenPopup",AcquireTokenPreRedirect:"acquireTokenPreRedirect",AcquireTokenRedirect:"acquireTokenRedirect",CryptoOptsGetPublicKeyThumbprint:"cryptoOptsGetPublicKeyThumbprint",CryptoOptsSignJwt:"cryptoOptsSignJwt",SilentCacheClientAcquireToken:"silentCacheClientAcquireToken",SilentIframeClientAcquireToken:"silentIframeClientAcquireToken",AwaitConcurrentIframe:"awaitConcurrentIframe",SilentRefreshClientAcquireToken:"silentRefreshClientAcquireToken",SsoSilent:"ssoSilent",StandardInteractionClientGetDiscoveredAuthority:"standardInteractionClientGetDiscoveredAuthority",FetchAccountIdWithNativeBroker:"fetchAccountIdWithNativeBroker",NativeInteractionClientAcquireToken:"nativeInteractionClientAcquireToken",BaseClientCreateTokenRequestHeaders:"baseClientCreateTokenRequestHeaders",NetworkClientSendPostRequestAsync:"networkClientSendPostRequestAsync",RefreshTokenClientExecutePostToTokenEndpoint:"refreshTokenClientExecutePostToTokenEndpoint",AuthorizationCodeClientExecutePostToTokenEndpoint:"authorizationCodeClientExecutePostToTokenEndpoint",BrokerHandhshake:"brokerHandshake",AcquireTokenByRefreshTokenInBroker:"acquireTokenByRefreshTokenInBroker",AcquireTokenByBroker:"acquireTokenByBroker",RefreshTokenClientExecuteTokenRequest:"refreshTokenClientExecuteTokenRequest",RefreshTokenClientAcquireToken:"refreshTokenClientAcquireToken",RefreshTokenClientAcquireTokenWithCachedRefreshToken:"refreshTokenClientAcquireTokenWithCachedRefreshToken",RefreshTokenClientAcquireTokenByRefreshToken:"refreshTokenClientAcquireTokenByRefreshToken",RefreshTokenClientCreateTokenRequestBody:"refreshTokenClientCreateTokenRequestBody",AcquireTokenFromCache:"acquireTokenFromCache",SilentFlowClientAcquireCachedToken:"silentFlowClientAcquireCachedToken",SilentFlowClientGenerateResultFromCacheRecord:"silentFlowClientGenerateResultFromCacheRecord",AcquireTokenBySilentIframe:"acquireTokenBySilentIframe",InitializeBaseRequest:"initializeBaseRequest",InitializeSilentRequest:"initializeSilentRequest",InitializeClientApplication:"initializeClientApplication",SilentIframeClientTokenHelper:"silentIframeClientTokenHelper",SilentHandlerInitiateAuthRequest:"silentHandlerInitiateAuthRequest",SilentHandlerMonitorIframeForHash:"silentHandlerMonitorIframeForHash",SilentHandlerLoadFrame:"silentHandlerLoadFrame",SilentHandlerLoadFrameSync:"silentHandlerLoadFrameSync",StandardInteractionClientCreateAuthCodeClient:"standardInteractionClientCreateAuthCodeClient",StandardInteractionClientGetClientConfiguration:"standardInteractionClientGetClientConfiguration",StandardInteractionClientInitializeAuthorizationRequest:"standardInteractionClientInitializeAuthorizationRequest",StandardInteractionClientInitializeAuthorizationCodeRequest:"standardInteractionClientInitializeAuthorizationCodeRequest",GetAuthCodeUrl:"getAuthCodeUrl",HandleCodeResponseFromServer:"handleCodeResponseFromServer",HandleCodeResponse:"handleCodeResponse",UpdateTokenEndpointAuthority:"updateTokenEndpointAuthority",AuthClientAcquireToken:"authClientAcquireToken",AuthClientExecuteTokenRequest:"authClientExecuteTokenRequest",AuthClientCreateTokenRequestBody:"authClientCreateTokenRequestBody",AuthClientCreateQueryString:"authClientCreateQueryString",PopTokenGenerateCnf:"popTokenGenerateCnf",PopTokenGenerateKid:"popTokenGenerateKid",HandleServerTokenResponse:"handleServerTokenResponse",DeserializeResponse:"deserializeResponse",AuthorityFactoryCreateDiscoveredInstance:"authorityFactoryCreateDiscoveredInstance",AuthorityResolveEndpointsAsync:"authorityResolveEndpointsAsync",AuthorityResolveEndpointsFromLocalSources:"authorityResolveEndpointsFromLocalSources",AuthorityGetCloudDiscoveryMetadataFromNetwork:"authorityGetCloudDiscoveryMetadataFromNetwork",AuthorityUpdateCloudDiscoveryMetadata:"authorityUpdateCloudDiscoveryMetadata",AuthorityGetEndpointMetadataFromNetwork:"authorityGetEndpointMetadataFromNetwork",AuthorityUpdateEndpointMetadata:"authorityUpdateEndpointMetadata",AuthorityUpdateMetadataWithRegionalInformation:"authorityUpdateMetadataWithRegionalInformation",RegionDiscoveryDetectRegion:"regionDiscoveryDetectRegion",RegionDiscoveryGetRegionFromIMDS:"regionDiscoveryGetRegionFromIMDS",RegionDiscoveryGetCurrentVersion:"regionDiscoveryGetCurrentVersion",AcquireTokenByCodeAsync:"acquireTokenByCodeAsync",GetEndpointMetadataFromNetwork:"getEndpointMetadataFromNetwork",GetCloudDiscoveryMetadataFromNetworkMeasurement:"getCloudDiscoveryMetadataFromNetworkMeasurement",HandleRedirectPromiseMeasurement:"handleRedirectPromise",HandleNativeRedirectPromiseMeasurement:"handleNativeRedirectPromise",UpdateCloudDiscoveryMetadataMeasurement:"updateCloudDiscoveryMetadataMeasurement",UsernamePasswordClientAcquireToken:"usernamePasswordClientAcquireToken",NativeMessageHandlerHandshake:"nativeMessageHandlerHandshake",NativeGenerateAuthResult:"nativeGenerateAuthResult",RemoveHiddenIframe:"removeHiddenIframe",ClearTokensAndKeysWithClaims:"clearTokensAndKeysWithClaims",CacheManagerGetRefreshToken:"cacheManagerGetRefreshToken",GeneratePkceCodes:"generatePkceCodes",GenerateCodeVerifier:"generateCodeVerifier",GenerateCodeChallengeFromVerifier:"generateCodeChallengeFromVerifier",Sha256Digest:"sha256Digest",GetRandomValues:"getRandomValues"},In=new Map([[vn.AcquireTokenByCode,"ATByCode"],[vn.AcquireTokenByRefreshToken,"ATByRT"],[vn.AcquireTokenSilent,"ATS"],[vn.AcquireTokenSilentAsync,"ATSAsync"],[vn.AcquireTokenPopup,"ATPopup"],[vn.AcquireTokenRedirect,"ATRedirect"],[vn.CryptoOptsGetPublicKeyThumbprint,"CryptoGetPKThumb"],[vn.CryptoOptsSignJwt,"CryptoSignJwt"],[vn.SilentCacheClientAcquireToken,"SltCacheClientAT"],[vn.SilentIframeClientAcquireToken,"SltIframeClientAT"],[vn.SilentRefreshClientAcquireToken,"SltRClientAT"],[vn.SsoSilent,"SsoSlt"],[vn.StandardInteractionClientGetDiscoveredAuthority,"StdIntClientGetDiscAuth"],[vn.FetchAccountIdWithNativeBroker,"FetchAccIdWithNtvBroker"],[vn.NativeInteractionClientAcquireToken,"NtvIntClientAT"],[vn.BaseClientCreateTokenRequestHeaders,"BaseClientCreateTReqHead"],[vn.NetworkClientSendPostRequestAsync,"NetClientSendPost"],[vn.RefreshTokenClientExecutePostToTokenEndpoint,"RTClientExecPost"],[vn.AuthorizationCodeClientExecutePostToTokenEndpoint,"AuthCodeClientExecPost"],[vn.BrokerHandhshake,"BrokerHandshake"],[vn.AcquireTokenByRefreshTokenInBroker,"ATByRTInBroker"],[vn.AcquireTokenByBroker,"ATByBroker"],[vn.RefreshTokenClientExecuteTokenRequest,"RTClientExecTReq"],[vn.RefreshTokenClientAcquireToken,"RTClientAT"],[vn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,"RTClientATWithCachedRT"],[vn.RefreshTokenClientAcquireTokenByRefreshToken,"RTClientATByRT"],[vn.RefreshTokenClientCreateTokenRequestBody,"RTClientCreateTReqBody"],[vn.AcquireTokenFromCache,"ATFromCache"],[vn.SilentFlowClientAcquireCachedToken,"SltFlowClientATCached"],[vn.SilentFlowClientGenerateResultFromCacheRecord,"SltFlowClientGenResFromCache"],[vn.AcquireTokenBySilentIframe,"ATBySltIframe"],[vn.InitializeBaseRequest,"InitBaseReq"],[vn.InitializeSilentRequest,"InitSltReq"],[vn.InitializeClientApplication,"InitClientApplication"],[vn.SilentIframeClientTokenHelper,"SIClientTHelper"],[vn.SilentHandlerInitiateAuthRequest,"SHandlerInitAuthReq"],[vn.SilentHandlerMonitorIframeForHash,"SltHandlerMonitorIframeForHash"],[vn.SilentHandlerLoadFrame,"SHandlerLoadFrame"],[vn.SilentHandlerLoadFrameSync,"SHandlerLoadFrameSync"],[vn.StandardInteractionClientCreateAuthCodeClient,"StdIntClientCreateAuthCodeClient"],[vn.StandardInteractionClientGetClientConfiguration,"StdIntClientGetClientConf"],[vn.StandardInteractionClientInitializeAuthorizationRequest,"StdIntClientInitAuthReq"],[vn.StandardInteractionClientInitializeAuthorizationCodeRequest,"StdIntClientInitAuthCodeReq"],[vn.GetAuthCodeUrl,"GetAuthCodeUrl"],[vn.HandleCodeResponseFromServer,"HandleCodeResFromServer"],[vn.HandleCodeResponse,"HandleCodeResp"],[vn.UpdateTokenEndpointAuthority,"UpdTEndpointAuth"],[vn.AuthClientAcquireToken,"AuthClientAT"],[vn.AuthClientExecuteTokenRequest,"AuthClientExecTReq"],[vn.AuthClientCreateTokenRequestBody,"AuthClientCreateTReqBody"],[vn.AuthClientCreateQueryString,"AuthClientCreateQueryStr"],[vn.PopTokenGenerateCnf,"PopTGenCnf"],[vn.PopTokenGenerateKid,"PopTGenKid"],[vn.HandleServerTokenResponse,"HandleServerTRes"],[vn.DeserializeResponse,"DeserializeRes"],[vn.AuthorityFactoryCreateDiscoveredInstance,"AuthFactCreateDiscInst"],[vn.AuthorityResolveEndpointsAsync,"AuthResolveEndpointsAsync"],[vn.AuthorityResolveEndpointsFromLocalSources,"AuthResolveEndpointsFromLocal"],[vn.AuthorityGetCloudDiscoveryMetadataFromNetwork,"AuthGetCDMetaFromNet"],[vn.AuthorityUpdateCloudDiscoveryMetadata,"AuthUpdCDMeta"],[vn.AuthorityGetEndpointMetadataFromNetwork,"AuthUpdCDMetaFromNet"],[vn.AuthorityUpdateEndpointMetadata,"AuthUpdEndpointMeta"],[vn.AuthorityUpdateMetadataWithRegionalInformation,"AuthUpdMetaWithRegInfo"],[vn.RegionDiscoveryDetectRegion,"RegDiscDetectReg"],[vn.RegionDiscoveryGetRegionFromIMDS,"RegDiscGetRegFromIMDS"],[vn.RegionDiscoveryGetCurrentVersion,"RegDiscGetCurrentVer"],[vn.AcquireTokenByCodeAsync,"ATByCodeAsync"],[vn.GetEndpointMetadataFromNetwork,"GetEndpointMetaFromNet"],[vn.GetCloudDiscoveryMetadataFromNetworkMeasurement,"GetCDMetaFromNet"],[vn.HandleRedirectPromiseMeasurement,"HandleRedirectPromise"],[vn.HandleNativeRedirectPromiseMeasurement,"HandleNtvRedirectPromise"],[vn.UpdateCloudDiscoveryMetadataMeasurement,"UpdateCDMeta"],[vn.UsernamePasswordClientAcquireToken,"UserPassClientAT"],[vn.NativeMessageHandlerHandshake,"NtvMsgHandlerHandshake"],[vn.NativeGenerateAuthResult,"NtvGenAuthRes"],[vn.RemoveHiddenIframe,"RemoveHiddenIframe"],[vn.ClearTokensAndKeysWithClaims,"ClearTAndKeysWithClaims"],[vn.CacheManagerGetRefreshToken,"CacheManagerGetRT"],[vn.GeneratePkceCodes,"GenPkceCodes"],[vn.GenerateCodeVerifier,"GenCodeVerifier"],[vn.GenerateCodeChallengeFromVerifier,"GenCodeChallengeFromVerifier"],[vn.Sha256Digest,"Sha256Digest"],[vn.GetRandomValues,"GetRandomValues"]]),Tn=1,wn=2,An=new Set(["accessTokenSize","durationMs","idTokenSize","matsSilentStatus","matsHttpStatus","refreshTokenSize","queuedTimeMs","startTimeMs","status","multiMatchedAT","multiMatchedID","multiMatchedRT"]),kn=(e,t,r,n,o)=>(...i)=>{r.trace(`Executing function ${t}`);const a=n?.startMeasurement(t,o);if(o){const e=t+"CallCount";n?.incrementFields({[e]:1},o)}try{const n=e(...i);return a?.end({success:!0}),r.trace(`Returning result from ${t}`),n}catch(e){r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e}},Sn=(e,t,r,n,o)=>(...i)=>{r.trace(`Executing function ${t}`);const a=n?.startMeasurement(t,o);if(o){const e=t+"CallCount";n?.incrementFields({[e]:1},o)}return n?.setPreQueueTime(t,o),e(...i).then((e=>(r.trace(`Returning result from ${t}`),a?.end({success:!0}),e))).catch((e=>{r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e}))};
/*! @azure/msal-common v14.16.1 2025-08-05 */
class bn{constructor(e,t,r,n){this.networkInterface=e,this.logger=t,this.performanceClient=r,this.correlationId=n}async detectRegion(e,r){this.performanceClient?.addQueueMeasurement(vn.RegionDiscoveryDetectRegion,this.correlationId);let n=e;if(n)r.region_source=se;else{const e=bn.IMDS_OPTIONS;try{const o=await Sn(this.getRegionFromIMDS.bind(this),vn.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(t.IMDS_VERSION,e);if(o.status===oe&&(n=o.body,r.region_source=ce),o.status===ie){const t=await Sn(this.getCurrentVersion.bind(this),vn.RegionDiscoveryGetCurrentVersion,this.logger,this.performanceClient,this.correlationId)(e);if(!t)return r.region_source=ae,null;const o=await Sn(this.getRegionFromIMDS.bind(this),vn.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(t,e);o.status===oe&&(n=o.body,r.region_source=ce)}}catch(e){return r.region_source=ae,null}}return n||(r.region_source=ae),n||null}async getRegionFromIMDS(e,r){return this.performanceClient?.addQueueMeasurement(vn.RegionDiscoveryGetRegionFromIMDS,this.correlationId),this.networkInterface.sendGetRequestAsync(`${t.IMDS_ENDPOINT}?api-version=${e}&format=text`,r,t.IMDS_TIMEOUT)}async getCurrentVersion(e){this.performanceClient?.addQueueMeasurement(vn.RegionDiscoveryGetCurrentVersion,this.correlationId);try{const r=await this.networkInterface.sendGetRequestAsync(`${t.IMDS_ENDPOINT}?format=json`,e);return r.status===ie&&r.body&&r.body["newest-versions"]&&r.body["newest-versions"].length>0?r.body["newest-versions"][0]:null}catch(e){return null}}}bn.IMDS_OPTIONS={headers:{Metadata:"true"}};
/*! @azure/msal-common v14.16.1 2025-08-05 */
class En{constructor(e,t,r,n,o,i,a,s){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=r,this.authorityOptions=n,this.regionDiscoveryMetadata={region_used:void 0,region_source:void 0,region_outcome:void 0},this.logger=o,this.performanceClient=a,this.correlationId=i,this.managedIdentity=s||!1,this.regionDiscovery=new bn(t,this.logger,this.performanceClient,this.correlationId)}getAuthorityType(e){if(e.HostNameAndPort.endsWith(t.CIAM_AUTH_URL))return Mr;const r=e.PathSegments;if(r.length)switch(r[0].toLowerCase()){case t.ADFS:return _r;case t.DSTS:return Pr}return Rr}get authorityType(){return this.getAuthorityType(this.canonicalAuthorityUrlComponents)}get protocolMode(){return this.authorityOptions.protocolMode}get options(){return this.authorityOptions}get canonicalAuthority(){return this._canonicalAuthority.urlString}set canonicalAuthority(e){this._canonicalAuthority=new Hr(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null}get canonicalAuthorityUrlComponents(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents}get hostnameAndPort(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()}get tenant(){return this.canonicalAuthorityUrlComponents.PathSegments[0]}get authorizationEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.authorization_endpoint);throw vt(_e)}get tokenEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint);throw vt(_e)}get deviceCodeEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));throw vt(_e)}get endSessionEndpoint(){if(this.discoveryComplete()){if(!this.metadata.end_session_endpoint)throw vt(ct);return this.replacePath(this.metadata.end_session_endpoint)}throw vt(_e)}get selfSignedJwtAudience(){if(this.discoveryComplete())return this.replacePath(this.metadata.issuer);throw vt(_e)}get jwksUri(){if(this.discoveryComplete())return this.replacePath(this.metadata.jwks_uri);throw vt(_e)}canReplaceTenant(e){return 1===e.PathSegments.length&&!En.reservedTenantDomains.has(e.PathSegments[0])&&this.getAuthorityType(e)===Rr&&this.protocolMode===Or.AAD}replaceTenant(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)}replacePath(e){let t=e;const r=new Hr(this.metadata.canonical_authority).getUrlComponents(),n=r.PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach(((e,o)=>{let i=n[o];if(0===o&&this.canReplaceTenant(r)){const e=new Hr(this.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];i!==e&&(this.logger.verbose(`Replacing tenant domain name ${i} with id ${e}`),i=e)}e!==i&&(t=t.replace(`/${i}/`,`/${e}/`))})),this.replaceTenant(t)}get defaultOpenIdConfigurationEndpoint(){const e=this.hostnameAndPort;return this.canonicalAuthority.endsWith("v2.0/")||this.authorityType===_r||this.protocolMode!==Or.AAD&&!this.isAliasOfKnownMicrosoftAuthority(e)?`${this.canonicalAuthority}.well-known/openid-configuration`:`${this.canonicalAuthority}v2.0/.well-known/openid-configuration`}discoveryComplete(){return!!this.metadata}async resolveEndpointsAsync(){this.performanceClient?.addQueueMeasurement(vn.AuthorityResolveEndpointsAsync,this.correlationId);const e=this.getCurrentMetadataEntity(),t=await Sn(this.updateCloudDiscoveryMetadata.bind(this),vn.AuthorityUpdateCloudDiscoveryMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,e.preferred_network);const r=await Sn(this.updateEndpointMetadata.bind(this),vn.AuthorityUpdateEndpointMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.updateCachedMetadata(e,t,{source:r}),this.performanceClient?.addFields({cloudDiscoverySource:t,authorityEndpointSource:r},this.correlationId)}getCurrentMetadataEntity(){let e=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);return e||(e={aliases:[],preferred_cache:this.hostnameAndPort,preferred_network:this.hostnameAndPort,canonical_authority:this.canonicalAuthority,authorization_endpoint:"",token_endpoint:"",end_session_endpoint:"",issuer:"",aliasesFromNetwork:!1,endpointsFromNetwork:!1,expiresAt:$t(),jwks_uri:""}),e}updateCachedMetadata(e,t,r){t!==G&&r?.source!==G&&(e.expiresAt=$t(),e.canonical_authority=this.canonicalAuthority);const n=this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache);this.cacheManager.setAuthorityMetadata(n,e),this.metadata=e}async updateEndpointMetadata(e){this.performanceClient?.addQueueMeasurement(vn.AuthorityUpdateEndpointMetadata,this.correlationId);const t=this.updateEndpointMetadataFromLocalSources(e);if(t){if(t.source===W&&this.authorityOptions.azureRegionConfiguration?.azureRegion&&t.metadata){Gt(e,await Sn(this.updateMetadataWithRegionalInformation.bind(this),vn.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(t.metadata),!1),e.canonical_authority=this.canonicalAuthority}return t.source}let r=await Sn(this.getEndpointMetadataFromNetwork.bind(this),vn.AuthorityGetEndpointMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return this.authorityOptions.azureRegionConfiguration?.azureRegion&&(r=await Sn(this.updateMetadataWithRegionalInformation.bind(this),vn.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(r)),Gt(e,r,!0),Q;throw vt(Me,this.defaultOpenIdConfigurationEndpoint)}updateEndpointMetadataFromLocalSources(e){this.logger.verbose("Attempting to get endpoint metadata from authority configuration");const t=this.getEndpointMetadataFromConfig();if(t)return this.logger.verbose("Found endpoint metadata in authority configuration"),Gt(e,t,!1),{source:$};if(this.logger.verbose("Did not find endpoint metadata in the config... Attempting to get endpoint metadata from the hardcoded values."),this.authorityOptions.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get endpoint metadata from the network metadata cache.");else{const t=this.getEndpointMetadataFromHardcodedValues();if(t)return Gt(e,t,!1),{source:W,metadata:t};this.logger.verbose("Did not find endpoint metadata in hardcoded values... Attempting to get endpoint metadata from the network metadata cache.")}const r=Wt(e);return this.isAuthoritySameType(e)&&e.endpointsFromNetwork&&!r?(this.logger.verbose("Found endpoint metadata in the cache."),{source:G}):(r&&this.logger.verbose("The metadata entity is expired."),null)}isAuthoritySameType(e){return new Hr(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length}getEndpointMetadataFromConfig(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch(e){throw Ir(sr)}return null}async getEndpointMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(vn.AuthorityGetEndpointMetadataFromNetwork,this.correlationId);const e={},t=this.defaultOpenIdConfigurationEndpoint;this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: attempting to retrieve OAuth endpoints from ${t}`);try{const r=await this.networkInterface.sendGetRequestAsync(t,e),n=function(e){return e.hasOwnProperty("authorization_endpoint")&&e.hasOwnProperty("token_endpoint")&&e.hasOwnProperty("issuer")&&e.hasOwnProperty("jwks_uri")}
/*! @azure/msal-common v14.16.1 2025-08-05 */(r.body);return n?r.body:(this.logger.verbose("Authority.getEndpointMetadataFromNetwork: could not parse response as OpenID configuration"),null)}catch(e){return this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: ${e}`),null}}getEndpointMetadataFromHardcodedValues(){return this.hostnameAndPort in Br?Br[this.hostnameAndPort]:null}async updateMetadataWithRegionalInformation(e){this.performanceClient?.addQueueMeasurement(vn.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId);const r=this.authorityOptions.azureRegionConfiguration?.azureRegion;if(r){if(r!==t.AZURE_REGION_AUTO_DISCOVER_FLAG)return this.regionDiscoveryMetadata.region_outcome=le,this.regionDiscoveryMetadata.region_used=r,En.replaceWithRegionalInformation(e,r);const n=await Sn(this.regionDiscovery.detectRegion.bind(this.regionDiscovery),vn.RegionDiscoveryDetectRegion,this.logger,this.performanceClient,this.correlationId)(this.authorityOptions.azureRegionConfiguration?.environmentRegion,this.regionDiscoveryMetadata);if(n)return this.regionDiscoveryMetadata.region_outcome=de,this.regionDiscoveryMetadata.region_used=n,En.replaceWithRegionalInformation(e,n);this.regionDiscoveryMetadata.region_outcome=he}return e}async updateCloudDiscoveryMetadata(e){this.performanceClient?.addQueueMeasurement(vn.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId);const t=this.updateCloudDiscoveryMetadataFromLocalSources(e);if(t)return t;const r=await Sn(this.getCloudDiscoveryMetadataFromNetwork.bind(this),vn.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return Qt(e,r,!0),Q;throw Ir(cr)}updateCloudDiscoveryMetadataFromLocalSources(e){this.logger.verbose("Attempting to get cloud discovery metadata  from authority configuration"),this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities||t.NOT_APPLICABLE}`),this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata||t.NOT_APPLICABLE}`),this.logger.verbosePii(`Canonical Authority: ${e.canonical_authority||t.NOT_APPLICABLE}`);const r=this.getCloudDiscoveryMetadataFromConfig();if(r)return this.logger.verbose("Found cloud discovery metadata in authority configuration"),Qt(e,r,!1),$;if(this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the hardcoded values."),this.options.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded cloud discovery metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get cloud discovery metadata from the network metadata cache.");else{const t=(n=this.hostnameAndPort,Kr(xr.metadata,n));if(t)return this.logger.verbose("Found cloud discovery metadata from hardcoded values."),Qt(e,t,!1),W;this.logger.verbose("Did not find cloud discovery metadata in hardcoded values... Attempting to get cloud discovery metadata from the network metadata cache.")}var n;const o=Wt(e);return this.isAuthoritySameType(e)&&e.aliasesFromNetwork&&!o?(this.logger.verbose("Found cloud discovery metadata in the cache."),G):(o&&this.logger.verbose("The metadata entity is expired."),null)}getCloudDiscoveryMetadataFromConfig(){if(this.authorityType===Mr)return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."),En.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);if(this.authorityOptions.cloudDiscoveryMetadata){this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");try{this.logger.verbose("Attempting to parse the cloud discovery metadata.");const e=Kr(JSON.parse(this.authorityOptions.cloudDiscoveryMetadata).metadata,this.hostnameAndPort);if(this.logger.verbose("Parsed the cloud discovery metadata."),e)return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."),e;this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.")}catch(e){throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."),Ir(ar)}}return this.isInKnownAuthorities()?(this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."),En.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)):null}async getCloudDiscoveryMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(vn.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId);const e=`${t.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`,r={};let n=null;try{const o=await this.networkInterface.sendGetRequestAsync(e,r);let i,a;if(function(e){return e.hasOwnProperty("tenant_discovery_endpoint")&&e.hasOwnProperty("metadata")}
/*! @azure/msal-common v14.16.1 2025-08-05 */(o.body))i=o.body,a=i.metadata,this.logger.verbosePii(`tenant_discovery_endpoint is: ${i.tenant_discovery_endpoint}`);else{if(!function(e){return e.hasOwnProperty("error")&&e.hasOwnProperty("error_description")}(o.body))return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"),null;if(this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${o.status}`),i=o.body,i.error===t.INVALID_INSTANCE)return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."),null;this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${i.error}`),this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${i.error_description}`),this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"),a=[]}this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."),n=Kr(a,this.hostnameAndPort)}catch(e){if(e instanceof Ae)this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.\nError: ${e.errorCode}\nError Description: ${e.errorMessage}`);else{const t=e;this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.\nError: ${t.name}\nError Description: ${t.message}`)}return null}return n||(this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."),this.logger.verbose("Creating custom Authority for custom domain scenario."),n=En.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),n}isInKnownAuthorities(){return this.authorityOptions.knownAuthorities.filter((e=>e&&Hr.getDomainFromUrl(e).toLowerCase()===this.hostnameAndPort)).length>0}static generateAuthority(e,r){let n;if(r&&r.azureCloudInstance!==St.None){const e=r.tenant?r.tenant:t.DEFAULT_COMMON_TENANT;n=`${r.azureCloudInstance}/${e}/`}return n||e}static createCloudDiscoveryMetadataFromHost(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}}getPreferredCache(){if(this.managedIdentity)return t.DEFAULT_AUTHORITY_HOST;if(this.discoveryComplete())return this.metadata.preferred_cache;throw vt(_e)}isAlias(e){return this.metadata.aliases.indexOf(e)>-1}isAliasOfKnownMicrosoftAuthority(e){return Dr.has(e)}static isPublicCloudAuthority(e){return t.KNOWN_PUBLIC_CLOUDS.indexOf(e)>=0}static buildRegionalAuthorityString(e,r,n){const o=new Hr(e);o.validateAsUri();const i=o.getUrlComponents();let a=`${r}.${i.HostNameAndPort}`;this.isPublicCloudAuthority(i.HostNameAndPort)&&(a=`${r}.${t.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`);const s=Hr.constructAuthorityUriFromObject({...o.getUrlComponents(),HostNameAndPort:a}).urlString;return n?`${s}?${n}`:s}static replaceWithRegionalInformation(e,t){const r={...e};return r.authorization_endpoint=En.buildRegionalAuthorityString(r.authorization_endpoint,t),r.token_endpoint=En.buildRegionalAuthorityString(r.token_endpoint,t),r.end_session_endpoint&&(r.end_session_endpoint=En.buildRegionalAuthorityString(r.end_session_endpoint,t)),r}static transformCIAMAuthority(e){let r=e;const n=new Hr(e).getUrlComponents();if(0===n.PathSegments.length&&n.HostNameAndPort.endsWith(t.CIAM_AUTH_URL)){r=`${r}${n.HostNameAndPort.split(".")[0]}${t.AAD_TENANT_DOMAIN_SUFFIX}`}return r}}function Rn(e){return e.endsWith(t.FORWARD_SLASH)?e:`${e}${t.FORWARD_SLASH}`}function _n(e){const t=e.cloudDiscoveryMetadata;let r;if(t)try{r=JSON.parse(t)}catch(e){throw Ir(ar)}return{canonicalAuthority:e.authority?Rn(e.authority):void 0,knownAuthorities:e.knownAuthorities,cloudDiscoveryMetadata:r}}
/*! @azure/msal-common v14.16.1 2025-08-05 */async function Pn(e,t,r,n,o,i,a){a?.addQueueMeasurement(vn.AuthorityFactoryCreateDiscoveredInstance,i);const s=En.transformCIAMAuthority(Rn(e)),c=new En(s,t,r,n,o,i,a);try{return await Sn(c.resolveEndpointsAsync.bind(c),vn.AuthorityResolveEndpointsAsync,o,a,i)(),c}catch(e){throw vt(_e)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */En.reservedTenantDomains=new Set(["{tenant}","{tenantid}",A,S,k]);class Mn extends Ae{constructor(e,t,r,n,o){super(e,t,r),this.name="ServerError",this.errorNo=n,this.status=o,Object.setPrototypeOf(this,Mn.prototype)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Nn{static generateThrottlingStorageKey(e){return`${X}.${JSON.stringify(e)}`}static preProcess(e,r,n){const o=Nn.generateThrottlingStorageKey(r),i=e.getThrottlingCache(o);if(i){if(i.throttleTime<Date.now())return void e.removeItem(o,n);throw new Mn(i.errorCodes?.join(" ")||t.EMPTY_STRING,i.errorMessage,i.subError)}}static postProcess(e,t,r,n){if(Nn.checkResponseStatus(r)||Nn.checkResponseForRetryAfter(r)){const o={throttleTime:Nn.calculateThrottleTime(parseInt(r.headers[d])),error:r.body.error,errorCodes:r.body.error_codes,errorMessage:r.body.error_description,subError:r.body.suberror};e.setThrottlingCache(Nn.generateThrottlingStorageKey(t),o,n)}}static checkResponseStatus(e){return 429===e.status||e.status>=500&&e.status<600}static checkResponseForRetryAfter(e){return!!e.headers&&(e.headers.hasOwnProperty(d)&&(e.status<200||e.status>=300))}static calculateThrottleTime(e){const t=e<=0?0:e,r=Date.now()/1e3;return Math.floor(1e3*Math.min(r+(t||Y),r+J))}static removeThrottle(e,t,r,n){const o={clientId:t,authority:r.authority,scopes:r.scopes,homeAccountIdentifier:n,claims:r.claims,authenticationScheme:r.authenticationScheme,resourceRequestMethod:r.resourceRequestMethod,resourceRequestUri:r.resourceRequestUri,shrClaims:r.shrClaims,sshKid:r.sshKid},i=this.generateThrottlingStorageKey(o);e.removeItem(i,r.correlationId)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class On extends Ae{constructor(e,t,r){super(e.errorCode,e.errorMessage,e.subError),Object.setPrototypeOf(this,On.prototype),this.name="NetworkError",this.error=e,this.httpStatus=t,this.responseHeaders=r}}function qn(e,t,r){return new On(e,t,r)}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Un{constructor(e,t){this.config=function({authOptions:e,systemOptions:t,loggerOptions:r,cacheOptions:n,storageInterface:o,networkInterface:i,cryptoInterface:a,clientCredentials:s,libraryInfo:c,telemetry:l,serverTelemetryManager:d,persistencePlugin:h,serializableCache:u}){const g={...Jr,...r};return{authOptions:(p=e,{clientCapabilities:[],azureCloudOptions:rn,skipAuthorityMetadataCache:!1,instanceAware:!1,...p}),systemOptions:{...Yr,...t},loggerOptions:g,cacheOptions:{...Xr,...n},storageInterface:o||new Vr(e.clientId,It,new wt(g)),networkInterface:i||Zr,cryptoInterface:a||It,clientCredentials:s||tn,libraryInfo:{...en,...c},telemetry:{...nn,...l},serverTelemetryManager:d||null,persistencePlugin:h||null,serializableCache:u||null};var p}(e),this.logger=new wt(this.config.loggerOptions,At,kt),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority,this.performanceClient=t}createTokenRequestHeaders(e){const r={};if(r[c]=t.URL_FORM_CONTENT_TYPE,!this.config.systemOptions.preventCorsPreflight&&e)switch(e.type){case an:try{const t=kr(e.credential);r[h]=`Oid:${t.uid}@${t.utid}`}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case sn:r[h]=`UPN: ${e.credential}`}return r}async executePostToTokenEndpoint(e,t,r,n,o,i){i&&this.performanceClient?.addQueueMeasurement(i,o);const a=await this.sendPostRequest(n,e,{body:t,headers:r},o);return this.config.serverTelemetryManager&&a.status<500&&429!==a.status&&this.config.serverTelemetryManager.clearTelemetryCache(),a}async sendPostRequest(e,t,r,n){let o;Nn.preProcess(this.cacheManager,e,n);try{o=await Sn(this.networkClient.sendPostRequestAsync.bind(this.networkClient),vn.NetworkClientSendPostRequestAsync,this.logger,this.performanceClient,n)(t,r);const e=o.headers||{};this.performanceClient?.addFields({refreshTokenSize:o.body.refresh_token?.length||0,httpVerToken:e[m]||"",requestId:e[p]||""},n)}catch(e){if(e instanceof On){const t=e.responseHeaders;throw t&&this.performanceClient?.addFields({httpVerToken:t[m]||"",requestId:t[p]||"",contentTypeHeader:t[c]||void 0,contentLengthHeader:t[l]||void 0,httpStatus:e.httpStatus},n),e.error}throw e instanceof Ae?e:vt(Pe)}return Nn.postProcess(this.cacheManager,e,o,n),o}async updateAuthority(e,t){this.performanceClient?.addQueueMeasurement(vn.UpdateTokenEndpointAuthority,t);const r=`https://${e}/${this.authority.tenant}/`,n=await Pn(r,this.networkClient,this.cacheManager,this.authority.options,this.logger,t,this.performanceClient);this.authority=n}createTokenQueryParameters(e){const t=new yn(e.correlationId,this.performanceClient);return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.addCorrelationId(e.correlationId),t.createQueryString()}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Ln="no_tokens_found",Hn="native_account_unavailable",Bn="refresh_token_expired",xn="interaction_required",Dn="consent_required",Fn="login_required",Kn="bad_token";var zn=Object.freeze({__proto__:null,badToken:Kn,consentRequired:Dn,interactionRequired:xn,loginRequired:Fn,nativeAccountUnavailable:Hn,noTokensFound:Ln,refreshTokenExpired:Bn});
/*! @azure/msal-common v14.16.1 2025-08-05 */const $n=[xn,Dn,Fn,Kn],Gn=["message_only","additional_action","basic_action","user_password_expired","consent_required","bad_token"],Qn={[Ln]:"No refresh token found in the cache. Please sign-in.",[Hn]:"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.",[Bn]:"Refresh token has expired.",[Kn]:"Identity provider returned bad_token due to an expired or invalid refresh token. Please invoke an interactive API to resolve."},Wn={noTokensFoundError:{code:Ln,desc:Qn[Ln]},native_account_unavailable:{code:Hn,desc:Qn[Hn]},bad_token:{code:Kn,desc:Qn[Kn]}};class jn extends Ae{constructor(e,r,n,o,i,a,s,c){super(e,r,n),Object.setPrototypeOf(this,jn.prototype),this.timestamp=o||t.EMPTY_STRING,this.traceId=i||t.EMPTY_STRING,this.correlationId=a||t.EMPTY_STRING,this.claims=s||t.EMPTY_STRING,this.name="InteractionRequiredAuthError",this.errorNo=c}}function Vn(e,t,r){const n=!!e&&$n.indexOf(e)>-1,o=!!r&&Gn.indexOf(r)>-1,i=!!t&&$n.some((e=>t.indexOf(e)>-1));return n||i||o}function Yn(e){return new jn(e,Qn[e])}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Jn{static setRequestState(e,r,n){const o=Jn.generateLibraryState(e,n);return r?`${o}${t.RESOURCE_DELIM}${r}`:o}static generateLibraryState(e,t){if(!e)throw vt(Ze);const r={id:e.createNewGuid()};t&&(r.meta=t);const n=JSON.stringify(r);return e.base64Encode(n)}static parseRequestState(e,r){if(!e)throw vt(Ze);if(!r)throw vt(Oe);try{const n=r.split(t.RESOURCE_DELIM),o=n[0],i=n.length>1?n.slice(1).join(t.RESOURCE_DELIM):t.EMPTY_STRING,a=e.base64Decode(o),s=JSON.parse(a);return{userRequestState:i||t.EMPTY_STRING,libraryState:s}}catch(e){throw vt(Oe)}}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const Xn="sw";class Zn{constructor(e,t){this.cryptoUtils=e,this.performanceClient=t}async generateCnf(e,t){this.performanceClient?.addQueueMeasurement(vn.PopTokenGenerateCnf,e.correlationId);const r=await Sn(this.generateKid.bind(this),vn.PopTokenGenerateCnf,t,this.performanceClient,e.correlationId)(e),n=this.cryptoUtils.base64UrlEncode(JSON.stringify(r));return{kid:r.kid,reqCnfString:n}}async generateKid(e){this.performanceClient?.addQueueMeasurement(vn.PopTokenGenerateKid,e.correlationId);return{kid:await this.cryptoUtils.getPublicKeyThumbprint(e),xms_ksl:Xn}}async signPopToken(e,t,r){return this.signPayload(e,t,r)}async signPayload(e,t,r,n){const{resourceRequestMethod:o,resourceRequestUri:i,shrClaims:a,shrNonce:s,shrOptions:c}=r,l=i?new Hr(i):void 0,d=l?.getUrlComponents();return this.cryptoUtils.signJwt({at:e,ts:Rt(),m:o?.toUpperCase(),u:d?.HostNameAndPort,nonce:s||this.cryptoUtils.createNewGuid(),p:d?.AbsolutePath,q:d?.QueryString?[[],d.QueryString]:void 0,client_claims:a||void 0,...n},t,c,r.correlationId)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class eo{constructor(e,t){this.cache=e,this.hasChanged=t}get cacheHasChanged(){return this.hasChanged}get tokenCache(){return this.cache}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class to{constructor(e,t,r,n,o,i,a){this.clientId=e,this.cacheStorage=t,this.cryptoObj=r,this.logger=n,this.serializableCache=o,this.persistencePlugin=i,this.performanceClient=a}validateServerAuthorizationCodeResponse(e,t){if(!e.state||!t)throw e.state?vt(Ue,"Cached State"):vt(Ue,"Server State");let r,n;try{r=decodeURIComponent(e.state)}catch(t){throw vt(Oe,e.state)}try{n=decodeURIComponent(t)}catch(t){throw vt(Oe,e.state)}if(r!==n)throw vt(qe);if(e.error||e.error_description||e.suberror){const t=function(e){const t="code=",r=e.error_uri?.lastIndexOf(t);return r&&r>=0?e.error_uri?.substring(r+5):void 0}(e);if(Vn(e.error,e.error_description,e.suberror))throw new jn(e.error||"",e.error_description,e.suberror,e.timestamp||"",e.trace_id||"",e.correlation_id||"",e.claims||"",t);throw new Mn(e.error||"",e.error_description,e.suberror,t)}}validateTokenResponse(e,a){if(e.error||e.error_description||e.suberror){const s=`Error(s): ${e.error_codes||t.NOT_AVAILABLE} - Timestamp: ${e.timestamp||t.NOT_AVAILABLE} - Description: ${e.error_description||t.NOT_AVAILABLE} - Correlation ID: ${e.correlation_id||t.NOT_AVAILABLE} - Trace ID: ${e.trace_id||t.NOT_AVAILABLE}`,c=e.error_codes?.length?e.error_codes[0]:void 0,l=new Mn(e.error,s,e.suberror,c,e.status);if(a&&e.status&&e.status>=o&&e.status<=i)return void this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently unavailable and the access token is unable to be refreshed.\n${l}`);if(a&&e.status&&e.status>=r&&e.status<=n)return void this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently available but is unable to refresh the access token.\n${l}`);if(Vn(e.error,e.error_description,e.suberror))throw new jn(e.error,e.error_description,e.suberror,e.timestamp||t.EMPTY_STRING,e.trace_id||t.EMPTY_STRING,e.correlation_id||t.EMPTY_STRING,e.claims||t.EMPTY_STRING,c);throw l}}async handleServerTokenResponse(e,r,n,o,i,a,s,c,l){let d,h;if(this.performanceClient?.addQueueMeasurement(vn.HandleServerTokenResponse,e.correlation_id),e.id_token){if(d=bt(e.id_token||t.EMPTY_STRING,this.cryptoObj.base64Decode),i&&i.nonce&&d.nonce!==i.nonce)throw vt(Le);if(o.maxAge||0===o.maxAge){const e=d.auth_time;if(!e)throw vt(He);Et(e,o.maxAge)}}this.homeAccountIdentifier=qr.generateHomeAccountId(e.client_info||t.EMPTY_STRING,r.authorityType,this.logger,this.cryptoObj,d),i&&i.state&&(h=Jn.parseRequestState(this.cryptoObj,i.state)),e.key_id=e.key_id||o.sshKid||void 0;const u=this.generateCacheRecord(e,r,n,o,d,a,i);let g;try{if(this.persistencePlugin&&this.serializableCache&&(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),g=new eo(this.serializableCache,!0),await this.persistencePlugin.beforeCacheAccess(g)),s&&!c&&u.account){const e=u.account.generateAccountKey();if(!this.cacheStorage.getAccount(e,o.correlationId,this.logger))return this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),await to.generateAuthenticationResult(this.cryptoObj,r,u,!1,o,d,h,void 0,l)}await this.cacheStorage.saveCacheRecord(u,o.correlationId,o.storeInCache)}finally{this.persistencePlugin&&this.serializableCache&&g&&(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),await this.persistencePlugin.afterCacheAccess(g))}return to.generateAuthenticationResult(this.cryptoObj,r,u,!1,o,d,h,e,l)}generateCacheRecord(e,t,r,n,o,i,a){const s=t.getPreferredCache();if(!s)throw vt(Je);const c=Nr(o);let l,d;e.id_token&&o&&(l=Nt(this.homeAccountIdentifier,s,e.id_token,this.clientId,c||""),d=ro(this.cacheStorage,t,this.homeAccountIdentifier,this.cryptoObj.base64Decode,n.correlationId,o,e.client_info,s,c,a,void 0,this.logger));let h=null;if(e.access_token){const o=e.scope?wr.fromString(e.scope):new wr(n.scopes||[]),a=("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0,l=("string"==typeof e.ext_expires_in?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0,d=("string"==typeof e.refresh_in?parseInt(e.refresh_in,10):e.refresh_in)||void 0,u=r+a,g=u+l,p=d&&d>0?r+d:void 0;h=Ot(this.homeAccountIdentifier,s,e.access_token,this.clientId,c||t.tenant||"",o.printScopes(),u,g,this.cryptoObj.base64Decode,p,e.token_type,i,e.key_id,n.claims,n.requestedClaimsHash)}let u=null;if(e.refresh_token){let t;if(e.refresh_token_expires_in){t=r+("string"==typeof e.refresh_token_expires_in?parseInt(e.refresh_token_expires_in,10):e.refresh_token_expires_in)}u=qt(this.homeAccountIdentifier,s,e.refresh_token,this.clientId,e.foci,i,t)}let g=null;return e.foci&&(g={clientId:this.clientId,environment:s,familyId:e.foci}),{account:d,idToken:l,accessToken:h,refreshToken:u,appMetadata:g}}static async generateAuthenticationResult(e,r,n,o,i,a,s,c,l){let d,h,u=t.EMPTY_STRING,g=[],p=null,m=t.EMPTY_STRING;if(n.accessToken){if(n.accessToken.tokenType!==V.POP||i.popKid)u=n.accessToken.secret;else{const t=new Zn(e),{secret:r,keyId:o}=n.accessToken;if(!o)throw vt(lt);u=await t.signPopToken(r,o,i)}g=wr.fromString(n.accessToken.target).asArray(),p=new Date(1e3*Number(n.accessToken.expiresOn)),d=new Date(1e3*Number(n.accessToken.extendedExpiresOn)),n.accessToken.refreshOn&&(h=new Date(1e3*Number(n.accessToken.refreshOn)))}n.appMetadata&&(m=n.appMetadata.familyId===F?F:"");const f=a?.oid||a?.sub||"",C=a?.tid||"";c?.spa_accountid&&n.account&&(n.account.nativeAccountId=c?.spa_accountid);const y=n.account?Er(n.account.getAccountInfo(),void 0,a,n.idToken?.secret):null;return{authority:r.canonicalAuthority,uniqueId:f,tenantId:C,scopes:g,account:y,idToken:n?.idToken?.secret||"",idTokenClaims:a||{},accessToken:u,fromCache:o,expiresOn:p,extExpiresOn:d,refreshOn:h,correlationId:i.correlationId,requestId:l||t.EMPTY_STRING,familyId:m,tokenType:n.accessToken?.tokenType||t.EMPTY_STRING,state:s?s.userRequestState:t.EMPTY_STRING,cloudGraphHostName:n.account?.cloudGraphHostName||t.EMPTY_STRING,msGraphHost:n.account?.msGraphHost||t.EMPTY_STRING,code:c?.spa_code,fromNativeBroker:!1}}}function ro(e,t,r,n,o,i,a,s,c,l,d,h){h?.verbose("setCachedAccount called");const u=e.getAccountKeys().find((e=>e.startsWith(r)));let g=null;u&&(g=e.getAccount(u,o,h));const p=g||qr.createAccount({homeAccountId:r,idTokenClaims:i,clientInfo:a,environment:s,cloudGraphHostName:l?.cloud_graph_host_name,msGraphHost:l?.msgraph_host,nativeAccountId:d},t,n),m=p.tenantProfiles||[],f=c||p.realm;if(f&&!m.find((e=>e.tenantId===f))){const e=br(r,p.localAccountId,f,i);m.push(e)}return p.tenantProfiles=m,p}
/*! @azure/msal-common v14.16.1 2025-08-05 */async function no(e,t,r){if("string"==typeof e)return e;return e({clientId:t,tokenEndpoint:r})}
/*! @azure/msal-common v14.16.1 2025-08-05 */class oo extends Un{constructor(e,t){super(e,t),this.includeRedirectUri=!0,this.oidcDefaultScopes=this.config.authOptions.authority.options.OIDCOptions?.defaultScopes}async getAuthCodeUrl(e){this.performanceClient?.addQueueMeasurement(vn.GetAuthCodeUrl,e.correlationId);const t=await Sn(this.createAuthCodeUrlQueryString.bind(this),vn.AuthClientCreateQueryString,this.logger,this.performanceClient,e.correlationId)(e);return Hr.appendQueryString(this.authority.authorizationEndpoint,t)}async acquireToken(e,t){if(this.performanceClient?.addQueueMeasurement(vn.AuthClientAcquireToken,e.correlationId),!e.code)throw vt(Ke);const r=Rt(),n=await Sn(this.executeTokenRequest.bind(this),vn.AuthClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(this.authority,e),o=n.headers?.[p],i=new to(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin,this.performanceClient);return i.validateTokenResponse(n.body),Sn(i.handleServerTokenResponse.bind(i),vn.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(n.body,this.authority,r,e,t,void 0,void 0,void 0,o)}handleFragmentResponse(e,t){if(new to(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,null,null).validateServerAuthorizationCodeResponse(e,t),!e.code)throw vt(at);return e}getLogoutUri(e){if(!e)throw Ir(nr);const t=this.createLogoutUrlQueryString(e);return Hr.appendQueryString(this.authority.endSessionEndpoint,t)}async executeTokenRequest(e,t){this.performanceClient?.addQueueMeasurement(vn.AuthClientExecuteTokenRequest,t.correlationId);const r=this.createTokenQueryParameters(t),n=Hr.appendQueryString(e.tokenEndpoint,r),o=await Sn(this.createTokenRequestBody.bind(this),vn.AuthClientCreateTokenRequestBody,this.logger,this.performanceClient,t.correlationId)(t);let i;if(t.clientInfo)try{const e=Ar(t.clientInfo,this.cryptoUtils.base64Decode);i={credential:`${e.uid}${B}${e.utid}`,type:an}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}const a=this.createTokenRequestHeaders(i||t.ccsCredential),s={clientId:t.tokenBodyParameters?.clientId||this.config.authOptions.clientId,authority:e.canonicalAuthority,scopes:t.scopes,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid};return Sn(this.executePostToTokenEndpoint.bind(this),vn.AuthorizationCodeClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,t.correlationId)(n,o,a,s,t.correlationId,vn.AuthorizationCodeClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){this.performanceClient?.addQueueMeasurement(vn.AuthClientCreateTokenRequestBody,e.correlationId);const t=new yn(e.correlationId,this.performanceClient);if(t.addClientId(e.embeddedClientId||e.tokenBodyParameters?.[cn]||this.config.authOptions.clientId),this.includeRedirectUri?t.addRedirectUri(e.redirectUri):Cn.validateRedirectUri(e.redirectUri),t.addScopes(e.scopes,!0,this.oidcDefaultScopes),t.addAuthorizationCode(e.code),t.addLibraryInfo(this.config.libraryInfo),t.addApplicationTelemetry(this.config.telemetry.application),t.addThrottling(),this.serverTelemetryManager&&!on(this.config)&&t.addServerTelemetry(this.serverTelemetryManager),e.codeVerifier&&t.addCodeVerifier(e.codeVerifier),this.config.clientCredentials.clientSecret&&t.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const r=this.config.clientCredentials.clientAssertion;t.addClientAssertion(await no(r.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),t.addClientAssertionType(r.assertionType)}if(t.addGrantType(N),t.addClientInfo(),e.authenticationScheme===V.POP){const r=new Zn(this.cryptoUtils,this.performanceClient);let n;if(e.popKid)n=this.cryptoUtils.encodeKid(e.popKid);else{n=(await Sn(r.generateCnf.bind(r),vn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString}t.addPopToken(n)}else if(e.authenticationScheme===V.SSH){if(!e.sshJwk)throw Ir(lr);t.addSshJwk(e.sshJwk)}let r;if((!Tr.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&t.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.clientInfo)try{const t=Ar(e.clientInfo,this.cryptoUtils.base64Decode);r={credential:`${t.uid}${B}${t.utid}`,type:an}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}else r=e.ccsCredential;if(this.config.systemOptions.preventCorsPreflight&&r)switch(r.type){case an:try{const e=kr(r.credential);t.addCcsOid(e)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case sn:t.addCcsUpn(r.credential)}return e.embeddedClientId&&t.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&t.addExtraQueryParameters(e.tokenBodyParameters),!e.enableSpaAuthorizationCode||e.tokenBodyParameters&&e.tokenBodyParameters[gn]||t.addExtraQueryParameters({[gn]:"1"}),t.createQueryString()}async createAuthCodeUrlQueryString(e){const t=e.correlationId||this.config.cryptoInterface.createNewGuid();this.performanceClient?.addQueueMeasurement(vn.AuthClientCreateQueryString,t);const r=new yn(t,this.performanceClient);r.addClientId(e.embeddedClientId||e.extraQueryParameters?.[cn]||this.config.authOptions.clientId);const n=[...e.scopes||[],...e.extraScopesToConsent||[]];if(r.addScopes(n,!0,this.oidcDefaultScopes),r.addRedirectUri(e.redirectUri),r.addCorrelationId(t),r.addResponseMode(e.responseMode),r.addResponseTypeCode(),r.addLibraryInfo(this.config.libraryInfo),on(this.config)||r.addApplicationTelemetry(this.config.telemetry.application),r.addClientInfo(),e.codeChallenge&&e.codeChallengeMethod&&r.addCodeChallengeParams(e.codeChallenge,e.codeChallengeMethod),e.prompt&&r.addPrompt(e.prompt),e.domainHint&&r.addDomainHint(e.domainHint),e.prompt!==R.SELECT_ACCOUNT)if(e.sid&&e.prompt===R.NONE)this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),r.addSid(e.sid);else if(e.account){const t=this.extractAccountSid(e.account);let n=this.extractLoginHint(e.account);if(n&&e.domainHint&&(this.logger.warning('AuthorizationCodeClient.createAuthCodeUrlQueryString: "domainHint" param is set, skipping opaque "login_hint" claim. Please consider not passing domainHint'),n=null),n){this.logger.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"),r.addLoginHint(n);try{const t=kr(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(t&&e.prompt===R.NONE){this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),r.addSid(t);try{const t=kr(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(e.loginHint)this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),r.addLoginHint(e.loginHint),r.addCcsUpn(e.loginHint);else if(e.account.username){this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),r.addLoginHint(e.account.username);try{const t=kr(e.account.homeAccountId);r.addCcsOid(t)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}}else e.loginHint&&(this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),r.addLoginHint(e.loginHint),r.addCcsUpn(e.loginHint));else this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");if(e.nonce&&r.addNonce(e.nonce),e.state&&r.addState(e.state),(e.claims||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(e.claims,this.config.authOptions.clientCapabilities),e.embeddedClientId&&r.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),this.addExtraQueryParams(e,r),e.nativeBroker&&(r.addNativeBroker(),e.authenticationScheme===V.POP)){const t=new Zn(this.cryptoUtils);let n;if(e.popKid)n=this.cryptoUtils.encodeKid(e.popKid);else{n=(await Sn(t.generateCnf.bind(t),vn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString}r.addPopToken(n)}return r.createQueryString()}createLogoutUrlQueryString(e){const t=new yn(e.correlationId,this.performanceClient);return e.postLogoutRedirectUri&&t.addPostLogoutRedirectUri(e.postLogoutRedirectUri),e.correlationId&&t.addCorrelationId(e.correlationId),e.idTokenHint&&t.addIdTokenHint(e.idTokenHint),e.state&&t.addState(e.state),e.logoutHint&&t.addLogoutHint(e.logoutHint),this.addExtraQueryParams(e,t),t.createQueryString()}addExtraQueryParams(e,t){!(e.extraQueryParameters&&e.extraQueryParameters.hasOwnProperty("instance_aware"))&&this.config.authOptions.instanceAware&&(e.extraQueryParameters=e.extraQueryParameters||{},e.extraQueryParameters.instance_aware="true"),e.extraQueryParameters&&t.addExtraQueryParameters(e.extraQueryParameters)}extractAccountSid(e){return e.idTokenClaims?.sid||null}extractLoginHint(e){return e.idTokenClaims?.login_hint||null}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class io extends Un{constructor(e,t){super(e,t)}async acquireToken(e){this.performanceClient?.addQueueMeasurement(vn.RefreshTokenClientAcquireToken,e.correlationId);const t=Rt(),r=await Sn(this.executeTokenRequest.bind(this),vn.RefreshTokenClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(e,this.authority),n=r.headers?.[p],o=new to(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin);return o.validateTokenResponse(r.body),Sn(o.handleServerTokenResponse.bind(o),vn.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(r.body,this.authority,t,e,void 0,void 0,!0,e.forceCache,n)}async acquireTokenByRefreshToken(e){if(!e)throw Ir(rr);if(this.performanceClient?.addQueueMeasurement(vn.RefreshTokenClientAcquireTokenByRefreshToken,e.correlationId),!e.account)throw vt(Ve);if(this.cacheManager.isAppMetadataFOCI(e.account.environment))try{return await Sn(this.acquireTokenWithCachedRefreshToken.bind(this),vn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!0)}catch(t){const r=t instanceof jn&&t.errorCode===Ln,n=t instanceof Mn&&t.errorCode===ee&&t.subError===te;if(r||n)return Sn(this.acquireTokenWithCachedRefreshToken.bind(this),vn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1);throw t}return Sn(this.acquireTokenWithCachedRefreshToken.bind(this),vn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1)}async acquireTokenWithCachedRefreshToken(e,t){this.performanceClient?.addQueueMeasurement(vn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,e.correlationId);const r=kn(this.cacheManager.getRefreshToken.bind(this.cacheManager),vn.CacheManagerGetRefreshToken,this.logger,this.performanceClient,e.correlationId)(e.account,t,e.correlationId,void 0,this.performanceClient);if(!r)throw Yn(Ln);if(r.expiresOn&&_t(r.expiresOn,e.refreshTokenExpirationOffsetSeconds||300))throw Yn(Bn);const n={...e,refreshToken:r.secret,authenticationScheme:e.authenticationScheme||V.BEARER,ccsCredential:{credential:e.account.homeAccountId,type:an}};try{return await Sn(this.acquireToken.bind(this),vn.RefreshTokenClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(n)}catch(t){if(t instanceof jn&&t.subError===Kn){this.logger.verbose("acquireTokenWithRefreshToken: bad refresh token, removing from cache");const t=Mt(r);this.cacheManager.removeRefreshToken(t,e.correlationId)}throw t}}async executeTokenRequest(e,t){this.performanceClient?.addQueueMeasurement(vn.RefreshTokenClientExecuteTokenRequest,e.correlationId);const r=this.createTokenQueryParameters(e),n=Hr.appendQueryString(t.tokenEndpoint,r),o=await Sn(this.createTokenRequestBody.bind(this),vn.RefreshTokenClientCreateTokenRequestBody,this.logger,this.performanceClient,e.correlationId)(e),i=this.createTokenRequestHeaders(e.ccsCredential),a={clientId:e.tokenBodyParameters?.clientId||this.config.authOptions.clientId,authority:t.canonicalAuthority,scopes:e.scopes,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid};return Sn(this.executePostToTokenEndpoint.bind(this),vn.RefreshTokenClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,e.correlationId)(n,o,i,a,e.correlationId,vn.RefreshTokenClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){this.performanceClient?.addQueueMeasurement(vn.RefreshTokenClientCreateTokenRequestBody,e.correlationId);const t=e.correlationId,r=new yn(t,this.performanceClient);if(r.addClientId(e.embeddedClientId||e.tokenBodyParameters?.[cn]||this.config.authOptions.clientId),e.redirectUri&&r.addRedirectUri(e.redirectUri),r.addScopes(e.scopes,!0,this.config.authOptions.authority.options.OIDCOptions?.defaultScopes),r.addGrantType(O),r.addClientInfo(),r.addLibraryInfo(this.config.libraryInfo),r.addApplicationTelemetry(this.config.telemetry.application),r.addThrottling(),this.serverTelemetryManager&&!on(this.config)&&r.addServerTelemetry(this.serverTelemetryManager),r.addRefreshToken(e.refreshToken),this.config.clientCredentials.clientSecret&&r.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const t=this.config.clientCredentials.clientAssertion;r.addClientAssertion(await no(t.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),r.addClientAssertionType(t.assertionType)}if(e.authenticationScheme===V.POP){const t=new Zn(this.cryptoUtils,this.performanceClient);let n;if(e.popKid)n=this.cryptoUtils.encodeKid(e.popKid);else{n=(await Sn(t.generateCnf.bind(t),vn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString}r.addPopToken(n)}else if(e.authenticationScheme===V.SSH){if(!e.sshJwk)throw Ir(lr);r.addSshJwk(e.sshJwk)}if((!Tr.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&r.addClaims(e.claims,this.config.authOptions.clientCapabilities),this.config.systemOptions.preventCorsPreflight&&e.ccsCredential)switch(e.ccsCredential.type){case an:try{const t=kr(e.ccsCredential.credential);r.addCcsOid(t)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case sn:r.addCcsUpn(e.ccsCredential.credential)}return e.embeddedClientId&&r.addBrokerParameters({brokerClientId:this.config.authOptions.clientId,brokerRedirectUri:this.config.authOptions.redirectUri}),e.tokenBodyParameters&&r.addExtraQueryParameters(e.tokenBodyParameters),r.createQueryString()}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class ao extends Un{constructor(e,t){super(e,t)}async acquireToken(e){try{const[t,r]=await this.acquireCachedToken({...e,scopes:e.scopes?.length?e.scopes:[...a]});if(r===fe){this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'. It's not expired, but must be refreshed.");new io(this.config,this.performanceClient).acquireTokenByRefreshToken(e).catch((()=>{}))}return t}catch(t){if(t instanceof yt&&t.errorCode===nt){return new io(this.config,this.performanceClient).acquireTokenByRefreshToken(e)}throw t}}async acquireCachedToken(e){this.performanceClient?.addQueueMeasurement(vn.SilentFlowClientAcquireCachedToken,e.correlationId);let t=ue;if(e.forceRefresh||!this.config.cacheOptions.claimsBasedCachingEnabled&&!Tr.isEmptyObj(e.claims))throw this.setCacheOutcome(ge,e.correlationId),vt(nt);if(!e.account)throw vt(Ve);const r=e.account.tenantId||function(e){const t=new Hr(e).getUrlComponents(),r=t.PathSegments.slice(-1)[0]?.toLowerCase();switch(r){case A:case k:case S:return;default:return r}}(e.authority),n=this.cacheManager.getTokenKeys(),o=this.cacheManager.getAccessToken(e.account,e,n,r,this.performanceClient);if(!o)throw this.setCacheOutcome(pe,e.correlationId),vt(nt);if(Pt(o.cachedAt)||_t(o.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))throw this.setCacheOutcome(me,e.correlationId),vt(nt);o.refreshOn&&_t(o.refreshOn,0)&&(t=fe);const i=e.authority||this.authority.getPreferredCache(),a={account:this.cacheManager.readAccountFromCache(e.account,e.correlationId),accessToken:o,idToken:this.cacheManager.getIdToken(e.account,e.correlationId,n,r,this.performanceClient),refreshToken:null,appMetadata:this.cacheManager.readAppMetadataFromCache(i)};return this.setCacheOutcome(t,e.correlationId),this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[await Sn(this.generateResultFromCacheRecord.bind(this),vn.SilentFlowClientGenerateResultFromCacheRecord,this.logger,this.performanceClient,e.correlationId)(a,e),t]}setCacheOutcome(e,t){this.serverTelemetryManager?.setCacheOutcome(e),this.performanceClient?.addFields({cacheOutcome:e},t),e!==ue&&this.logger.info(`Token refresh is required due to cache outcome: ${e}`)}async generateResultFromCacheRecord(e,t){let r;if(this.performanceClient?.addQueueMeasurement(vn.SilentFlowClientGenerateResultFromCacheRecord,t.correlationId),e.idToken&&(r=bt(e.idToken.secret,this.config.cryptoInterface.base64Decode)),t.maxAge||0===t.maxAge){const e=r?.auth_time;if(!e)throw vt(He);Et(e,t.maxAge)}return to.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,r)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const so={sendGetRequestAsync:()=>Promise.reject(vt(gt)),sendPostRequestAsync:()=>Promise.reject(vt(gt))};
/*! @azure/msal-common v14.16.1 2025-08-05 */function co(e){const{skus:t,libraryName:r,libraryVersion:n,extensionName:o,extensionVersion:i}=e,a=new Map([[0,[r,n]],[2,[o,i]]]);let s=[];if(t?.length){if(s=t.split(","),s.length<4)return t}else s=Array.from({length:4},(()=>"|"));return a.forEach(((e,t)=>{2===e.length&&e[0]?.length&&e[1]?.length&&function(e){const{skuArr:t,index:r,skuName:n,skuVersion:o}=e;if(r>=t.length)return;t[r]=[n,o].join("|")}({skuArr:s,index:t,skuName:e[0],skuVersion:e[1]})})),s.join(",")}class lo{constructor(e,r){this.cacheOutcome=ue,this.cacheManager=r,this.apiId=e.apiId,this.correlationId=e.correlationId,this.wrapperSKU=e.wrapperSKU||t.EMPTY_STRING,this.wrapperVer=e.wrapperVer||t.EMPTY_STRING,this.telemetryCacheKey=j.CACHE_KEY+H+e.clientId}generateCurrentRequestHeaderValue(){const e=`${this.apiId}${j.VALUE_SEPARATOR}${this.cacheOutcome}`,t=[this.wrapperSKU,this.wrapperVer],r=this.getNativeBrokerErrorCode();r?.length&&t.push(`broker_error=${r}`);const n=t.join(j.VALUE_SEPARATOR),o=[e,this.getRegionDiscoveryFields()].join(j.VALUE_SEPARATOR);return[j.SCHEMA_VERSION,o,n].join(j.CATEGORY_SEPARATOR)}generateLastRequestHeaderValue(){const e=this.getLastRequests(),t=lo.maxErrorsToSend(e),r=e.failedRequests.slice(0,2*t).join(j.VALUE_SEPARATOR),n=e.errors.slice(0,t).join(j.VALUE_SEPARATOR),o=e.errors.length,i=[o,t<o?j.OVERFLOW_TRUE:j.OVERFLOW_FALSE].join(j.VALUE_SEPARATOR);return[j.SCHEMA_VERSION,e.cacheHits,r,n,i].join(j.CATEGORY_SEPARATOR)}cacheFailedRequest(e){const t=this.getLastRequests();t.errors.length>=j.MAX_CACHED_ERRORS&&(t.failedRequests.shift(),t.failedRequests.shift(),t.errors.shift()),t.failedRequests.push(this.apiId,this.correlationId),e instanceof Error&&e&&e.toString()?e instanceof Ae?e.subError?t.errors.push(e.subError):e.errorCode?t.errors.push(e.errorCode):t.errors.push(e.toString()):t.errors.push(e.toString()):t.errors.push(j.UNKNOWN_ERROR),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t,this.correlationId)}incrementCacheHits(){const e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e,this.correlationId),e.cacheHits}getLastRequests(){return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||{failedRequests:[],errors:[],cacheHits:0}}clearTelemetryCache(){const e=this.getLastRequests(),t=lo.maxErrorsToSend(e);if(t===e.errors.length)this.cacheManager.removeItem(this.telemetryCacheKey,this.correlationId);else{const r={failedRequests:e.failedRequests.slice(2*t),errors:e.errors.slice(t),cacheHits:0};this.cacheManager.setServerTelemetry(this.telemetryCacheKey,r,this.correlationId)}}static maxErrorsToSend(e){let r,n=0,o=0;const i=e.errors.length;for(r=0;r<i;r++){const i=e.failedRequests[2*r]||t.EMPTY_STRING,a=e.failedRequests[2*r+1]||t.EMPTY_STRING,s=e.errors[r]||t.EMPTY_STRING;if(o+=i.toString().length+a.toString().length+s.length+3,!(o<j.MAX_LAST_HEADER_BYTES))break;n+=1}return n}getRegionDiscoveryFields(){const e=[];return e.push(this.regionUsed||t.EMPTY_STRING),e.push(this.regionSource||t.EMPTY_STRING),e.push(this.regionOutcome||t.EMPTY_STRING),e.join(",")}updateRegionDiscoveryMetadata(e){this.regionUsed=e.region_used,this.regionSource=e.region_source,this.regionOutcome=e.region_outcome}setCacheOutcome(e){this.cacheOutcome=e}setNativeBrokerErrorCode(e){const t=this.getLastRequests();t.nativeBrokerErrorCode=e,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t,this.correlationId)}getNativeBrokerErrorCode(){return this.getLastRequests().nativeBrokerErrorCode}clearNativeBrokerErrorCode(){const e=this.getLastRequests();delete e.nativeBrokerErrorCode,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e,this.correlationId)}static makeExtraSkuString(e){return co(e)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */const ho="missing_kid_error",uo="missing_alg_error",go={[ho]:"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.",[uo]:"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."};class po extends Ae{constructor(e,t){super(e,t),this.name="JoseHeaderError",Object.setPrototypeOf(this,po.prototype)}}function mo(e){return new po(e,go[e])}
/*! @azure/msal-common v14.16.1 2025-08-05 */class fo{constructor(e){this.typ=e.typ,this.alg=e.alg,this.kid=e.kid}static getShrHeaderString(e){if(!e.kid)throw mo(ho);if(!e.alg)throw mo(uo);const t=new fo({typ:e.typ||Ce.Pop,kid:e.kid,alg:e.alg});return JSON.stringify(t)}}
/*! @azure/msal-common v14.16.1 2025-08-05 */class Co{startMeasurement(){}endMeasurement(){}flushMeasurement(){return null}}class yo{generateId(){return"callback-id"}startMeasurement(e,t){return{end:()=>null,discard:()=>{},add:()=>{},increment:()=>{},event:{eventId:this.generateId(),status:Tn,authority:"",libraryName:"",libraryVersion:"",clientId:"",name:e,startTimeMs:Date.now(),correlationId:t||""},measurement:new Co}}startPerformanceMeasurement(){return new Co}calculateQueuedTime(){return 0}addQueueMeasurement(){}setPreQueueTime(){}endMeasurement(){return null}discardMeasurements(){}removePerformanceCallback(){return!0}addPerformanceCallback(){return""}emitEvents(){}addFields(){}incrementFields(){}cacheEventByCorrelationId(){}}
/*! @azure/msal-common v14.16.1 2025-08-05 */function vo(e,t,r,n=5){if(e instanceof Error)return e instanceof Ae?(r.errorCode=e.errorCode,r.subErrorCode=e.subError,void((e instanceof Mn||e instanceof jn)&&(r.serverErrorNo=e.errorNo))):void(e instanceof Qr?r.errorCode=e.errorCode:r.errorStack?.length?t.trace("PerformanceClient.addErrorStack: Stack already exist",r.correlationId):e.stack?.length?(e.stack&&(r.errorStack=function(e,t){if(t<0)return[];const r=e.split("\n")||[],n=[],o=r[0];o.startsWith("TypeError: Cannot read property")||o.startsWith("TypeError: Cannot read properties of")||o.startsWith("TypeError: Cannot set property")||o.startsWith("TypeError: Cannot set properties of")||o.endsWith("is not a function")?n.push(Io(o)):(o.startsWith("SyntaxError")||o.startsWith("TypeError"))&&n.push(Io(o.replace(/['].*[']|["].*["]/g,"<redacted>")));for(let e=1;e<r.length&&!(n.length>=t);e++){const t=r[e];n.push(Io(t))}return n}(e.stack,n)),r.errorName=e.name):t.trace("PerformanceClient.addErrorStack: Input stack is empty",r.correlationId));t.trace("PerformanceClient.addErrorStack: Input error is not instance of Error",r.correlationId)}function Io(e){const t=e.lastIndexOf(" ")+1;if(t<1)return e;const r=e.substring(t);let n=r.lastIndexOf("/");return n=n<0?r.lastIndexOf("\\"):n,n>=0?(e.substring(0,t)+"("+r.substring(n+1)+(")"===r.charAt(r.length-1)?"":")")).trimStart():e.trimStart()}class To{constructor(e,t,r,n,o,i,a,s){this.authority=t,this.libraryName=n,this.libraryVersion=o,this.applicationTelemetry=i,this.clientId=e,this.logger=r,this.callbacks=new Map,this.eventsByCorrelationId=new Map,this.eventStack=new Map,this.queueMeasurements=new Map,this.preQueueTimeByCorrelationId=new Map,this.intFields=a||new Set;for(const e of An)this.intFields.add(e);this.abbreviations=s||new Map;for(const[e,t]of In)this.abbreviations.set(e,t)}startPerformanceMeasurement(e,t){return{}}getPreQueueTime(e,t){const r=this.preQueueTimeByCorrelationId.get(t);if(r){if(r.name===e)return r.time;this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue time found for ${e}, unable to add queue measurement`)}else this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue times found for correlationId: ${t}, unable to add queue measurement`)}calculateQueuedTime(e,t){return e<1?(this.logger.trace(`PerformanceClient: preQueueTime should be a positive integer and not ${e}`),0):t<1?(this.logger.trace(`PerformanceClient: currentTime should be a positive integer and not ${t}`),0):t<e?(this.logger.trace("PerformanceClient: currentTime is less than preQueueTime, check how time is being retrieved"),0):t-e}addQueueMeasurement(e,t,r,n){if(!t)return void this.logger.trace(`PerformanceClient.addQueueMeasurement: correlationId not provided for ${e}, cannot add queue measurement`);if(0===r)this.logger.trace(`PerformanceClient.addQueueMeasurement: queue time provided for ${e} is ${r}`);else if(!r)return void this.logger.trace(`PerformanceClient.addQueueMeasurement: no queue time provided for ${e}`);const o={eventName:e,queueTime:n?0:r,manuallyCompleted:n},i=this.queueMeasurements.get(t);if(i)i.push(o),this.queueMeasurements.set(t,i);else{this.logger.trace(`PerformanceClient.addQueueMeasurement: adding correlationId ${t} to queue measurements`);const e=[o];this.queueMeasurements.set(t,e)}this.preQueueTimeByCorrelationId.delete(t)}startMeasurement(e,t){const r=t||this.generateId();t||this.logger.info(`PerformanceClient: No correlation id provided for ${e}, generating`,r),this.logger.trace(`PerformanceClient: Performance measurement started for ${e}`,r);const n={eventId:this.generateId(),status:Tn,authority:this.authority,libraryName:this.libraryName,libraryVersion:this.libraryVersion,clientId:this.clientId,name:e,startTimeMs:Date.now(),correlationId:r,appName:this.applicationTelemetry?.appName,appVersion:this.applicationTelemetry?.appVersion};var o,i,a;return this.cacheEventByCorrelationId(n),o=n,i=this.abbreviations,(a=this.eventStack.get(r))&&a.push({name:i.get(o.name)||o.name}),{end:(e,t)=>this.endMeasurement({...n,...e},t),discard:()=>this.discardMeasurements(n.correlationId),add:e=>this.addFields(e,n.correlationId),increment:e=>this.incrementFields(e,n.correlationId),event:n,measurement:new Co}}endMeasurement(e,t){const r=this.eventsByCorrelationId.get(e.correlationId);if(!r)return this.logger.trace(`PerformanceClient: Measurement not found for ${e.eventId}`,e.correlationId),null;const n=e.eventId===r.eventId;let o={totalQueueTime:0,totalQueueCount:0,manuallyCompletedCount:0};e.durationMs=Math.round(e.durationMs||this.getDurationMs(e.startTimeMs));const i=JSON.stringify(function(e,t,r,n){if(!r?.length)return;const o=e=>e.length?e[e.length-1]:void 0,i=t.get(e.name)||e.name,a=o(r);if(a?.name!==i)return;const s=r?.pop();if(!s)return;const c=n instanceof Ae?n.errorCode:n instanceof Error?n.name:void 0,l=n instanceof Ae?n.subError:void 0;c&&s.childErr!==c&&(s.err=c,l&&(s.subErr=l)),delete s.name,delete s.childErr;const d={...s,dur:e.durationMs};e.success||(d.fail=1);const h=o(r);if(!h)return{[i]:d};let u;if(c&&(h.childErr=c),h[i]){const e=Object.keys(h).filter((e=>e.startsWith(i))).length;u=`${i}_${e+1}`}else u=i;return h[u]=d,h}(e,this.abbreviations,this.eventStack.get(r.correlationId),t));if(n?(o=this.getQueueInfo(e.correlationId),this.discardMeasurements(r.correlationId)):r.incompleteSubMeasurements?.delete(e.eventId),this.logger.trace(`PerformanceClient: Performance measurement ended for ${e.name}: ${e.durationMs} ms`,e.correlationId),t&&vo(t,this.logger,r),!n)return r[e.name+"DurationMs"]=Math.floor(e.durationMs),{...r};n&&!t&&(r.errorCode||r.subErrorCode)&&(this.logger.trace(`PerformanceClient: Remove error and sub-error codes for root event ${e.name} as intermediate error was successfully handled`,e.correlationId),r.errorCode=void 0,r.subErrorCode=void 0);let a={...r,...e},s=0;return a.incompleteSubMeasurements?.forEach((t=>{this.logger.trace(`PerformanceClient: Incomplete submeasurement ${t.name} found for ${e.name}`,a.correlationId),s++})),a.incompleteSubMeasurements=void 0,a={...a,queuedTimeMs:o.totalQueueTime,queuedCount:o.totalQueueCount,queuedManuallyCompletedCount:o.manuallyCompletedCount,status:wn,incompleteSubsCount:s,context:i},this.truncateIntegralFields(a),this.emitEvents([a],e.correlationId),a}addFields(e,t){this.logger.trace("PerformanceClient: Updating static fields");const r=this.eventsByCorrelationId.get(t);r?this.eventsByCorrelationId.set(t,{...r,...e}):this.logger.trace("PerformanceClient: Event not found for",t)}incrementFields(e,t){this.logger.trace("PerformanceClient: Updating counters");const r=this.eventsByCorrelationId.get(t);if(r)for(const t in e){if(r.hasOwnProperty(t)){if(isNaN(Number(r[t])))return}else r[t]=0;r[t]+=e[t]}else this.logger.trace("PerformanceClient: Event not found for",t)}cacheEventByCorrelationId(e){const t=this.eventsByCorrelationId.get(e.correlationId);t?(this.logger.trace(`PerformanceClient: Performance measurement for ${e.name} added/updated`,e.correlationId),t.incompleteSubMeasurements=t.incompleteSubMeasurements||new Map,t.incompleteSubMeasurements.set(e.eventId,{name:e.name,startTimeMs:e.startTimeMs})):(this.logger.trace(`PerformanceClient: Performance measurement for ${e.name} started`,e.correlationId),this.eventsByCorrelationId.set(e.correlationId,{...e}),this.eventStack.set(e.correlationId,[]))}getQueueInfo(e){const t=this.queueMeasurements.get(e);t||this.logger.trace(`PerformanceClient: no queue measurements found for for correlationId: ${e}`);let r=0,n=0,o=0;return t?.forEach((e=>{r+=e.queueTime,n++,o+=e.manuallyCompleted?1:0})),{totalQueueTime:r,totalQueueCount:n,manuallyCompletedCount:o}}discardMeasurements(e){this.logger.trace("PerformanceClient: Performance measurements discarded",e),this.eventsByCorrelationId.delete(e),this.logger.trace("PerformanceClient: QueueMeasurements discarded",e),this.queueMeasurements.delete(e),this.logger.trace("PerformanceClient: Pre-queue times discarded",e),this.preQueueTimeByCorrelationId.delete(e),this.logger.trace("PerformanceClient: Event stack discarded",e),this.eventStack.delete(e)}addPerformanceCallback(e){for(const[t,r]of this.callbacks)if(r.toString()===e.toString())return this.logger.warning(`PerformanceClient: Performance callback is already registered with id: ${t}`),t;const t=this.generateId();return this.callbacks.set(t,e),this.logger.verbose(`PerformanceClient: Performance callback registered with id: ${t}`),t}removePerformanceCallback(e){const t=this.callbacks.delete(e);return t?this.logger.verbose(`PerformanceClient: Performance callback ${e} removed.`):this.logger.verbose(`PerformanceClient: Performance callback ${e} not removed.`),t}emitEvents(e,t){this.logger.verbose("PerformanceClient: Emitting performance events",t),this.callbacks.forEach(((r,n)=>{this.logger.trace(`PerformanceClient: Emitting event to callback ${n}`,t),r.apply(null,[e])}))}truncateIntegralFields(e){this.intFields.forEach((t=>{t in e&&"number"==typeof e[t]&&(e[t]=Math.floor(e[t]))}))}getDurationMs(e){const t=Date.now()-e;return t<0?t:0}}const wo="pkce_not_created",Ao="crypto_nonexistent",ko="empty_navigate_uri",So="hash_empty_error",bo="no_state_in_hash",Eo="hash_does_not_contain_known_properties",Ro="unable_to_parse_state",_o="state_interaction_type_mismatch",Po="interaction_in_progress",Mo="popup_window_error",No="empty_window_error",Oo="user_cancelled",qo="monitor_popup_timeout",Uo="monitor_window_timeout",Lo="redirect_in_iframe",Ho="block_iframe_reload",Bo="block_nested_popups",xo="iframe_closed_prematurely",Do="silent_logout_unsupported",Fo="no_account_error",Ko="silent_prompt_value_error",zo="no_token_request_cache_error",$o="unable_to_parse_token_request_cache_error",Go="no_cached_authority_error",Qo="auth_request_not_set_error",Wo="invalid_cache_type",jo="non_browser_environment",Vo="database_not_open",Yo="no_network_connectivity",Jo="post_request_failed",Xo="get_request_failed",Zo="failed_to_parse_response",ei="unable_to_load_token",ti="crypto_key_not_found",ri="auth_code_required",ni="auth_code_or_nativeAccountId_required",oi="spa_code_and_nativeAccountId_present",ii="database_unavailable",ai="unable_to_acquire_token_from_native_platform",si="native_handshake_timeout",ci="native_extension_not_installed",li="native_connection_not_established",di="uninitialized_public_client_application",hi="native_prompt_not_supported",ui="invalid_base64_string",gi="invalid_pop_token_request",pi="failed_to_build_headers",mi="failed_to_parse_headers";var fi=Object.freeze({__proto__:null,authCodeOrNativeAccountIdRequired:ni,authCodeRequired:ri,authRequestNotSetError:Qo,blockIframeReload:Ho,blockNestedPopups:Bo,cryptoKeyNotFound:ti,cryptoNonExistent:Ao,databaseNotOpen:Vo,databaseUnavailable:ii,emptyNavigateUri:ko,emptyWindowError:No,failedToBuildHeaders:pi,failedToParseHeaders:mi,failedToParseResponse:Zo,getRequestFailed:Xo,hashDoesNotContainKnownProperties:Eo,hashEmptyError:So,iframeClosedPrematurely:xo,interactionInProgress:Po,invalidBase64String:ui,invalidCacheType:Wo,invalidPopTokenRequest:gi,monitorPopupTimeout:qo,monitorWindowTimeout:Uo,nativeConnectionNotEstablished:li,nativeExtensionNotInstalled:ci,nativeHandshakeTimeout:si,nativePromptNotSupported:hi,noAccountError:Fo,noCachedAuthorityError:Go,noNetworkConnectivity:Yo,noStateInHash:bo,noTokenRequestCacheError:zo,nonBrowserEnvironment:jo,pkceNotCreated:wo,popupWindowError:Mo,postRequestFailed:Jo,redirectInIframe:Lo,silentLogoutUnsupported:Do,silentPromptValueError:Ko,spaCodeAndNativeAccountIdPresent:oi,stateInteractionTypeMismatch:_o,unableToAcquireTokenFromNativePlatform:ai,unableToLoadToken:ei,unableToParseState:Ro,unableToParseTokenRequestCacheError:$o,uninitializedPublicClientApplication:di,userCancelled:Oo});const Ci="For more visit: aka.ms/msaljs/browser-errors",yi={[wo]:"The PKCE code challenge and verifier could not be generated.",[Ao]:"The crypto object or function is not available.",[ko]:"Navigation URI is empty. Please check stack trace for more info.",[So]:`Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. ${Ci}`,[bo]:"Hash does not contain state. Please verify that the request originated from msal.",[Eo]:`Hash does not contain known properites. Please verify that your redirectUri is not changing the hash.  ${Ci}`,[Ro]:"Unable to parse state. Please verify that the request originated from msal.",[_o]:"Hash contains state but the interaction type does not match the caller.",[Po]:`Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.   ${Ci}`,[Mo]:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.",[No]:"window.open returned null or undefined window object.",[Oo]:"User cancelled the flow.",[qo]:`Token acquisition in popup failed due to timeout.  ${Ci}`,[Uo]:`Token acquisition in iframe failed due to timeout.  ${Ci}`,[Lo]:"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.",[Ho]:`Request was blocked inside an iframe because MSAL detected an authentication response.  ${Ci}`,[Bo]:"Request was blocked inside a popup because MSAL detected it was running in a popup.",[xo]:"The iframe being monitored was closed prematurely.",[Do]:"Silent logout not supported. Please call logoutRedirect or logoutPopup instead.",[Fo]:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.",[Ko]:"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.",[zo]:"No token request found in cache.",[$o]:"The cached token request could not be parsed.",[Go]:"No cached authority found.",[Qo]:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler",[Wo]:"Invalid cache type",[jo]:"Login and token requests are not supported in non-browser environments.",[Vo]:"Database is not open!",[Yo]:"No network connectivity. Check your internet connection.",[Jo]:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'",[Xo]:"Network request failed. Please check the network trace to determine root cause.",[Zo]:"Failed to parse network response. Check network trace.",[ei]:"Error loading token to cache.",[ti]:"Cryptographic Key or Keypair not found in browser storage.",[ri]:"An authorization code must be provided (as the `code` property on the request) to this flow.",[ni]:"An authorization code or nativeAccountId must be provided to this flow.",[oi]:"Request cannot contain both spa code and native account id.",[ii]:"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.",[ai]:`Unable to acquire token from native platform.  ${Ci}`,[si]:"Timed out while attempting to establish connection to browser extension",[ci]:"Native extension is not installed. If you think this is a mistake call the initialize function.",[li]:`Connection to native platform has not been established. Please install a compatible browser extension and run initialize().  ${Ci}`,[di]:`You must call and await the initialize function before attempting to call any other MSAL API.  ${Ci}`,[hi]:"The provided prompt is not supported by the native platform. This request should be routed to the web based flow.",[ui]:"Invalid base64 encoded string.",[gi]:"Invalid PoP token request. The request should not have both a popKid value and signPopToken set to true.",[pi]:"Failed to build request headers object.",[mi]:"Failed to parse response headers"},vi={pkceNotGenerated:{code:wo,desc:yi[wo]},cryptoDoesNotExist:{code:Ao,desc:yi[Ao]},emptyNavigateUriError:{code:ko,desc:yi[ko]},hashEmptyError:{code:So,desc:yi[So]},hashDoesNotContainStateError:{code:bo,desc:yi[bo]},hashDoesNotContainKnownPropertiesError:{code:Eo,desc:yi[Eo]},unableToParseStateError:{code:Ro,desc:yi[Ro]},stateInteractionTypeMismatchError:{code:_o,desc:yi[_o]},interactionInProgress:{code:Po,desc:yi[Po]},popupWindowError:{code:Mo,desc:yi[Mo]},emptyWindowError:{code:No,desc:yi[No]},userCancelledError:{code:Oo,desc:yi[Oo]},monitorPopupTimeoutError:{code:qo,desc:yi[qo]},monitorIframeTimeoutError:{code:Uo,desc:yi[Uo]},redirectInIframeError:{code:Lo,desc:yi[Lo]},blockTokenRequestsInHiddenIframeError:{code:Ho,desc:yi[Ho]},blockAcquireTokenInPopupsError:{code:Bo,desc:yi[Bo]},iframeClosedPrematurelyError:{code:xo,desc:yi[xo]},silentLogoutUnsupportedError:{code:Do,desc:yi[Do]},noAccountError:{code:Fo,desc:yi[Fo]},silentPromptValueError:{code:Ko,desc:yi[Ko]},noTokenRequestCacheError:{code:zo,desc:yi[zo]},unableToParseTokenRequestCacheError:{code:$o,desc:yi[$o]},noCachedAuthorityError:{code:Go,desc:yi[Go]},authRequestNotSet:{code:Qo,desc:yi[Qo]},invalidCacheType:{code:Wo,desc:yi[Wo]},notInBrowserEnvironment:{code:jo,desc:yi[jo]},databaseNotOpen:{code:Vo,desc:yi[Vo]},noNetworkConnectivity:{code:Yo,desc:yi[Yo]},postRequestFailed:{code:Jo,desc:yi[Jo]},getRequestFailed:{code:Xo,desc:yi[Xo]},failedToParseNetworkResponse:{code:Zo,desc:yi[Zo]},unableToLoadTokenError:{code:ei,desc:yi[ei]},signingKeyNotFoundInStorage:{code:ti,desc:yi[ti]},authCodeRequired:{code:ri,desc:yi[ri]},authCodeOrNativeAccountRequired:{code:ni,desc:yi[ni]},spaCodeAndNativeAccountPresent:{code:oi,desc:yi[oi]},databaseUnavailable:{code:ii,desc:yi[ii]},unableToAcquireTokenFromNativePlatform:{code:ai,desc:yi[ai]},nativeHandshakeTimeout:{code:si,desc:yi[si]},nativeExtensionNotInstalled:{code:ci,desc:yi[ci]},nativeConnectionNotEstablished:{code:li,desc:yi[li]},uninitializedPublicClientApplication:{code:di,desc:yi[di]},nativePromptNotSupported:{code:hi,desc:yi[hi]},invalidBase64StringError:{code:ui,desc:yi[ui]},invalidPopTokenRequest:{code:gi,desc:yi[gi]}};class Ii extends Ae{constructor(e,t){super(e,yi[e],t),Object.setPrototypeOf(this,Ii.prototype),this.name="BrowserAuthError"}}function Ti(e,t){return new Ii(e,t)}const wi="invalid_grant",Ai=483,ki=600,Si="msal",bi=30,Ei="msal.js.browser",Ri="53ee284d-920a-4b59-9d30-a60315b26836",_i="ppnbnpeolgkicgegkbkbjmhlideopiji",Pi="MATS",Mi="Handshake",Ni="HandshakeResponse",Oi="GetToken",qi="Response",Ui={LocalStorage:"localStorage",SessionStorage:"sessionStorage",MemoryStorage:"memoryStorage"},Li="GET",Hi="POST",Bi="authority",xi="request.state",Di="nonce.id_token",Fi="request.origin",Ki="urlHash",zi="request.params",$i="interaction.status",Gi="ccs.credential",Qi="request.correlationId",Wi="request.native",ji="msal.account.keys",Vi="msal.token.keys",Yi="msal.version",Ji="wrapper.sku",Xi="wrapper.version",Zi={acquireTokenRedirect:861,acquireTokenPopup:862,ssoSilent:863,acquireTokenSilent_authCode:864,handleRedirectPromise:865,acquireTokenByCode:866,acquireTokenSilent_silentFlow:61,logout:961,logoutPopup:962};var ea;e.InteractionType=void 0,(ea=e.InteractionType||(e.InteractionType={})).Redirect="redirect",ea.Popup="popup",ea.Silent="silent",ea.None="none";const ta={Startup:"startup",Login:"login",Logout:"logout",AcquireToken:"acquireToken",SsoSilent:"ssoSilent",HandleRedirect:"handleRedirect",None:"none"},ra={scopes:a},na="msal.db",oa=`${na}.keys`,ia={Default:0,AccessToken:1,AccessTokenAndRefreshToken:2,RefreshToken:3,RefreshTokenAndNetwork:4,Skip:5},aa=[ia.Default,ia.Skip,ia.RefreshTokenAndNetwork];function sa(e){return encodeURIComponent(la(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"))}function ca(e){return da(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function la(e){return da((new TextEncoder).encode(e))}function da(e){const t=Array.from(e,(e=>String.fromCodePoint(e))).join("");return btoa(t)}const ha="SHA-256",ua=new Uint8Array([1,0,1]),ga="0123456789abcdef",pa=new Uint32Array(1),ma={name:"RSASSA-PKCS1-v1_5",hash:ha,modulusLength:2048,publicExponent:ua};async function fa(e,t,r){t?.addQueueMeasurement(vn.Sha256Digest,r);const n=(new TextEncoder).encode(e);return window.crypto.subtle.digest(ha,n)}function Ca(e){return window.crypto.getRandomValues(e)}function ya(){return window.crypto.getRandomValues(pa),pa[0]}function va(){const e=Date.now(),t=1024*ya()+(1023&ya()),r=new Uint8Array(16),n=Math.trunc(t/2**30),o=t&2**30-1,i=ya();r[0]=e/2**40,r[1]=e/2**32,r[2]=e/2**24,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|n>>>8,r[7]=n,r[8]=128|o>>>24,r[9]=o>>>16,r[10]=o>>>8,r[11]=o,r[12]=i>>>24,r[13]=i>>>16,r[14]=i>>>8,r[15]=i;let a="";for(let e=0;e<r.length;e++)a+=ga.charAt(r[e]>>>4),a+=ga.charAt(15&r[e]),3!==e&&5!==e&&7!==e&&9!==e||(a+="-");return a}async function Ia(e){return window.crypto.subtle.exportKey("jwk",e)}async function Ta(e){const t=await fa(e);return ca(new Uint8Array(t))}const wa="storage_not_supported",Aa="stubbed_public_client_application_called",ka="in_mem_redirect_unavailable";var Sa=Object.freeze({__proto__:null,inMemRedirectUnavailable:ka,storageNotSupported:wa,stubbedPublicClientApplicationCalled:Aa});const ba={[wa]:"Given storage configuration option was not supported.",[Aa]:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors",[ka]:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."},Ea={storageNotSupportedError:{code:wa,desc:ba[wa]},stubPcaInstanceCalled:{code:Aa,desc:ba[Aa]},inMemRedirectUnavailable:{code:ka,desc:ba[ka]}};class Ra extends Ae{constructor(e,t){super(e,t),this.name="BrowserConfigurationAuthError",Object.setPrototypeOf(this,Ra.prototype)}}function _a(e){return new Ra(e,ba[e])}function Pa(e){e.location.hash="","function"==typeof e.history.replaceState&&e.history.replaceState(null,"",`${e.location.origin}${e.location.pathname}${e.location.search}`)}function Ma(e){const t=e.split("#");t.shift(),window.location.hash=t.length>0?t.join("#"):""}function Na(){return window.parent!==window}function Oa(){return"undefined"!=typeof window&&!!window.opener&&window.opener!==window&&"string"==typeof window.name&&0===window.name.indexOf(`${Si}.`)}function qa(){return"undefined"!=typeof window&&window.location?window.location.href.split("?")[0].split("#")[0]:""}function Ua(){const e=new Hr(window.location.href).getUrlComponents();return`${e.Protocol}//${e.HostNameAndPort}/`}function La(){if(Hr.hashContainsKnownProperties(window.location.hash)&&Na())throw Ti(Ho)}function Ha(e){if(Na()&&!e)throw Ti(Lo)}function Ba(){if(Oa())throw Ti(Bo)}function xa(){if("undefined"==typeof window)throw Ti(jo)}function Da(e){if(!e)throw Ti(di)}function Fa(e){xa(),La(),Ba(),Da(e)}function Ka(e,t){if(Fa(e),Ha(t.system.allowRedirectInIframe),t.cache.cacheLocation===Ui.MemoryStorage&&!t.cache.storeAuthStateInCookie)throw _a(ka)}function za(e){const t=document.createElement("link");t.rel="preconnect",t.href=new URL(e).origin,t.crossOrigin="anonymous",document.head.appendChild(t),window.setTimeout((()=>{try{document.head.removeChild(t)}catch{}}),1e4)}function $a(){return va()}var Ga=Object.freeze({__proto__:null,blockAPICallsBeforeInitialize:Da,blockAcquireTokenInPopups:Ba,blockNonBrowserEnvironment:xa,blockRedirectInIframe:Ha,blockReloadInHiddenIframes:La,clearHash:Pa,createGuid:$a,getCurrentUri:qa,getHomepage:Ua,invoke:kn,invokeAsync:Sn,isInIframe:Na,isInPopup:Oa,preconnect:za,preflightCheck:Fa,redirectPreflightCheck:Ka,replaceHash:Ma});class Qa{navigateInternal(e,t){return Qa.defaultNavigateWindow(e,t)}navigateExternal(e,t){return Qa.defaultNavigateWindow(e,t)}static defaultNavigateWindow(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise((e=>{setTimeout((()=>{e(!0)}),t.timeout)}))}}class Wa{async sendGetRequestAsync(e,t){let r,n={},o=0;const i=ja(t);try{r=await fetch(e,{method:Li,headers:i})}catch(e){throw Ti(window.navigator.onLine?Xo:Yo)}n=Va(r.headers);try{return o=r.status,{headers:n,body:await r.json(),status:o}}catch(e){throw qn(Ti(Zo),o,n)}}async sendPostRequestAsync(e,t){const r=t&&t.body||"",n=ja(t);let o,i=0,a={};try{o=await fetch(e,{method:Hi,headers:n,body:r})}catch(e){throw Ti(window.navigator.onLine?Jo:Yo)}a=Va(o.headers);try{return i=o.status,{headers:a,body:await o.json(),status:i}}catch(e){throw qn(Ti(Zo),i,a)}}}function ja(e){try{const t=new Headers;if(!e||!e.headers)return t;const r=e.headers;return Object.entries(r).forEach((([e,r])=>{t.append(e,r)})),t}catch(e){throw Ti(pi)}}function Va(e){try{const t={};return e.forEach(((e,r)=>{t[r]=e})),t}catch(e){throw Ti(mi)}}const Ya=1e4;const Ja="@azure/msal-browser",Xa="3.30.0";class Za{static loggerCallback(t,r){switch(t){case e.LogLevel.Error:return void console.error(r);case e.LogLevel.Info:return void console.info(r);case e.LogLevel.Verbose:return void console.debug(r);case e.LogLevel.Warning:return void console.warn(r);default:return void console.log(r)}}constructor(r){let n;this.browserEnvironment="undefined"!=typeof window,this.config=function({auth:r,cache:n,system:o,telemetry:i},a){const s={clientId:t.EMPTY_STRING,authority:`${t.DEFAULT_AUTHORITY}`,knownAuthorities:[],cloudDiscoveryMetadata:t.EMPTY_STRING,authorityMetadata:t.EMPTY_STRING,redirectUri:"undefined"!=typeof window?qa():"",postLogoutRedirectUri:t.EMPTY_STRING,navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:Or.AAD,OIDCOptions:{serverResponseType:P.FRAGMENT,defaultScopes:[t.OPENID_SCOPE,t.PROFILE_SCOPE,t.OFFLINE_ACCESS_SCOPE]},azureCloudOptions:{azureCloudInstance:St.None,tenant:t.EMPTY_STRING},skipAuthorityMetadataCache:!1,supportsNestedAppAuth:!1,instanceAware:!1},c={cacheLocation:Ui.SessionStorage,temporaryCacheLocation:Ui.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!(!n||n.cacheLocation!==Ui.LocalStorage),claimsBasedCachingEnabled:!1},l={loggerCallback:()=>{},logLevel:e.LogLevel.Info,piiLoggingEnabled:!1},d={...{...Yr,loggerOptions:l,networkClient:a?new Wa:so,navigationClient:new Qa,loadFrameTimeout:0,windowHashTimeout:o?.loadFrameTimeout||6e4,iframeHashTimeout:o?.loadFrameTimeout||Ya,navigateFrameWait:0,redirectNavigationTimeout:3e4,asyncPopups:!1,allowRedirectInIframe:!1,allowNativeBroker:!1,nativeBrokerHandshakeTimeout:o?.nativeBrokerHandshakeTimeout||2e3,pollIntervalMilliseconds:bi},...o,loggerOptions:o?.loggerOptions||l},h={application:{appName:t.EMPTY_STRING,appVersion:t.EMPTY_STRING},client:new yo};r?.protocolMode!==Or.OIDC&&r?.OIDCOptions&&new wt(d.loggerOptions).warning(JSON.stringify(Ir(gr)));if(r?.protocolMode&&r.protocolMode!==Or.AAD&&d?.allowNativeBroker)throw Ir(pr);return{auth:{...s,...r,OIDCOptions:{...s.OIDCOptions,...r?.OIDCOptions}},cache:{...c,...n},system:d,telemetry:{...h,...i}}}(r,this.browserEnvironment);try{n=window[Ui.SessionStorage]}catch(e){}const o=n?.getItem("msal.browser.log.level"),i=n?.getItem("msal.browser.log.pii")?.toLowerCase(),a="true"===i||"false"!==i&&void 0,s={...this.config.system.loggerOptions},c=o&&Object.keys(e.LogLevel).includes(o)?e.LogLevel[o]:void 0;c&&(s.loggerCallback=Za.loggerCallback,s.logLevel=c),void 0!==a&&(s.piiLoggingEnabled=a),this.logger=new wt(s,Ja,Xa),this.available=!1}getConfig(){return this.config}getLogger(){return this.logger}isAvailable(){return this.available}isBrowserEnvironment(){return this.browserEnvironment}}const es="USER_INTERACTION_REQUIRED",ts="USER_CANCEL",rs="NO_NETWORK",ns="TRANSIENT_ERROR",os="PERSISTENT_ERROR",is="DISABLED",as="ACCOUNT_UNAVAILABLE",ss="NESTED_APP_AUTH_UNAVAILABLE";class cs{static async initializeNestedAppAuthBridge(){if(void 0===window)throw new Error("window is undefined");if(void 0===window.nestedAppAuthBridge)throw new Error("window.nestedAppAuthBridge is undefined");try{window.nestedAppAuthBridge.addEventListener("message",(e=>{const t="string"==typeof e?e:e.data,r=JSON.parse(t),n=cs.bridgeRequests.find((e=>e.requestId===r.requestId));void 0!==n&&(cs.bridgeRequests.splice(cs.bridgeRequests.indexOf(n),1),r.success?n.resolve(r):n.reject(r.error))}));const e=await new Promise(((e,t)=>{const r=cs.buildRequest("GetInitContext"),n={requestId:r.requestId,method:r.method,resolve:e,reject:t};cs.bridgeRequests.push(n),window.nestedAppAuthBridge.postMessage(JSON.stringify(r))}));return cs.validateBridgeResultOrThrow(e.initContext)}catch(e){throw window.console.log(e),e}}getTokenInteractive(e){return this.getToken("GetTokenPopup",e)}getTokenSilent(e){return this.getToken("GetToken",e)}async getToken(e,t){const r=await this.sendRequest(e,{tokenParams:t});return{token:cs.validateBridgeResultOrThrow(r.token),account:cs.validateBridgeResultOrThrow(r.account)}}getHostCapabilities(){return this.capabilities??null}getAccountContext(){return this.accountContext?this.accountContext:null}static buildRequest(e,t){return{messageType:"NestedAppAuthRequest",method:e,requestId:va(),sendTime:Date.now(),clientLibrary:Ei,clientLibraryVersion:Xa,...t}}sendRequest(e,t){const r=cs.buildRequest(e,t);return new Promise(((e,t)=>{const n={requestId:r.requestId,method:r.method,resolve:e,reject:t};cs.bridgeRequests.push(n),window.nestedAppAuthBridge.postMessage(JSON.stringify(r))}))}static validateBridgeResultOrThrow(e){if(void 0===e){throw{status:ss}}return e}constructor(e,t,r,n){this.sdkName=e,this.sdkVersion=t,this.accountContext=r,this.capabilities=n}static async create(){const e=await cs.initializeNestedAppAuthBridge();return new cs(e.sdkName,e.sdkVersion,e.accountContext,e.capabilities)}}cs.bridgeRequests=[];class ls extends Za{constructor(){super(...arguments),this.bridgeProxy=void 0,this.accountContext=null}getModuleName(){return ls.MODULE_NAME}getId(){return ls.ID}getBridgeProxy(){return this.bridgeProxy}async initialize(){try{if("undefined"!=typeof window){"function"==typeof window.__initializeNestedAppAuth&&await window.__initializeNestedAppAuth();const e=await cs.create();this.accountContext=e.getAccountContext(),this.bridgeProxy=e,this.available=void 0!==e}}catch(e){this.logger.infoPii(`Could not initialize Nested App Auth bridge (${e})`)}return this.logger.info(`Nested App Auth Bridge available: ${this.available}`),this.available}}ls.MODULE_NAME="",ls.ID="NestedAppOperatingContext";class ds extends Za{getModuleName(){return ds.MODULE_NAME}getId(){return ds.ID}async initialize(){return this.available="undefined"!=typeof window,this.available}}function hs(e){return(new TextDecoder).decode(function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Ti(ui)}const r=atob(t);return Uint8Array.from(r,(e=>e.codePointAt(0)||0))}(e))}ds.MODULE_NAME="",ds.ID="StandardOperatingContext";class us{constructor(){this.dbName=na,this.version=1,this.tableName=oa,this.dbOpen=!1}async open(){return new Promise(((e,t)=>{const r=window.indexedDB.open(this.dbName,this.version);r.addEventListener("upgradeneeded",(e=>{e.target.result.createObjectStore(this.tableName)})),r.addEventListener("success",(t=>{const r=t;this.db=r.target.result,this.dbOpen=!0,e()})),r.addEventListener("error",(()=>t(Ti(ii))))}))}closeConnection(){const e=this.db;e&&this.dbOpen&&(e.close(),this.dbOpen=!1)}async validateDbIsOpen(){if(!this.dbOpen)return this.open()}async getItem(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Ti(Vo));const n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).get(e);n.addEventListener("success",(e=>{const r=e;this.closeConnection(),t(r.target.result)})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async setItem(e,t){return await this.validateDbIsOpen(),new Promise(((r,n)=>{if(!this.db)return n(Ti(Vo));const o=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).put(t,e);o.addEventListener("success",(()=>{this.closeConnection(),r()})),o.addEventListener("error",(e=>{this.closeConnection(),n(e)}))}))}async removeItem(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Ti(Vo));const n=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).delete(e);n.addEventListener("success",(()=>{this.closeConnection(),t()})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async getKeys(){return await this.validateDbIsOpen(),new Promise(((e,t)=>{if(!this.db)return t(Ti(Vo));const r=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).getAllKeys();r.addEventListener("success",(t=>{const r=t;this.closeConnection(),e(r.target.result)})),r.addEventListener("error",(e=>{this.closeConnection(),t(e)}))}))}async containsKey(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Ti(Vo));const n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).count(e);n.addEventListener("success",(e=>{const r=e;this.closeConnection(),t(1===r.target.result)})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async deleteDatabase(){return this.db&&this.dbOpen&&this.closeConnection(),new Promise(((e,t)=>{const r=window.indexedDB.deleteDatabase(na),n=setTimeout((()=>t(!1)),200);r.addEventListener("success",(()=>(clearTimeout(n),e(!0)))),r.addEventListener("blocked",(()=>(clearTimeout(n),e(!0)))),r.addEventListener("error",(()=>(clearTimeout(n),t(!1))))}))}}class gs{constructor(){this.cache=new Map}getItem(e){return this.cache.get(e)||null}setItem(e,t){this.cache.set(e,t)}removeItem(e){this.cache.delete(e)}getKeys(){const e=[];return this.cache.forEach(((t,r)=>{e.push(r)})),e}containsKey(e){return this.cache.has(e)}clear(){this.cache.clear()}}class ps{constructor(e){this.inMemoryCache=new gs,this.indexedDBCache=new us,this.logger=e}handleDatabaseAccessError(e){if(!(e instanceof Ii&&e.errorCode===ii))throw e;this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.")}async getItem(e){const t=this.inMemoryCache.getItem(e);if(!t)try{return this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.getItem(e)}catch(e){this.handleDatabaseAccessError(e)}return t}async setItem(e,t){this.inMemoryCache.setItem(e,t);try{await this.indexedDBCache.setItem(e,t)}catch(e){this.handleDatabaseAccessError(e)}}async removeItem(e){this.inMemoryCache.removeItem(e);try{await this.indexedDBCache.removeItem(e)}catch(e){this.handleDatabaseAccessError(e)}}async getKeys(){const e=this.inMemoryCache.getKeys();if(0===e.length)try{return this.logger.verbose("In-memory cache is empty, now querying persistent storage."),await this.indexedDBCache.getKeys()}catch(e){this.handleDatabaseAccessError(e)}return e}async containsKey(e){const t=this.inMemoryCache.containsKey(e);if(!t)try{return this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.containsKey(e)}catch(e){this.handleDatabaseAccessError(e)}return t}clearInMemory(){this.logger.verbose("Deleting in-memory keystore"),this.inMemoryCache.clear(),this.logger.verbose("In-memory keystore deleted")}async clearPersistent(){try{this.logger.verbose("Deleting persistent keystore");const e=await this.indexedDBCache.deleteDatabase();return e&&this.logger.verbose("Persistent keystore deleted"),e}catch(e){return this.handleDatabaseAccessError(e),!1}}}class ms{constructor(e,t,r){this.logger=e,function(e){if(!window)throw Ti(jo);if(!window.crypto)throw Ti(Ao);if(!e&&!window.crypto.subtle)throw Ti(Ao,"crypto_subtle_undefined")}(r??!1),this.cache=new ps(this.logger),this.performanceClient=t}createNewGuid(){return va()}base64Encode(e){return la(e)}base64Decode(e){return hs(e)}base64UrlEncode(e){return sa(e)}encodeKid(e){return this.base64UrlEncode(JSON.stringify({kid:e}))}async getPublicKeyThumbprint(e){const t=this.performanceClient?.startMeasurement(vn.CryptoOptsGetPublicKeyThumbprint,e.correlationId),r=await async function(e,t){return window.crypto.subtle.generateKey(ma,e,t)}(ms.EXTRACTABLE,ms.POP_KEY_USAGES),n=await Ia(r.publicKey),o=fs({e:n.e,kty:n.kty,n:n.n}),i=await this.hashString(o),a=await Ia(r.privateKey),s=await async function(e,t,r){return window.crypto.subtle.importKey("jwk",e,ma,t,r)}(a,!1,["sign"]);return await this.cache.setItem(i,{privateKey:s,publicKey:r.publicKey,requestMethod:e.resourceRequestMethod,requestUri:e.resourceRequestUri}),t&&t.end({success:!0}),i}async removeTokenBindingKey(e){await this.cache.removeItem(e);return!await this.cache.containsKey(e)}async clearKeystore(){this.cache.clearInMemory();try{return await this.cache.clearPersistent(),!0}catch(e){return e instanceof Error?this.logger.error(`Clearing keystore failed with error: ${e.message}`):this.logger.error("Clearing keystore failed with unknown error"),!1}}async signJwt(e,t,r,n){const o=this.performanceClient?.startMeasurement(vn.CryptoOptsSignJwt,n),i=await this.cache.getItem(t);if(!i)throw Ti(ti);const a=await Ia(i.publicKey),s=fs(a),c=sa(JSON.stringify({kid:t})),l=sa(fo.getShrHeaderString({...r?.header,alg:a.alg,kid:c}));e.cnf={jwk:JSON.parse(s)};const d=`${l}.${sa(JSON.stringify(e))}`,h=(new TextEncoder).encode(d),u=await async function(e,t){return window.crypto.subtle.sign(ma,e,t)}(i.privateKey,h),g=`${d}.${ca(new Uint8Array(u))}`;return o&&o.end({success:!0}),g}async hashString(e){return Ta(e)}}function fs(e){return JSON.stringify(e,Object.keys(e).sort())}ms.POP_KEY_USAGES=["sign","verify"],ms.EXTRACTABLE=!0;class Cs{constructor(){if(!window.localStorage)throw _a(wa)}getItem(e){return window.localStorage.getItem(e)}setItem(e,t){window.localStorage.setItem(e,t)}removeItem(e){window.localStorage.removeItem(e)}getKeys(){return Object.keys(window.localStorage)}containsKey(e){return window.localStorage.hasOwnProperty(e)}}class ys{constructor(){if(!window.sessionStorage)throw _a(wa)}getItem(e){return window.sessionStorage.getItem(e)}setItem(e,t){window.sessionStorage.setItem(e,t)}removeItem(e){window.sessionStorage.removeItem(e)}getKeys(){return Object.keys(window.sessionStorage)}containsKey(e){return window.sessionStorage.hasOwnProperty(e)}}function vs(e,t){if(!t)return null;try{return Jn.parseRequestState(e,t).libraryState.meta}catch(e){throw vt(Oe)}}const Is=864e5;class Ts{getItem(e){const t=`${encodeURIComponent(e)}`,r=document.cookie.split(";");for(let e=0;e<r.length;e++){const n=r[e],[o,...i]=decodeURIComponent(n).trim().split("="),a=i.join("=");if(o===t)return a}return""}setItem(e,t,r,n=!0){let o=`${encodeURIComponent(e)}=${encodeURIComponent(t)};path=/;SameSite=Lax;`;if(r){const e=function(e){const t=new Date,r=new Date(t.getTime()+e*Is);return r.toUTCString()}(r);o+=`expires=${e};`}n&&(o+="Secure;"),document.cookie=o}removeItem(e){this.setItem(e,"",-1)}getKeys(){const e=document.cookie.split(";"),t=[];return e.forEach((e=>{const r=decodeURIComponent(e).trim().split("=");t.push(r[0])})),t}containsKey(e){return this.getKeys().includes(e)}}class ws extends jr{constructor(e,t,r,n,o,i){super(e,r,n,o),this.cacheConfig=t,this.logger=n,this.internalStorage=new gs,this.browserStorage=this.setupBrowserStorage(this.cacheConfig.cacheLocation),this.temporaryCacheStorage=this.setupBrowserStorage(this.cacheConfig.temporaryCacheLocation),this.cookieStorage=new Ts,t.cacheMigrationEnabled&&(this.migrateCacheEntries(),this.createKeyMaps()),this.performanceClient=i}setupBrowserStorage(e){try{switch(e){case Ui.LocalStorage:return new Cs;case Ui.SessionStorage:return new ys}}catch(e){this.logger.error(e)}return this.cacheConfig.cacheLocation=Ui.MemoryStorage,new gs}migrateCacheEntries(){const e=this.browserStorage.getItem(Yi);e&&this.logger.info(`MSAL.js was last initialized with version ${e}`),e!==Xa&&this.browserStorage.setItem(Yi,Xa);const r=`${t.CACHE_PREFIX}.${f}`,n=`${t.CACHE_PREFIX}.${C}`,o=`${t.CACHE_PREFIX}.${v}`,i=`${t.CACHE_PREFIX}.${I}`,a=[this.browserStorage.getItem(r),this.browserStorage.getItem(n),this.browserStorage.getItem(o),this.browserStorage.getItem(i)];[f,C,v,I].forEach(((e,t)=>{const r=a[t];r&&this.setTemporaryCache(e,r,!0)}))}createKeyMaps(){this.logger.trace("BrowserCacheManager - createKeyMaps called.");const e=this.cryptoImpl.createNewGuid(),t=this.getItem(ji),r=this.getItem(`${Vi}.${this.clientId}`);if(t&&r)return void this.logger.verbose("BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration.");this.browserStorage.getKeys().forEach((t=>{if(this.isCredentialKey(t)){const r=this.getItem(t);if(r){const n=this.validateAndParseJson(r);if(n&&n.hasOwnProperty("credentialType"))switch(n.credentialType){case x.ID_TOKEN:if(Ht(n)){this.logger.trace("BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - idToken with key: ${t} found, saving key to token key map`);const r=n,o=this.updateCredentialCacheKey(t,r,e);return void this.addTokenKey(o,x.ID_TOKEN,e)}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed idToken validation on key: ${t}`);break;case x.ACCESS_TOKEN:case x.ACCESS_TOKEN_WITH_AUTH_SCHEME:if(Lt(n)){this.logger.trace("BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - accessToken with key: ${t} found, saving key to token key map`);const r=n,o=this.updateCredentialCacheKey(t,r,e);return void this.addTokenKey(o,x.ACCESS_TOKEN,e)}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed accessToken validation on key: ${t}`);break;case x.REFRESH_TOKEN:if(Bt(n)){this.logger.trace("BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - refreshToken with key: ${t} found, saving key to token key map`);const r=n,o=this.updateCredentialCacheKey(t,r,e);return void this.addTokenKey(o,x.REFRESH_TOKEN,e)}this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping."),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: ${t}`)}}}if(this.isAccountKey(t)){const r=this.getItem(t);if(r){const n=this.validateAndParseJson(r);n&&qr.isAccountEntity(n)&&(this.logger.trace("BrowserCacheManager:createKeyMaps - account found, saving key to account key map"),this.logger.tracePii(`BrowserCacheManager:createKeyMaps - account with key: ${t} found, saving key to account key map`),this.addAccountKeyToMap(t,e))}}}))}validateAndParseJson(e){try{const t=JSON.parse(e);return t&&"object"==typeof t?t:null}catch(e){return null}}getItem(e){return this.browserStorage.getItem(e)}setItem(e,t,r){let n=[];for(let o=0;o<=20;o++)try{this.browserStorage.setItem(e,t),o>0&&this.removeAccessTokenKeys(n.slice(0,o),r);break}catch(i){const a=Wr(i);if(!(a.errorCode===zr&&o<20))throw a;if(n.length||(n=e===`${Vi}.${this.clientId}`?JSON.parse(t).accessToken:this.getTokenKeys().accessToken),n.length<=o)throw a;this.removeAccessToken(n[o],r,!1)}}getAccount(e,t,r){this.logger.trace("BrowserCacheManager.getAccount called");const n=this.getCachedAccountEntity(e,t);return this.updateOutdatedCachedAccount(e,n,t,r)}getCachedAccountEntity(e,t){const r=this.getItem(e);if(!r)return this.removeAccountKeyFromMap(e,t),null;const n=this.validateAndParseJson(r);return n&&qr.isAccountEntity(n)?jr.toObject(new qr,n):null}setAccount(e,t){this.logger.trace("BrowserCacheManager.setAccount called");const r=e.generateAccountKey();e.lastUpdatedAt=Date.now().toString(),this.setItem(r,JSON.stringify(e),t),this.addAccountKeyToMap(r,t)}getAccountKeys(){this.logger.trace("BrowserCacheManager.getAccountKeys called");const e=this.getItem(ji);return e?JSON.parse(e):(this.logger.verbose("BrowserCacheManager.getAccountKeys - No account keys found"),[])}addAccountKeyToMap(e,t){this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"),this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${e}`);const r=this.getAccountKeys();-1===r.indexOf(e)?(r.push(e),this.setItem(ji,JSON.stringify(r),t),this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added")):this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map")}removeAccountKeyFromMap(e,t){this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"),this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${e}`);const r=this.getAccountKeys(),n=r.indexOf(e);if(n>-1){if(r.splice(n,1),0===r.length)return void this.removeItem(ji);this.setItem(ji,JSON.stringify(r),t),this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed")}else this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map")}async removeAccount(e,t){super.removeAccount(e,t),this.removeAccountKeyFromMap(e,t)}removeOutdatedAccount(e,t){this.removeItem(e),this.removeAccountKeyFromMap(e,t)}removeIdToken(e,t){super.removeIdToken(e,t),this.removeTokenKey(e,x.ID_TOKEN,t)}removeAccessToken(e,t,r=!0){super.removeAccessToken(e,t),this.performanceClient?.incrementFields({accessTokensRemoved:1},t),r&&this.removeTokenKey(e,x.ACCESS_TOKEN,t)}removeAccessTokenKeys(e,t){this.logger.trace("removeAccessTokenKey called");const r=this.getTokenKeys();let n=0;if(e.forEach((e=>{const t=r.accessToken.indexOf(e);t>-1&&(r.accessToken.splice(t,1),n++)})),n>0)return this.logger.info(`removed ${n} accessToken keys from tokenKeys map`),void this.setTokenKeys(r,t)}removeRefreshToken(e,t){super.removeRefreshToken(e,t),this.removeTokenKey(e,x.REFRESH_TOKEN,t)}getTokenKeys(){this.logger.trace("BrowserCacheManager.getTokenKeys called");const e=this.getItem(`${Vi}.${this.clientId}`);if(e){const t=this.validateAndParseJson(e);if(t&&t.hasOwnProperty("idToken")&&t.hasOwnProperty("accessToken")&&t.hasOwnProperty("refreshToken"))return t;this.logger.error("BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.")}else this.logger.verbose("BrowserCacheManager.getTokenKeys - No token keys found");return{idToken:[],accessToken:[],refreshToken:[]}}setTokenKeys(e,t){0!==e.idToken.length||0!==e.accessToken.length||0!==e.refreshToken.length?this.setItem(`${Vi}.${this.clientId}`,JSON.stringify(e),t):this.removeItem(`${Vi}.${this.clientId}`)}addTokenKey(e,t,r){this.logger.trace("BrowserCacheManager addTokenKey called");const n=this.getTokenKeys();switch(t){case x.ID_TOKEN:-1===n.idToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"),n.idToken.push(e));break;case x.ACCESS_TOKEN:const r=n.accessToken.indexOf(e);-1!==r&&n.accessToken.splice(r,1),this.logger.trace(`access token ${-1===r?"added to":"updated in"} map`),n.accessToken.push(e);break;case x.REFRESH_TOKEN:-1===n.refreshToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"),n.refreshToken.push(e));break;default:throw this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${t}`),vt(et)}this.setTokenKeys(n,r)}removeTokenKey(e,t,r,n=this.getTokenKeys()){switch(this.logger.trace("BrowserCacheManager removeTokenKey called"),t){case x.ID_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${e} from map`);const r=n.idToken.indexOf(e);r>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"),n.idToken.splice(r,1)):this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");break;case x.ACCESS_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${e} from map`);const o=n.accessToken.indexOf(e);o>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"),n.accessToken.splice(o,1)):this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");break;case x.REFRESH_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${e} from map`);const i=n.refreshToken.indexOf(e);i>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"),n.refreshToken.splice(i,1)):this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");break;default:throw this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${t}`),vt(et)}this.setTokenKeys(n,r)}getIdTokenCredential(e,t){const r=this.getItem(e);if(!r)return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeIdToken(e,t),null;const n=this.validateAndParseJson(r);return n&&Ht(n)?(this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"),n):(this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),null)}setIdTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setIdTokenCredential called");const r=Mt(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(r,JSON.stringify(e),t),this.addTokenKey(r,x.ID_TOKEN,t)}getAccessTokenCredential(e,t){const r=this.getItem(e);if(!r)return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,x.ACCESS_TOKEN,t),null;const n=this.validateAndParseJson(r);return n&&Lt(n)?(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"),n):(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),null)}setAccessTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");const r=Mt(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(r,JSON.stringify(e),t),this.addTokenKey(r,x.ACCESS_TOKEN,t)}getRefreshTokenCredential(e,t){const r=this.getItem(e);if(!r)return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,x.REFRESH_TOKEN,t),null;const n=this.validateAndParseJson(r);return n&&Bt(n)?(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"),n):(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),null)}setRefreshTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");const r=Mt(e);e.lastUpdatedAt=Date.now().toString(),this.setItem(r,JSON.stringify(e),t),this.addTokenKey(r,x.REFRESH_TOKEN,t)}getAppMetadata(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&(n=e,(o=r)&&0===n.indexOf(D)&&o.hasOwnProperty("clientId")&&o.hasOwnProperty("environment"))?(this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"),r):(this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null);var n,o}setAppMetadata(e,t){this.logger.trace("BrowserCacheManager.setAppMetadata called");const r=function({environment:e,clientId:t}){return[D,e,t].join(H).toLowerCase()}(e);this.setItem(r,JSON.stringify(e),t)}getServerTelemetry(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){const r=0===e.indexOf(j.CACHE_KEY);let n=!0;return t&&(n=t.hasOwnProperty("failedRequests")&&t.hasOwnProperty("errors")&&t.hasOwnProperty("cacheHits")),r&&n}(e,r)?(this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"),r):(this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null)}setServerTelemetry(e,t,r){this.logger.trace("BrowserCacheManager.setServerTelemetry called"),this.setItem(e,JSON.stringify(t),r)}getAuthorityMetadata(e){const t=this.internalStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){return!!t&&0===e.indexOf(K)&&t.hasOwnProperty("aliases")&&t.hasOwnProperty("preferred_cache")&&t.hasOwnProperty("preferred_network")&&t.hasOwnProperty("canonical_authority")&&t.hasOwnProperty("authorization_endpoint")&&t.hasOwnProperty("token_endpoint")&&t.hasOwnProperty("issuer")&&t.hasOwnProperty("aliasesFromNetwork")&&t.hasOwnProperty("endpointsFromNetwork")&&t.hasOwnProperty("expiresAt")&&t.hasOwnProperty("jwks_uri")}(e,r)?(this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"),r):null}getAuthorityMetadataKeys(){return this.internalStorage.getKeys().filter((e=>this.isAuthorityMetadata(e)))}setWrapperMetadata(e,t){this.internalStorage.setItem(Ji,e),this.internalStorage.setItem(Xi,t)}getWrapperMetadata(){return[this.internalStorage.getItem(Ji)||t.EMPTY_STRING,this.internalStorage.getItem(Xi)||t.EMPTY_STRING]}setAuthorityMetadata(e,t){this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))}getActiveAccount(e){const t=this.generateCacheKey(w),r=this.getItem(t);if(!r){this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema");const t=this.generateCacheKey(T),r=this.getItem(t);if(!r)return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null;const n=this.getAccountInfoFilteredBy({localAccountId:r},e);return n?(this.logger.trace("BrowserCacheManager.getActiveAccount: Legacy active account cache schema found"),this.logger.trace("BrowserCacheManager.getActiveAccount: Adding active account filters cache schema"),this.setActiveAccount(n,e),n):null}const n=this.validateAndParseJson(r);return n?(this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"),this.getAccountInfoFilteredBy({homeAccountId:n.homeAccountId,localAccountId:n.localAccountId,tenantId:n.tenantId},e)):(this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null)}setActiveAccount(e,t){const r=this.generateCacheKey(w),n=this.generateCacheKey(T);if(e){this.logger.verbose("setActiveAccount: Active account set");const o={homeAccountId:e.homeAccountId,localAccountId:e.localAccountId,tenantId:e.tenantId,lastUpdatedAt:Date.now().toString()};this.setItem(r,JSON.stringify(o),t),this.setItem(n,e.localAccountId,t)}else this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.browserStorage.removeItem(r),this.browserStorage.removeItem(n)}getThrottlingCache(e){const t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){let r=!1;e&&(r=0===e.indexOf(X));let n=!0;return t&&(n=t.hasOwnProperty("throttleTime")),r&&n}(e,r)?(this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"),r):(this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null)}setThrottlingCache(e,t,r){this.logger.trace("BrowserCacheManager.setThrottlingCache called"),this.setItem(e,JSON.stringify(t),r)}getTemporaryCache(e,t){const r=t?this.generateCacheKey(e):e;if(this.cacheConfig.storeAuthStateInCookie){const e=this.cookieStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"),e}const n=this.temporaryCacheStorage.getItem(r);if(!n){if(this.cacheConfig.cacheLocation===Ui.LocalStorage){const e=this.browserStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"),e}return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"),null}return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"),n}setTemporaryCache(e,t,r){const n=r?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(n,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.cookieStorage.setItem(n,t,void 0,this.cacheConfig.secureCookies))}removeItem(e){this.browserStorage.removeItem(e)}removeTemporaryItem(e){this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.cookieStorage.removeItem(e))}getKeys(){return this.browserStorage.getKeys()}async clear(e){await this.removeAllAccounts(e),this.removeAppMetadata(e),this.temporaryCacheStorage.getKeys().forEach((e=>{-1===e.indexOf(t.CACHE_PREFIX)&&-1===e.indexOf(this.clientId)||this.removeTemporaryItem(e)})),this.browserStorage.getKeys().forEach((e=>{-1===e.indexOf(t.CACHE_PREFIX)&&-1===e.indexOf(this.clientId)||this.browserStorage.removeItem(e)})),this.internalStorage.clear()}async clearTokensAndKeysWithClaims(e,t){e.addQueueMeasurement(vn.ClearTokensAndKeysWithClaims,t);const r=this.getTokenKeys();let n=0;r.accessToken.forEach((e=>{const r=this.getAccessTokenCredential(e,t);r?.requestedClaimsHash&&e.includes(r.requestedClaimsHash.toLowerCase())&&(this.removeAccessToken(e,t),n++)})),n>0&&this.logger.warning(`${n} access tokens with claims in the cache keys have been removed from the cache.`)}generateCacheKey(e){return this.validateAndParseJson(e)?JSON.stringify(e):Tr.startsWith(e,t.CACHE_PREFIX)||Tr.startsWith(e,y)?e:`${t.CACHE_PREFIX}.${this.clientId}.${e}`}generateAuthorityKey(e){const{libraryState:{id:t}}=Jn.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${Bi}.${t}`)}generateNonceKey(e){const{libraryState:{id:t}}=Jn.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${Di}.${t}`)}generateStateKey(e){const{libraryState:{id:t}}=Jn.parseRequestState(this.cryptoImpl,e);return this.generateCacheKey(`${xi}.${t}`)}getCachedAuthority(e){const t=this.generateStateKey(e),r=this.getTemporaryCache(t);if(!r)return null;const n=this.generateAuthorityKey(r);return this.getTemporaryCache(n)}updateCacheEntries(e,t,r,n,o){this.logger.trace("BrowserCacheManager.updateCacheEntries called");const i=this.generateStateKey(e);this.setTemporaryCache(i,e,!1);const a=this.generateNonceKey(e);this.setTemporaryCache(a,t,!1);const s=this.generateAuthorityKey(e);if(this.setTemporaryCache(s,r,!1),o){const e={credential:o.homeAccountId,type:an};this.setTemporaryCache(Gi,JSON.stringify(e),!0)}else if(n){const e={credential:n,type:sn};this.setTemporaryCache(Gi,JSON.stringify(e),!0)}}resetRequestCache(e){this.logger.trace("BrowserCacheManager.resetRequestCache called"),e&&(this.temporaryCacheStorage.getKeys().forEach((t=>{-1!==t.indexOf(e)&&this.removeTemporaryItem(t)})),this.removeTemporaryItem(this.generateStateKey(e)),this.removeTemporaryItem(this.generateNonceKey(e)),this.removeTemporaryItem(this.generateAuthorityKey(e))),this.removeTemporaryItem(this.generateCacheKey(zi)),this.removeTemporaryItem(this.generateCacheKey(Fi)),this.removeTemporaryItem(this.generateCacheKey(Ki)),this.removeTemporaryItem(this.generateCacheKey(Qi)),this.removeTemporaryItem(this.generateCacheKey(Gi)),this.removeTemporaryItem(this.generateCacheKey(Wi)),this.setInteractionInProgress(!1)}cleanRequestByState(e){if(this.logger.trace("BrowserCacheManager.cleanRequestByState called"),e){const r=this.generateStateKey(e),n=this.temporaryCacheStorage.getItem(r);this.logger.infoPii(`BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: ${n}`),this.resetRequestCache(n||t.EMPTY_STRING)}}cleanRequestByInteractionType(e){this.logger.trace("BrowserCacheManager.cleanRequestByInteractionType called"),this.temporaryCacheStorage.getKeys().forEach((t=>{if(-1===t.indexOf(xi))return;const r=this.temporaryCacheStorage.getItem(t);if(!r)return;const n=vs(this.cryptoImpl,r);n&&n.interactionType===e&&(this.logger.infoPii(`BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: ${r}`),this.resetRequestCache(r))})),this.setInteractionInProgress(!1)}cacheCodeRequest(e){this.logger.trace("BrowserCacheManager.cacheCodeRequest called");const t=la(JSON.stringify(e));this.setTemporaryCache(zi,t,!0)}getCachedRequest(e){this.logger.trace("BrowserCacheManager.getCachedRequest called");const t=this.getTemporaryCache(zi,!0);if(!t)throw Ti(zo);let r;try{r=JSON.parse(hs(t))}catch(e){throw this.logger.errorPii(`Attempted to parse: ${t}`),this.logger.error(`Parsing cached token request threw with error: ${e}`),Ti($o)}if(this.removeTemporaryItem(this.generateCacheKey(zi)),!r.authority){const t=this.generateAuthorityKey(e),n=this.getTemporaryCache(t);if(!n)throw Ti(Go);r.authority=n}return r}getCachedNativeRequest(){this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");const e=this.getTemporaryCache(Wi,!0);if(!e)return this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"),null;const t=this.validateAndParseJson(e);return t||(this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"),null)}isInteractionInProgress(e){const t=this.getInteractionInProgress();return e?t===this.clientId:!!t}getInteractionInProgress(){const e=`${t.CACHE_PREFIX}.${$i}`;return this.getTemporaryCache(e,!1)}setInteractionInProgress(e){const r=`${t.CACHE_PREFIX}.${$i}`;if(e){if(this.getInteractionInProgress())throw Ti(Po);this.setTemporaryCache(r,this.clientId,!1)}else e||this.getInteractionInProgress()!==this.clientId||this.removeTemporaryItem(r)}getLegacyLoginHint(){const e=this.getTemporaryCache(y);e&&(this.browserStorage.removeItem(y),this.logger.verbose("Cached ADAL id token retrieved."));const t=this.getTemporaryCache(f,!0);t&&(this.browserStorage.removeItem(this.generateCacheKey(f)),this.logger.verbose("Cached MSAL.js v1 id token retrieved"));const r=t||e;if(r){const e=bt(r,hs);if(e.preferred_username)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint"),e.preferred_username;if(e.upn)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint"),e.upn;this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.")}return null}updateCredentialCacheKey(e,t,r){const n=Mt(t);if(e!==n){const o=this.getItem(e);if(o)return this.browserStorage.removeItem(e),this.setItem(n,o,r),this.logger.verbose(`Updated an outdated ${t.credentialType} cache key`),n;this.logger.error(`Attempted to update an outdated ${t.credentialType} cache key but no item matching the outdated key was found in storage`)}return e}async hydrateCache(e,t){const r=Nt(e.account?.homeAccountId,e.account?.environment,e.idToken,this.clientId,e.tenantId);let n;t.claims&&(n=await this.cryptoImpl.hashString(t.claims));const o={idToken:r,accessToken:Ot(e.account?.homeAccountId,e.account.environment,e.accessToken,this.clientId,e.tenantId,e.scopes.join(" "),e.expiresOn?e.expiresOn.getTime()/1e3:0,e.extExpiresOn?e.extExpiresOn.getTime()/1e3:0,hs,void 0,e.tokenType,void 0,t.sshKid,t.claims,n)};return this.saveCacheRecord(o,e.correlationId)}async saveCacheRecord(e,t,r){try{await super.saveCacheRecord(e,t,r)}catch(e){if(e instanceof Qr&&this.performanceClient&&t)try{const e=this.getTokenKeys();this.performanceClient.addFields({cacheRtCount:e.refreshToken.length,cacheIdCount:e.idToken.length,cacheAtCount:e.accessToken.length},t)}catch(e){}throw e}}}const As=(e,t)=>new ws(e,{cacheLocation:Ui.MemoryStorage,temporaryCacheLocation:Ui.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1},It,t);function ks(e,t,r,n,o){return e.verbose("getAllAccounts called"),r?t.getAllAccounts(n,o):[]}function Ss(e,t,r,n){if(t.trace("getAccount called"),0===Object.keys(e).length)return t.warning("getAccount: No accountFilter provided"),null;const o=r.getAccountInfoFilteredBy(e,n);return o?(t.verbose("getAccount: Account matching provided filter found, returning"),o):(t.verbose("getAccount: No matching account found, returning null"),null)}function bs(e,t,r,n){if(t.trace("getAccountByUsername called"),!e)return t.warning("getAccountByUsername: No username provided"),null;const o=r.getAccountInfoFilteredBy({username:e},n);return o?(t.verbose("getAccountByUsername: Account matching username found, returning"),t.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${e}`),o):(t.verbose("getAccountByUsername: No matching account found, returning null"),null)}function Es(e,t,r,n){if(t.trace("getAccountByHomeId called"),!e)return t.warning("getAccountByHomeId: No homeAccountId provided"),null;const o=r.getAccountInfoFilteredBy({homeAccountId:e},n);return o?(t.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"),t.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${e}`),o):(t.verbose("getAccountByHomeId: No matching account found, returning null"),null)}function Rs(e,t,r,n){if(t.trace("getAccountByLocalId called"),!e)return t.warning("getAccountByLocalId: No localAccountId provided"),null;const o=r.getAccountInfoFilteredBy({localAccountId:e},n);return o?(t.verbose("getAccountByLocalId: Account matching localAccountId found, returning"),t.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${e}`),o):(t.verbose("getAccountByLocalId: No matching account found, returning null"),null)}function _s(e,t,r){t.setActiveAccount(e,r)}function Ps(e,t){return e.getActiveAccount(t)}const Ms={INITIALIZE_START:"msal:initializeStart",INITIALIZE_END:"msal:initializeEnd",ACCOUNT_ADDED:"msal:accountAdded",ACCOUNT_REMOVED:"msal:accountRemoved",ACTIVE_ACCOUNT_CHANGED:"msal:activeAccountChanged",LOGIN_START:"msal:loginStart",LOGIN_SUCCESS:"msal:loginSuccess",LOGIN_FAILURE:"msal:loginFailure",ACQUIRE_TOKEN_START:"msal:acquireTokenStart",ACQUIRE_TOKEN_SUCCESS:"msal:acquireTokenSuccess",ACQUIRE_TOKEN_FAILURE:"msal:acquireTokenFailure",ACQUIRE_TOKEN_NETWORK_START:"msal:acquireTokenFromNetworkStart",SSO_SILENT_START:"msal:ssoSilentStart",SSO_SILENT_SUCCESS:"msal:ssoSilentSuccess",SSO_SILENT_FAILURE:"msal:ssoSilentFailure",ACQUIRE_TOKEN_BY_CODE_START:"msal:acquireTokenByCodeStart",ACQUIRE_TOKEN_BY_CODE_SUCCESS:"msal:acquireTokenByCodeSuccess",ACQUIRE_TOKEN_BY_CODE_FAILURE:"msal:acquireTokenByCodeFailure",HANDLE_REDIRECT_START:"msal:handleRedirectStart",HANDLE_REDIRECT_END:"msal:handleRedirectEnd",POPUP_OPENED:"msal:popupOpened",LOGOUT_START:"msal:logoutStart",LOGOUT_SUCCESS:"msal:logoutSuccess",LOGOUT_FAILURE:"msal:logoutFailure",LOGOUT_END:"msal:logoutEnd",RESTORE_FROM_BFCACHE:"msal:restoreFromBFCache"};class Ns{constructor(e){this.eventCallbacks=new Map,this.logger=e||new wt({})}addEventCallback(e,t,r){if("undefined"!=typeof window){const n=r||$a();return this.eventCallbacks.has(n)?(this.logger.error(`Event callback with id: ${n} is already registered. Please provide a unique id or remove the existing callback and try again.`),null):(this.eventCallbacks.set(n,[e,t||[]]),this.logger.verbose(`Event callback registered with id: ${n}`),n)}return null}removeEventCallback(e){this.eventCallbacks.delete(e),this.logger.verbose(`Event callback ${e} removed.`)}emitEvent(e,t,r,n){if("undefined"!=typeof window){const o={eventType:e,interactionType:t||null,payload:r||null,error:n||null,timestamp:Date.now()};this.eventCallbacks.forEach((([t,r],n)=>{(0===r.length||r.includes(e))&&(this.logger.verbose(`Emitting event to callback ${n}: ${e}`),t.apply(null,[o]))}))}}}class Os{constructor(e,t,r,n,o,i,a,s,c){this.config=e,this.browserStorage=t,this.browserCrypto=r,this.networkClient=this.config.system.networkClient,this.eventHandler=o,this.navigationClient=i,this.nativeMessageHandler=s,this.correlationId=c||va(),this.logger=n.clone(Ei,Xa,this.correlationId),this.performanceClient=a}async clearCacheOnLogout(e){if(e){qr.accountInfoIsEqual(e,this.browserStorage.getActiveAccount(this.correlationId),!1)&&(this.logger.verbose("Setting active account to null"),this.browserStorage.setActiveAccount(null,this.correlationId));try{await this.browserStorage.removeAccount(qr.generateAccountCacheKey(e),this.correlationId),this.logger.verbose("Cleared cache items belonging to the account provided in the logout request.")}catch(e){this.logger.error("Account provided in logout request was not found. Local cache unchanged.")}}else try{this.logger.verbose("No account provided in logout request, clearing all cache items.",this.correlationId),await this.browserStorage.clear(this.correlationId),await this.browserCrypto.clearKeystore()}catch(e){this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged.")}}getRedirectUri(e){this.logger.verbose("getRedirectUri called");const t=e||this.config.auth.redirectUri;return Hr.getAbsoluteUrl(t,qa())}initializeServerTelemetryManager(e,t){this.logger.verbose("initializeServerTelemetryManager called");const r={clientId:this.config.auth.clientId,correlationId:this.correlationId,apiId:e,forceRefresh:t||!1,wrapperSKU:this.browserStorage.getWrapperMetadata()[0],wrapperVer:this.browserStorage.getWrapperMetadata()[1]};return new lo(r,this.browserStorage)}async getDiscoveredAuthority(e){const{account:t}=e,r=e.requestExtraQueryParameters&&e.requestExtraQueryParameters.hasOwnProperty("instance_aware")?e.requestExtraQueryParameters.instance_aware:void 0;this.performanceClient.addQueueMeasurement(vn.StandardInteractionClientGetDiscoveredAuthority,this.correlationId);const n={protocolMode:this.config.auth.protocolMode,OIDCOptions:this.config.auth.OIDCOptions,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},o=e.requestAuthority||this.config.auth.authority,i=r?.length?"true"===r:this.config.auth.instanceAware,a=t&&i?this.config.auth.authority.replace(Hr.getDomainFromUrl(o),t.environment):o,s=En.generateAuthority(a,e.requestAzureCloudOptions||this.config.auth.azureCloudOptions),c=await Sn(Pn,vn.AuthorityFactoryCreateDiscoveredInstance,this.logger,this.performanceClient,this.correlationId)(s,this.config.system.networkClient,this.browserStorage,n,this.logger,this.correlationId,this.performanceClient);if(t&&!c.isAlias(t.environment))throw Ir(mr);return c}}async function qs(e,t,r){e.addQueueMeasurement(vn.GeneratePkceCodes,r);const n=kn(Us,vn.GenerateCodeVerifier,t,e,r)(e,t,r);return{verifier:n,challenge:await Sn(Ls,vn.GenerateCodeChallengeFromVerifier,t,e,r)(n,e,t,r)}}function Us(e,t,r){try{const n=new Uint8Array(32);kn(Ca,vn.GetRandomValues,t,e,r)(n);return ca(n)}catch(e){throw Ti(wo)}}async function Ls(e,t,r,n){t.addQueueMeasurement(vn.GenerateCodeChallengeFromVerifier,n);try{const o=await Sn(fa,vn.Sha256Digest,r,t,n)(e,t,n);return ca(new Uint8Array(o))}catch(e){throw Ti(wo)}}async function Hs(e,t,r,n){r.addQueueMeasurement(vn.InitializeBaseRequest,e.correlationId);const o=e.authority||t.auth.authority,i=[...e&&e.scopes||[]],a={...e,correlationId:e.correlationId,authority:o,scopes:i};if(a.authenticationScheme){if(a.authenticationScheme===V.SSH){if(!e.sshJwk)throw Ir(lr);if(!e.sshKid)throw Ir(dr)}n.verbose(`Authentication Scheme set to "${a.authenticationScheme}" as configured in Auth request`)}else a.authenticationScheme=V.BEARER,n.verbose('Authentication Scheme wasn\'t explicitly set in request, defaulting to "Bearer" request');return t.cache.claimsBasedCachingEnabled&&e.claims&&!Tr.isEmptyObj(e.claims)&&(a.requestedClaimsHash=await Ta(e.claims)),a}async function Bs(e,t,r,n,o){n.addQueueMeasurement(vn.InitializeSilentRequest,e.correlationId);const i=await Sn(Hs,vn.InitializeBaseRequest,o,n,e.correlationId)(e,r,n,o);return{...e,...i,account:t,forceRefresh:e.forceRefresh||!1}}class xs extends Os{async initializeAuthorizationCodeRequest(e){this.performanceClient.addQueueMeasurement(vn.StandardInteractionClientInitializeAuthorizationCodeRequest,this.correlationId);const r=await Sn(qs,vn.GeneratePkceCodes,this.logger,this.performanceClient,this.correlationId)(this.performanceClient,this.logger,this.correlationId),n={...e,redirectUri:e.redirectUri,code:t.EMPTY_STRING,codeVerifier:r.verifier};return e.codeChallenge=r.challenge,e.codeChallengeMethod=t.S256_CODE_CHALLENGE_METHOD,n}initializeLogoutRequest(e){this.logger.verbose("initializeLogoutRequest called",e?.correlationId);const t={correlationId:this.correlationId||va(),...e};if(e)if(e.logoutHint)this.logger.verbose("logoutHint has already been set in logoutRequest");else if(e.account){const r=this.getLogoutHintFromIdTokenClaims(e.account);r&&(this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"),t.logoutHint=r)}else this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set");else this.logger.verbose("logoutHint will not be set since no logout request was configured");return e&&null===e.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri",t.correlationId):e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request",t.correlationId),t.postLogoutRedirectUri=Hr.getAbsoluteUrl(e.postLogoutRedirectUri,qa())):null===this.config.auth.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect",t.correlationId):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri",t.correlationId),t.postLogoutRedirectUri=Hr.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,qa())):(this.logger.verbose("Setting postLogoutRedirectUri to current page",t.correlationId),t.postLogoutRedirectUri=Hr.getAbsoluteUrl(qa(),qa())),t}getLogoutHintFromIdTokenClaims(e){const t=e.idTokenClaims;if(t){if(t.login_hint)return t.login_hint;this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request")}else this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");return null}async createAuthCodeClient(e){this.performanceClient.addQueueMeasurement(vn.StandardInteractionClientCreateAuthCodeClient,this.correlationId);const t=await Sn(this.getClientConfiguration.bind(this),vn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)(e);return new oo(t,this.performanceClient)}async getClientConfiguration(e){const{serverTelemetryManager:r,requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:i,account:a}=e;this.performanceClient.addQueueMeasurement(vn.StandardInteractionClientGetClientConfiguration,this.correlationId);const s=await Sn(this.getDiscoveredAuthority.bind(this),vn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,this.correlationId)({requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:i,account:a}),c=this.config.system.loggerOptions;return{authOptions:{clientId:this.config.auth.clientId,authority:s,clientCapabilities:this.config.auth.clientCapabilities,redirectUri:this.config.auth.redirectUri},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds,preventCorsPreflight:!0},loggerOptions:{loggerCallback:c.loggerCallback,piiLoggingEnabled:c.piiLoggingEnabled,logLevel:c.logLevel,correlationId:this.correlationId},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:r,libraryInfo:{sku:Ei,version:Xa,cpu:t.EMPTY_STRING,os:t.EMPTY_STRING},telemetry:this.config.telemetry}}async initializeAuthorizationRequest(e,r){this.performanceClient.addQueueMeasurement(vn.StandardInteractionClientInitializeAuthorizationRequest,this.correlationId);const n=this.getRedirectUri(e.redirectUri),o={interactionType:r},i=Jn.setRequestState(this.browserCrypto,e&&e.state||t.EMPTY_STRING,o),a={...await Sn(Hs,vn.InitializeBaseRequest,this.logger,this.performanceClient,this.correlationId)({...e,correlationId:this.correlationId},this.config,this.performanceClient,this.logger),redirectUri:n,state:i,nonce:e.nonce||va(),responseMode:this.config.auth.OIDCOptions.serverResponseType};if(e.loginHint||e.sid)return a;const s=e.account||this.browserStorage.getActiveAccount(this.correlationId);if(s&&(this.logger.verbose("Setting validated request account",this.correlationId),this.logger.verbosePii(`Setting validated request account: ${s.homeAccountId}`,this.correlationId),a.account=s),!a.loginHint&&!s){const e=this.browserStorage.getLegacyLoginHint();e&&(a.loginHint=e)}return a}}const Ds="user_switch",Fs={[Ds]:"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."};class Ks extends Ae{constructor(e,t,r){super(e,t),Object.setPrototypeOf(this,Ks.prototype),this.name="NativeAuthError",this.ext=r}}function zs(e){return!(!e.ext||!e.ext.status||"PERSISTENT_ERROR"!==e.ext.status&&"DISABLED"!==e.ext.status)||(!(!e.ext||!e.ext.error||-**********!==e.ext.error)||"ContentError"===e.errorCode)}function $s(e,t,r){if(r&&r.status)switch(r.status){case"ACCOUNT_UNAVAILABLE":return Yn(Hn);case"USER_INTERACTION_REQUIRED":return new jn(e,t);case"USER_CANCEL":return Ti(Oo);case"NO_NETWORK":return Ti(Yo)}return new Ks(e,Fs[e]||t,r)}class Gs extends xs{async acquireToken(e){this.performanceClient.addQueueMeasurement(vn.SilentCacheClientAcquireToken,e.correlationId);const t=this.initializeServerTelemetryManager(Zi.acquireTokenSilent_silentFlow),r=await Sn(this.getClientConfiguration.bind(this),vn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,account:e.account}),n=new ao(r,this.performanceClient);this.logger.verbose("Silent auth client created");try{const t=(await Sn(n.acquireCachedToken.bind(n),vn.SilentFlowClientAcquireCachedToken,this.logger,this.performanceClient,e.correlationId)(e))[0];return this.performanceClient.addFields({fromCache:!0},e.correlationId),t}catch(e){throw e instanceof Ii&&e.errorCode===ti&&this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."),e}}logout(e){this.logger.verbose("logoutRedirect called");const t=this.initializeLogoutRequest(e);return this.clearCacheOnLogout(t?.account)}}class Qs extends Os{constructor(e,t,r,n,o,i,a,s,c,l,d,h){super(e,t,r,n,o,i,s,c,h),this.apiId=a,this.accountId=l,this.nativeMessageHandler=c,this.nativeStorageManager=d,this.silentCacheClient=new Gs(e,this.nativeStorageManager,r,n,o,i,s,c,h),this.serverTelemetryManager=this.initializeServerTelemetryManager(this.apiId);const u=this.nativeMessageHandler.getExtensionId()===_i?"chrome":this.nativeMessageHandler.getExtensionId()?.length?"unknown":void 0;this.skus=lo.makeExtraSkuString({libraryName:Ei,libraryVersion:Xa,extensionName:u,extensionVersion:this.nativeMessageHandler.getExtensionVersion()})}addRequestSKUs(e){e.extraParameters={...e.extraParameters,[pn]:this.skus}}async acquireToken(e){this.performanceClient.addQueueMeasurement(vn.NativeInteractionClientAcquireToken,e.correlationId),this.logger.trace("NativeInteractionClient - acquireToken called.");const t=this.performanceClient.startMeasurement(vn.NativeInteractionClientAcquireToken,e.correlationId),r=Rt();try{const n=await this.initializeNativeRequest(e);try{const e=await this.acquireTokensFromCache(this.accountId,n);return t.end({success:!0,isNativeBroker:!1,fromCache:!0}),e}catch(e){this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call")}const{...o}=n,i={method:Oi,request:o},a=await this.nativeMessageHandler.sendMessage(i),s=this.validateNativeResponse(a);return await this.handleNativeResponse(s,n,r).then((e=>(t.end({success:!0,isNativeBroker:!0,requestId:e.requestId}),this.serverTelemetryManager.clearNativeBrokerErrorCode(),e))).catch((e=>{throw t.end({success:!1,errorCode:e.errorCode,subErrorCode:e.subError,isNativeBroker:!0}),e}))}catch(e){throw e instanceof Ks&&this.serverTelemetryManager.setNativeBrokerErrorCode(e.errorCode),e}}createSilentCacheRequest(e,t){return{authority:e.authority,correlationId:this.correlationId,scopes:wr.fromString(e.scope).asArray(),account:t,forceRefresh:!1}}async acquireTokensFromCache(e,t){if(!e)throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"),vt(Xe);const r=this.browserStorage.getBaseAccountInfo({nativeAccountId:e},t.correlationId);if(!r)throw vt(Xe);try{const e=this.createSilentCacheRequest(t,r),n=await this.silentCacheClient.acquireToken(e),o={...r,idTokenClaims:n?.idTokenClaims,idToken:n?.idToken};return{...n,account:o}}catch(e){throw e}}async acquireTokenRedirect(e,t){this.logger.trace("NativeInteractionClient - acquireTokenRedirect called.");const{...r}=e;delete r.onRedirectNavigate;const n=await this.initializeNativeRequest(r),o={method:Oi,request:n};try{const e=await this.nativeMessageHandler.sendMessage(o);this.validateNativeResponse(e)}catch(e){if(e instanceof Ks&&(this.serverTelemetryManager.setNativeBrokerErrorCode(e.errorCode),zs(e)))throw e}this.browserStorage.setTemporaryCache(Wi,JSON.stringify(n),!0);const i={apiId:Zi.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},a=this.config.auth.navigateToLoginRequestUrl?window.location.href:this.getRedirectUri(e.redirectUri);t.end({success:!0}),await this.navigationClient.navigateExternal(a,i)}async handleRedirectPromise(e,t){if(this.logger.trace("NativeInteractionClient - handleRedirectPromise called."),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const r=this.browserStorage.getCachedNativeRequest();if(!r)return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."),e&&t&&e?.addFields({errorCode:"no_cached_request"},t),null;const{prompt:n,...o}=r;n&&this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(Wi));const i={method:Oi,request:o},a=Rt();try{this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker.");const e=await this.nativeMessageHandler.sendMessage(i);this.validateNativeResponse(e);const t=this.handleNativeResponse(e,o,a);this.browserStorage.setInteractionInProgress(!1);const r=await t;return this.serverTelemetryManager.clearNativeBrokerErrorCode(),r}catch(e){throw this.browserStorage.setInteractionInProgress(!1),e}}logout(){return this.logger.trace("NativeInteractionClient - logout called."),Promise.reject("Logout not implemented yet")}async handleNativeResponse(e,t,r){this.logger.trace("NativeInteractionClient - handleNativeResponse called.");const n=bt(e.id_token,hs),o=this.createHomeAccountIdentifier(e,n),i=this.browserStorage.getAccountInfoFilteredBy({nativeAccountId:t.accountId},this.correlationId)?.homeAccountId;if(o!==i&&e.account.id!==t.accountId)throw $s(Ds);const a=await this.getDiscoveredAuthority({requestAuthority:t.authority}),s=ro(this.browserStorage,a,o,hs,this.correlationId,n,e.client_info,void 0,n.tid,void 0,e.account.id,this.logger),c=await this.generateAuthenticationResult(e,t,n,s,a.canonicalAuthority,r);return this.cacheAccount(s),this.cacheNativeTokens(e,t,o,n,e.access_token,c.tenantId,r),c}createHomeAccountIdentifier(e,r){return qr.generateHomeAccountId(e.client_info||t.EMPTY_STRING,Rr,this.logger,this.browserCrypto,r)}generateScopes(e,t){return e.scope?wr.fromString(e.scope):wr.fromString(t.scope)}async generatePopAccessToken(e,t){if(t.tokenType===V.POP&&t.signPopToken){if(e.shr)return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"),e.shr;const r=new Zn(this.browserCrypto),n={resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,shrNonce:t.shrNonce};if(!t.keyId)throw vt(lt);return r.signPopToken(e.access_token,t.keyId,n)}return e.access_token}async generateAuthenticationResult(e,r,n,o,i,a){const s=this.addTelemetryFromNativeResponse(e),c=e.scope?wr.fromString(e.scope):wr.fromString(r.scope),l=e.account.properties||{},d=l.UID||n.oid||n.sub||t.EMPTY_STRING,h=l.TenantId||n.tid||t.EMPTY_STRING,u=Er(o.getAccountInfo(),void 0,n,e.id_token);u.nativeAccountId!==e.account.id&&(u.nativeAccountId=e.account.id);const g=await this.generatePopAccessToken(e,r),p=r.tokenType===V.POP?V.POP:V.BEARER;return{authority:i,uniqueId:d,tenantId:h,scopes:c.asArray(),account:u,idToken:e.id_token,idTokenClaims:n,accessToken:g,fromCache:!!s&&this.isResponseFromCache(s),expiresOn:new Date(1e3*Number(a+e.expires_in)),tokenType:p,correlationId:this.correlationId,state:e.state,fromNativeBroker:!0}}cacheAccount(e){this.browserStorage.setAccount(e,this.correlationId),this.browserStorage.removeAccountContext(e,this.correlationId).catch((e=>{this.logger.error(`Error occurred while removing account context from browser storage. ${e}`)}))}cacheNativeTokens(e,r,n,o,i,a,s){const c=Nt(n,r.authority,e.id_token||"",r.clientId,o.tid||""),l=s+(r.tokenType===V.POP?t.SHR_NONCE_VALIDITY:("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0),d=this.generateScopes(e,r),h={idToken:c,accessToken:Ot(n,r.authority,i,r.clientId,o.tid||a,d.printScopes(),l,0,hs,void 0,r.tokenType,void 0,r.keyId)};this.nativeStorageManager.saveCacheRecord(h,r.correlationId,r.storeInCache)}addTelemetryFromNativeResponse(e){const t=this.getMATSFromResponse(e);return t?(this.performanceClient.addFields({extensionId:this.nativeMessageHandler.getExtensionId(),extensionVersion:this.nativeMessageHandler.getExtensionVersion(),matsBrokerVersion:t.broker_version,matsAccountJoinOnStart:t.account_join_on_start,matsAccountJoinOnEnd:t.account_join_on_end,matsDeviceJoin:t.device_join,matsPromptBehavior:t.prompt_behavior,matsApiErrorCode:t.api_error_code,matsUiVisible:t.ui_visible,matsSilentCode:t.silent_code,matsSilentBiSubCode:t.silent_bi_sub_code,matsSilentMessage:t.silent_message,matsSilentStatus:t.silent_status,matsHttpStatus:t.http_status,matsHttpEventCount:t.http_event_count},this.correlationId),t):null}validateNativeResponse(e){if(e.hasOwnProperty("access_token")&&e.hasOwnProperty("id_token")&&e.hasOwnProperty("client_info")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scope")&&e.hasOwnProperty("expires_in"))return e;throw ke(ye,"Response missing expected properties.")}getMATSFromResponse(e){if(e.properties.MATS)try{return JSON.parse(e.properties.MATS)}catch(e){this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead")}return null}isResponseFromCache(e){return void 0===e.is_cached?(this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."),!1):!!e.is_cached}async initializeNativeRequest(e){this.logger.trace("NativeInteractionClient - initializeNativeRequest called");const t=e.authority||this.config.auth.authority;e.account&&await this.getDiscoveredAuthority({requestAuthority:t,requestAzureCloudOptions:e.azureCloudOptions,account:e.account});const r=new Hr(t);r.validateAsUri();const{scopes:n,...o}=e,i=new wr(n||[]);i.appendScopes(a);const s={...o,accountId:this.accountId,clientId:this.config.auth.clientId,authority:r.urlString,scope:i.printScopes(),redirectUri:this.getRedirectUri(e.redirectUri),prompt:(()=>{switch(this.apiId){case Zi.ssoSilent:case Zi.acquireTokenSilent_silentFlow:return this.logger.trace("initializeNativeRequest: silent request sets prompt to none"),R.NONE}if(e.prompt)switch(e.prompt){case R.NONE:case R.CONSENT:case R.LOGIN:return this.logger.trace("initializeNativeRequest: prompt is compatible with native flow"),e.prompt;default:throw this.logger.trace(`initializeNativeRequest: prompt = ${e.prompt} is not compatible with native flow`),Ti(hi)}else this.logger.trace("initializeNativeRequest: prompt was not provided")})(),correlationId:this.correlationId,tokenType:e.authenticationScheme,windowTitleSubstring:document.title,extraParameters:{...e.extraQueryParameters,...e.tokenQueryParameters},extendedExpiryToken:!1,keyId:e.popKid};if(s.signPopToken&&e.popKid)throw Ti(gi);if(this.handleExtraBrokerParams(s),s.extraParameters=s.extraParameters||{},s.extraParameters.telemetry=Pi,e.authenticationScheme===V.POP){const t={resourceRequestUri:e.resourceRequestUri,resourceRequestMethod:e.resourceRequestMethod,shrClaims:e.shrClaims,shrNonce:e.shrNonce},r=new Zn(this.browserCrypto);let n;if(s.keyId)n=this.browserCrypto.base64UrlEncode(JSON.stringify({kid:s.keyId})),s.signPopToken=!1;else{const o=await Sn(r.generateCnf.bind(r),vn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(t,this.logger);n=o.reqCnfString,s.keyId=o.kid,s.signPopToken=!0}s.reqCnf=n}return this.addRequestSKUs(s),s}handleExtraBrokerParams(e){const t=e.extraParameters&&e.extraParameters.hasOwnProperty(mn)&&e.extraParameters.hasOwnProperty(fn)&&e.extraParameters.hasOwnProperty(cn);if(!e.embeddedClientId&&!t)return;let r="";const n=e.redirectUri;e.embeddedClientId?(e.redirectUri=this.config.auth.redirectUri,r=e.embeddedClientId):e.extraParameters&&(e.redirectUri=e.extraParameters[fn],r=e.extraParameters[cn]),e.extraParameters={child_client_id:r,child_redirect_uri:n},this.performanceClient?.addFields({embeddedClientId:r,embeddedRedirectUri:n},e.correlationId)}}class Ws{constructor(e,t,r,n){this.logger=e,this.handshakeTimeoutMs=t,this.extensionId=n,this.resolvers=new Map,this.handshakeResolvers=new Map,this.messageChannel=new MessageChannel,this.windowListener=this.onWindowMessage.bind(this),this.performanceClient=r,this.handshakeEvent=r.startMeasurement(vn.NativeMessageHandlerHandshake)}async sendMessage(e){this.logger.trace("NativeMessageHandler - sendMessage called.");const t={channel:Ri,extensionId:this.extensionId,responseId:va(),body:e};return this.logger.trace("NativeMessageHandler - Sending request to browser extension"),this.logger.tracePii(`NativeMessageHandler - Sending request to browser extension: ${JSON.stringify(t)}`),this.messageChannel.port1.postMessage(t),new Promise(((e,r)=>{this.resolvers.set(t.responseId,{resolve:e,reject:r})}))}static async createProvider(e,t,r){e.trace("NativeMessageHandler - createProvider called.");try{const n=new Ws(e,t,r,_i);return await n.sendHandshakeRequest(),n}catch(n){const o=new Ws(e,t,r);return await o.sendHandshakeRequest(),o}}async sendHandshakeRequest(){this.logger.trace("NativeMessageHandler - sendHandshakeRequest called."),window.addEventListener("message",this.windowListener,!1);const e={channel:Ri,extensionId:this.extensionId,responseId:va(),body:{method:Mi}};return this.handshakeEvent.add({extensionId:this.extensionId,extensionHandshakeTimeoutMs:this.handshakeTimeoutMs}),this.messageChannel.port1.onmessage=e=>{this.onChannelMessage(e)},window.postMessage(e,window.origin,[this.messageChannel.port2]),new Promise(((t,r)=>{this.handshakeResolvers.set(e.responseId,{resolve:t,reject:r}),this.timeoutId=window.setTimeout((()=>{window.removeEventListener("message",this.windowListener,!1),this.messageChannel.port1.close(),this.messageChannel.port2.close(),this.handshakeEvent.end({extensionHandshakeTimedOut:!0,success:!1}),r(Ti(si)),this.handshakeResolvers.delete(e.responseId)}),this.handshakeTimeoutMs)}))}onWindowMessage(e){if(this.logger.trace("NativeMessageHandler - onWindowMessage called"),e.source!==window)return;const t=e.data;if(t.channel&&t.channel===Ri&&(!t.extensionId||t.extensionId===this.extensionId)&&t.body.method===Mi){const e=this.handshakeResolvers.get(t.responseId);if(!e)return void this.logger.trace(`NativeMessageHandler.onWindowMessage - resolver can't be found for request ${t.responseId}`);this.logger.verbose(t.extensionId?`Extension with id: ${t.extensionId} not installed`:"No extension installed"),clearTimeout(this.timeoutId),this.messageChannel.port1.close(),this.messageChannel.port2.close(),window.removeEventListener("message",this.windowListener,!1),this.handshakeEvent.end({success:!1,extensionInstalled:!1}),e.reject(Ti(ci))}}onChannelMessage(e){this.logger.trace("NativeMessageHandler - onChannelMessage called.");const t=e.data,r=this.resolvers.get(t.responseId),n=this.handshakeResolvers.get(t.responseId);try{const e=t.body.method;if(e===qi){if(!r)return;const e=t.body.response;if(this.logger.trace("NativeMessageHandler - Received response from browser extension"),this.logger.tracePii(`NativeMessageHandler - Received response from browser extension: ${JSON.stringify(e)}`),"Success"!==e.status)r.reject($s(e.code,e.description,e.ext));else{if(!e.result)throw ke(ye,"Event does not contain result.");e.result.code&&e.result.description?r.reject($s(e.result.code,e.result.description,e.result.ext)):r.resolve(e.result)}this.resolvers.delete(t.responseId)}else if(e===Ni){if(!n)return void this.logger.trace(`NativeMessageHandler.onChannelMessage - resolver can't be found for request ${t.responseId}`);clearTimeout(this.timeoutId),window.removeEventListener("message",this.windowListener,!1),this.extensionId=t.extensionId,this.extensionVersion=t.body.version,this.logger.verbose(`NativeMessageHandler - Received HandshakeResponse from extension: ${this.extensionId}`),this.handshakeEvent.end({extensionInstalled:!0,success:!0}),n.resolve(),this.handshakeResolvers.delete(t.responseId)}}catch(t){this.logger.error("Error parsing response from WAM Extension"),this.logger.errorPii(`Error parsing response from WAM Extension: ${t}`),this.logger.errorPii(`Unable to parse ${e}`),r?r.reject(t):n&&n.reject(t)}}getExtensionId(){return this.extensionId}getExtensionVersion(){return this.extensionVersion}static isNativeAvailable(e,t,r,n){if(t.trace("isNativeAvailable called"),!e.system.allowNativeBroker)return t.trace("isNativeAvailable: allowNativeBroker is not enabled, returning false"),!1;if(!r)return t.trace("isNativeAvailable: WAM extension provider is not initialized, returning false"),!1;if(n)switch(n){case V.BEARER:case V.POP:return t.trace("isNativeAvailable: authenticationScheme is supported, returning true"),!0;default:return t.trace("isNativeAvailable: authenticationScheme is not supported, returning false"),!1}return!0}}class js{constructor(e,t,r,n,o){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=o}async handleCodeResponse(e,t){let r;this.performanceClient.addQueueMeasurement(vn.HandleCodeResponse,t.correlationId);try{r=this.authModule.handleFragmentResponse(e,t.state)}catch(e){throw e instanceof Mn&&e.subError===Oo?Ti(Oo):e}return Sn(this.handleCodeResponseFromServer.bind(this),vn.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)(r,t)}async handleCodeResponseFromServer(e,t,r=!0){if(this.performanceClient.addQueueMeasurement(vn.HandleCodeResponseFromServer,t.correlationId),this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"),this.authCodeRequest.code=e.code,e.cloud_instance_host_name&&await Sn(this.authModule.updateAuthority.bind(this.authModule),vn.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,t.correlationId)(e.cloud_instance_host_name,t.correlationId),r&&(e.nonce=t.nonce||void 0),e.state=t.state,e.client_info)this.authCodeRequest.clientInfo=e.client_info;else{const e=this.createCcsCredentials(t);e&&(this.authCodeRequest.ccsCredential=e)}return await Sn(this.authModule.acquireToken.bind(this.authModule),vn.AuthClientAcquireToken,this.logger,this.performanceClient,t.correlationId)(this.authCodeRequest,e)}createCcsCredentials(e){return e.account?{credential:e.account.homeAccountId,type:an}:e.loginHint?{credential:e.loginHint,type:sn}:null}}function Vs(e,t,r){const n=Lr(e);if(!n)throw Ur(e)?(r.error(`A ${t} is present in the iframe but it does not contain known properties. It's likely that the ${t} has been replaced by code running on the redirectUri page.`),r.errorPii(`The ${t} detected is: ${e}`),Ti(Eo)):(r.error(`The request has returned to the redirectUri but a ${t} is not present. It's likely that the ${t} has been removed or the page has been redirected by code running on the redirectUri page.`),Ti(So));return n}class Ys extends xs{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,a,c,l),this.unloadWindow=this.unloadWindow.bind(this),this.nativeStorage=s}acquireToken(e){try{const t={popupName:this.generatePopupName(e.scopes||a,e.authority||this.config.auth.authority),popupWindowAttributes:e.popupWindowAttributes||{},popupWindowParent:e.popupWindowParent??window};return this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(e,t)):(this.logger.verbose("asyncPopup set to false, opening popup before acquiring token"),t.popup=this.openSizedPopup("about:blank",t),this.acquireTokenPopupAsync(e,t))}catch(e){return Promise.reject(e)}}logout(e){try{this.logger.verbose("logoutPopup called");const t=this.initializeLogoutRequest(e),r={popupName:this.generateLogoutPopupName(t),popupWindowAttributes:e?.popupWindowAttributes||{},popupWindowParent:e?.popupWindowParent??window},n=e&&e.authority,o=e&&e.mainWindowRedirectUri;return this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true"),this.logoutPopupAsync(t,r,n,o)):(this.logger.verbose("asyncPopup set to false, opening popup"),r.popup=this.openSizedPopup("about:blank",r),this.logoutPopupAsync(t,r,n,o))}catch(e){return Promise.reject(e)}}async acquireTokenPopupAsync(t,r){this.logger.verbose("acquireTokenPopupAsync called");const n=this.initializeServerTelemetryManager(Zi.acquireTokenPopup),o=await Sn(this.initializeAuthorizationRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(t,e.InteractionType.Popup);za(o.authority);try{const i=await Sn(this.initializeAuthorizationCodeRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(o),a=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:o.authority,requestAzureCloudOptions:o.azureCloudOptions,requestExtraQueryParameters:o.extraQueryParameters,account:o.account}),s=Ws.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme);let c;s&&(c=this.performanceClient.startMeasurement(vn.FetchAccountIdWithNativeBroker,t.correlationId));const l=await a.getAuthCodeUrl({...o,nativeBroker:s}),d=new js(a,this.browserStorage,i,this.logger,this.performanceClient),h=this.initiateAuthRequest(l,r);this.eventHandler.emitEvent(Ms.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:h},null);const u=await this.monitorPopupForHash(h,r.popupWindowParent),g=kn(Vs,vn.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(u,this.config.auth.OIDCOptions.serverResponseType,this.logger);if(Nn.removeThrottle(this.browserStorage,this.config.auth.clientId,i),g.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),c&&c.end({success:!0,isNativeBroker:!0}),!this.nativeMessageHandler)throw Ti(li);const e=new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,g.accountId,this.nativeStorage,o.correlationId),{userRequestState:t}=Jn.parseRequestState(this.browserCrypto,o.state);return await e.acquireToken({...o,state:t,prompt:void 0})}return await d.handleCodeResponse(g,o)}catch(e){throw r.popup?.close(),e instanceof Ae&&(e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e)),e}}async logoutPopupAsync(t,r,n,o){this.logger.verbose("logoutPopupAsync called"),this.eventHandler.emitEvent(Ms.LOGOUT_START,e.InteractionType.Popup,t);const i=this.initializeServerTelemetryManager(Zi.logoutPopup);try{await this.clearCacheOnLogout(t.account);const a=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:i,requestAuthority:n,account:t.account||void 0});try{a.authority.endSessionEndpoint}catch{if(t.account?.homeAccountId&&t.postLogoutRedirectUri&&a.authority.protocolMode===Or.OIDC){if(this.browserStorage.removeAccount(t.account?.homeAccountId,this.correlationId),this.eventHandler.emitEvent(Ms.LOGOUT_SUCCESS,e.InteractionType.Popup,t),o){const e={apiId:Zi.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=Hr.getAbsoluteUrl(o,qa());await this.navigationClient.navigateInternal(t,e)}return void r.popup?.close()}}const s=a.getLogoutUri(t);this.eventHandler.emitEvent(Ms.LOGOUT_SUCCESS,e.InteractionType.Popup,t);const c=this.openPopup(s,r);if(this.eventHandler.emitEvent(Ms.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:c},null),await this.monitorPopupForHash(c,r.popupWindowParent).catch((()=>{})),o){const e={apiId:Zi.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=Hr.getAbsoluteUrl(o,qa());this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii(`Redirecting main window to: ${t}`),await this.navigationClient.navigateInternal(t,e)}else this.logger.verbose("No main window navigation requested")}catch(t){throw r.popup?.close(),t instanceof Ae&&(t.setCorrelationId(this.correlationId),i.cacheFailedRequest(t)),this.browserStorage.setInteractionInProgress(!1),this.eventHandler.emitEvent(Ms.LOGOUT_FAILURE,e.InteractionType.Popup,null,t),this.eventHandler.emitEvent(Ms.LOGOUT_END,e.InteractionType.Popup),t}this.eventHandler.emitEvent(Ms.LOGOUT_END,e.InteractionType.Popup)}initiateAuthRequest(e,t){if(e)return this.logger.infoPii(`Navigate to: ${e}`),this.openPopup(e,t);throw this.logger.error("Navigate url is empty"),Ti(ko)}monitorPopupForHash(e,t){return new Promise(((t,r)=>{this.logger.verbose("PopupHandler.monitorPopupForHash - polling started");const n=setInterval((()=>{if(e.closed)return this.logger.error("PopupHandler.monitorPopupForHash - window closed"),clearInterval(n),void r(Ti(Oo));let o="";try{o=e.location.href}catch(e){}if(!o||"about:blank"===o)return;clearInterval(n);let i="";const a=this.config.auth.OIDCOptions.serverResponseType;e&&(i=a===P.QUERY?e.location.search:e.location.hash),this.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"),t(i)}),this.config.system.pollIntervalMilliseconds)})).finally((()=>{this.cleanPopup(e,t)}))}openPopup(e,t){try{let r;if(t.popup?(r=t.popup,this.logger.verbosePii(`Navigating popup window to: ${e}`),r.location.assign(e)):void 0===t.popup&&(this.logger.verbosePii(`Opening popup window to: ${e}`),r=this.openSizedPopup(e,t)),!r)throw Ti(No);return r.focus&&r.focus(),this.currentWindow=r,t.popupWindowParent.addEventListener("beforeunload",this.unloadWindow),r}catch(e){throw this.logger.error("error opening popup "+e.message),this.browserStorage.setInteractionInProgress(!1),Ti(Mo)}}openSizedPopup(e,{popupName:t,popupWindowAttributes:r,popupWindowParent:n}){const o=n.screenLeft?n.screenLeft:n.screenX,i=n.screenTop?n.screenTop:n.screenY,a=n.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,s=n.innerHeight||document.documentElement.clientHeight||document.body.clientHeight;let c=r.popupSize?.width,l=r.popupSize?.height,d=r.popupPosition?.top,h=r.popupPosition?.left;return(!c||c<0||c>a)&&(this.logger.verbose("Default popup window width used. Window width not configured or invalid."),c=Ai),(!l||l<0||l>s)&&(this.logger.verbose("Default popup window height used. Window height not configured or invalid."),l=ki),(!d||d<0||d>s)&&(this.logger.verbose("Default popup window top position used. Window top not configured or invalid."),d=Math.max(0,s/2-ki/2+i)),(!h||h<0||h>a)&&(this.logger.verbose("Default popup window left position used. Window left not configured or invalid."),h=Math.max(0,a/2-Ai/2+o)),n.open(e,t,`width=${c}, height=${l}, top=${d}, left=${h}, scrollbars=yes`)}unloadWindow(t){this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Popup),this.currentWindow&&this.currentWindow.close(),t.preventDefault()}cleanPopup(e,t){e.close(),t.removeEventListener("beforeunload",this.unloadWindow),this.browserStorage.setInteractionInProgress(!1)}generatePopupName(e,t){return`${Si}.${this.config.auth.clientId}.${e.join("-")}.${t}.${this.correlationId}`}generateLogoutPopupName(e){const t=e.account&&e.account.homeAccountId;return`${Si}.${this.config.auth.clientId}.${t}.${this.correlationId}`}}class Js{constructor(e,t,r,n,o){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=o}async initiateAuthRequest(e,t){if(this.logger.verbose("RedirectHandler.initiateAuthRequest called"),e){t.redirectStartPage&&(this.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page"),this.browserStorage.setTemporaryCache(Fi,t.redirectStartPage,!0)),this.browserStorage.setTemporaryCache(Qi,this.authCodeRequest.correlationId,!0),this.browserStorage.cacheCodeRequest(this.authCodeRequest),this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${e}`);const r={apiId:Zi.acquireTokenRedirect,timeout:t.redirectTimeout,noHistory:!1};if("function"==typeof t.onRedirectNavigate){this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback");return!1!==t.onRedirectNavigate(e)?(this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),void await t.navigationClient.navigateExternal(e,r)):void this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation")}return this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),void await t.navigationClient.navigateExternal(e,r)}throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),Ti(ko)}async handleCodeResponse(e,t){this.logger.verbose("RedirectHandler.handleCodeResponse called"),this.browserStorage.setInteractionInProgress(!1);const r=this.browserStorage.generateStateKey(t),n=this.browserStorage.getTemporaryCache(r);if(!n)throw vt(Ue,"Cached State");let o;try{o=this.authModule.handleFragmentResponse(e,n)}catch(e){throw e instanceof Mn&&e.subError===Oo?Ti(Oo):e}const i=this.browserStorage.generateNonceKey(n),a=this.browserStorage.getTemporaryCache(i);if(this.authCodeRequest.code=o.code,o.cloud_instance_host_name&&await Sn(this.authModule.updateAuthority.bind(this.authModule),vn.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,this.authCodeRequest.correlationId)(o.cloud_instance_host_name,this.authCodeRequest.correlationId),o.nonce=a||void 0,o.state=n,o.client_info)this.authCodeRequest.clientInfo=o.client_info;else{const e=this.checkCcsCredentials();e&&(this.authCodeRequest.ccsCredential=e)}const s=await this.authModule.acquireToken(this.authCodeRequest,o);return this.browserStorage.cleanRequestByState(t),s}checkCcsCredentials(){const e=this.browserStorage.getTemporaryCache(Gi,!0);if(e)try{return JSON.parse(e)}catch(t){this.authModule.logger.error("Cache credential could not be parsed"),this.authModule.logger.errorPii(`Cache credential could not be parsed: ${e}`)}return null}}class Xs extends xs{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,a,c,l),this.nativeStorage=s}async acquireToken(t){const r=await Sn(this.initializeAuthorizationRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(t,e.InteractionType.Redirect);this.browserStorage.updateCacheEntries(r.state,r.nonce,r.authority,r.loginHint||"",r.account||null);const n=this.initializeServerTelemetryManager(Zi.acquireTokenRedirect),o=t=>{t.persisted&&(this.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."),this.browserStorage.cleanRequestByState(r.state),this.eventHandler.emitEvent(Ms.RESTORE_FROM_BFCACHE,e.InteractionType.Redirect))};try{const e=await Sn(this.initializeAuthorizationCodeRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,this.correlationId)(r),i=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:r.authority,requestAzureCloudOptions:r.azureCloudOptions,requestExtraQueryParameters:r.extraQueryParameters,account:r.account}),a=new Js(i,this.browserStorage,e,this.logger,this.performanceClient),s=await i.getAuthCodeUrl({...r,nativeBroker:Ws.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme)}),c=this.getRedirectStartPage(t.redirectStartPage);return this.logger.verbosePii(`Redirect start page: ${c}`),window.addEventListener("pageshow",o),await a.initiateAuthRequest(s,{navigationClient:this.navigationClient,redirectTimeout:this.config.system.redirectNavigationTimeout,redirectStartPage:c,onRedirectNavigate:t.onRedirectNavigate||this.config.auth.onRedirectNavigate})}catch(e){throw e instanceof Ae&&(e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e)),window.removeEventListener("pageshow",o),this.browserStorage.cleanRequestByState(r.state),e}}async handleRedirectPromise(r="",n){const o=this.initializeServerTelemetryManager(Zi.handleRedirectPromise);try{if(!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const[i,a]=this.getRedirectResponse(r||"");if(!i)return this.logger.info("handleRedirectPromise did not detect a response as a result of a redirect. Cleaning temporary cache."),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),"back_forward"!==function(){if("undefined"==typeof window||void 0===window.performance||"function"!=typeof window.performance.getEntriesByType)return;const e=window.performance.getEntriesByType("navigation"),t=e.length?e[0]:void 0;return t?.type}()?n.event.errorCode="no_server_response":this.logger.verbose("Back navigation event detected. Muting no_server_response error"),null;const s=this.browserStorage.getTemporaryCache(Fi,!0)||t.EMPTY_STRING,c=Hr.removeHashFromUrl(s);if(c===Hr.removeHashFromUrl(window.location.href)&&this.config.auth.navigateToLoginRequestUrl){this.logger.verbose("Current page is loginRequestUrl, handling response"),s.indexOf("#")>-1&&Ma(s);return await this.handleResponse(i,o)}if(!this.config.auth.navigateToLoginRequestUrl)return this.logger.verbose("NavigateToLoginRequestUrl set to false, handling response"),await this.handleResponse(i,o);if(!Na()||this.config.system.allowRedirectInIframe){this.browserStorage.setTemporaryCache(Ki,a,!0);const e={apiId:Zi.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0};let t=!0;if(s&&"null"!==s)this.logger.verbose(`Navigating to loginRequestUrl: ${s}`),t=await this.navigationClient.navigateInternal(s,e);else{const r=Ua();this.browserStorage.setTemporaryCache(Fi,r,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),t=await this.navigationClient.navigateInternal(r,e)}if(!t)return await this.handleResponse(i,o)}return null}catch(t){throw t instanceof Ae&&(t.setCorrelationId(this.correlationId),o.cacheFailedRequest(t)),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),t}}getRedirectResponse(t){this.logger.verbose("getRedirectResponseHash called");let r=t;r||(r=this.config.auth.OIDCOptions.serverResponseType===P.QUERY?window.location.search:window.location.hash);let n=Lr(r);if(n){try{!function(e,t,r){if(!e.state)throw Ti(bo);const n=vs(t,e.state);if(!n)throw Ti(Ro);if(n.interactionType!==r)throw Ti(_o)}(n,this.browserCrypto,e.InteractionType.Redirect)}catch(e){return e instanceof Ae&&this.logger.error(`Interaction type validation failed due to ${e.errorCode}: ${e.errorMessage}`),[null,""]}return Pa(window),this.logger.verbose("Hash contains known properties, returning response hash"),[n,r]}const o=this.browserStorage.getTemporaryCache(Ki,!0);return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(Ki)),o&&(n=Lr(o),n)?(this.logger.verbose("Hash does not contain known properties, returning cached hash"),[n,o]):[null,""]}async handleResponse(e,t){const r=e.state;if(!r)throw Ti(bo);const n=this.browserStorage.getCachedRequest(r);if(this.logger.verbose("handleResponse called, retrieved cached request"),e.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw Ti(li);const t=new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,e.accountId,this.nativeStorage,n.correlationId),{userRequestState:o}=Jn.parseRequestState(this.browserCrypto,r);return t.acquireToken({...n,state:o,prompt:void 0}).finally((()=>{this.browserStorage.cleanRequestByState(r)}))}const o=this.browserStorage.getCachedAuthority(r);if(!o)throw Ti(Go);const i=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:o});Nn.removeThrottle(this.browserStorage,this.config.auth.clientId,n);return new Js(i,this.browserStorage,n,this.logger,this.performanceClient).handleCodeResponse(e,r)}async logout(t){this.logger.verbose("logoutRedirect called");const r=this.initializeLogoutRequest(t),n=this.initializeServerTelemetryManager(Zi.logout);try{this.eventHandler.emitEvent(Ms.LOGOUT_START,e.InteractionType.Redirect,t),await this.clearCacheOnLogout(r.account);const o={apiId:Zi.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},i=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:t&&t.authority,requestExtraQueryParameters:t?.extraQueryParameters,account:t&&t.account||void 0});if(i.authority.protocolMode===Or.OIDC)try{i.authority.endSessionEndpoint}catch{if(r.account?.homeAccountId)return this.browserStorage.removeAccount(r.account?.homeAccountId,this.correlationId),void this.eventHandler.emitEvent(Ms.LOGOUT_SUCCESS,e.InteractionType.Redirect,r)}const a=i.getLogoutUri(r);if(this.eventHandler.emitEvent(Ms.LOGOUT_SUCCESS,e.InteractionType.Redirect,r),!t||"function"!=typeof t.onRedirectNavigate)return this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),void await this.navigationClient.navigateExternal(a,o);if(!1!==t.onRedirectNavigate(a))return this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),void await this.navigationClient.navigateExternal(a,o);this.browserStorage.setInteractionInProgress(!1),this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation")}catch(t){throw t instanceof Ae&&(t.setCorrelationId(this.correlationId),n.cacheFailedRequest(t)),this.eventHandler.emitEvent(Ms.LOGOUT_FAILURE,e.InteractionType.Redirect,null,t),this.eventHandler.emitEvent(Ms.LOGOUT_END,e.InteractionType.Redirect),t}this.eventHandler.emitEvent(Ms.LOGOUT_END,e.InteractionType.Redirect)}getRedirectStartPage(e){const t=e||window.location.href;return Hr.getAbsoluteUrl(t,qa())}}async function Zs(e,t,r,n,o){if(t.addQueueMeasurement(vn.SilentHandlerInitiateAuthRequest,n),!e)throw r.info("Navigate url is empty"),Ti(ko);return o?Sn(tc,vn.SilentHandlerLoadFrame,r,t,n)(e,o,t,n):kn(rc,vn.SilentHandlerLoadFrameSync,r,t,n)(e)}async function ec(e,t,r,n,o,i,a){return n.addQueueMeasurement(vn.SilentHandlerMonitorIframeForHash,i),new Promise(((n,i)=>{t<Ya&&o.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${t}ms) than the default (10000ms). This may result in timeouts.`);const s=window.setTimeout((()=>{window.clearInterval(c),i(Ti(Uo))}),t),c=window.setInterval((()=>{let t="";const r=e.contentWindow;try{t=r?r.location.href:""}catch(e){}if(!t||"about:blank"===t)return;let o="";r&&(o=a===P.QUERY?r.location.search:r.location.hash),window.clearTimeout(s),window.clearInterval(c),n(o)}),r)})).finally((()=>{kn(oc,vn.RemoveHiddenIframe,o,n,i)(e)}))}function tc(e,t,r,n){return r.addQueueMeasurement(vn.SilentHandlerLoadFrame,n),new Promise(((r,n)=>{const o=nc();window.setTimeout((()=>{o?(o.src=e,r(o)):n("Unable to load iframe")}),t)}))}function rc(e){const t=nc();return t.src=e,t}function nc(){const e=document.createElement("iframe");return e.className="msalSilentIframe",e.style.visibility="hidden",e.style.position="absolute",e.style.width=e.style.height="0",e.style.border="0",e.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.body.appendChild(e),e}function oc(e){document.body===e.parentNode&&document.body.removeChild(e)}class ic extends xs{constructor(e,t,r,n,o,i,a,s,c,l,d){super(e,t,r,n,o,i,s,l,d),this.apiId=a,this.nativeStorage=c}async acquireToken(t){this.performanceClient.addQueueMeasurement(vn.SilentIframeClientAcquireToken,t.correlationId),t.loginHint||t.sid||t.account&&t.account.username||this.logger.warning("No user hint provided. The authorization server may need more information to complete this request.");const r={...t};r.prompt?r.prompt!==R.NONE&&r.prompt!==R.NO_SESSION&&(this.logger.warning(`SilentIframeClient. Replacing invalid prompt ${r.prompt} with ${R.NONE}`),r.prompt=R.NONE):r.prompt=R.NONE;const n=await Sn(this.initializeAuthorizationRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,t.correlationId)(r,e.InteractionType.Silent);za(n.authority);const o=this.initializeServerTelemetryManager(this.apiId);let i;try{return i=await Sn(this.createAuthCodeClient.bind(this),vn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,t.correlationId)({serverTelemetryManager:o,requestAuthority:n.authority,requestAzureCloudOptions:n.azureCloudOptions,requestExtraQueryParameters:n.extraQueryParameters,account:n.account}),await Sn(this.silentTokenHelper.bind(this),vn.SilentIframeClientTokenHelper,this.logger,this.performanceClient,t.correlationId)(i,n)}catch(n){if(n instanceof Ae&&(n.setCorrelationId(this.correlationId),o.cacheFailedRequest(n)),!(i&&n instanceof Ae&&n.errorCode===wi))throw n;this.performanceClient.addFields({retryError:n.errorCode},this.correlationId);const a=await Sn(this.initializeAuthorizationRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,t.correlationId)(r,e.InteractionType.Silent);return await Sn(this.silentTokenHelper.bind(this),vn.SilentIframeClientTokenHelper,this.logger,this.performanceClient,this.correlationId)(i,a)}}logout(){return Promise.reject(Ti(Do))}async silentTokenHelper(e,t){const r=t.correlationId;this.performanceClient.addQueueMeasurement(vn.SilentIframeClientTokenHelper,r);const n=await Sn(this.initializeAuthorizationCodeRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationCodeRequest,this.logger,this.performanceClient,r)(t),o=await Sn(e.getAuthCodeUrl.bind(e),vn.GetAuthCodeUrl,this.logger,this.performanceClient,r)({...t,nativeBroker:Ws.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme)}),i=new js(e,this.browserStorage,n,this.logger,this.performanceClient),a=await Sn(Zs,vn.SilentHandlerInitiateAuthRequest,this.logger,this.performanceClient,r)(o,this.performanceClient,this.logger,r,this.config.system.navigateFrameWait),s=this.config.auth.OIDCOptions.serverResponseType,c=await Sn(ec,vn.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,r)(a,this.config.system.iframeHashTimeout,this.config.system.pollIntervalMilliseconds,this.performanceClient,this.logger,r,s),l=kn(Vs,vn.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(c,s,this.logger);if(l.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw Ti(li);const e=new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.apiId,this.performanceClient,this.nativeMessageHandler,l.accountId,this.browserStorage,r),{userRequestState:n}=Jn.parseRequestState(this.browserCrypto,t.state);return Sn(e.acquireToken.bind(e),vn.NativeInteractionClientAcquireToken,this.logger,this.performanceClient,r)({...t,state:n,prompt:t.prompt||R.NONE})}return Sn(i.handleCodeResponse.bind(i),vn.HandleCodeResponse,this.logger,this.performanceClient,r)(l,t)}}class ac extends xs{async acquireToken(e){this.performanceClient.addQueueMeasurement(vn.SilentRefreshClientAcquireToken,e.correlationId);const t=await Sn(Hs,vn.InitializeBaseRequest,this.logger,this.performanceClient,e.correlationId)(e,this.config,this.performanceClient,this.logger),r={...e,...t};e.redirectUri&&(r.redirectUri=this.getRedirectUri(e.redirectUri));const n=this.initializeServerTelemetryManager(Zi.acquireTokenSilent_silentFlow),o=await this.createRefreshTokenClient({serverTelemetryManager:n,authorityUrl:r.authority,azureCloudOptions:r.azureCloudOptions,account:r.account});return Sn(o.acquireTokenByRefreshToken.bind(o),vn.RefreshTokenClientAcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(r).catch((e=>{throw e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e),e}))}logout(){return Promise.reject(Ti(Do))}async createRefreshTokenClient(e){const t=await Sn(this.getClientConfiguration.bind(this),vn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:e.serverTelemetryManager,requestAuthority:e.authorityUrl,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account});return new io(t,this.performanceClient)}}class sc{constructor(e,t,r,n){this.isBrowserEnvironment="undefined"!=typeof window,this.config=e,this.storage=t,this.logger=r,this.cryptoObj=n}loadExternalTokens(e,t,r){if(!this.isBrowserEnvironment)throw Ti(jo);const n=e.correlationId||va(),o=t.id_token?bt(t.id_token,hs):void 0,i={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},a=e.authority?new En(En.generateAuthority(e.authority,e.azureCloudOptions),this.config.system.networkClient,this.storage,i,this.logger,e.correlationId||va()):void 0,s=this.loadAccount(e,r.clientInfo||t.client_info||"",n,o,a),c=this.loadIdToken(t,s.homeAccountId,s.environment,s.realm,n),l=this.loadAccessToken(e,t,s.homeAccountId,s.environment,s.realm,r,n),d=this.loadRefreshToken(t,s.homeAccountId,s.environment,n);return this.generateAuthenticationResult(e,{account:s,idToken:c,accessToken:l,refreshToken:d},o,a)}loadAccount(e,t,r,n,o){if(this.logger.verbose("TokenCache - loading account"),e.account){const t=qr.createFromAccountInfo(e.account);return this.storage.setAccount(t,r),t}if(!o||!t&&!n)throw this.logger.error("TokenCache - if an account is not provided on the request, authority and either clientInfo or idToken must be provided instead."),Ti(ei);const i=qr.generateHomeAccountId(t,o.authorityType,this.logger,this.cryptoObj,n),a=n?.tid,s=ro(this.storage,o,i,hs,r,n,t,o.hostnameAndPort,a,void 0,void 0,this.logger);return this.storage.setAccount(s,r),s}loadIdToken(e,t,r,n,o){if(!e.id_token)return this.logger.verbose("TokenCache - no id token found in response"),null;this.logger.verbose("TokenCache - loading id token");const i=Nt(t,r,e.id_token,this.config.auth.clientId,n);return this.storage.setIdTokenCredential(i,o),i}loadAccessToken(e,t,r,n,o,i,a){if(!t.access_token)return this.logger.verbose("TokenCache - no access token found in response"),null;if(!t.expires_in)return this.logger.error("TokenCache - no expiration set on the access token. Cannot add it to the cache."),null;if(!(t.scope||e.scopes&&e.scopes.length))return this.logger.error("TokenCache - scopes not specified in the request or response. Cannot add token to the cache."),null;this.logger.verbose("TokenCache - loading access token");const s=t.scope?wr.fromString(t.scope):new wr(e.scopes),c=i.expiresOn||t.expires_in+(new Date).getTime()/1e3,l=i.extendedExpiresOn||(t.ext_expires_in||t.expires_in)+(new Date).getTime()/1e3,d=Ot(r,n,t.access_token,this.config.auth.clientId,o,s.printScopes(),c,l,hs);return this.storage.setAccessTokenCredential(d,a),d}loadRefreshToken(e,t,r,n){if(!e.refresh_token)return this.logger.verbose("TokenCache - no refresh token found in response"),null;this.logger.verbose("TokenCache - loading refresh token");const o=qt(t,r,e.refresh_token,this.config.auth.clientId,e.foci,void 0,e.refresh_token_expires_in);return this.storage.setRefreshTokenCredential(o,n),o}generateAuthenticationResult(e,t,r,n){let o,i="",a=[],s=null;t?.accessToken&&(i=t.accessToken.secret,a=wr.fromString(t.accessToken.target).asArray(),s=new Date(1e3*Number(t.accessToken.expiresOn)),o=new Date(1e3*Number(t.accessToken.extendedExpiresOn)));const c=t.account;return{authority:n?n.canonicalAuthority:"",uniqueId:t.account.localAccountId,tenantId:t.account.realm,scopes:a,account:c.getAccountInfo(),idToken:t.idToken?.secret||"",idTokenClaims:r||{},accessToken:i,fromCache:!0,expiresOn:s,correlationId:e.correlationId||"",requestId:"",extExpiresOn:o,familyId:t.refreshToken?.familyId||"",tokenType:t?.accessToken?.tokenType||"",state:e.state||"",cloudGraphHostName:c.cloudGraphHostName||"",msGraphHost:c.msGraphHost||"",fromNativeBroker:!1}}}class cc extends oo{constructor(e){super(e),this.includeRedirectUri=!1}}class lc extends xs{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,s,c,l),this.apiId=a}async acquireToken(t){if(!t.code)throw Ti(ri);const r=await Sn(this.initializeAuthorizationRequest.bind(this),vn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,t.correlationId)(t,e.InteractionType.Silent),n=this.initializeServerTelemetryManager(this.apiId);try{const e={...r,code:t.code},o=await Sn(this.getClientConfiguration.bind(this),vn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,t.correlationId)({serverTelemetryManager:n,requestAuthority:r.authority,requestAzureCloudOptions:r.azureCloudOptions,requestExtraQueryParameters:r.extraQueryParameters,account:r.account}),i=new cc(o);this.logger.verbose("Auth code client created");const a=new js(i,this.browserStorage,e,this.logger,this.performanceClient);return await Sn(a.handleCodeResponseFromServer.bind(a),vn.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)({code:t.code,msgraph_host:t.msGraphHost,cloud_graph_host_name:t.cloudGraphHostName,cloud_instance_host_name:t.cloudInstanceHostName},r,!1)}catch(e){throw e instanceof Ae&&(e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e)),e}}logout(){return Promise.reject(Ti(Do))}}function dc(e){const t=e?.idTokenClaims;return t?.tfp||t?.acr?"B2C":t?.tid?"9188040d-6c67-4c5b-b112-36a304b66dad"===t?.tid?"MSA":"AAD":void 0}function hc(e,t){try{Fa(e)}catch(e){throw t.end({success:!1},e),e}}class uc{constructor(e){this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.initialized=!1,this.logger=this.operatingContext.getLogger(),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,this.hybridAuthCodeResponses=new Map,this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new ms(this.logger,this.performanceClient):It,this.eventHandler=new Ns(this.logger),this.browserStorage=this.isBrowserEnvironment?new ws(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,_n(this.config.auth),this.performanceClient):As(this.config.auth.clientId,this.logger);const t={cacheLocation:Ui.MemoryStorage,temporaryCacheLocation:Ui.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1};this.nativeInternalStorage=new ws(this.config.auth.clientId,t,this.browserCrypto,this.logger,void 0,this.performanceClient),this.tokenCache=new sc(this.config,this.browserStorage,this.logger,this.browserCrypto),this.activeSilentTokenRequests=new Map,this.trackPageVisibility=this.trackPageVisibility.bind(this),this.trackPageVisibilityWithMeasurement=this.trackPageVisibilityWithMeasurement.bind(this),this.listeningToStorageEvents=!1,this.handleAccountCacheChange=this.handleAccountCacheChange.bind(this)}static async createController(e,t){const r=new uc(e);return await r.initialize(t),r}trackPageVisibility(e){e&&(this.logger.info("Perf: Visibility change detected"),this.performanceClient.incrementFields({visibilityChangeCount:1},e))}async initialize(e){if(this.logger.trace("initialize called"),this.initialized)return void this.logger.info("initialize has already been called, exiting early.");if(!this.isBrowserEnvironment)return this.logger.info("in non-browser environment, exiting early."),this.initialized=!0,void this.eventHandler.emitEvent(Ms.INITIALIZE_END);const t=e?.correlationId||this.getRequestCorrelationId(),r=this.config.system.allowNativeBroker,n=this.performanceClient.startMeasurement(vn.InitializeClientApplication,t);if(this.eventHandler.emitEvent(Ms.INITIALIZE_START),r)try{this.nativeExtensionProvider=await Ws.createProvider(this.logger,this.config.system.nativeBrokerHandshakeTimeout,this.performanceClient)}catch(e){this.logger.verbose(e)}this.config.cache.claimsBasedCachingEnabled||(this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"),await Sn(this.browserStorage.clearTokensAndKeysWithClaims.bind(this.browserStorage),vn.ClearTokensAndKeysWithClaims,this.logger,this.performanceClient,t)(this.performanceClient,t)),this.initialized=!0,this.eventHandler.emitEvent(Ms.INITIALIZE_END),n.end({allowNativeBroker:r,success:!0})}async handleRedirectPromise(e){if(this.logger.verbose("handleRedirectPromise called"),Da(this.initialized),this.isBrowserEnvironment){const t=e||"";let r=this.redirectResponse.get(t);return void 0===r?(r=this.handleRedirectPromiseInternal(e),this.redirectResponse.set(t,r),this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise")):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),r}return this.logger.verbose("handleRedirectPromise returns null, not browser environment"),null}async handleRedirectPromiseInternal(t){const r=this.getAllAccounts(),n=this.browserStorage.getCachedNativeRequest(),o=n&&Ws.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider)&&this.nativeExtensionProvider&&!t,i=o?n?.correlationId:this.browserStorage.getTemporaryCache(Qi,!0)||"",a=this.performanceClient.startMeasurement(vn.AcquireTokenRedirect,i);let s;if(this.eventHandler.emitEvent(Ms.HANDLE_REDIRECT_START,e.InteractionType.Redirect),o&&this.nativeExtensionProvider){this.logger.trace("handleRedirectPromise - acquiring token from native platform");const e=new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.handleRedirectPromise,this.performanceClient,this.nativeExtensionProvider,n.accountId,this.nativeInternalStorage,n.correlationId);s=Sn(e.handleRedirectPromise.bind(e),vn.HandleNativeRedirectPromiseMeasurement,this.logger,this.performanceClient,a.event.correlationId)(this.performanceClient,a.event.correlationId)}else{this.logger.trace("handleRedirectPromise - acquiring token from web flow");const e=this.createRedirectClient(i);s=Sn(e.handleRedirectPromise.bind(e),vn.HandleRedirectPromiseMeasurement,this.logger,this.performanceClient,a.event.correlationId)(t,a)}return s.then((t=>{if(t){r.length<this.getAllAccounts().length?(this.eventHandler.emitEvent(Ms.LOGIN_SUCCESS,e.InteractionType.Redirect,t),this.logger.verbose("handleRedirectResponse returned result, login success")):(this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Redirect,t),this.logger.verbose("handleRedirectResponse returned result, acquire token success")),a.end({success:!0,accountType:dc(t.account)})}else a.event.errorCode?a.end({success:!1}):a.discard();return this.eventHandler.emitEvent(Ms.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})).catch((t=>{const n=t;throw r.length>0?this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,n):this.eventHandler.emitEvent(Ms.LOGIN_FAILURE,e.InteractionType.Redirect,null,n),this.eventHandler.emitEvent(Ms.HANDLE_REDIRECT_END,e.InteractionType.Redirect),a.end({success:!1},n),t}))}async acquireTokenRedirect(t){const r=this.getRequestCorrelationId(t);this.logger.verbose("acquireTokenRedirect called",r);const n=this.performanceClient.startMeasurement(vn.AcquireTokenPreRedirect,r);n.add({accountType:dc(t.account),scenarioId:t.scenarioId});const o=t.onRedirectNavigate;if(o)t.onRedirectNavigate=e=>{const t="function"==typeof o?o(e):void 0;return!1!==t?n.end({success:!0}):n.discard(),t};else{const e=this.config.auth.onRedirectNavigate;this.config.auth.onRedirectNavigate=t=>{const r="function"==typeof e?e(t):void 0;return!1!==r?n.end({success:!0}):n.discard(),r}}const i=this.getAllAccounts().length>0;try{let o;if(Ka(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0),i?this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_START,e.InteractionType.Redirect,t):this.eventHandler.emitEvent(Ms.LOGIN_START,e.InteractionType.Redirect,t),this.nativeExtensionProvider&&this.canUseNative(t)){o=new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.acquireTokenRedirect,this.performanceClient,this.nativeExtensionProvider,this.getNativeAccountId(t),this.nativeInternalStorage,r).acquireTokenRedirect(t,n).catch((e=>{if(e instanceof Ks&&zs(e)){this.nativeExtensionProvider=void 0;return this.createRedirectClient(r).acquireToken(t)}if(e instanceof jn){this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow");return this.createRedirectClient(r).acquireToken(t)}throw this.browserStorage.setInteractionInProgress(!1),e}))}else{o=this.createRedirectClient(r).acquireToken(t)}return await o}catch(t){throw n.end({success:!1},t),i?this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,t):this.eventHandler.emitEvent(Ms.LOGIN_FAILURE,e.InteractionType.Redirect,null,t),t}}acquireTokenPopup(t){const r=this.getRequestCorrelationId(t),n=this.performanceClient.startMeasurement(vn.AcquireTokenPopup,r);n.add({scenarioId:t.scenarioId,accountType:dc(t.account)});try{this.logger.verbose("acquireTokenPopup called",r),hc(this.initialized,n),this.browserStorage.setInteractionInProgress(!0)}catch(e){return Promise.reject(e)}const o=this.getAllAccounts();let i;if(o.length>0?this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_START,e.InteractionType.Popup,t):this.eventHandler.emitEvent(Ms.LOGIN_START,e.InteractionType.Popup,t),this.canUseNative(t))i=this.acquireTokenNative({...t,correlationId:r},Zi.acquireTokenPopup).then((e=>(this.browserStorage.setInteractionInProgress(!1),n.end({success:!0,isNativeBroker:!0,accountType:dc(e.account)}),e))).catch((e=>{if(e instanceof Ks&&zs(e)){this.nativeExtensionProvider=void 0;return this.createPopupClient(r).acquireToken(t)}if(e instanceof jn){this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow");return this.createPopupClient(r).acquireToken(t)}throw this.browserStorage.setInteractionInProgress(!1),e}));else{i=this.createPopupClient(r).acquireToken(t)}return i.then((t=>(o.length<this.getAllAccounts().length?this.eventHandler.emitEvent(Ms.LOGIN_SUCCESS,e.InteractionType.Popup,t):this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,t),n.end({success:!0,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:dc(t.account)}),t))).catch((t=>(o.length>0?this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,t):this.eventHandler.emitEvent(Ms.LOGIN_FAILURE,e.InteractionType.Popup,null,t),n.end({success:!1},t),Promise.reject(t))))}trackPageVisibilityWithMeasurement(){const e=this.ssoSilentMeasurement||this.acquireTokenByCodeAsyncMeasurement;e&&(this.logger.info("Perf: Visibility change detected in ",e.event.name),e.increment({visibilityChangeCount:1}))}async ssoSilent(t){const r=this.getRequestCorrelationId(t),n={...t,prompt:t.prompt,correlationId:r};let o;if(this.ssoSilentMeasurement=this.performanceClient.startMeasurement(vn.SsoSilent,r),this.ssoSilentMeasurement?.add({scenarioId:t.scenarioId,accountType:dc(t.account)}),hc(this.initialized,this.ssoSilentMeasurement),this.ssoSilentMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),this.logger.verbose("ssoSilent called",r),this.eventHandler.emitEvent(Ms.SSO_SILENT_START,e.InteractionType.Silent,n),this.canUseNative(n))o=this.acquireTokenNative(n,Zi.ssoSilent).catch((e=>{if(e instanceof Ks&&zs(e)){this.nativeExtensionProvider=void 0;return this.createSilentIframeClient(n.correlationId).acquireToken(n)}throw e}));else{o=this.createSilentIframeClient(n.correlationId).acquireToken(n)}return o.then((t=>(this.eventHandler.emitEvent(Ms.SSO_SILENT_SUCCESS,e.InteractionType.Silent,t),this.ssoSilentMeasurement?.end({success:!0,isNativeBroker:t.fromNativeBroker,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:dc(t.account)}),t))).catch((t=>{throw this.eventHandler.emitEvent(Ms.SSO_SILENT_FAILURE,e.InteractionType.Silent,null,t),this.ssoSilentMeasurement?.end({success:!1},t),t})).finally((()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)}))}async acquireTokenByCode(t){const r=this.getRequestCorrelationId(t);this.logger.trace("acquireTokenByCode called",r);const n=this.performanceClient.startMeasurement(vn.AcquireTokenByCode,r);hc(this.initialized,n),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_BY_CODE_START,e.InteractionType.Silent,t),n.add({scenarioId:t.scenarioId});try{if(t.code&&t.nativeAccountId)throw Ti(oi);if(t.code){const o=t.code;let i=this.hybridAuthCodeResponses.get(o);return i?(this.logger.verbose("Existing acquireTokenByCode request found",r),n.discard()):(this.logger.verbose("Initiating new acquireTokenByCode request",r),i=this.acquireTokenByCodeAsync({...t,correlationId:r}).then((t=>(this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_BY_CODE_SUCCESS,e.InteractionType.Silent,t),this.hybridAuthCodeResponses.delete(o),n.end({success:!0,isNativeBroker:t.fromNativeBroker,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:dc(t.account)}),t))).catch((t=>{throw this.hybridAuthCodeResponses.delete(o),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),n.end({success:!1},t),t})),this.hybridAuthCodeResponses.set(o,i)),await i}if(t.nativeAccountId){if(this.canUseNative(t,t.nativeAccountId)){const e=await this.acquireTokenNative({...t,correlationId:r},Zi.acquireTokenByCode,t.nativeAccountId).catch((e=>{throw e instanceof Ks&&zs(e)&&(this.nativeExtensionProvider=void 0),e}));return n.end({accountType:dc(e.account),success:!0}),e}throw Ti(ai)}throw Ti(ni)}catch(t){throw this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),n.end({success:!1},t),t}}async acquireTokenByCodeAsync(e){this.logger.trace("acquireTokenByCodeAsync called",e.correlationId),this.acquireTokenByCodeAsyncMeasurement=this.performanceClient.startMeasurement(vn.AcquireTokenByCodeAsync,e.correlationId),this.acquireTokenByCodeAsyncMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement);const t=this.createSilentAuthCodeClient(e.correlationId);return await t.acquireToken(e).then((e=>(this.acquireTokenByCodeAsyncMeasurement?.end({success:!0,fromCache:e.fromCache,isNativeBroker:e.fromNativeBroker}),e))).catch((e=>{throw this.acquireTokenByCodeAsyncMeasurement?.end({success:!1},e),e})).finally((()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)}))}async acquireTokenFromCache(e,t){switch(this.performanceClient.addQueueMeasurement(vn.AcquireTokenFromCache,e.correlationId),t){case ia.Default:case ia.AccessToken:case ia.AccessTokenAndRefreshToken:const t=this.createSilentCacheClient(e.correlationId);return Sn(t.acquireToken.bind(t),vn.SilentCacheClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw vt(nt)}}async acquireTokenByRefreshToken(e,t){switch(this.performanceClient.addQueueMeasurement(vn.AcquireTokenByRefreshToken,e.correlationId),t){case ia.Default:case ia.AccessTokenAndRefreshToken:case ia.RefreshToken:case ia.RefreshTokenAndNetwork:const t=this.createSilentRefreshClient(e.correlationId);return Sn(t.acquireToken.bind(t),vn.SilentRefreshClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw vt(nt)}}async acquireTokenBySilentIframe(e){this.performanceClient.addQueueMeasurement(vn.AcquireTokenBySilentIframe,e.correlationId);const t=this.createSilentIframeClient(e.correlationId);return Sn(t.acquireToken.bind(t),vn.SilentIframeClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e)}async logout(e){const t=this.getRequestCorrelationId(e);return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.",t),this.logoutRedirect({correlationId:t,...e})}async logoutRedirect(e){const t=this.getRequestCorrelationId(e);Ka(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0);return this.createRedirectClient(t).logout(e)}logoutPopup(e){try{const t=this.getRequestCorrelationId(e);Fa(this.initialized),this.browserStorage.setInteractionInProgress(!0);return this.createPopupClient(t).logout(e)}catch(e){return Promise.reject(e)}}async clearCache(e){if(!this.isBrowserEnvironment)return void this.logger.info("in non-browser environment, returning early.");const t=this.getRequestCorrelationId(e);return this.createSilentCacheClient(t).logout(e)}getAllAccounts(e){const t=this.getRequestCorrelationId();return ks(this.logger,this.browserStorage,this.isBrowserEnvironment,t,e)}getAccount(e){const t=this.getRequestCorrelationId();return Ss(e,this.logger,this.browserStorage,t)}getAccountByUsername(e){const t=this.getRequestCorrelationId();return bs(e,this.logger,this.browserStorage,t)}getAccountByHomeId(e){const t=this.getRequestCorrelationId();return Es(e,this.logger,this.browserStorage,t)}getAccountByLocalId(e){const t=this.getRequestCorrelationId();return Rs(e,this.logger,this.browserStorage,t)}setActiveAccount(e){const t=this.getRequestCorrelationId();_s(e,this.browserStorage,t)}getActiveAccount(){const e=this.getRequestCorrelationId();return Ps(this.browserStorage,e)}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");const r=qr.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return this.browserStorage.setAccount(r,e.correlationId),e.fromNativeBroker?(this.logger.verbose("Response was from native broker, storing in-memory"),this.nativeInternalStorage.hydrateCache(e,t)):this.browserStorage.hydrateCache(e,t)}async acquireTokenNative(e,t,r){if(this.logger.trace("acquireTokenNative called"),!this.nativeExtensionProvider)throw Ti(li);return new Qs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,t,this.performanceClient,this.nativeExtensionProvider,r||this.getNativeAccountId(e),this.nativeInternalStorage,e.correlationId).acquireToken(e)}canUseNative(e,t){if(this.logger.trace("canUseNative called"),!Ws.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme))return this.logger.trace("canUseNative: isNativeAvailable returned false, returning false"),!1;if(e.prompt)switch(e.prompt){case R.NONE:case R.CONSENT:case R.LOGIN:this.logger.trace("canUseNative: prompt is compatible with native flow");break;default:return this.logger.trace(`canUseNative: prompt = ${e.prompt} is not compatible with native flow, returning false`),!1}return!(!t&&!this.getNativeAccountId(e))||(this.logger.trace("canUseNative: nativeAccountId is not available, returning false"),!1)}getNativeAccountId(e){const t=e.account||this.getAccount({loginHint:e.loginHint,sid:e.sid})||this.getActiveAccount();return t&&t.nativeAccountId||""}createPopupClient(e){return new Ys(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createRedirectClient(e){return new Xs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentIframeClient(e){return new ic(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.ssoSilent,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)}createSilentCacheClient(e){return new Gs(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentRefreshClient(e){return new ac(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)}createSilentAuthCodeClient(e){return new lc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Zi.acquireTokenByCode,this.performanceClient,this.nativeExtensionProvider,e)}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){return xa(),this.performanceClient.addPerformanceCallback(e)}removePerformanceCallback(e){return this.performanceClient.removePerformanceCallback(e)}enableAccountStorageEvents(){"undefined"!=typeof window&&(this.listeningToStorageEvents?this.logger.verbose("Account storage listener already registered."):(this.logger.verbose("Adding account storage listener."),this.listeningToStorageEvents=!0,window.addEventListener("storage",this.handleAccountCacheChange)))}disableAccountStorageEvents(){"undefined"!=typeof window&&(this.listeningToStorageEvents?(this.logger.verbose("Removing account storage listener."),window.removeEventListener("storage",this.handleAccountCacheChange),this.listeningToStorageEvents=!1):this.logger.verbose("No account storage listener registered."))}handleAccountCacheChange(e){try{e.key?.includes(w)&&this.eventHandler.emitEvent(Ms.ACTIVE_ACCOUNT_CHANGED);const t=e.newValue||e.oldValue;if(!t)return;const r=JSON.parse(t);if("object"!=typeof r||!qr.isAccountEntity(r))return;const n=jr.toObject(new qr,r).getAccountInfo();!e.oldValue&&e.newValue?(this.logger.info("Account was added to cache in a different window"),this.eventHandler.emitEvent(Ms.ACCOUNT_ADDED,void 0,n)):!e.newValue&&e.oldValue&&(this.logger.info("Account was removed from cache in a different window"),this.eventHandler.emitEvent(Ms.ACCOUNT_REMOVED,void 0,n))}catch(e){return}}getTokenCache(){return this.tokenCache}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){this.navigationClient=e}getConfiguration(){return this.config}getPerformanceClient(){return this.performanceClient}isBrowserEnv(){return this.isBrowserEnvironment}getRequestCorrelationId(e){return e?.correlationId?e.correlationId:this.isBrowserEnvironment?va():t.EMPTY_STRING}async loginRedirect(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginRedirect called",t),this.acquireTokenRedirect({correlationId:t,...e||ra})}loginPopup(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginPopup called",t),this.acquireTokenPopup({correlationId:t,...e||ra})}async acquireTokenSilent(e){const r=this.getRequestCorrelationId(e),n=this.performanceClient.startMeasurement(vn.AcquireTokenSilent,r);n.add({cacheLookupPolicy:e.cacheLookupPolicy,scenarioId:e.scenarioId}),hc(this.initialized,n),this.logger.verbose("acquireTokenSilent called",r);const o=e.account||this.getActiveAccount();if(!o)throw Ti(Fo);n.add({accountType:dc(o)});const i={clientId:this.config.auth.clientId,authority:e.authority||t.EMPTY_STRING,scopes:e.scopes,homeAccountIdentifier:o.homeAccountId,claims:e.claims,authenticationScheme:e.authenticationScheme,resourceRequestMethod:e.resourceRequestMethod,resourceRequestUri:e.resourceRequestUri,shrClaims:e.shrClaims,sshKid:e.sshKid,shrOptions:e.shrOptions},a=JSON.stringify(i),s=this.activeSilentTokenRequests.get(a);if(void 0===s){this.logger.verbose("acquireTokenSilent called for the first time, storing active request",r);const t=Sn(this.acquireTokenSilentAsync.bind(this),vn.AcquireTokenSilentAsync,this.logger,this.performanceClient,r)({...e,correlationId:r},o).then((t=>(this.activeSilentTokenRequests.delete(a),n.end({success:!0,fromCache:t.fromCache,isNativeBroker:t.fromNativeBroker,cacheLookupPolicy:e.cacheLookupPolicy,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length}),t))).catch((e=>{throw this.activeSilentTokenRequests.delete(a),n.end({success:!1},e),e}));return this.activeSilentTokenRequests.set(a,t),{...await t,state:e.state}}return this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call",r),n.discard(),{...await s,state:e.state}}async acquireTokenSilentAsync(t,r){const n=()=>this.trackPageVisibility(t.correlationId);this.performanceClient.addQueueMeasurement(vn.AcquireTokenSilentAsync,t.correlationId),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_START,e.InteractionType.Silent,t),t.correlationId&&this.performanceClient.incrementFields({visibilityChangeCount:0},t.correlationId),document.addEventListener("visibilitychange",n);const o=await Sn(Bs,vn.InitializeSilentRequest,this.logger,this.performanceClient,t.correlationId)(t,r,this.config,this.performanceClient,this.logger),i=t.cacheLookupPolicy||ia.Default;return this.acquireTokenSilentNoIframe(o,i).catch((async e=>{const t=function(e,t){const r=!(e instanceof jn&&e.subError!==Kn),n=e.errorCode===wi||e.errorCode===nt,o=r&&n||e.errorCode===Ln||e.errorCode===Bn,i=aa.includes(t);return o&&i}(e,i);if(t){if(this.activeIframeRequest){if(i!==ia.Skip){const[t,r]=this.activeIframeRequest;this.logger.verbose(`Iframe request is already in progress, awaiting resolution for request with correlationId: ${r}`,o.correlationId);const n=this.performanceClient.startMeasurement(vn.AwaitConcurrentIframe,o.correlationId);n.add({awaitIframeCorrelationId:r});const a=await t;if(n.end({success:a}),a)return this.logger.verbose(`Parallel iframe request with correlationId: ${r} succeeded. Retrying cache and/or RT redemption`,o.correlationId),this.acquireTokenSilentNoIframe(o,i);throw this.logger.info(`Iframe request with correlationId: ${r} failed. Interaction is required.`),e}return this.logger.warning("Another iframe request is currently in progress and CacheLookupPolicy is set to Skip. This may result in degraded performance and/or reliability for both calls. Please consider changing the CacheLookupPolicy to take advantage of request queuing and token cache.",o.correlationId),Sn(this.acquireTokenBySilentIframe.bind(this),vn.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o)}{let e;return this.activeIframeRequest=[new Promise((t=>{e=t})),o.correlationId],this.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.",o.correlationId),Sn(this.acquireTokenBySilentIframe.bind(this),vn.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o).then((t=>(e(!0),t))).catch((t=>{throw e(!1),t})).finally((()=>{this.activeIframeRequest=void 0}))}}throw e})).then((r=>(this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,r),t.correlationId&&this.performanceClient.addFields({fromCache:r.fromCache,isNativeBroker:r.fromNativeBroker},t.correlationId),r))).catch((t=>{throw this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,t),t})).finally((()=>{document.removeEventListener("visibilitychange",n)}))}async acquireTokenSilentNoIframe(t,r){return Ws.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,t.authenticationScheme)&&t.account.nativeAccountId?(this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"),this.acquireTokenNative(t,Zi.acquireTokenSilent_silentFlow).catch((async e=>{if(e instanceof Ks&&zs(e))throw this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"),this.nativeExtensionProvider=void 0,vt(nt);throw e}))):(this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"),Sn(this.acquireTokenFromCache.bind(this),vn.AcquireTokenFromCache,this.logger,this.performanceClient,t.correlationId)(t,r).catch((n=>{if(r===ia.AccessToken)throw n;return this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_NETWORK_START,e.InteractionType.Silent,t),Sn(this.acquireTokenByRefreshToken.bind(this),vn.AcquireTokenByRefreshToken,this.logger,this.performanceClient,t.correlationId)(t,r)})))}}class gc{constructor(e,t,r,n){this.clientId=e,this.clientCapabilities=t,this.crypto=r,this.logger=n}toNaaTokenRequest(e){let t;t=void 0===e.extraQueryParameters?new Map:new Map(Object.entries(e.extraQueryParameters));const r=e.correlationId||this.crypto.createNewGuid(),n=new yn(r).addClientCapabilitiesToClaims(e.claims,this.clientCapabilities),o=e.scopes||a;return{platformBrokerId:e.account?.homeAccountId,clientId:this.clientId,authority:e.authority,scope:o.join(" "),correlationId:r,claims:Tr.isEmptyObj(n)?void 0:n,state:e.state,authenticationScheme:e.authenticationScheme||V.BEARER,extraParameters:t}}fromNaaTokenResponse(e,t,r){if(!t.token.id_token||!t.token.access_token)throw vt(Re);const n=new Date(1e3*(r+(t.token.expires_in||0))),o=bt(t.token.id_token,this.crypto.base64Decode),i=this.fromNaaAccountInfo(t.account,t.token.id_token,o),a=t.token.scope||e.scope;return{authority:t.token.authority||i.environment,uniqueId:i.localAccountId,tenantId:i.tenantId,scopes:a.split(" "),account:i,idToken:t.token.id_token,idTokenClaims:o,accessToken:t.token.access_token,fromCache:!1,expiresOn:n,tokenType:e.authenticationScheme||V.BEARER,correlationId:e.correlationId,extExpiresOn:n,state:e.state}}fromNaaAccountInfo(e,t,r){const n=r||e.idTokenClaims,o=e.localAccountId||n?.oid||n?.sub||"",i=e.tenantId||n?.tid||"",a=e.homeAccountId||`${o}.${i}`,s=e.username||n?.preferred_username||"",c=e.name||n?.name,l=new Map,d=br(a,o,i,n);l.set(i,d);return{homeAccountId:a,environment:e.environment,tenantId:i,username:s,localAccountId:o,name:c,idToken:t,idTokenClaims:n,tenantProfiles:l}}fromBridgeError(e){if(!function(e){return void 0!==e.status}(e))return new Ae("unknown_error","An unknown error occurred");switch(e.status){case ts:return new yt(ht);case rs:return new yt(dt);case as:return new yt(Xe);case is:return new yt(pt);case ss:return new yt(e.code||pt,e.description);case ns:case os:return new Mn(e.code,e.description);case es:return new jn(e.code,e.description);default:return new Ae(e.code,e.description)}}toAuthenticationResultFromCache(e,t,r,n,o){if(!t||!r)throw vt(Re);const i=bt(t.secret,this.crypto.base64Decode),a=r.target||n.scopes.join(" ");return{authority:r.environment||e.environment,uniqueId:e.localAccountId,tenantId:e.tenantId,scopes:a.split(" "),account:e,idToken:t.secret,idTokenClaims:i||{},accessToken:r.secret,fromCache:!0,expiresOn:new Date(1e3*Number(r.expiresOn)),tokenType:n.authenticationScheme||V.BEARER,correlationId:o,extExpiresOn:new Date(1e3*Number(r.extendedExpiresOn)),state:n.state}}}const pc={code:"unsupported_method",desc:"This method is not supported in nested app environment."};class mc extends Ae{constructor(e,t){super(e,t),Object.setPrototypeOf(this,mc.prototype),this.name="NestedAppAuthError"}static createUnsupportedError(){return new mc(pc.code,pc.desc)}}class fc{constructor(e){this.operatingContext=e;const t=this.operatingContext.getBridgeProxy();if(void 0===t)throw new Error("unexpected: bridgeProxy is undefined");this.bridgeProxy=t,this.config=e.getConfig(),this.logger=this.operatingContext.getLogger(),this.performanceClient=this.config.telemetry.client,this.browserCrypto=e.isBrowserEnvironment()?new ms(this.logger,this.performanceClient,!0):It,this.browserStorage=this.operatingContext.isBrowserEnvironment()?new ws(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,_n(this.config.auth)):As(this.config.auth.clientId,this.logger),this.eventHandler=new Ns(this.logger),this.nestedAppAuthAdapter=new gc(this.config.auth.clientId,this.config.auth.clientCapabilities,this.browserCrypto,this.logger);const r=this.bridgeProxy.getAccountContext();if(r){const e=va();_s(Ss(r,this.logger,this.browserStorage,e),this.browserStorage,e)}}static async createController(e){const t=new fc(e);return Promise.resolve(t)}initialize(){return Promise.resolve()}ensureValidRequest(e){return e?.correlationId?e:{...e,correlationId:this.browserCrypto.createNewGuid()}}async acquireTokenInteractive(t){const r=this.ensureValidRequest(t);this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_START,e.InteractionType.Popup,r);const n=this.performanceClient.startMeasurement(vn.AcquireTokenPopup,r.correlationId);n?.add({nestedAppAuthRequest:!0});try{const o=this.nestedAppAuthAdapter.toNaaTokenRequest(r),i=Rt(),a=await this.bridgeProxy.getTokenInteractive(o),s={...this.nestedAppAuthAdapter.fromNaaTokenResponse(o,a,i)};return await this.hydrateCache(s,t),this.browserStorage.setActiveAccount(s.account,s.correlationId),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,s),n.add({accessTokenSize:s.accessToken.length,idTokenSize:s.idToken.length}),n.end({success:!0,requestId:s.requestId}),s}catch(t){const r=this.nestedAppAuthAdapter.fromBridgeError(t);throw this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,t),n.end({success:!1},t),r}}async acquireTokenSilentInternal(t){const r=this.ensureValidRequest(t);this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_START,e.InteractionType.Silent,r);const n=await this.acquireTokenFromCache(r);if(n)return this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,n),n;const o=this.performanceClient.startMeasurement(vn.SsoSilent,r.correlationId);o?.increment({visibilityChangeCount:0}),o?.add({nestedAppAuthRequest:!0});try{const n=this.nestedAppAuthAdapter.toNaaTokenRequest(r),i=Rt(),a=await this.bridgeProxy.getTokenSilent(n),s=this.nestedAppAuthAdapter.fromNaaTokenResponse(n,a,i);return await this.hydrateCache(s,t),this.browserStorage.setActiveAccount(s.account,s.correlationId),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,s),o?.add({accessTokenSize:s.accessToken.length,idTokenSize:s.idToken.length}),o?.end({success:!0,requestId:s.requestId}),s}catch(t){const r=this.nestedAppAuthAdapter.fromBridgeError(t);throw this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,t),o?.end({success:!1},t),r}}async acquireTokenFromCache(t){const r=this.performanceClient.startMeasurement(vn.AcquireTokenSilent,t.correlationId);if(r?.add({nestedAppAuthRequest:!0}),t.claims)return this.logger.verbose("Claims are present in the request, skipping cache lookup"),null;let n=null;switch(t.cacheLookupPolicy){case ia.Default:case ia.AccessToken:case ia.AccessTokenAndRefreshToken:n=await this.acquireTokenFromCacheInternal(t);break;default:return null}return n?(this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,n),r?.add({accessTokenSize:n?.accessToken.length,idTokenSize:n?.idToken.length}),r?.end({success:!0}),n):(this.logger.error("Cached tokens are not found for the account, proceeding with silent token request."),this.eventHandler.emitEvent(Ms.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null),r?.end({success:!1}),null)}async acquireTokenFromCacheInternal(e){const t=e.correlationId||va(),r=this.bridgeProxy.getAccountContext();let n=null;if(r){n=Ss(r,this.logger,this.browserStorage,t)||e.account}if(!n)return this.logger.verbose("No active account found, falling back to the host"),Promise.resolve(null);this.logger.verbose("active account found, attempting to acquire token silently");const o={...e,correlationId:t,authority:e.authority||n.environment,scopes:e.scopes?.length?e.scopes:[...a]},i=this.browserStorage.getTokenKeys(),s=this.browserStorage.getAccessToken(n,o,i,n.tenantId,this.performanceClient);if(!s)return this.logger.verbose("No cached access token found"),Promise.resolve(null);if(Pt(s.cachedAt)||_t(s.expiresOn,this.config.system.tokenRenewalOffsetSeconds))return this.logger.verbose("Cached access token has expired"),Promise.resolve(null);const c=this.browserStorage.getIdToken(n,o.correlationId,i,n.tenantId,this.performanceClient);return c?this.nestedAppAuthAdapter.toAuthenticationResultFromCache(n,c,s,o,o.correlationId):(this.logger.verbose("No cached id token found"),Promise.resolve(null))}async acquireTokenPopup(e){return this.acquireTokenInteractive(e)}acquireTokenRedirect(e){throw mc.createUnsupportedError()}async acquireTokenSilent(e){return this.acquireTokenSilentInternal(e)}acquireTokenByCode(e){throw mc.createUnsupportedError()}acquireTokenNative(e,t,r){throw mc.createUnsupportedError()}acquireTokenByRefreshToken(e,t){throw mc.createUnsupportedError()}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){throw mc.createUnsupportedError()}removePerformanceCallback(e){throw mc.createUnsupportedError()}enableAccountStorageEvents(){throw mc.createUnsupportedError()}disableAccountStorageEvents(){throw mc.createUnsupportedError()}getAllAccounts(e){const t=va();return ks(this.logger,this.browserStorage,this.isBrowserEnv(),t,e)}getAccount(e){const t=va();return Ss(e,this.logger,this.browserStorage,t)}getAccountByUsername(e){const t=va();return bs(e,this.logger,this.browserStorage,t)}getAccountByHomeId(e){const t=va();return Es(e,this.logger,this.browserStorage,t)}getAccountByLocalId(e){const t=va();return Rs(e,this.logger,this.browserStorage,t)}setActiveAccount(e){const t=va();return _s(e,this.browserStorage,t)}getActiveAccount(){const e=va();return Ps(this.browserStorage,e)}handleRedirectPromise(e){return Promise.resolve(null)}loginPopup(e){return this.acquireTokenInteractive(e||ra)}loginRedirect(e){throw mc.createUnsupportedError()}logout(e){throw mc.createUnsupportedError()}logoutRedirect(e){throw mc.createUnsupportedError()}logoutPopup(e){throw mc.createUnsupportedError()}ssoSilent(e){return this.acquireTokenSilentInternal(e)}getTokenCache(){throw mc.createUnsupportedError()}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){}setNavigationClient(e){this.logger.warning("setNavigationClient is not supported in nested app auth")}getConfiguration(){return this.config}isBrowserEnv(){return this.operatingContext.isBrowserEnvironment()}getBrowserCrypto(){return this.browserCrypto}getPerformanceClient(){throw mc.createUnsupportedError()}getRedirectResponse(){throw mc.createUnsupportedError()}async clearCache(e){throw mc.createUnsupportedError()}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");const r=qr.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return this.browserStorage.setAccount(r,e.correlationId),this.browserStorage.hydrateCache(e,t)}}async function Cc(e){const t=new ds(e),r=new ls(e),n=[t.initialize(),r.initialize()];return await Promise.all(n),r.isAvailable()&&e.auth.supportsNestedAppAuth?fc.createController(r):t.isAvailable()?uc.createController(t):null}class yc{static async createPublicClientApplication(e){const t=await async function(e,t){const r=new ds(e);return await r.initialize(),uc.createController(r,t)}(e);return new yc(e,t)}constructor(e,t){this.controller=t||new uc(new ds(e))}async initialize(e){return this.controller.initialize(e)}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}async function vc(e){const t=new yc(e);return await t.initialize(),t}class Ic{constructor(e){this.initialized=!1,this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.logger=e.getLogger(),this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new ms(this.logger,this.performanceClient):It,this.browserStorage=this.isBrowserEnvironment?new ws(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,void 0,this.performanceClient):As(this.config.auth.clientId,this.logger)}getBrowserStorage(){return this.browserStorage}getAccount(e){return null}getAccountByHomeId(e){return null}getAccountByLocalId(e){return null}getAccountByUsername(e){return null}getAllAccounts(){return[]}initialize(){return this.initialized=!0,Promise.resolve()}acquireTokenPopup(e){return Da(this.initialized),xa(),{}}acquireTokenRedirect(e){return Da(this.initialized),xa(),Promise.resolve()}acquireTokenSilent(e){return Da(this.initialized),xa(),{}}acquireTokenByCode(e){return Da(this.initialized),xa(),{}}acquireTokenNative(e,t,r){return Da(this.initialized),xa(),{}}acquireTokenByRefreshToken(e,t){return Da(this.initialized),xa(),{}}addEventCallback(e,t){return null}removeEventCallback(e){}addPerformanceCallback(e){return Da(this.initialized),xa(),""}removePerformanceCallback(e){return Da(this.initialized),xa(),!0}enableAccountStorageEvents(){Da(this.initialized),xa()}disableAccountStorageEvents(){Da(this.initialized),xa()}handleRedirectPromise(e){return Da(this.initialized),Promise.resolve(null)}loginPopup(e){return Da(this.initialized),xa(),{}}loginRedirect(e){return Da(this.initialized),xa(),{}}logout(e){return Da(this.initialized),xa(),{}}logoutRedirect(e){return Da(this.initialized),xa(),{}}logoutPopup(e){return Da(this.initialized),xa(),{}}ssoSilent(e){return Da(this.initialized),xa(),{}}getTokenCache(){return Da(this.initialized),xa(),{}}getLogger(){return this.logger}setLogger(e){Da(this.initialized),xa()}setActiveAccount(e){Da(this.initialized),xa()}getActiveAccount(){return Da(this.initialized),xa(),null}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){Da(this.initialized),xa()}getConfiguration(){return this.config}isBrowserEnv(){return Da(this.initialized),xa(),!0}getBrowserCrypto(){return Da(this.initialized),xa(),{}}getPerformanceClient(){return Da(this.initialized),xa(),{}}getRedirectResponse(){return Da(this.initialized),xa(),{}}async clearCache(e){Da(this.initialized),xa()}async hydrateCache(e,t){Da(this.initialized),xa()}}class Tc extends Za{getId(){return Tc.ID}getModuleName(){return Tc.MODULE_NAME}async initialize(){return!0}}Tc.MODULE_NAME="",Tc.ID="UnknownOperatingContext";class wc{static async createPublicClientApplication(e){const t=await Cc(e);let r;return r=null!==t?new wc(e,t):new wc(e),r}constructor(e,t){if(this.configuration=e,t)this.controller=t;else{const t=new Tc(e);this.controller=new Ic(t)}}async initialize(){if(this.controller instanceof Ic){const e=await Cc(this.configuration);return null!==e&&(this.controller=e),this.controller.initialize()}return Promise.resolve()}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}const Ac={initialize:()=>Promise.reject(_a(Aa)),acquireTokenPopup:()=>Promise.reject(_a(Aa)),acquireTokenRedirect:()=>Promise.reject(_a(Aa)),acquireTokenSilent:()=>Promise.reject(_a(Aa)),acquireTokenByCode:()=>Promise.reject(_a(Aa)),getAllAccounts:()=>[],getAccount:()=>null,getAccountByHomeId:()=>null,getAccountByUsername:()=>null,getAccountByLocalId:()=>null,handleRedirectPromise:()=>Promise.reject(_a(Aa)),loginPopup:()=>Promise.reject(_a(Aa)),loginRedirect:()=>Promise.reject(_a(Aa)),logout:()=>Promise.reject(_a(Aa)),logoutRedirect:()=>Promise.reject(_a(Aa)),logoutPopup:()=>Promise.reject(_a(Aa)),ssoSilent:()=>Promise.reject(_a(Aa)),addEventCallback:()=>null,removeEventCallback:()=>{},addPerformanceCallback:()=>"",removePerformanceCallback:()=>!1,enableAccountStorageEvents:()=>{},disableAccountStorageEvents:()=>{},getTokenCache:()=>{throw _a(Aa)},getLogger:()=>{throw _a(Aa)},setLogger:()=>{},setActiveAccount:()=>{},getActiveAccount:()=>null,initializeWrapperLibrary:()=>{},setNavigationClient:()=>{},getConfiguration:()=>{throw _a(Aa)},hydrateCache:()=>Promise.reject(_a(Aa)),clearCache:()=>Promise.reject(_a(Aa))};function kc(){let e;try{e=window[Ui.SessionStorage];const t=e?.getItem("msal.browser.performance.enabled");if(1===Number(t))return Promise.resolve().then((function(){return Rc}))}catch(e){}}function Sc(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.now}function bc(e){if(e&&Sc())return Math.round(window.performance.now()-e)}class Ec{constructor(e,t){this.correlationId=t,this.measureName=Ec.makeMeasureName(e,t),this.startMark=Ec.makeStartMark(e,t),this.endMark=Ec.makeEndMark(e,t)}static makeMeasureName(e,t){return`msal.measure.${e}.${t}`}static makeStartMark(e,t){return`msal.start.${e}.${t}`}static makeEndMark(e,t){return`msal.end.${e}.${t}`}static supportsBrowserPerformance(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.mark&&"function"==typeof window.performance.measure&&"function"==typeof window.performance.clearMarks&&"function"==typeof window.performance.clearMeasures&&"function"==typeof window.performance.getEntriesByName}static flushMeasurements(e,t){if(Ec.supportsBrowserPerformance())try{t.forEach((t=>{const r=Ec.makeMeasureName(t.name,e);window.performance.getEntriesByName(r,"measure").length>0&&(window.performance.clearMeasures(r),window.performance.clearMarks(Ec.makeStartMark(r,e)),window.performance.clearMarks(Ec.makeEndMark(r,e)))}))}catch(e){}}startMeasurement(){if(Ec.supportsBrowserPerformance())try{window.performance.mark(this.startMark)}catch(e){}}endMeasurement(){if(Ec.supportsBrowserPerformance())try{window.performance.mark(this.endMark),window.performance.measure(this.measureName,this.startMark,this.endMark)}catch(e){}}flushMeasurement(){if(Ec.supportsBrowserPerformance())try{const e=window.performance.getEntriesByName(this.measureName,"measure");if(e.length>0){const t=e[0].duration;return window.performance.clearMeasures(this.measureName),window.performance.clearMarks(this.startMark),window.performance.clearMarks(this.endMark),t}}catch(e){}return null}}var Rc=Object.freeze({__proto__:null,BrowserPerformanceMeasurement:Ec});e.AccountEntity=qr,e.ApiId=Zi,e.AuthError=Ae,e.AuthErrorCodes=Ie,e.AuthErrorMessage=we,e.AuthenticationHeaderParser=class{constructor(e){this.headers=e}getShrNonce(){const e=this.headers[g];if(e){const t=this.parseChallenges(e);if(t.nextnonce)return t.nextnonce;throw Ir(ur)}const t=this.headers[u];if(t){const e=this.parseChallenges(t);if(e.nonce)return e.nonce;throw Ir(ur)}throw Ir(hr)}parseChallenges(e){const r=e.indexOf(" "),n=e.substr(r+1).split(","),o={};return n.forEach((e=>{const[r,n]=e.split("=");o[r]=unescape(n.replace(/['"]+/g,t.EMPTY_STRING))})),o}}
/*! @azure/msal-common v14.16.1 2025-08-05 */,e.AuthenticationScheme=V,e.AzureCloudInstance=St,e.BrowserAuthError=Ii,e.BrowserAuthErrorCodes=fi,e.BrowserAuthErrorMessage=vi,e.BrowserCacheLocation=Ui,e.BrowserConfigurationAuthError=Ra,e.BrowserConfigurationAuthErrorCodes=Sa,e.BrowserConfigurationAuthErrorMessage=Ea,e.BrowserPerformanceClient=class extends To{constructor(e,r,n){super(e.auth.clientId,e.auth.authority||`${t.DEFAULT_AUTHORITY}`,new wt(e.system?.loggerOptions||{},Ja,Xa),Ja,Xa,e.telemetry?.application||{appName:"",appVersion:""},r,n)}generateId(){return va()}getPageVisibility(){return document.visibilityState?.toString()||null}deleteIncompleteSubMeasurements(e){kc()?.then((t=>{const r=this.eventsByCorrelationId.get(e.event.correlationId),n=r&&r.eventId===e.event.eventId,o=[];n&&r?.incompleteSubMeasurements&&r.incompleteSubMeasurements.forEach((e=>{o.push({...e})})),t.BrowserPerformanceMeasurement.flushMeasurements(e.event.correlationId,o)}))}startMeasurement(e,t){const r=this.getPageVisibility(),n=super.startMeasurement(e,t),o=Sc()?window.performance.now():void 0,i=kc()?.then((t=>new t.BrowserPerformanceMeasurement(e,n.event.correlationId)));return i?.then((e=>e.startMeasurement())),{...n,end:(e,t)=>{const a=n.end({...e,startPageVisibility:r,endPageVisibility:this.getPageVisibility(),durationMs:bc(o)},t);return i?.then((e=>e.endMeasurement())),this.deleteIncompleteSubMeasurements(n),a},discard:()=>{n.discard(),i?.then((e=>e.flushMeasurement())),this.deleteIncompleteSubMeasurements(n)}}}setPreQueueTime(e,t){if(!Sc())return void this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to set telemetry queue time for ${e}`);if(!t)return void this.logger.trace(`BrowserPerformanceClient: correlationId for ${e} not provided, unable to set telemetry queue time`);const r=this.preQueueTimeByCorrelationId.get(t);r&&(this.logger.trace(`BrowserPerformanceClient: Incomplete pre-queue ${r.name} found`,t),this.addQueueMeasurement(r.name,t,void 0,!0)),this.preQueueTimeByCorrelationId.set(t,{name:e,time:window.performance.now()})}addQueueMeasurement(e,t,r,n){if(!Sc())return void this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to add queue measurement for ${e}`);if(!t)return void this.logger.trace(`BrowserPerformanceClient: correlationId for ${e} not provided, unable to add queue measurement`);const o=super.getPreQueueTime(e,t);if(!o)return;const i=window.performance.now(),a=r||super.calculateQueuedTime(o,i);return super.addQueueMeasurement(e,t,a,n)}},e.BrowserPerformanceMeasurement=Ec,e.BrowserStorage=class{constructor(e){if(e===Ui.LocalStorage)this.windowStorage=new Cs;else{if(e!==Ui.SessionStorage)throw _a(wa);this.windowStorage=new ys}}getItem(e){return this.windowStorage.getItem(e)}setItem(e,t){this.windowStorage.setItem(e,t)}removeItem(e){this.windowStorage.removeItem(e)}getKeys(){return Object.keys(this.windowStorage)}containsKey(e){return this.windowStorage.hasOwnProperty(e)}},e.BrowserUtils=Ga,e.CacheLookupPolicy=ia,e.ClientAuthError=yt,e.ClientAuthErrorCodes=mt,e.ClientAuthErrorMessage=Ct,e.ClientConfigurationError=vr,e.ClientConfigurationErrorCodes=fr,e.ClientConfigurationErrorMessage=yr,e.DEFAULT_IFRAME_TIMEOUT_MS=Ya,e.EventHandler=Ns,e.EventMessageUtils=class{static getInteractionStatusFromEvent(t,r){switch(t.eventType){case Ms.LOGIN_START:return ta.Login;case Ms.SSO_SILENT_START:return ta.SsoSilent;case Ms.ACQUIRE_TOKEN_START:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup)return ta.AcquireToken;break;case Ms.HANDLE_REDIRECT_START:return ta.HandleRedirect;case Ms.LOGOUT_START:return ta.Logout;case Ms.SSO_SILENT_SUCCESS:case Ms.SSO_SILENT_FAILURE:if(r&&r!==ta.SsoSilent)break;return ta.None;case Ms.LOGOUT_END:if(r&&r!==ta.Logout)break;return ta.None;case Ms.HANDLE_REDIRECT_END:if(r&&r!==ta.HandleRedirect)break;return ta.None;case Ms.LOGIN_SUCCESS:case Ms.LOGIN_FAILURE:case Ms.ACQUIRE_TOKEN_SUCCESS:case Ms.ACQUIRE_TOKEN_FAILURE:case Ms.RESTORE_FROM_BFCACHE:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup){if(r&&r!==ta.Login&&r!==ta.AcquireToken)break;return ta.None}}return null}},e.EventType=Ms,e.InteractionRequiredAuthError=jn,e.InteractionRequiredAuthErrorCodes=zn,e.InteractionRequiredAuthErrorMessage=Wn,e.InteractionStatus=ta,e.JsonWebTokenTypes=Ce,e.LocalStorage=Cs,e.Logger=wt,e.MemoryStorage=gs,e.NavigationClient=Qa,e.OIDC_DEFAULT_SCOPES=a,e.PerformanceEvents=vn,e.PromptValue=R,e.ProtocolMode=Or,e.PublicClientApplication=yc,e.PublicClientNext=wc,e.ServerError=Mn,e.ServerResponseType=P,e.SessionStorage=ys,e.SignedHttpRequest=class{constructor(e,t){const r=t&&t.loggerOptions||{};this.logger=new wt(r,Ja,Xa),this.cryptoOps=new ms(this.logger),this.popTokenGenerator=new Zn(this.cryptoOps),this.shrParameters=e}async generatePublicKeyThumbprint(){const{kid:e}=await this.popTokenGenerator.generateKid(this.shrParameters);return e}async signRequest(e,t,r){return this.popTokenGenerator.signPayload(e,t,this.shrParameters,r)}async removeKeys(e){return this.cryptoOps.removeTokenBindingKey(e)}},e.StringUtils=Tr,e.StubPerformanceClient=yo,e.UrlString=Hr,e.WrapperSKU={React:"@azure/msal-react",Angular:"@azure/msal-angular"},e.createNestablePublicClientApplication=async function(e){const t=new ls(e);if(await t.initialize(),t.isAvailable()){const r=new fc(t);return new yc(e,r)}return vc(e)},e.createStandardPublicClientApplication=vc,e.stubbedPublicClientApplication=Ac,e.version=Xa}));
