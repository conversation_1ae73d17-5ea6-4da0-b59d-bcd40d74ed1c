import React from 'react';
import { useThemeStyles } from '../../services/theme/themeContext';

export interface LoadingSpinnerProps {
  /**
   * Size of the spinner
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Color of the spinner
   */
  color?: 'primary' | 'secondary' | 'white';
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Loading text to display
   */
  text?: string;
}

/**
 * Loading spinner component
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = 'primary',
  className = '',
  text,
}) => {
  const { getThemeClass } = useThemeStyles();

  const sizeClasses = {
    small: 'zb-spinner-sm theme-spinner-sm',
    medium: 'zb-spinner theme-spinner',
    large: 'zb-spinner-lg theme-spinner-lg',
  };

  const colorClasses = {
    primary: 'zb-spinner-primary theme-spinner-primary',
    secondary: 'zb-spinner-secondary theme-spinner-secondary',
    white: 'zb-spinner-white theme-spinner-white',
  };

  const spinnerClasses = [
    getThemeClass('zb-spinner theme-spinner'),
    getThemeClass(sizeClasses[size]),
    getThemeClass(colorClasses[color]),
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className="flex flex-col items-center justify-center">
      <svg
        className={spinnerClasses}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {text && (
        <p className={`mt-2 text-sm ${colorClasses[color]}`}>
          {text}
        </p>
      )}
    </div>
  );
};
