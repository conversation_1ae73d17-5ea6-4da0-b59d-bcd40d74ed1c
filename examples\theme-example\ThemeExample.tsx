/**
 * Theme System Example
 * 
 * Demonstrates how to use the theme system in a React component
 */

import React, { useState } from 'react';
import {
  ThemeProvider,
  useTheme,
  useThemeStyles,
  useCurrentTheme,
  useIsTheme,
  ThemeMode
} from '@shared/services/theme';
import { Button, ThemeSwitcher } from '@shared/components';

// Example of a theme-aware component
const ThemedCard: React.FC<{ children: React.ReactNode; title: string }> = ({ 
  children, 
  title 
}) => {
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  
  return (
    <div className={getThemeClass('theme-card')}>
      <div className="theme-card-header">
        <h3 className="theme-card-title">{title}</h3>
      </div>
      <div className="theme-card-content">
        {children}
      </div>
    </div>
  );
};

// Example of conditional theme rendering
const ThemeSpecificContent: React.FC = () => {
  const currentTheme = useCurrentTheme();
  const isCRMTheme = useIsTheme(ThemeMode.CRM);
  const isMFETheme = useIsTheme(ThemeMode.MFE);
  
  return (
    <div>
      <h4 className="theme-text-primary">Current Theme: {currentTheme}</h4>
      
      {isCRMTheme && (
        <div className="theme-bg-secondary" style={{ padding: '16px', borderRadius: '4px' }}>
          <p className="theme-text-primary">
            🏢 You're using the CRM theme! This is optimized for Dynamics 365 integration.
          </p>
          <ul className="theme-text-secondary">
            <li>Dynamics 365 branded colors</li>
            <li>Conservative border radius</li>
            <li>Segoe UI font family</li>
            <li>Subtle shadows and spacing</li>
          </ul>
        </div>
      )}
      
      {isMFETheme && (
        <div className="theme-bg-secondary" style={{ padding: '16px', borderRadius: '8px' }}>
          <p className="theme-text-primary">
            🚀 You're using the MFE theme! This is optimized for modern micro frontends.
          </p>
          <ul className="theme-text-secondary">
            <li>Modern gradient colors</li>
            <li>Rounded corners and shadows</li>
            <li>Inter font family</li>
            <li>Enhanced visual effects</li>
          </ul>
        </div>
      )}
    </div>
  );
};

// Example of using theme variables programmatically
const ThemeVariablesDemo: React.FC = () => {
  const { getCSSVariable } = useThemeStyles();
  
  const [variables, setVariables] = useState<Record<string, string>>({});
  
  const loadVariables = () => {
    const vars = {
      'Primary Color': getCSSVariable('--theme-primary'),
      'Secondary Color': getCSSVariable('--theme-secondary'),
      'Background': getCSSVariable('--theme-bg-primary'),
      'Text Color': getCSSVariable('--theme-text-primary'),
      'Border Color': getCSSVariable('--theme-border-primary'),
      'Font Family': getCSSVariable('--theme-font-family'),
      'Border Radius': getCSSVariable('--theme-radius-base'),
      'Shadow': getCSSVariable('--theme-shadow-base'),
    };
    setVariables(vars);
  };
  
  React.useEffect(() => {
    loadVariables();
  }, [getCSSVariable]);
  
  return (
    <div>
      <div style={{ display: 'flex', gap: '8px', marginBottom: '16px' }}>
        <Button variant="secondary" onClick={loadVariables}>
          Refresh Variables
        </Button>
      </div>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '8px' 
      }}>
        {Object.entries(variables).map(([name, value]) => (
          <div 
            key={name}
            style={{
              padding: '12px',
              backgroundColor: 'var(--theme-bg-tertiary)',
              border: '1px solid var(--theme-border-primary)',
              borderRadius: 'var(--theme-radius-base)',
              fontSize: 'var(--theme-font-size-sm)'
            }}
          >
            <div style={{ 
              fontWeight: 'var(--theme-font-weight-medium)',
              color: 'var(--theme-text-primary)',
              marginBottom: '4px'
            }}>
              {name}
            </div>
            <div style={{ 
              color: 'var(--theme-text-secondary)',
              fontFamily: 'monospace',
              fontSize: '0.85em'
            }}>
              {value || 'Not set'}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Example of theme state management
const ThemeControls: React.FC = () => {
  const { 
    currentTheme, 
    isLoading, 
    error, 
    switchTheme, 
    resetTheme 
  } = useTheme();
  
  const handleSwitchTheme = async () => {
    const newTheme = currentTheme === ThemeMode.CRM ? ThemeMode.MFE : ThemeMode.CRM;
    try {
      await switchTheme(newTheme);
    } catch (err) {
      console.error('Failed to switch theme:', err);
    }
  };
  
  const handleResetTheme = async () => {
    try {
      await resetTheme();
    } catch (err) {
      console.error('Failed to reset theme:', err);
    }
  };
  
  return (
    <div>
      <div style={{ display: 'flex', gap: '12px', alignItems: 'center', marginBottom: '16px' }}>
        <Button 
          variant="primary" 
          onClick={handleSwitchTheme}
          disabled={isLoading}
        >
          {isLoading ? 'Switching...' : `Switch to ${currentTheme === ThemeMode.CRM ? 'MFE' : 'CRM'} Theme`}
        </Button>
        
        <Button 
          variant="secondary" 
          onClick={handleResetTheme}
          disabled={isLoading}
        >
          Reset Theme
        </Button>
        
        <ThemeSwitcher 
          variant="dropdown" 
          size="md" 
          showLabels={true} 
          showIcons={true}
        />
      </div>
      
      {error && (
        <div style={{
          padding: '12px',
          backgroundColor: 'var(--theme-error-light)',
          color: 'var(--theme-error)',
          border: '1px solid var(--theme-border-error)',
          borderRadius: 'var(--theme-radius-base)',
          marginBottom: '16px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div style={{
        padding: '12px',
        backgroundColor: 'var(--theme-bg-tertiary)',
        border: '1px solid var(--theme-border-primary)',
        borderRadius: 'var(--theme-radius-base)',
        fontSize: 'var(--theme-font-size-sm)'
      }}>
        <div><strong>Current Theme:</strong> {currentTheme}</div>
        <div><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
        <div><strong>Error:</strong> {error || 'None'}</div>
      </div>
    </div>
  );
};

// Main example component
const ThemeExampleContent: React.FC = () => {
  return (
    <div style={{ 
      padding: '24px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: 'var(--theme-font-family)'
    }}>
      <h1 className="theme-text-primary" style={{ marginBottom: '32px' }}>
        Theme System Example
      </h1>
      
      <div style={{ 
        display: 'grid', 
        gap: '24px',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))'
      }}>
        <ThemedCard title="Theme Controls">
          <ThemeControls />
        </ThemedCard>
        
        <ThemedCard title="Theme-Specific Content">
          <ThemeSpecificContent />
        </ThemedCard>
        
        <ThemedCard title="CSS Variables">
          <ThemeVariablesDemo />
        </ThemedCard>
        
        <ThemedCard title="Component Examples">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <Button variant="primary">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="danger">Danger Button</Button>
            
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button variant="primary" size="small">Small</Button>
              <Button variant="primary" size="medium">Medium</Button>
              <Button variant="primary" size="large">Large</Button>
            </div>
            
            <input 
              className="theme-input" 
              placeholder="Theme-aware input"
              style={{ marginTop: '8px' }}
            />
            
            <select className="theme-input theme-select" style={{ marginTop: '8px' }}>
              <option>Theme-aware select</option>
              <option>Option 1</option>
              <option>Option 2</option>
            </select>
          </div>
        </ThemedCard>
      </div>
    </div>
  );
};

// Example with ThemeProvider
export const ThemeExample: React.FC = () => {
  return (
    <ThemeProvider 
      enableAutoDetection={true}
      enablePersistence={true}
      storageKey="theme-example"
    >
      <ThemeExampleContent />
    </ThemeProvider>
  );
};

export default ThemeExample;
