/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

export const CLIENT_ID = "client_id";
export const REDIRECT_URI = "redirect_uri";
export const RESPONSE_TYPE = "response_type";
export const RESPONSE_MODE = "response_mode";
export const GRANT_TYPE = "grant_type";
export const CLAIMS = "claims";
export const SCOPE = "scope";
export const ERROR = "error";
export const ERROR_DESCRIPTION = "error_description";
export const ACCESS_TOKEN = "access_token";
export const ID_TOKEN = "id_token";
export const REFRESH_TOKEN = "refresh_token";
export const EXPIRES_IN = "expires_in";
export const REFRESH_TOKEN_EXPIRES_IN = "refresh_token_expires_in";
export const STATE = "state";
export const NONCE = "nonce";
export const PROMPT = "prompt";
export const SESSION_STATE = "session_state";
export const CLIENT_INFO = "client_info";
export const CODE = "code";
export const CODE_CHALLENGE = "code_challenge";
export const CODE_CHALLENGE_METHOD = "code_challenge_method";
export const CODE_VERIFIER = "code_verifier";
export const CLIENT_REQUEST_ID = "client-request-id";
export const X_CLIENT_SKU = "x-client-SKU";
export const X_CLIENT_VER = "x-client-VER";
export const X_CLIENT_OS = "x-client-OS";
export const X_CLIENT_CPU = "x-client-CPU";
export const X_CLIENT_CURR_TELEM = "x-client-current-telemetry";
export const X_CLIENT_LAST_TELEM = "x-client-last-telemetry";
export const X_MS_LIB_CAPABILITY = "x-ms-lib-capability";
export const X_APP_NAME = "x-app-name";
export const X_APP_VER = "x-app-ver";
export const POST_LOGOUT_URI = "post_logout_redirect_uri";
export const ID_TOKEN_HINT = "id_token_hint";
export const DEVICE_CODE = "device_code";
export const CLIENT_SECRET = "client_secret";
export const CLIENT_ASSERTION = "client_assertion";
export const CLIENT_ASSERTION_TYPE = "client_assertion_type";
export const TOKEN_TYPE = "token_type";
export const REQ_CNF = "req_cnf";
export const OBO_ASSERTION = "assertion";
export const REQUESTED_TOKEN_USE = "requested_token_use";
export const ON_BEHALF_OF = "on_behalf_of";
export const FOCI = "foci";
export const CCS_HEADER = "X-AnchorMailbox";
export const RETURN_SPA_CODE = "return_spa_code";
export const NATIVE_BROKER = "nativebroker";
export const LOGOUT_HINT = "logout_hint";
export const SID = "sid";
export const LOGIN_HINT = "login_hint";
export const DOMAIN_HINT = "domain_hint";
export const X_CLIENT_EXTRA_SKU = "x-client-xtra-sku";
export const BROKER_CLIENT_ID = "brk_client_id";
export const BROKER_REDIRECT_URI = "brk_redirect_uri";
