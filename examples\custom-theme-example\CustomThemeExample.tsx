/**
 * Custom Theme Example
 * 
 * Demonstrates the new custom theme system with Dynamics CRM and ZB Champion themes
 */

import React, { useState } from 'react';
import {
  ThemeProvider,
  useTheme,
  useThemeStyles,
  useCurrentTheme,
  ThemeMode
} from '@shared/services/theme';
import { Button, ThemeSwitcher } from '@shared/components';

// Example component showing theme differences
const ThemeShowcase: React.FC = () => {
  const { currentTheme } = useTheme();
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  
  const primaryColor = getCSSVariable('--theme-primary');
  const fontFamily = getCSSVariable('--theme-font-family');
  
  return (
    <div className={getThemeClass('theme-card')} style={{ marginBottom: '24px' }}>
      <div className="theme-card-header">
        <h3 className="theme-card-title">Current Theme: {currentTheme}</h3>
      </div>
      <div className="theme-card-content">
        <div style={{ marginBottom: '16px' }}>
          <p className="theme-text-primary">
            <strong>Primary Color:</strong> {primaryColor}
          </p>
          <p className="theme-text-primary">
            <strong>Font Family:</strong> {fontFamily}
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <Button variant="primary" size="medium">
            Primary Button
          </Button>
          <Button variant="secondary" size="medium">
            Secondary Button
          </Button>
          <Button variant="danger" size="medium">
            Danger Button
          </Button>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <input 
            className="theme-input" 
            placeholder="Theme-aware input field"
            style={{ width: '100%', marginBottom: '8px' }}
          />
          <select className="theme-input theme-select" style={{ width: '100%' }}>
            <option>Theme-aware select</option>
            <option>Option 1</option>
            <option>Option 2</option>
          </select>
        </div>
      </div>
    </div>
  );
};

// Component showing theme-specific features
const ThemeFeatures: React.FC = () => {
  const currentTheme = useCurrentTheme();
  
  return (
    <div className="theme-card" style={{ marginBottom: '24px' }}>
      <div className="theme-card-header">
        <h3 className="theme-card-title">Theme-Specific Features</h3>
      </div>
      <div className="theme-card-content">
        {currentTheme === ThemeMode.CRM && (
          <div>
            <h4 className="theme-text-primary">🏢 Dynamics CRM Theme</h4>
            <ul className="theme-text-secondary">
              <li>Segoe UI font family for enterprise consistency</li>
              <li>Conservative 4px border radius</li>
              <li>Dynamics 365 blue (#0078d4) primary color</li>
              <li>Subtle shadows and spacing</li>
              <li>32px button height for accessibility</li>
              <li>Enterprise-grade form controls</li>
            </ul>
            <div style={{ 
              padding: '12px', 
              backgroundColor: 'var(--theme-primary-light)',
              borderLeft: '4px solid var(--theme-primary)',
              marginTop: '12px'
            }}>
              <p className="theme-text-primary" style={{ margin: 0 }}>
                This theme is optimized for Dynamics 365 integration and enterprise environments.
              </p>
            </div>
          </div>
        )}
        
        {currentTheme === ThemeMode.MFE && (
          <div>
            <h4 className="theme-text-primary">🎯 ZB Champion Theme</h4>
            <ul className="theme-text-secondary">
              <li>RNHouseSans font family for brand consistency</li>
              <li>Modern 16px border radius</li>
              <li>ZB Champion purple (#5e10b1) primary color</li>
              <li>Enhanced visual effects and gradients</li>
              <li>44px button height for touch-friendly interface</li>
              <li>Mobile-responsive design (840px breakpoint)</li>
            </ul>
            <div style={{ 
              padding: '12px', 
              backgroundColor: 'var(--theme-primary-light)',
              borderLeft: '4px solid var(--theme-primary)',
              marginTop: '12px',
              borderRadius: '8px'
            }}>
              <p className="theme-text-primary" style={{ margin: 0 }}>
                This theme provides modern, branded styling for standalone applications.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Component showing CSS variables
const CSSVariablesDemo: React.FC = () => {
  const { getCSSVariable } = useThemeStyles();
  
  const variables = [
    { name: 'Primary Color', var: '--theme-primary' },
    { name: 'Secondary Color', var: '--theme-secondary' },
    { name: 'Background', var: '--theme-bg-primary' },
    { name: 'Text Color', var: '--theme-text-primary' },
    { name: 'Border Color', var: '--theme-border-primary' },
    { name: 'Font Family', var: '--theme-font-family' },
    { name: 'Border Radius', var: '--theme-radius-base' },
    { name: 'Shadow', var: '--theme-shadow-base' },
  ];
  
  return (
    <div className="theme-card">
      <div className="theme-card-header">
        <h3 className="theme-card-title">CSS Custom Properties</h3>
      </div>
      <div className="theme-card-content">
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '12px' 
        }}>
          {variables.map(({ name, var: varName }) => (
            <div 
              key={varName}
              style={{
                padding: '12px',
                backgroundColor: 'var(--theme-bg-tertiary)',
                border: '1px solid var(--theme-border-primary)',
                borderRadius: 'var(--theme-radius-base)',
                fontSize: 'var(--theme-font-size-sm)'
              }}
            >
              <div style={{ 
                fontWeight: 'var(--theme-font-weight-medium)',
                color: 'var(--theme-text-primary)',
                marginBottom: '4px'
              }}>
                {name}
              </div>
              <div style={{ 
                color: 'var(--theme-text-secondary)',
                fontFamily: 'monospace',
                fontSize: '0.85em'
              }}>
                {getCSSVariable(varName) || 'Not set'}
              </div>
              <div style={{ 
                color: 'var(--theme-text-tertiary)',
                fontSize: '0.8em',
                marginTop: '2px'
              }}>
                {varName}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main example content
const CustomThemeExampleContent: React.FC = () => {
  const { switchTheme, isLoading, error } = useTheme();
  const currentTheme = useCurrentTheme();
  
  const handleQuickSwitch = async () => {
    const newTheme = currentTheme === ThemeMode.CRM ? ThemeMode.MFE : ThemeMode.CRM;
    await switchTheme(newTheme);
  };
  
  return (
    <div style={{ 
      padding: '24px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: 'var(--theme-font-family)',
      minHeight: '100vh',
      backgroundColor: 'var(--theme-bg-secondary)'
    }}>
      <header style={{ 
        marginBottom: '32px',
        textAlign: 'center'
      }}>
        <h1 className="theme-text-primary" style={{ 
          fontSize: 'var(--theme-font-size-3xl)',
          marginBottom: '16px'
        }}>
          Custom Theme System Demo
        </h1>
        <p className="theme-text-secondary" style={{ 
          fontSize: 'var(--theme-font-size-lg)',
          marginBottom: '24px'
        }}>
          Demonstrating Dynamics CRM and ZB Champion themes
        </p>
        
        <div style={{ 
          display: 'flex', 
          gap: '16px', 
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <Button 
            variant="primary" 
            onClick={handleQuickSwitch}
            disabled={isLoading}
          >
            {isLoading ? 'Switching...' : `Switch to ${currentTheme === ThemeMode.CRM ? 'ZB Champion' : 'Dynamics CRM'}`}
          </Button>
          
          <ThemeSwitcher 
            variant="dropdown" 
            size="lg" 
            showLabels={true} 
            showIcons={true}
          />
        </div>
        
        {error && (
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: 'var(--theme-error-light)',
            color: 'var(--theme-error)',
            border: '1px solid var(--theme-border-error)',
            borderRadius: 'var(--theme-radius-base)'
          }}>
            <strong>Error:</strong> {error}
          </div>
        )}
      </header>
      
      <div style={{ 
        display: 'grid', 
        gap: '24px'
      }}>
        <ThemeShowcase />
        <ThemeFeatures />
        <CSSVariablesDemo />
      </div>
      
      <footer style={{ 
        marginTop: '48px',
        textAlign: 'center',
        padding: '24px',
        borderTop: '1px solid var(--theme-border-tertiary)'
      }}>
        <p className="theme-text-secondary">
          Theme system supports runtime switching between deployment contexts
        </p>
      </footer>
    </div>
  );
};

// Main example component with ThemeProvider
export const CustomThemeExample: React.FC = () => {
  return (
    <ThemeProvider 
      enableAutoDetection={true}
      enablePersistence={true}
      storageKey="custom-theme-example"
    >
      <CustomThemeExampleContent />
    </ThemeProvider>
  );
};

export default CustomThemeExample;
