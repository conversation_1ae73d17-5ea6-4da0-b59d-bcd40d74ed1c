var Tr = Object.defineProperty;
var Er = (i, t, o) => t in i ? Tr(i, t, { enumerable: !0, configurable: !0, writable: !0, value: o }) : i[t] = o;
var H = (i, t, o) => Er(i, typeof t != "symbol" ? t + "" : t, o);
import $e, { createContext as br, useState as N, useEffect as De, useCallback as se, useContext as Cr } from "react";
import { T as I, c as _r, g as Rr } from "./index-Cpdl-0cO.mjs";
import { l as E } from "./logger-jecF8wz6.mjs";
var ue = { exports: {} }, U = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ae;
function wr() {
  if (Ae) return U;
  Ae = 1;
  var i = $e, t = Symbol.for("react.element"), o = Symbol.for("react.fragment"), d = Object.prototype.hasOwnProperty, y = i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, s = { key: !0, ref: !0, __self: !0, __source: !0 };
  function x(P, g, O) {
    var b, T = {}, F = null, R = null;
    O !== void 0 && (F = "" + O), g.key !== void 0 && (F = "" + g.key), g.ref !== void 0 && (R = g.ref);
    for (b in g) d.call(g, b) && !s.hasOwnProperty(b) && (T[b] = g[b]);
    if (P && P.defaultProps) for (b in g = P.defaultProps, g) T[b] === void 0 && (T[b] = g[b]);
    return { $$typeof: t, type: P, key: F, ref: R, props: T, _owner: y.current };
  }
  return U.Fragment = o, U.jsx = x, U.jsxs = x, U;
}
var z = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ie;
function Sr() {
  return Ie || (Ie = 1, process.env.NODE_ENV !== "production" && function() {
    var i = $e, t = Symbol.for("react.element"), o = Symbol.for("react.portal"), d = Symbol.for("react.fragment"), y = Symbol.for("react.strict_mode"), s = Symbol.for("react.profiler"), x = Symbol.for("react.provider"), P = Symbol.for("react.context"), g = Symbol.for("react.forward_ref"), O = Symbol.for("react.suspense"), b = Symbol.for("react.suspense_list"), T = Symbol.for("react.memo"), F = Symbol.for("react.lazy"), R = Symbol.for("react.offscreen"), G = Symbol.iterator, X = "@@iterator";
    function Z(e) {
      if (e === null || typeof e != "object")
        return null;
      var r = G && e[G] || e[X];
      return typeof r == "function" ? r : null;
    }
    var k = i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function h(e) {
      {
        for (var r = arguments.length, n = new Array(r > 1 ? r - 1 : 0), a = 1; a < r; a++)
          n[a - 1] = arguments[a];
        v("error", e, n);
      }
    }
    function v(e, r, n) {
      {
        var a = k.ReactDebugCurrentFrame, l = a.getStackAddendum();
        l !== "" && (r += "%s", n = n.concat([l]));
        var f = n.map(function(c) {
          return String(c);
        });
        f.unshift("Warning: " + r), Function.prototype.apply.call(console[e], console, f);
      }
    }
    var M = !1, Ve = !1, We = !1, Ye = !1, Ne = !1, ce;
    ce = Symbol.for("react.module.reference");
    function Ue(e) {
      return !!(typeof e == "string" || typeof e == "function" || e === d || e === s || Ne || e === y || e === O || e === b || Ye || e === R || M || Ve || We || typeof e == "object" && e !== null && (e.$$typeof === F || e.$$typeof === T || e.$$typeof === x || e.$$typeof === P || e.$$typeof === g || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      e.$$typeof === ce || e.getModuleId !== void 0));
    }
    function ze(e, r, n) {
      var a = e.displayName;
      if (a)
        return a;
      var l = r.displayName || r.name || "";
      return l !== "" ? n + "(" + l + ")" : n;
    }
    function le(e) {
      return e.displayName || "Context";
    }
    function j(e) {
      if (e == null)
        return null;
      if (typeof e.tag == "number" && h("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof e == "function")
        return e.displayName || e.name || null;
      if (typeof e == "string")
        return e;
      switch (e) {
        case d:
          return "Fragment";
        case o:
          return "Portal";
        case s:
          return "Profiler";
        case y:
          return "StrictMode";
        case O:
          return "Suspense";
        case b:
          return "SuspenseList";
      }
      if (typeof e == "object")
        switch (e.$$typeof) {
          case P:
            var r = e;
            return le(r) + ".Consumer";
          case x:
            var n = e;
            return le(n._context) + ".Provider";
          case g:
            return ze(e, e.render, "ForwardRef");
          case T:
            var a = e.displayName || null;
            return a !== null ? a : j(e.type) || "Memo";
          case F: {
            var l = e, f = l._payload, c = l._init;
            try {
              return j(c(f));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var D = Object.assign, W = 0, fe, he, de, me, pe, ve, ge;
    function ye() {
    }
    ye.__reactDisabledLog = !0;
    function Ge() {
      {
        if (W === 0) {
          fe = console.log, he = console.info, de = console.warn, me = console.error, pe = console.group, ve = console.groupCollapsed, ge = console.groupEnd;
          var e = {
            configurable: !0,
            enumerable: !0,
            value: ye,
            writable: !0
          };
          Object.defineProperties(console, {
            info: e,
            log: e,
            warn: e,
            error: e,
            group: e,
            groupCollapsed: e,
            groupEnd: e
          });
        }
        W++;
      }
    }
    function Ke() {
      {
        if (W--, W === 0) {
          var e = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: D({}, e, {
              value: fe
            }),
            info: D({}, e, {
              value: he
            }),
            warn: D({}, e, {
              value: de
            }),
            error: D({}, e, {
              value: me
            }),
            group: D({}, e, {
              value: pe
            }),
            groupCollapsed: D({}, e, {
              value: ve
            }),
            groupEnd: D({}, e, {
              value: ge
            })
          });
        }
        W < 0 && h("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var Q = k.ReactCurrentDispatcher, ee;
    function K(e, r, n) {
      {
        if (ee === void 0)
          try {
            throw Error();
          } catch (l) {
            var a = l.stack.trim().match(/\n( *(at )?)/);
            ee = a && a[1] || "";
          }
        return `
` + ee + e;
      }
    }
    var re = !1, q;
    {
      var qe = typeof WeakMap == "function" ? WeakMap : Map;
      q = new qe();
    }
    function Te(e, r) {
      if (!e || re)
        return "";
      {
        var n = q.get(e);
        if (n !== void 0)
          return n;
      }
      var a;
      re = !0;
      var l = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var f;
      f = Q.current, Q.current = null, Ge();
      try {
        if (r) {
          var c = function() {
            throw Error();
          };
          if (Object.defineProperty(c.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(c, []);
            } catch (_) {
              a = _;
            }
            Reflect.construct(e, [], c);
          } else {
            try {
              c.call();
            } catch (_) {
              a = _;
            }
            e.call(c.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (_) {
            a = _;
          }
          e();
        }
      } catch (_) {
        if (_ && a && typeof _.stack == "string") {
          for (var u = _.stack.split(`
`), C = a.stack.split(`
`), m = u.length - 1, p = C.length - 1; m >= 1 && p >= 0 && u[m] !== C[p]; )
            p--;
          for (; m >= 1 && p >= 0; m--, p--)
            if (u[m] !== C[p]) {
              if (m !== 1 || p !== 1)
                do
                  if (m--, p--, p < 0 || u[m] !== C[p]) {
                    var S = `
` + u[m].replace(" at new ", " at ");
                    return e.displayName && S.includes("<anonymous>") && (S = S.replace("<anonymous>", e.displayName)), typeof e == "function" && q.set(e, S), S;
                  }
                while (m >= 1 && p >= 0);
              break;
            }
        }
      } finally {
        re = !1, Q.current = f, Ke(), Error.prepareStackTrace = l;
      }
      var L = e ? e.displayName || e.name : "", A = L ? K(L) : "";
      return typeof e == "function" && q.set(e, A), A;
    }
    function Be(e, r, n) {
      return Te(e, !1);
    }
    function Je(e) {
      var r = e.prototype;
      return !!(r && r.isReactComponent);
    }
    function B(e, r, n) {
      if (e == null)
        return "";
      if (typeof e == "function")
        return Te(e, Je(e));
      if (typeof e == "string")
        return K(e);
      switch (e) {
        case O:
          return K("Suspense");
        case b:
          return K("SuspenseList");
      }
      if (typeof e == "object")
        switch (e.$$typeof) {
          case g:
            return Be(e.render);
          case T:
            return B(e.type, r, n);
          case F: {
            var a = e, l = a._payload, f = a._init;
            try {
              return B(f(l), r, n);
            } catch {
            }
          }
        }
      return "";
    }
    var Y = Object.prototype.hasOwnProperty, Ee = {}, be = k.ReactDebugCurrentFrame;
    function J(e) {
      if (e) {
        var r = e._owner, n = B(e.type, e._source, r ? r.type : null);
        be.setExtraStackFrame(n);
      } else
        be.setExtraStackFrame(null);
    }
    function Xe(e, r, n, a, l) {
      {
        var f = Function.call.bind(Y);
        for (var c in e)
          if (f(e, c)) {
            var u = void 0;
            try {
              if (typeof e[c] != "function") {
                var C = Error((a || "React class") + ": " + n + " type `" + c + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof e[c] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw C.name = "Invariant Violation", C;
              }
              u = e[c](r, c, a, n, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (m) {
              u = m;
            }
            u && !(u instanceof Error) && (J(l), h("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", a || "React class", n, c, typeof u), J(null)), u instanceof Error && !(u.message in Ee) && (Ee[u.message] = !0, J(l), h("Failed %s type: %s", n, u.message), J(null));
          }
      }
    }
    var Ze = Array.isArray;
    function te(e) {
      return Ze(e);
    }
    function Qe(e) {
      {
        var r = typeof Symbol == "function" && Symbol.toStringTag, n = r && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return n;
      }
    }
    function er(e) {
      try {
        return Ce(e), !1;
      } catch {
        return !0;
      }
    }
    function Ce(e) {
      return "" + e;
    }
    function _e(e) {
      if (er(e))
        return h("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Qe(e)), Ce(e);
    }
    var Re = k.ReactCurrentOwner, rr = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, we, Se;
    function tr(e) {
      if (Y.call(e, "ref")) {
        var r = Object.getOwnPropertyDescriptor(e, "ref").get;
        if (r && r.isReactWarning)
          return !1;
      }
      return e.ref !== void 0;
    }
    function nr(e) {
      if (Y.call(e, "key")) {
        var r = Object.getOwnPropertyDescriptor(e, "key").get;
        if (r && r.isReactWarning)
          return !1;
      }
      return e.key !== void 0;
    }
    function or(e, r) {
      typeof e.ref == "string" && Re.current;
    }
    function ar(e, r) {
      {
        var n = function() {
          we || (we = !0, h("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        n.isReactWarning = !0, Object.defineProperty(e, "key", {
          get: n,
          configurable: !0
        });
      }
    }
    function ir(e, r) {
      {
        var n = function() {
          Se || (Se = !0, h("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        n.isReactWarning = !0, Object.defineProperty(e, "ref", {
          get: n,
          configurable: !0
        });
      }
    }
    var sr = function(e, r, n, a, l, f, c) {
      var u = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: t,
        // Built-in properties that belong on the element
        type: e,
        key: r,
        ref: n,
        props: c,
        // Record the component responsible for creating this element.
        _owner: f
      };
      return u._store = {}, Object.defineProperty(u._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(u, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: a
      }), Object.defineProperty(u, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: l
      }), Object.freeze && (Object.freeze(u.props), Object.freeze(u)), u;
    };
    function ur(e, r, n, a, l) {
      {
        var f, c = {}, u = null, C = null;
        n !== void 0 && (_e(n), u = "" + n), nr(r) && (_e(r.key), u = "" + r.key), tr(r) && (C = r.ref, or(r, l));
        for (f in r)
          Y.call(r, f) && !rr.hasOwnProperty(f) && (c[f] = r[f]);
        if (e && e.defaultProps) {
          var m = e.defaultProps;
          for (f in m)
            c[f] === void 0 && (c[f] = m[f]);
        }
        if (u || C) {
          var p = typeof e == "function" ? e.displayName || e.name || "Unknown" : e;
          u && ar(c, p), C && ir(c, p);
        }
        return sr(e, u, C, l, a, Re.current, c);
      }
    }
    var ne = k.ReactCurrentOwner, Pe = k.ReactDebugCurrentFrame;
    function $(e) {
      if (e) {
        var r = e._owner, n = B(e.type, e._source, r ? r.type : null);
        Pe.setExtraStackFrame(n);
      } else
        Pe.setExtraStackFrame(null);
    }
    var oe;
    oe = !1;
    function ae(e) {
      return typeof e == "object" && e !== null && e.$$typeof === t;
    }
    function xe() {
      {
        if (ne.current) {
          var e = j(ne.current.type);
          if (e)
            return `

Check the render method of \`` + e + "`.";
        }
        return "";
      }
    }
    function cr(e) {
      return "";
    }
    var Oe = {};
    function lr(e) {
      {
        var r = xe();
        if (!r) {
          var n = typeof e == "string" ? e : e.displayName || e.name;
          n && (r = `

Check the top-level render call using <` + n + ">.");
        }
        return r;
      }
    }
    function Fe(e, r) {
      {
        if (!e._store || e._store.validated || e.key != null)
          return;
        e._store.validated = !0;
        var n = lr(r);
        if (Oe[n])
          return;
        Oe[n] = !0;
        var a = "";
        e && e._owner && e._owner !== ne.current && (a = " It was passed a child from " + j(e._owner.type) + "."), $(e), h('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', n, a), $(null);
      }
    }
    function ke(e, r) {
      {
        if (typeof e != "object")
          return;
        if (te(e))
          for (var n = 0; n < e.length; n++) {
            var a = e[n];
            ae(a) && Fe(a, r);
          }
        else if (ae(e))
          e._store && (e._store.validated = !0);
        else if (e) {
          var l = Z(e);
          if (typeof l == "function" && l !== e.entries)
            for (var f = l.call(e), c; !(c = f.next()).done; )
              ae(c.value) && Fe(c.value, r);
        }
      }
    }
    function fr(e) {
      {
        var r = e.type;
        if (r == null || typeof r == "string")
          return;
        var n;
        if (typeof r == "function")
          n = r.propTypes;
        else if (typeof r == "object" && (r.$$typeof === g || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        r.$$typeof === T))
          n = r.propTypes;
        else
          return;
        if (n) {
          var a = j(r);
          Xe(n, e.props, "prop", a, e);
        } else if (r.PropTypes !== void 0 && !oe) {
          oe = !0;
          var l = j(r);
          h("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", l || "Unknown");
        }
        typeof r.getDefaultProps == "function" && !r.getDefaultProps.isReactClassApproved && h("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function hr(e) {
      {
        for (var r = Object.keys(e.props), n = 0; n < r.length; n++) {
          var a = r[n];
          if (a !== "children" && a !== "key") {
            $(e), h("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", a), $(null);
            break;
          }
        }
        e.ref !== null && ($(e), h("Invalid attribute `ref` supplied to `React.Fragment`."), $(null));
      }
    }
    var je = {};
    function Me(e, r, n, a, l, f) {
      {
        var c = Ue(e);
        if (!c) {
          var u = "";
          (e === void 0 || typeof e == "object" && e !== null && Object.keys(e).length === 0) && (u += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var C = cr();
          C ? u += C : u += xe();
          var m;
          e === null ? m = "null" : te(e) ? m = "array" : e !== void 0 && e.$$typeof === t ? (m = "<" + (j(e.type) || "Unknown") + " />", u = " Did you accidentally export a JSX literal instead of a component?") : m = typeof e, h("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", m, u);
        }
        var p = ur(e, r, n, l, f);
        if (p == null)
          return p;
        if (c) {
          var S = r.children;
          if (S !== void 0)
            if (a)
              if (te(S)) {
                for (var L = 0; L < S.length; L++)
                  ke(S[L], e);
                Object.freeze && Object.freeze(S);
              } else
                h("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              ke(S, e);
        }
        if (Y.call(r, "key")) {
          var A = j(e), _ = Object.keys(r).filter(function(yr) {
            return yr !== "key";
          }), ie = _.length > 0 ? "{key: someKey, " + _.join(": ..., ") + ": ...}" : "{key: someKey}";
          if (!je[A + ie]) {
            var gr = _.length > 0 ? "{" + _.join(": ..., ") + ": ...}" : "{}";
            h(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, ie, A, gr, A), je[A + ie] = !0;
          }
        }
        return e === d ? hr(p) : fr(p), p;
      }
    }
    function dr(e, r, n) {
      return Me(e, r, n, !0);
    }
    function mr(e, r, n) {
      return Me(e, r, n, !1);
    }
    var pr = mr, vr = dr;
    z.Fragment = d, z.jsx = pr, z.jsxs = vr;
  }()), z;
}
process.env.NODE_ENV === "production" ? ue.exports = wr() : ue.exports = Sr();
var Le = ue.exports, w = /* @__PURE__ */ ((i) => (i.THEME_CHANGED = "theme_changed", i.THEME_LOADING = "theme_loading", i.THEME_LOADED = "theme_loaded", i.THEME_ERROR = "theme_error", i.THEME_RESET = "theme_reset", i))(w || {});
class Pr {
  constructor(t = {}) {
    H(this, "currentTheme");
    H(this, "themeConfig");
    H(this, "options");
    H(this, "listeners", []);
    H(this, "loadedStylesheets", /* @__PURE__ */ new Set());
    this.options = {
      enableAutoDetection: !0,
      enablePersistence: !0,
      storageKey: "crm-app-theme",
      defaultTheme: I.CRM,
      ...t
    }, this.currentTheme = this.detectInitialTheme(), this.themeConfig = this.getThemeConfigForMode(this.currentTheme);
  }
  /**
   * Initialize the theme manager
   */
  async initialize() {
    try {
      this.emitEvent(w.THEME_LOADING, this.currentTheme), await this.applyTheme(this.currentTheme), this.emitEvent(w.THEME_LOADED, this.currentTheme), E.info("Theme manager initialized", { theme: this.currentTheme });
    } catch (t) {
      const o = t instanceof Error ? t.message : "Theme initialization failed";
      throw this.emitEvent(w.THEME_ERROR, this.currentTheme, { error: o }), E.error("Theme manager initialization failed:", t), t;
    }
  }
  /**
   * Switch to a different theme
   */
  async switchTheme(t) {
    if (t === this.currentTheme)
      return;
    const o = this.currentTheme;
    try {
      this.emitEvent(w.THEME_LOADING, t, { previousTheme: o }), this.currentTheme = t, this.themeConfig = this.getThemeConfigForMode(t), await this.applyTheme(t), this.options.enablePersistence && this.persistTheme(t), this.emitEvent(w.THEME_CHANGED, t, { previousTheme: o }), E.info("Theme switched", { from: o, to: t });
    } catch (d) {
      this.currentTheme = o, this.themeConfig = this.getThemeConfigForMode(o);
      const y = d instanceof Error ? d.message : "Theme switch failed";
      throw this.emitEvent(w.THEME_ERROR, t, { error: y, previousTheme: o }), E.error("Theme switch failed:", d), d;
    }
  }
  /**
   * Reset theme to deployment context default
   */
  async resetTheme() {
    const t = this.options.enableAutoDetection ? this.detectThemeFromDeploymentContext() : this.options.defaultTheme;
    await this.switchTheme(t), this.options.enablePersistence && this.clearPersistedTheme(), this.emitEvent(w.THEME_RESET, t);
  }
  /**
   * Get current theme
   */
  getCurrentTheme() {
    return this.currentTheme;
  }
  /**
   * Get current theme configuration
   */
  getThemeConfig() {
    return this.themeConfig;
  }
  /**
   * Subscribe to theme events
   */
  onThemeChange(t) {
    return this.listeners.push(t), () => {
      const o = this.listeners.indexOf(t);
      o > -1 && this.listeners.splice(o, 1);
    };
  }
  /**
   * Apply theme to the DOM
   */
  async applyTheme(t) {
    document.documentElement.setAttribute("data-theme", t), document.body.setAttribute("data-theme", t), await this.loadThemeStylesheet(t), this.applyCSSCustomProperties(this.themeConfig), this.updateMetaThemeColor(this.themeConfig.primaryColor);
  }
  /**
   * Load theme-specific CSS stylesheet
   */
  async loadThemeStylesheet(t) {
    const o = `theme-${t}`;
    if (this.removeThemeStylesheets(), !this.loadedStylesheets.has(o))
      return new Promise((d, y) => {
        const s = document.createElement("link");
        s.id = o, s.rel = "stylesheet", s.type = "text/css", s.href = this.getThemeStylesheetPath(t), s.onload = () => {
          this.loadedStylesheets.add(o), d();
        }, s.onerror = () => {
          y(new Error(`Failed to load theme stylesheet: ${s.href}`));
        }, document.head.appendChild(s);
      });
  }
  /**
   * Remove existing theme stylesheets
   */
  removeThemeStylesheets() {
    document.querySelectorAll('link[id^="theme-"]').forEach((o) => {
      o.remove(), this.loadedStylesheets.delete(o.id);
    });
  }
  /**
   * Get theme stylesheet path
   */
  getThemeStylesheetPath(t) {
    return `/src/shared/styles/themes/${t}-theme.css`;
  }
  /**
   * Apply CSS custom properties to document root
   */
  applyCSSCustomProperties(t) {
    const o = document.documentElement;
    o.style.setProperty("--theme-primary", t.primaryColor), o.style.setProperty("--theme-secondary", t.secondaryColor), o.style.setProperty("--theme-bg-primary", t.backgroundColor), o.style.setProperty("--theme-text-primary", t.textColor), o.style.setProperty("--theme-border-primary", t.borderColor), o.style.setProperty("--theme-font-family", t.fontFamily), Object.entries(t.customProperties).forEach(([d, y]) => {
      o.style.setProperty(d, y);
    });
  }
  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(t) {
    let o = document.querySelector('meta[name="theme-color"]');
    o || (o = document.createElement("meta"), o.setAttribute("name", "theme-color"), document.head.appendChild(o)), o.setAttribute("content", t);
  }
  /**
   * Detect initial theme based on options
   */
  detectInitialTheme() {
    if (this.options.enablePersistence) {
      const t = this.getPersistedTheme();
      if (t)
        return t;
    }
    return this.options.enableAutoDetection ? this.detectThemeFromDeploymentContext() : this.options.defaultTheme;
  }
  /**
   * Detect theme from deployment context
   */
  detectThemeFromDeploymentContext() {
    try {
      return _r().mode;
    } catch (t) {
      return E.warn("Failed to detect theme from deployment context:", t), this.options.defaultTheme;
    }
  }
  /**
   * Get theme configuration for a specific mode
   */
  getThemeConfigForMode(t) {
    try {
      const o = Rr();
      return o.theme.mode === t ? o.theme : this.createThemeConfigForMode(t);
    } catch (o) {
      return E.warn("Failed to get theme config from deployment context:", o), this.createThemeConfigForMode(t);
    }
  }
  /**
   * Create theme configuration for a specific mode
   */
  createThemeConfigForMode(t) {
    switch (t) {
      case I.CRM:
        return {
          mode: I.CRM,
          primaryColor: "#0078d4",
          secondaryColor: "#106ebe",
          backgroundColor: "#ffffff",
          textColor: "#323130",
          borderColor: "#8a8886",
          fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
          customProperties: {
            "--crm-header-bg": "#0078d4",
            "--crm-sidebar-bg": "#f3f2f1",
            "--crm-card-bg": "#ffffff",
            "--crm-border-radius": "2px",
            "--crm-shadow": "0 2px 4px rgba(0, 0, 0, 0.1)"
          }
        };
      case I.MFE:
        return {
          mode: I.MFE,
          primaryColor: "#6366f1",
          secondaryColor: "#4f46e5",
          backgroundColor: "#f8fafc",
          textColor: "#1e293b",
          borderColor: "#e2e8f0",
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
          customProperties: {
            "--mfe-header-bg": "#1e293b",
            "--mfe-sidebar-bg": "#f1f5f9",
            "--mfe-card-bg": "#ffffff",
            "--mfe-border-radius": "8px",
            "--mfe-shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
          }
        };
      default:
        throw new Error(`Unsupported theme mode: ${t}`);
    }
  }
  /**
   * Persist theme to storage
   */
  persistTheme(t) {
    try {
      localStorage.setItem(this.options.storageKey, t);
    } catch (o) {
      E.warn("Failed to persist theme:", o);
    }
  }
  /**
   * Get persisted theme from storage
   */
  getPersistedTheme() {
    try {
      const t = localStorage.getItem(this.options.storageKey);
      if (t && Object.values(I).includes(t))
        return t;
    } catch (t) {
      E.warn("Failed to get persisted theme:", t);
    }
    return null;
  }
  /**
   * Clear persisted theme from storage
   */
  clearPersistedTheme() {
    try {
      localStorage.removeItem(this.options.storageKey);
    } catch (t) {
      E.warn("Failed to clear persisted theme:", t);
    }
  }
  /**
   * Emit theme event
   */
  emitEvent(t, o, d) {
    const y = {
      event: t,
      theme: o,
      timestamp: /* @__PURE__ */ new Date(),
      ...d
    };
    this.listeners.forEach((s) => {
      try {
        s(y);
      } catch (x) {
        E.error("Error in theme event listener:", x);
      }
    }), E.debug("Theme event emitted:", y);
  }
}
const He = br(null), jr = ({
  children: i,
  defaultTheme: t = I.CRM,
  enableAutoDetection: o = !0,
  enablePersistence: d = !0,
  storageKey: y = "crm-app-theme"
}) => {
  const [s] = N(() => new Pr({
    defaultTheme: t,
    enableAutoDetection: o,
    enablePersistence: d,
    storageKey: y
  })), [x, P] = N(t), [g, O] = N(s.getThemeConfig()), [b, T] = N(!0), [F, R] = N(null);
  De(() => {
    (async () => {
      try {
        T(!0), R(null), await s.initialize(), P(s.getCurrentTheme()), O(s.getThemeConfig()), T(!1), E.info("Theme provider initialized", {
          theme: s.getCurrentTheme()
        });
      } catch (v) {
        const M = v instanceof Error ? v.message : "Theme initialization failed";
        R(M), T(!1), E.error("Theme provider initialization failed:", v);
      }
    })();
  }, [s]), De(() => s.onThemeChange((v) => {
    switch (v.event) {
      case w.THEME_LOADING:
        T(!0), R(null);
        break;
      case w.THEME_CHANGED:
      case w.THEME_LOADED:
      case w.THEME_RESET:
        P(v.theme), O(s.getThemeConfig()), T(!1), R(null);
        break;
      case w.THEME_ERROR:
        T(!1), R(v.error || "Theme error occurred");
        break;
    }
  }), [s]);
  const G = se(async (h) => {
    try {
      await s.switchTheme(h);
    } catch (v) {
      const M = v instanceof Error ? v.message : "Theme switch failed";
      R(M), E.error("Theme switch failed:", v);
    }
  }, [s]), X = se(async () => {
    try {
      await s.resetTheme();
    } catch (h) {
      const v = h instanceof Error ? h.message : "Theme reset failed";
      R(v), E.error("Theme reset failed:", h);
    }
  }, [s]), Z = se(async (h) => {
    try {
      await s.switchTheme(h);
    } catch (v) {
      const M = v instanceof Error ? v.message : "Theme application failed";
      R(M), E.error("Theme application failed:", v);
    }
  }, [s]), k = {
    currentTheme: x,
    themeConfig: g,
    isLoading: b,
    error: F,
    switchTheme: G,
    resetTheme: X,
    applyTheme: Z
  };
  return /* @__PURE__ */ Le.jsx(He.Provider, { value: k, children: i });
}, V = () => {
  const i = Cr(He);
  if (!i)
    throw new Error("useTheme must be used within a ThemeProvider");
  return i;
}, Mr = () => {
  const { currentTheme: i } = V();
  return i;
}, Dr = () => {
  const { themeConfig: i } = V();
  return i;
}, Ar = (i) => {
  const { currentTheme: t } = V();
  return t === i;
}, Ir = () => {
  const { themeConfig: i } = V();
  return {
    primary: i.primaryColor,
    secondary: i.secondaryColor,
    background: i.backgroundColor,
    text: i.textColor,
    border: i.borderColor,
    fontFamily: i.fontFamily,
    ...i.customProperties
  };
}, $r = () => {
  const { currentTheme: i, themeConfig: t } = V();
  return {
    currentTheme: i,
    themeConfig: t,
    getThemeClass: (s) => `${s} theme-${i}`,
    getThemeStyle: (s) => s[i] || {},
    getCSSVariable: (s) => getComputedStyle(document.documentElement).getPropertyValue(s)
  };
}, Lr = (i) => {
  const t = (o) => {
    const d = V();
    return /* @__PURE__ */ Le.jsx(i, { ...o, theme: d });
  };
  return t.displayName = `withTheme(${i.displayName || i.name})`, t;
};
export {
  w as T,
  Pr as a,
  jr as b,
  Mr as c,
  Dr as d,
  Ar as e,
  Ir as f,
  $r as g,
  Le as j,
  V as u,
  Lr as w
};
