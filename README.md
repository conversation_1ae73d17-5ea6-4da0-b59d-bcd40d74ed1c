# CRM React Apps Monorepo

A modern multi-app React monorepo built with Vite, TypeScript, and shared packages. Includes legacy PCF components for Dynamics 365 integration.

## 🏗️ Architecture

This monorepo contains:

- **Modern React Apps** (`apps/`) - Independent React 18 applications with Vite
- **Shared Packages** (`shared/`) - Reusable components, services, and utilities with deployment abstraction
- **PCF Components** (`pcf/`) - Dynamics 365 Power Platform Components (React 16)

### 🔄 Migration-Ready Architecture

The monorepo supports seamless migration from Dynamics 365 web resources to standalone SPAs:

- **Deployment Context Detection** - Automatically detects runtime environment
- **Authentication Abstraction** - Unified auth layer supporting both D365 implicit auth and MSAL
- **API Client Abstraction** - Switches between Xrm.WebApi and external Dataverse API calls
- **Configuration-Driven** - Environment-based switching with minimal code changes

## 📁 Project Structure

```
crm-react-apps/
├── apps/
│   ├── transcript-and-summary/     # Transcript and Summary App
│   └── if-party-master/           # IF Party Master App
├── pcf/
│   ├── contact-timeline-control/   # Dynamics 365 PCF Control
│   └── shared/                    # PCF-specific shared components
├── shared/
│   ├── components/                # Reusable React components
│   ├── services/                  # API clients and business logic
│   └── utils/                     # Pure utility functions
├── tsconfig.base.json             # Shared TypeScript configuration
├── package.json                   # Workspace configuration
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm 9+

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd crm-react-apps

# Install all dependencies
npm install

# Optional: Copy environment variables template
cp .env.example .env
```

### Development

Start individual apps in development mode:

```bash
# Default mode (auto-detects deployment context)
npm run dev:transcript-and-summary
npm run dev:if-party-master

# Specific deployment modes
npm run dev:transcript-and-summary:webresource    # D365 web resource mode
npm run dev:transcript-and-summary:standalone     # Standalone SPA mode
npm run dev:if-party-master:webresource
npm run dev:if-party-master:standalone
```

Apps will be available at:
- Transcript and Summary: http://localhost:5173
- IF Party Master: http://localhost:5174

### Building

Build individual apps:

```bash
# Default builds
npm run build:transcript-and-summary
npm run build:if-party-master
npm run build:shared
npm run build:all

# Deployment-specific builds
npm run build:all:webresource                    # For D365 web resource deployment
npm run build:all:standalone                     # For standalone SPA deployment
npm run build:transcript-and-summary:webresource
npm run build:transcript-and-summary:standalone
npm run build:if-party-master:webresource
npm run build:if-party-master:standalone
```

### Preview Production Builds

```bash
npm run preview:transcript-and-summary
npm run preview:if-party-master
```

## 📦 Import Aliases

Use these aliases to import shared code:

```typescript
// Components
import { Button } from '@shared/components/Button';
import { DatePicker } from '@shared/components';

// Services (deployment-aware)
import { getApiClient } from '@shared/services/api/apiFactory';
import { useAuth } from '@shared/services';

// Configuration
import { getDeploymentConfig, isWebResourceMode } from '@shared/config';

// Utils
import { formatDate } from '@shared/utils/formatDate';
import { logger } from '@shared/utils';
```

## 🔄 Migration Support

The monorepo supports migration from Dynamics 365 web resources to standalone SPAs:

```typescript
// Automatic deployment detection
import { getDeploymentConfig } from '@shared/config';
const config = getDeploymentConfig();

// Unified authentication (works in both modes)
import { useAuth } from '@shared/services';
const { user, isAuthenticated, login } = useAuth();

// Deployment-aware API client
import { getApiClient } from '@shared/services';
const apiClient = await getApiClient();
const contacts = await apiClient.retrieveMultipleRecords('contact');
```

See [MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md) for detailed migration instructions.

## 🔧 PCF Development

PCF components use React 16.14.0 for Dynamics 365 compatibility:

```bash
# Build PCF component
npm run build:pcf

# The built component will be in pcf/contact-timeline-control/dist/
```

### PCF Deployment

1. Build the PCF component: `npm run build:pcf`
2. Use Power Platform CLI to package and deploy
3. Import into your Dynamics 365 environment

## 🧪 Testing

```bash
# Type checking
npm run type-check

# Linting
npm run lint
```

## 📝 Adding New Apps

1. Create new directory under `apps/`
2. Copy structure from existing app
3. Update root `package.json` scripts
4. Configure Vite with path aliases

## 📝 Adding New Shared Packages

1. Create new directory under `shared/`
2. Add `index.ts` barrel export
3. Update `tsconfig.base.json` paths if needed
4. Import using `@shared/your-package`

## 🔍 Troubleshooting

### Common Issues

- **Module not found**: Check path aliases in `vite.config.ts` and `tsconfig.json`
- **Type errors**: Ensure `tsconfig.base.json` is extended properly
- **PCF build fails**: Verify React 16 dependencies in PCF package.json
- **Environment variables**: Use `VITE_` prefix for variables accessible in browser code

### Environment Variables

Create a `.env` file in the root directory for custom configuration:

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Custom variables (must start with VITE_)
VITE_APP_TITLE=My CRM App
```

### Clean Build

```bash
npm run clean
npm install
npm run build:all
```

## 🤝 Contributing

1. Create feature branch
2. Make changes
3. Test locally with `npm run type-check` and `npm run lint`
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details
