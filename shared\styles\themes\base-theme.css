/**
 * Base Theme Styles
 * 
 * Common styles that apply to all themes
 * These styles use CSS custom properties that are defined in specific theme files
 */

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  margin: 0;
  font-family: var(--theme-font-family, system-ui, sans-serif);
  font-size: var(--theme-font-size-base, 1rem);
  line-height: var(--theme-line-height-normal, 1.5);
  color: var(--theme-text-primary, #000);
  background-color: var(--theme-bg-primary, #fff);
  transition: background-color var(--theme-transition-base, 200ms ease-in-out),
              color var(--theme-transition-base, 200ms ease-in-out);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--theme-spacing-md, 16px) 0;
  font-weight: var(--theme-font-weight-semibold, 600);
  line-height: var(--theme-line-height-tight, 1.25);
  color: var(--theme-text-primary);
}

h1 { font-size: var(--theme-font-size-3xl, 1.875rem); }
h2 { font-size: var(--theme-font-size-2xl, 1.5rem); }
h3 { font-size: var(--theme-font-size-xl, 1.25rem); }
h4 { font-size: var(--theme-font-size-lg, 1.125rem); }
h5 { font-size: var(--theme-font-size-base, 1rem); }
h6 { font-size: var(--theme-font-size-sm, 0.875rem); }

p {
  margin: 0 0 var(--theme-spacing-md, 16px) 0;
  color: var(--theme-text-primary);
}

/* Links */
a {
  color: var(--theme-primary, #0078d4);
  text-decoration: none;
  transition: color var(--theme-transition-fast, 150ms ease-in-out);
}

a:hover {
  color: var(--theme-primary-hover, #106ebe);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin: 0 0 var(--theme-spacing-md, 16px) 0;
  padding-left: var(--theme-spacing-lg, 24px);
}

li {
  margin-bottom: var(--theme-spacing-xs, 4px);
}

/* Base Component Classes */

/* Buttons */
.theme-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--theme-spacing-xs, 4px);
  padding: var(--theme-button-padding-base, 8px 16px);
  height: var(--theme-button-height-base, 32px);
  border: 1px solid transparent;
  border-radius: var(--theme-radius-base, 4px);
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base, 1rem);
  font-weight: var(--theme-font-weight-medium, 500);
  line-height: 1;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--theme-transition-base, 200ms ease-in-out);
  user-select: none;
  white-space: nowrap;
}

.theme-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.theme-button-sm {
  padding: var(--theme-button-padding-sm, 4px 8px);
  height: var(--theme-button-height-sm, 24px);
  font-size: var(--theme-font-size-sm, 0.875rem);
}

.theme-button-lg {
  padding: var(--theme-button-padding-lg, 12px 24px);
  height: var(--theme-button-height-lg, 40px);
  font-size: var(--theme-font-size-lg, 1.125rem);
}

/* Cards */
.theme-card {
  display: block;
  padding: var(--theme-spacing-lg, 24px);
  background-color: var(--theme-bg-primary, #fff);
  border: 1px solid var(--theme-border-primary, #e2e8f0);
  border-radius: var(--theme-radius-lg, 8px);
  box-shadow: var(--theme-shadow-base, 0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all var(--theme-transition-base, 200ms ease-in-out);
}

.theme-card-header {
  margin-bottom: var(--theme-spacing-md, 16px);
  padding-bottom: var(--theme-spacing-md, 16px);
  border-bottom: 1px solid var(--theme-border-tertiary, #f1f5f9);
}

.theme-card-title {
  margin: 0;
  font-size: var(--theme-font-size-lg, 1.125rem);
  font-weight: var(--theme-font-weight-semibold, 600);
  color: var(--theme-text-primary);
}

.theme-card-content {
  color: var(--theme-text-primary);
}

/* Form Controls */
.theme-input {
  display: block;
  width: 100%;
  padding: var(--theme-input-padding, 8px 12px);
  height: var(--theme-input-height, 32px);
  border: var(--theme-input-border-width, 1px) solid var(--theme-border-primary, #e2e8f0);
  border-radius: var(--theme-radius-base, 4px);
  font-family: var(--theme-font-family);
  font-size: var(--theme-font-size-base, 1rem);
  background-color: var(--theme-bg-primary, #fff);
  color: var(--theme-text-primary, #000);
  transition: all var(--theme-transition-base, 200ms ease-in-out);
}

.theme-input::placeholder {
  color: var(--theme-text-tertiary, #64748b);
}

.theme-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--theme-bg-tertiary, #f1f5f9);
}

.theme-textarea {
  resize: vertical;
  min-height: 80px;
  height: auto;
}

.theme-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 32px;
  appearance: none;
}

/* Loading Spinner */
.theme-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme-border-tertiary, #f1f5f9);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #0078d4);
  animation: theme-spin 1s ease-in-out infinite;
}

.theme-spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

.theme-spinner-lg {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

@keyframes theme-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Layout Components */
.theme-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--theme-spacing-lg, 24px);
  height: var(--theme-header-height, 48px);
  background-color: var(--theme-primary, #0078d4);
  color: var(--theme-text-inverse, #fff);
  border-bottom: 1px solid var(--theme-border-primary, #e2e8f0);
}

.theme-sidebar {
  display: flex;
  flex-direction: column;
  width: var(--theme-sidebar-width, 280px);
  height: 100vh;
  background-color: var(--theme-bg-secondary, #f8fafc);
  border-right: 1px solid var(--theme-border-primary, #e2e8f0);
  overflow-y: auto;
}

.theme-main-content {
  flex: 1;
  padding: var(--theme-spacing-lg, 24px);
  max-width: var(--theme-content-max-width, 1200px);
  margin: 0 auto;
}

/* Utility Classes */
.theme-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.theme-transition {
  transition: all var(--theme-transition-base, 200ms ease-in-out);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .theme-sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: var(--theme-header-height, 48px);
    left: 0;
    z-index: var(--theme-z-modal, 1050);
    transform: translateX(-100%);
    transition: transform var(--theme-transition-base, 200ms ease-in-out);
  }

  .theme-sidebar.is-open {
    transform: translateX(0);
  }

  .theme-main-content {
    padding: var(--theme-spacing-md, 16px);
  }
}
