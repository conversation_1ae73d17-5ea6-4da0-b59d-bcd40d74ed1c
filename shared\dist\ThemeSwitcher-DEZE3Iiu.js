"use strict";const e=require("./themeContext-C9lKRaQT.js"),E=require("react"),t=require("./index-olv4Xr2Y.js"),M=({children:n,onClick:c,variant:i="primary",size:a="medium",disabled:m=!1,type:p="button",className:h="",style:x,...j})=>{const{getThemeClass:o}=e.useThemeStyles(),s="theme-button",l={primary:"theme-button-primary",secondary:"theme-button-secondary",danger:"theme-button-danger"},u={small:"theme-button-sm",medium:"theme-button-base",large:"theme-button-lg"},f=m?"disabled":"",g=[o(s),o(l[i]),o(u[a]),f,h].filter(Boolean).join(" ");return e.jsxRuntimeExports.jsx("button",{type:p,className:g,style:x,onClick:c,disabled:m,...j,children:n})},T=({size:n="medium",color:c="primary",className:i="",text:a})=>{const{getThemeClass:m}=e.useThemeStyles(),p={small:"theme-spinner-sm",medium:"theme-spinner",large:"theme-spinner-lg"},h={primary:"theme-spinner-primary",secondary:"theme-spinner-secondary",white:"theme-spinner-white"},x=[m("theme-spinner"),m(p[n]),m(h[c]),i].filter(Boolean).join(" ");return e.jsxRuntimeExports.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsxRuntimeExports.jsxs("svg",{className:x,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsxRuntimeExports.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsxRuntimeExports.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a&&e.jsxRuntimeExports.jsx("p",{className:`mt-2 text-sm ${h[c]}`,children:a})]})},b={[t.ThemeMode.CRM]:"Dynamics CRM",[t.ThemeMode.MFE]:"ZB Champion"},v={[t.ThemeMode.CRM]:"🏢",[t.ThemeMode.MFE]:"🎯"},C={[t.ThemeMode.CRM]:"Dynamics 365 enterprise styling",[t.ThemeMode.MFE]:"ZB Champion standard theme"},N=({variant:n="dropdown",size:c="md",showLabels:i=!0,showIcons:a=!0,className:m="",disabled:p=!1,hideIfDisabled:h=!0})=>{const{switchTheme:x,isLoading:j,error:o}=e.useTheme(),s=e.useCurrentTheme(),[l,u]=E.useState(!1),g=t.getDeploymentConfig().features.enableThemeSwitching;if(!g&&h)return null;const d=p||!g||j,y=async r=>{if(!(d||r===s))try{await x(r),u(!1)}catch(R){console.error("Failed to switch theme:",R)}},w=`theme-switcher ${(()=>{switch(c){case"sm":return"theme-switcher-sm";case"lg":return"theme-switcher-lg";default:return"theme-switcher-md"}})()} ${m}`;return n==="toggle"?e.jsxRuntimeExports.jsxs("div",{className:`${w} theme-switcher-toggle`,children:[e.jsxRuntimeExports.jsxs("button",{className:`theme-toggle-button ${d?"disabled":""}`,onClick:()=>y(s===t.ThemeMode.CRM?t.ThemeMode.MFE:t.ThemeMode.CRM),disabled:d,title:`Switch to ${s===t.ThemeMode.CRM?b[t.ThemeMode.MFE]:b[t.ThemeMode.CRM]}`,children:[a&&e.jsxRuntimeExports.jsx("span",{className:"theme-icon",children:v[s===t.ThemeMode.CRM?t.ThemeMode.MFE:t.ThemeMode.CRM]}),i&&e.jsxRuntimeExports.jsx("span",{className:"theme-label",children:s===t.ThemeMode.CRM?"Switch to MFE":"Switch to CRM"})]}),o&&e.jsxRuntimeExports.jsx("div",{className:"theme-error",title:o,children:"⚠️"})]}):n==="buttons"?e.jsxRuntimeExports.jsxs("div",{className:`${w} theme-switcher-buttons`,children:[Object.values(t.ThemeMode).map(r=>e.jsxRuntimeExports.jsxs("button",{className:`theme-button ${s===r?"active":""} ${d?"disabled":""}`,onClick:()=>y(r),disabled:d,title:C[r],children:[a&&e.jsxRuntimeExports.jsx("span",{className:"theme-icon",children:v[r]}),i&&e.jsxRuntimeExports.jsx("span",{className:"theme-label",children:b[r]})]},r)),o&&e.jsxRuntimeExports.jsx("div",{className:"theme-error",title:o,children:"⚠️"})]}):e.jsxRuntimeExports.jsxs("div",{className:`${w} theme-switcher-dropdown`,children:[e.jsxRuntimeExports.jsxs("button",{className:`theme-dropdown-trigger ${l?"open":""} ${d?"disabled":""}`,onClick:()=>!d&&u(!l),disabled:d,"aria-expanded":l,"aria-haspopup":"listbox",children:[a&&e.jsxRuntimeExports.jsx("span",{className:"theme-icon",children:v[s]}),i&&e.jsxRuntimeExports.jsx("span",{className:"theme-label",children:b[s]}),e.jsxRuntimeExports.jsx("span",{className:"theme-dropdown-arrow",children:"▼"})]}),l&&e.jsxRuntimeExports.jsx("div",{className:"theme-dropdown-menu",children:Object.values(t.ThemeMode).map(r=>e.jsxRuntimeExports.jsxs("button",{className:`theme-dropdown-item ${s===r?"active":""}`,onClick:()=>y(r),role:"option","aria-selected":s===r,children:[a&&e.jsxRuntimeExports.jsx("span",{className:"theme-icon",children:v[r]}),e.jsxRuntimeExports.jsxs("div",{className:"theme-info",children:[i&&e.jsxRuntimeExports.jsx("span",{className:"theme-label",children:b[r]}),e.jsxRuntimeExports.jsx("span",{className:"theme-description",children:C[r]})]}),s===r&&e.jsxRuntimeExports.jsx("span",{className:"theme-check",children:"✓"})]},r))}),o&&e.jsxRuntimeExports.jsx("div",{className:"theme-error",title:o,children:"⚠️"}),l&&e.jsxRuntimeExports.jsx("div",{className:"theme-dropdown-backdrop",onClick:()=>u(!1)})]})},k=`
.theme-switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.theme-switcher-sm {
  font-size: 0.875rem;
}

.theme-switcher-md {
  font-size: 1rem;
}

.theme-switcher-lg {
  font-size: 1.125rem;
}

/* Toggle variant */
.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-toggle-button:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

/* Buttons variant */
.theme-switcher-buttons {
  display: flex;
  gap: 4px;
  padding: 2px;
  background-color: var(--theme-bg-tertiary);
  border-radius: var(--theme-radius-lg);
}

.theme-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--theme-radius-base);
  background-color: transparent;
  color: var(--theme-text-secondary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-button:hover:not(.disabled) {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.theme-button.active {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

/* Dropdown variant */
.theme-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
}

.theme-dropdown-trigger:hover:not(.disabled) {
  background-color: var(--theme-bg-secondary);
  border-color: var(--theme-border-secondary);
}

.theme-dropdown-arrow {
  font-size: 0.75em;
  transition: transform var(--theme-transition-base);
}

.theme-dropdown-trigger.open .theme-dropdown-arrow {
  transform: rotate(180deg);
}

.theme-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--theme-radius-lg);
  box-shadow: var(--theme-shadow-lg);
  z-index: var(--theme-z-dropdown);
  overflow: hidden;
}

.theme-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background-color: transparent;
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all var(--theme-transition-base);
  text-align: left;
}

.theme-dropdown-item:hover {
  background-color: var(--theme-bg-secondary);
}

.theme-dropdown-item.active {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.theme-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.theme-description {
  font-size: 0.875em;
  color: var(--theme-text-secondary);
}

.theme-check {
  color: var(--theme-primary);
  font-weight: bold;
}

.theme-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: calc(var(--theme-z-dropdown) - 1);
}

/* Disabled state */
.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* Error indicator */
.theme-error {
  color: var(--theme-error);
  font-size: 1.2em;
  cursor: help;
}
`;if(typeof document<"u"){const n=document.createElement("style");n.textContent=k,document.head.appendChild(n)}exports.Button=M;exports.LoadingSpinner=T;exports.ThemeSwitcher=N;
