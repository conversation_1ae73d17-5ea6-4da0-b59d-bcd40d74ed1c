/*! @azure/msal-common v14.16.1 2025-08-05 */
const u = {
  LIBRARY_NAME: "MSAL.JS",
  SKU: "msal.js.common",
  // Prefix for all library cache entries
  CACHE_PREFIX: "msal",
  // default authority
  DEFAULT_AUTHORITY: "https://login.microsoftonline.com/common/",
  DEFAULT_AUTHORITY_HOST: "login.microsoftonline.com",
  DEFAULT_COMMON_TENANT: "common",
  // ADFS String
  ADFS: "adfs",
  DSTS: "dstsv2",
  // Default AAD Instance Discovery Endpoint
  AAD_INSTANCE_DISCOVERY_ENDPT: "https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",
  // CIAM URL
  CIAM_AUTH_URL: ".ciamlogin.com",
  AAD_TENANT_DOMAIN_SUFFIX: ".onmicrosoft.com",
  // Resource delimiter - used for certain cache entries
  RESOURCE_DELIM: "|",
  // Placeholder for non-existent account ids/objects
  NO_ACCOUNT: "NO_ACCOUNT",
  // Claims
  CLAIMS: "claims",
  // Consumer UTID
  CONSUMER_UTID: "9188040d-6c67-4c5b-b112-36a304b66dad",
  // Default scopes
  OPENID_SCOPE: "openid",
  PROFILE_SCOPE: "profile",
  OFFLINE_ACCESS_SCOPE: "offline_access",
  EMAIL_SCOPE: "email",
  // Default response type for authorization code flow
  CODE_RESPONSE_TYPE: "code",
  CODE_GRANT_TYPE: "authorization_code",
  RT_GRANT_TYPE: "refresh_token",
  FRAGMENT_RESPONSE_MODE: "fragment",
  S256_CODE_CHALLENGE_METHOD: "S256",
  URL_FORM_CONTENT_TYPE: "application/x-www-form-urlencoded;charset=utf-8",
  AUTHORIZATION_PENDING: "authorization_pending",
  NOT_DEFINED: "not_defined",
  EMPTY_STRING: "",
  NOT_APPLICABLE: "N/A",
  NOT_AVAILABLE: "Not Available",
  FORWARD_SLASH: "/",
  IMDS_ENDPOINT: "http://169.254.169.254/metadata/instance/compute/location",
  IMDS_VERSION: "2020-06-01",
  IMDS_TIMEOUT: 2e3,
  AZURE_REGION_AUTO_DISCOVER_FLAG: "TryAutoDetect",
  REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX: "login.microsoft.com",
  KNOWN_PUBLIC_CLOUDS: [
    "login.microsoftonline.com",
    "login.windows.net",
    "login.microsoft.com",
    "sts.windows.net"
  ],
  TOKEN_RESPONSE_TYPE: "token",
  ID_TOKEN_RESPONSE_TYPE: "id_token",
  SHR_NONCE_VALIDITY: 240,
  INVALID_INSTANCE: "invalid_instance"
}, We = {
  CLIENT_ERROR_RANGE_START: 400,
  CLIENT_ERROR_RANGE_END: 499,
  SERVER_ERROR_RANGE_START: 500,
  SERVER_ERROR_RANGE_END: 599
}, De = [
  u.OPENID_SCOPE,
  u.PROFILE_SCOPE,
  u.OFFLINE_ACCESS_SCOPE
], Dn = [...De, u.EMAIL_SCOPE], F = {
  CONTENT_TYPE: "Content-Type",
  CONTENT_LENGTH: "Content-Length",
  RETRY_AFTER: "Retry-After",
  CCS_HEADER: "X-AnchorMailbox",
  WWWAuthenticate: "WWW-Authenticate",
  AuthenticationInfo: "Authentication-Info",
  X_MS_REQUEST_ID: "x-ms-request-id",
  X_MS_HTTP_VERSION: "x-ms-httpver"
}, H = {
  ID_TOKEN: "idtoken",
  CLIENT_INFO: "client.info",
  ADAL_ID_TOKEN: "adal.idtoken",
  ERROR: "error",
  ERROR_DESC: "error.description",
  ACTIVE_ACCOUNT: "active-account",
  ACTIVE_ACCOUNT_FILTERS: "active-account-filters"
  // new cache entry for active_account for a more robust version for browser
}, ge = {
  COMMON: "common",
  ORGANIZATIONS: "organizations",
  CONSUMERS: "consumers"
}, je = {
  ACCESS_TOKEN: "access_token",
  XMS_CC: "xms_cc"
}, D = {
  LOGIN: "login",
  SELECT_ACCOUNT: "select_account",
  CONSENT: "consent",
  NONE: "none",
  CREATE: "create",
  NO_SESSION: "no_session"
}, Kn = {
  PLAIN: "plain",
  S256: "S256"
}, $e = {
  QUERY: "query",
  FRAGMENT: "fragment"
}, ii = {
  ...$e
}, fo = {
  AUTHORIZATION_CODE_GRANT: "authorization_code",
  REFRESH_TOKEN_GRANT: "refresh_token"
}, Je = {
  MSSTS_ACCOUNT_TYPE: "MSSTS",
  ADFS_ACCOUNT_TYPE: "ADFS",
  GENERIC_ACCOUNT_TYPE: "Generic"
  // NTLM, Kerberos, FBA, Basic etc
}, B = {
  CACHE_KEY_SEPARATOR: "-",
  CLIENT_INFO_SEPARATOR: "."
}, A = {
  ID_TOKEN: "IdToken",
  ACCESS_TOKEN: "AccessToken",
  ACCESS_TOKEN_WITH_AUTH_SCHEME: "AccessToken_With_AuthScheme",
  REFRESH_TOKEN: "RefreshToken"
}, Vt = "appmetadata", ai = "client_info", Be = "1", ot = {
  CACHE_KEY: "authority-metadata",
  REFRESH_TIME_SECONDS: 3600 * 24
  // 24 Hours
}, $ = {
  CONFIG: "config",
  CACHE: "cache",
  NETWORK: "network",
  HARDCODED_VALUES: "hardcoded_values"
}, K = {
  SCHEMA_VERSION: 5,
  MAX_LAST_HEADER_BYTES: 330,
  MAX_CACHED_ERRORS: 50,
  CACHE_KEY: "server-telemetry",
  CATEGORY_SEPARATOR: "|",
  VALUE_SEPARATOR: ",",
  OVERFLOW_TRUE: "1",
  OVERFLOW_FALSE: "0",
  UNKNOWN_ERROR: "unknown_error"
}, k = {
  BEARER: "Bearer",
  POP: "pop",
  SSH: "ssh-cert"
}, Fe = {
  // Default time to throttle RequestThumbprint in seconds
  DEFAULT_THROTTLE_TIME_SECONDS: 60,
  // Default maximum time to throttle in seconds, overrides what the server sends back
  DEFAULT_MAX_THROTTLE_TIME_SECONDS: 3600,
  // Prefix for storing throttling entries
  THROTTLING_PREFIX: "throttling",
  // Value assigned to the x-ms-lib-capability header to indicate to the server the library supports throttling
  X_MS_LIB_CAPABILITY_VALUE: "retry-after, h429"
}, xn = {
  INVALID_GRANT_ERROR: "invalid_grant",
  CLIENT_MISMATCH_ERROR: "client_mismatch"
}, Bn = {
  username: "username",
  password: "password"
}, Xe = {
  httpSuccess: 200,
  httpBadRequest: 400
}, Se = {
  FAILED_AUTO_DETECTION: "1",
  INTERNAL_CACHE: "2",
  ENVIRONMENT_VARIABLE: "3",
  IMDS: "4"
}, wt = {
  CONFIGURED_NO_AUTO_DETECTION: "2",
  AUTO_DETECTION_REQUESTED_SUCCESSFUL: "4",
  AUTO_DETECTION_REQUESTED_FAILED: "5"
}, ue = {
  // When a token is found in the cache or the cache is not supposed to be hit when making the request
  NOT_APPLICABLE: "0",
  // When the token request goes to the identity provider because force_refresh was set to true. Also occurs if claims were requested
  FORCE_REFRESH_OR_CLAIMS: "1",
  // When the token request goes to the identity provider because no cached access token exists
  NO_CACHED_ACCESS_TOKEN: "2",
  // When the token request goes to the identity provider because cached access token expired
  CACHED_ACCESS_TOKEN_EXPIRED: "3",
  // When the token request goes to the identity provider because refresh_in was used and the existing token needs to be refreshed
  PROACTIVELY_REFRESHED: "4"
}, si = {
  Pop: "pop"
}, ci = 300;
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Qt = "unexpected_error", hi = "post_request_failed";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Fn = {
  [Qt]: "Unexpected error in authentication.",
  [hi]: "Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."
};
class O extends Error {
  constructor(e, t, n) {
    const o = t ? `${e}: ${t}` : e;
    super(o), Object.setPrototypeOf(this, O.prototype), this.errorCode = e || u.EMPTY_STRING, this.errorMessage = t || u.EMPTY_STRING, this.subError = n || u.EMPTY_STRING, this.name = "AuthError";
  }
  setCorrelationId(e) {
    this.correlationId = e;
  }
}
function Co(a, e) {
  return new O(a, e ? `${Fn[a]} ${e}` : Fn[a]);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Yt = "client_info_decoding_error", yo = "client_info_empty_error", Wt = "token_parsing_error", To = "null_or_empty_token", se = "endpoints_resolution_error", Io = "network_error", Ao = "openid_config_error", Eo = "hash_not_deserialized", Pe = "invalid_state", So = "state_mismatch", rt = "state_not_found", vo = "nonce_mismatch", jt = "auth_time_not_found", wo = "max_age_transpired", li = "multiple_matching_tokens", di = "multiple_matching_accounts", _o = "multiple_matching_appMetadata", ko = "request_cannot_be_made", Ro = "cannot_remove_empty_scope", bo = "cannot_append_scopeset", Ot = "empty_input_scopeset", ui = "device_code_polling_cancelled", gi = "device_code_expired", pi = "device_code_unknown_error", Jt = "no_account_in_silent_request", Oo = "invalid_cache_record", Xt = "invalid_cache_environment", Nt = "no_account_found", Pt = "no_crypto_object", Mt = "unexpected_credential_type", mi = "invalid_assertion", fi = "invalid_client_credential", ce = "token_refresh_required", Ci = "user_timeout_reached", No = "token_claims_cnf_required_for_signedjwt", Po = "authorization_code_missing_from_server_response", yi = "binding_key_not_removed", Mo = "end_session_endpoint_not_supported", Zt = "key_id_missing", Ti = "no_network_connectivity", Ii = "user_canceled", Ai = "missing_tenant_id_error", S = "method_not_implemented", Ei = "nested_app_auth_bridge_disabled";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Gn = {
  [Yt]: "The client info could not be parsed/decoded correctly",
  [yo]: "The client info was empty",
  [Wt]: "Token cannot be parsed",
  [To]: "The token is null or empty",
  [se]: "Endpoints cannot be resolved",
  [Io]: "Network request failed",
  [Ao]: "Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.",
  [Eo]: "The hash parameters could not be deserialized",
  [Pe]: "State was not the expected format",
  [So]: "State mismatch error",
  [rt]: "State not found",
  [vo]: "Nonce mismatch error",
  [jt]: "Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information.",
  [wo]: "Max Age is set to 0, or too much time has elapsed since the last end-user authentication.",
  [li]: "The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account.",
  [di]: "The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account",
  [_o]: "The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata",
  [ko]: "Token request cannot be made without authorization code or refresh token.",
  [Ro]: "Cannot remove null or empty scope from ScopeSet",
  [bo]: "Cannot append ScopeSet",
  [Ot]: "Empty input ScopeSet cannot be processed",
  [ui]: "Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.",
  [gi]: "Device code is expired.",
  [pi]: "Device code stopped polling for unknown reasons.",
  [Jt]: "Please pass an account object, silent flow is not supported without account information",
  [Oo]: "Cache record object was null or undefined.",
  [Xt]: "Invalid environment when attempting to create cache entry",
  [Nt]: "No account found in cache for given key.",
  [Pt]: "No crypto object detected.",
  [Mt]: "Unexpected credential type.",
  [mi]: "Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515",
  [fi]: "Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential",
  [ce]: "Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.",
  [Ci]: "User defined timeout for device code polling reached",
  [No]: "Cannot generate a POP jwt if the token_claims are not populated",
  [Po]: "Server response does not contain an authorization code to proceed",
  [yi]: "Could not remove the credential's binding key from storage.",
  [Mo]: "The provided authority does not support logout",
  [Zt]: "A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.",
  [Ti]: "No network connectivity. Check your internet connection.",
  [Ii]: "User cancelled the flow.",
  [Ai]: "A tenant id - not common, organizations, or consumers - must be specified when using the client_credentials flow.",
  [S]: "This method has not been implemented",
  [Ei]: "The nested app auth bridge is disabled"
};
class gt extends O {
  constructor(e, t) {
    super(e, t ? `${Gn[e]}: ${t}` : Gn[e]), this.name = "ClientAuthError", Object.setPrototypeOf(this, gt.prototype);
  }
}
function p(a, e) {
  return new gt(a, e);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const it = {
  createNewGuid: () => {
    throw p(S);
  },
  base64Decode: () => {
    throw p(S);
  },
  base64Encode: () => {
    throw p(S);
  },
  base64UrlEncode: () => {
    throw p(S);
  },
  encodeKid: () => {
    throw p(S);
  },
  async getPublicKeyThumbprint() {
    throw p(S);
  },
  async removeTokenBindingKey() {
    throw p(S);
  },
  async clearKeystore() {
    throw p(S);
  },
  async signJwt() {
    throw p(S);
  },
  async hashString() {
    throw p(S);
  }
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
var N;
(function(a) {
  a[a.Error = 0] = "Error", a[a.Warning = 1] = "Warning", a[a.Info = 2] = "Info", a[a.Verbose = 3] = "Verbose", a[a.Trace = 4] = "Trace";
})(N || (N = {}));
class pe {
  constructor(e, t, n) {
    this.level = N.Info;
    const o = () => {
    }, r = e || pe.createDefaultLoggerOptions();
    this.localCallback = r.loggerCallback || o, this.piiLoggingEnabled = r.piiLoggingEnabled || !1, this.level = typeof r.logLevel == "number" ? r.logLevel : N.Info, this.correlationId = r.correlationId || u.EMPTY_STRING, this.packageName = t || u.EMPTY_STRING, this.packageVersion = n || u.EMPTY_STRING;
  }
  static createDefaultLoggerOptions() {
    return {
      loggerCallback: () => {
      },
      piiLoggingEnabled: !1,
      logLevel: N.Info
    };
  }
  /**
   * Create new Logger with existing configurations.
   */
  clone(e, t, n) {
    return new pe({
      loggerCallback: this.localCallback,
      piiLoggingEnabled: this.piiLoggingEnabled,
      logLevel: this.level,
      correlationId: n || this.correlationId
    }, e, t);
  }
  /**
   * Log message with required options.
   */
  logMessage(e, t) {
    if (t.logLevel > this.level || !this.piiLoggingEnabled && t.containsPii)
      return;
    const r = `${`[${(/* @__PURE__ */ new Date()).toUTCString()}] : [${t.correlationId || this.correlationId || ""}]`} : ${this.packageName}@${this.packageVersion} : ${N[t.logLevel]} - ${e}`;
    this.executeCallback(t.logLevel, r, t.containsPii || !1);
  }
  /**
   * Execute callback with message.
   */
  executeCallback(e, t, n) {
    this.localCallback && this.localCallback(e, t, n);
  }
  /**
   * Logs error messages.
   */
  error(e, t) {
    this.logMessage(e, {
      logLevel: N.Error,
      containsPii: !1,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs error messages with PII.
   */
  errorPii(e, t) {
    this.logMessage(e, {
      logLevel: N.Error,
      containsPii: !0,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs warning messages.
   */
  warning(e, t) {
    this.logMessage(e, {
      logLevel: N.Warning,
      containsPii: !1,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs warning messages with PII.
   */
  warningPii(e, t) {
    this.logMessage(e, {
      logLevel: N.Warning,
      containsPii: !0,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs info messages.
   */
  info(e, t) {
    this.logMessage(e, {
      logLevel: N.Info,
      containsPii: !1,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs info messages with PII.
   */
  infoPii(e, t) {
    this.logMessage(e, {
      logLevel: N.Info,
      containsPii: !0,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs verbose messages.
   */
  verbose(e, t) {
    this.logMessage(e, {
      logLevel: N.Verbose,
      containsPii: !1,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs verbose messages with PII.
   */
  verbosePii(e, t) {
    this.logMessage(e, {
      logLevel: N.Verbose,
      containsPii: !0,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs trace messages.
   */
  trace(e, t) {
    this.logMessage(e, {
      logLevel: N.Trace,
      containsPii: !1,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Logs trace messages with PII.
   */
  tracePii(e, t) {
    this.logMessage(e, {
      logLevel: N.Trace,
      containsPii: !0,
      correlationId: t || u.EMPTY_STRING
    });
  }
  /**
   * Returns whether PII Logging is enabled or not.
   */
  isPiiLoggingEnabled() {
    return this.piiLoggingEnabled || !1;
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Uo = "@azure/msal-common", en = "14.16.1";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const tn = {
  // AzureCloudInstance is not specified.
  None: "none"
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Ae(a, e) {
  const t = Si(a);
  try {
    const n = e(t);
    return JSON.parse(n);
  } catch {
    throw p(Wt);
  }
}
function Si(a) {
  if (!a)
    throw p(To);
  const t = /^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(a);
  if (!t || t.length < 4)
    throw p(Wt);
  return t[2];
}
function Lo(a, e) {
  if (e === 0 || Date.now() - 3e5 > a + e)
    throw p(wo);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function re() {
  return Math.round((/* @__PURE__ */ new Date()).getTime() / 1e3);
}
function Ut(a, e) {
  const t = Number(a) || 0;
  return re() + e > t;
}
function vi(a) {
  return Number(a) > re();
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function ke(a) {
  return [
    wi(a),
    _i(a),
    ki(a),
    Ri(a),
    bi(a)
  ].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
}
function pt(a, e, t, n, o) {
  return {
    credentialType: A.ID_TOKEN,
    homeAccountId: a,
    environment: e,
    clientId: n,
    secret: t,
    realm: o
  };
}
function mt(a, e, t, n, o, r, i, s, c, h, d, g, f, T, E) {
  var M, G;
  const v = {
    homeAccountId: a,
    credentialType: A.ACCESS_TOKEN,
    secret: t,
    cachedAt: re().toString(),
    expiresOn: i.toString(),
    extendedExpiresOn: s.toString(),
    environment: e,
    clientId: n,
    realm: o,
    target: r,
    tokenType: d || k.BEARER
  };
  if (g && (v.userAssertionHash = g), h && (v.refreshOn = h.toString()), T && (v.requestedClaims = T, v.requestedClaimsHash = E), ((M = v.tokenType) == null ? void 0 : M.toLowerCase()) !== k.BEARER.toLowerCase())
    switch (v.credentialType = A.ACCESS_TOKEN_WITH_AUTH_SCHEME, v.tokenType) {
      case k.POP:
        const L = Ae(t, c);
        if (!((G = L == null ? void 0 : L.cnf) != null && G.kid))
          throw p(No);
        v.keyId = L.cnf.kid;
        break;
      case k.SSH:
        v.keyId = f;
    }
  return v;
}
function Ho(a, e, t, n, o, r, i) {
  const s = {
    credentialType: A.REFRESH_TOKEN,
    homeAccountId: a,
    environment: e,
    clientId: n,
    secret: t
  };
  return r && (s.userAssertionHash = r), o && (s.familyId = o), i && (s.expiresOn = i.toString()), s;
}
function nn(a) {
  return a.hasOwnProperty("homeAccountId") && a.hasOwnProperty("environment") && a.hasOwnProperty("credentialType") && a.hasOwnProperty("clientId") && a.hasOwnProperty("secret");
}
function qn(a) {
  return a ? nn(a) && a.hasOwnProperty("realm") && a.hasOwnProperty("target") && (a.credentialType === A.ACCESS_TOKEN || a.credentialType === A.ACCESS_TOKEN_WITH_AUTH_SCHEME) : !1;
}
function $n(a) {
  return a ? nn(a) && a.hasOwnProperty("realm") && a.credentialType === A.ID_TOKEN : !1;
}
function zn(a) {
  return a ? nn(a) && a.credentialType === A.REFRESH_TOKEN : !1;
}
function wi(a) {
  return [
    a.homeAccountId,
    a.environment
  ].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
}
function _i(a) {
  const e = a.credentialType === A.REFRESH_TOKEN && a.familyId || a.clientId;
  return [
    a.credentialType,
    e,
    a.realm || ""
  ].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
}
function ki(a) {
  return (a.target || "").toLowerCase();
}
function Ri(a) {
  return (a.requestedClaimsHash || "").toLowerCase();
}
function bi(a) {
  return a.tokenType && a.tokenType.toLowerCase() !== k.BEARER.toLowerCase() ? a.tokenType.toLowerCase() : "";
}
function Oi(a, e) {
  const t = a.indexOf(K.CACHE_KEY) === 0;
  let n = !0;
  return e && (n = e.hasOwnProperty("failedRequests") && e.hasOwnProperty("errors") && e.hasOwnProperty("cacheHits")), t && n;
}
function Ni(a, e) {
  let t = !1;
  a && (t = a.indexOf(Fe.THROTTLING_PREFIX) === 0);
  let n = !0;
  return e && (n = e.hasOwnProperty("throttleTime")), t && n;
}
function Pi({ environment: a, clientId: e }) {
  return [
    Vt,
    a,
    e
  ].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
}
function Mi(a, e) {
  return e ? a.indexOf(Vt) === 0 && e.hasOwnProperty("clientId") && e.hasOwnProperty("environment") : !1;
}
function Ui(a, e) {
  return e ? a.indexOf(ot.CACHE_KEY) === 0 && e.hasOwnProperty("aliases") && e.hasOwnProperty("preferred_cache") && e.hasOwnProperty("preferred_network") && e.hasOwnProperty("canonical_authority") && e.hasOwnProperty("authorization_endpoint") && e.hasOwnProperty("token_endpoint") && e.hasOwnProperty("issuer") && e.hasOwnProperty("aliasesFromNetwork") && e.hasOwnProperty("endpointsFromNetwork") && e.hasOwnProperty("expiresAt") && e.hasOwnProperty("jwks_uri") : !1;
}
function Vn() {
  return re() + ot.REFRESH_TIME_SECONDS;
}
function Ze(a, e, t) {
  a.authorization_endpoint = e.authorization_endpoint, a.token_endpoint = e.token_endpoint, a.end_session_endpoint = e.end_session_endpoint, a.issuer = e.issuer, a.endpointsFromNetwork = t, a.jwks_uri = e.jwks_uri;
}
function _t(a, e, t) {
  a.aliases = e.aliases, a.preferred_cache = e.preferred_cache, a.preferred_network = e.preferred_network, a.aliasesFromNetwork = t;
}
function Qn(a) {
  return a.expiresAt <= re();
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Do = "redirect_uri_empty", Li = "claims_request_parsing_error", Ko = "authority_uri_insecure", xe = "url_parse_error", xo = "empty_url_error", Bo = "empty_input_scopes_error", Fo = "invalid_prompt_value", on = "invalid_claims", Go = "token_request_empty", qo = "logout_request_empty", $o = "invalid_code_challenge_method", rn = "pkce_params_missing", an = "invalid_cloud_discovery_metadata", zo = "invalid_authority_metadata", Vo = "untrusted_authority", ft = "missing_ssh_jwk", Qo = "missing_ssh_kid", Hi = "missing_nonce_authentication_header", Di = "invalid_authentication_header", Yo = "cannot_set_OIDCOptions", Wo = "cannot_allow_native_broker", jo = "authority_mismatch";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Ki = {
  [Do]: "A redirect URI is required for all calls, and none has been set.",
  [Li]: "Could not parse the given claims request object.",
  [Ko]: "Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options",
  [xe]: "URL could not be parsed into appropriate segments.",
  [xo]: "URL was empty or null.",
  [Bo]: "Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.",
  [Fo]: "Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest",
  [on]: "Given claims parameter must be a stringified JSON object.",
  [Go]: "Token request was empty and not found in cache.",
  [qo]: "The logout request was null or undefined.",
  [$o]: 'code_challenge_method passed is invalid. Valid values are "plain" and "S256".',
  [rn]: "Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request",
  [an]: "Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields",
  [zo]: "Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.",
  [Vo]: "The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.",
  [ft]: "Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.",
  [Qo]: "Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.",
  [Hi]: "Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.",
  [Di]: "Invalid authentication header provided",
  [Yo]: "Cannot set OIDCOptions parameter. Please change the protocol mode to OIDC or use a non-Microsoft authority.",
  [Wo]: "Cannot set allowNativeBroker parameter to true when not in AAD protocol mode.",
  [jo]: "Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."
};
class sn extends O {
  constructor(e) {
    super(e, Ki[e]), this.name = "ClientConfigurationError", Object.setPrototypeOf(this, sn.prototype);
  }
}
function R(a) {
  return new sn(a);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class j {
  /**
   * Check if stringified object is empty
   * @param strObj
   */
  static isEmptyObj(e) {
    if (e)
      try {
        const t = JSON.parse(e);
        return Object.keys(t).length === 0;
      } catch {
      }
    return !0;
  }
  static startsWith(e, t) {
    return e.indexOf(t) === 0;
  }
  static endsWith(e, t) {
    return e.length >= t.length && e.lastIndexOf(t) === e.length - t.length;
  }
  /**
   * Parses string into an object.
   *
   * @param query
   */
  static queryStringToObject(e) {
    const t = {}, n = e.split("&"), o = (r) => decodeURIComponent(r.replace(/\+/g, " "));
    return n.forEach((r) => {
      if (r.trim()) {
        const [i, s] = r.split(/=(.+)/g, 2);
        i && s && (t[o(i)] = o(s));
      }
    }), t;
  }
  /**
   * Trims entries in an array.
   *
   * @param arr
   */
  static trimArrayEntries(e) {
    return e.map((t) => t.trim());
  }
  /**
   * Removes empty strings from array
   * @param arr
   */
  static removeEmptyStringsFromArray(e) {
    return e.filter((t) => !!t);
  }
  /**
   * Attempts to parse a string into JSON
   * @param str
   */
  static jsonParseHelper(e) {
    try {
      return JSON.parse(e);
    } catch {
      return null;
    }
  }
  /**
   * Tests if a given string matches a given pattern, with support for wildcards and queries.
   * @param pattern Wildcard pattern to string match. Supports "*" for wildcards and "?" for queries
   * @param input String to match against
   */
  static matchPattern(e, t) {
    return new RegExp(e.replace(/\\/g, "\\\\").replace(/\*/g, "[^ ]*").replace(/\?/g, "\\?")).test(t);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class P {
  constructor(e) {
    const t = e ? j.trimArrayEntries([...e]) : [], n = t ? j.removeEmptyStringsFromArray(t) : [];
    this.validateInputScopes(n), this.scopes = /* @__PURE__ */ new Set(), n.forEach((o) => this.scopes.add(o));
  }
  /**
   * Factory method to create ScopeSet from space-delimited string
   * @param inputScopeString
   * @param appClientId
   * @param scopesRequired
   */
  static fromString(e) {
    const n = (e || u.EMPTY_STRING).split(" ");
    return new P(n);
  }
  /**
   * Creates the set of scopes to search for in cache lookups
   * @param inputScopeString
   * @returns
   */
  static createSearchScopes(e) {
    const t = new P(e);
    return t.containsOnlyOIDCScopes() ? t.removeScope(u.OFFLINE_ACCESS_SCOPE) : t.removeOIDCScopes(), t;
  }
  /**
   * Used to validate the scopes input parameter requested  by the developer.
   * @param {Array<string>} inputScopes - Developer requested permissions. Not all scopes are guaranteed to be included in the access token returned.
   * @param {boolean} scopesRequired - Boolean indicating whether the scopes array is required or not
   */
  validateInputScopes(e) {
    if (!e || e.length < 1)
      throw R(Bo);
  }
  /**
   * Check if a given scope is present in this set of scopes.
   * @param scope
   */
  containsScope(e) {
    const t = this.printScopesLowerCase().split(" "), n = new P(t);
    return e ? n.scopes.has(e.toLowerCase()) : !1;
  }
  /**
   * Check if a set of scopes is present in this set of scopes.
   * @param scopeSet
   */
  containsScopeSet(e) {
    return !e || e.scopes.size <= 0 ? !1 : this.scopes.size >= e.scopes.size && e.asArray().every((t) => this.containsScope(t));
  }
  /**
   * Check if set of scopes contains only the defaults
   */
  containsOnlyOIDCScopes() {
    let e = 0;
    return Dn.forEach((t) => {
      this.containsScope(t) && (e += 1);
    }), this.scopes.size === e;
  }
  /**
   * Appends single scope if passed
   * @param newScope
   */
  appendScope(e) {
    e && this.scopes.add(e.trim());
  }
  /**
   * Appends multiple scopes if passed
   * @param newScopes
   */
  appendScopes(e) {
    try {
      e.forEach((t) => this.appendScope(t));
    } catch {
      throw p(bo);
    }
  }
  /**
   * Removes element from set of scopes.
   * @param scope
   */
  removeScope(e) {
    if (!e)
      throw p(Ro);
    this.scopes.delete(e.trim());
  }
  /**
   * Removes default scopes from set of scopes
   * Primarily used to prevent cache misses if the default scopes are not returned from the server
   */
  removeOIDCScopes() {
    Dn.forEach((e) => {
      this.scopes.delete(e);
    });
  }
  /**
   * Combines an array of scopes with the current set of scopes.
   * @param otherScopes
   */
  unionScopeSets(e) {
    if (!e)
      throw p(Ot);
    const t = /* @__PURE__ */ new Set();
    return e.scopes.forEach((n) => t.add(n.toLowerCase())), this.scopes.forEach((n) => t.add(n.toLowerCase())), t;
  }
  /**
   * Check if scopes intersect between this set and another.
   * @param otherScopes
   */
  intersectingScopeSets(e) {
    if (!e)
      throw p(Ot);
    e.containsOnlyOIDCScopes() || e.removeOIDCScopes();
    const t = this.unionScopeSets(e), n = e.getScopeCount(), o = this.getScopeCount();
    return t.size < o + n;
  }
  /**
   * Returns size of set of scopes.
   */
  getScopeCount() {
    return this.scopes.size;
  }
  /**
   * Returns the scopes as an array of string values
   */
  asArray() {
    const e = [];
    return this.scopes.forEach((t) => e.push(t)), e;
  }
  /**
   * Prints scopes into a space-delimited string
   */
  printScopes() {
    return this.scopes ? this.asArray().join(" ") : u.EMPTY_STRING;
  }
  /**
   * Prints scopes into a space-delimited lower-case string (used for caching)
   */
  printScopesLowerCase() {
    return this.printScopes().toLowerCase();
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function at(a, e) {
  if (!a)
    throw p(yo);
  try {
    const t = e(a);
    return JSON.parse(t);
  } catch {
    throw p(Yt);
  }
}
function Re(a) {
  if (!a)
    throw p(Yt);
  const e = a.split(B.CLIENT_INFO_SEPARATOR, 2);
  return {
    uid: e[0],
    utid: e.length < 2 ? u.EMPTY_STRING : e[1]
  };
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function st(a, e) {
  return !!a && !!e && a === e.split(".")[1];
}
function cn(a, e, t, n) {
  if (n) {
    const { oid: o, sub: r, tid: i, name: s, tfp: c, acr: h } = n, d = i || c || h || "";
    return {
      tenantId: d,
      localAccountId: o || r || "",
      name: s,
      isHomeTenant: st(d, a)
    };
  } else
    return {
      tenantId: t,
      localAccountId: e,
      isHomeTenant: st(t, a)
    };
}
function hn(a, e, t, n) {
  let o = a;
  if (e) {
    const { isHomeTenant: r, ...i } = e;
    o = { ...a, ...i };
  }
  if (t) {
    const { isHomeTenant: r, ...i } = cn(a.homeAccountId, a.localAccountId, a.tenantId, t);
    return o = {
      ...o,
      ...i,
      idTokenClaims: t,
      idToken: n
    }, o;
  }
  return o;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const W = {
  Default: 0,
  Adfs: 1,
  Dsts: 2,
  Ciam: 3
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Jo(a) {
  return a && (a.tid || a.tfp || a.acr) || null;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const le = {
  AAD: "AAD",
  OIDC: "OIDC"
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
class U {
  /**
   * Generate Account Id key component as per the schema: <home_account_id>-<environment>
   */
  generateAccountId() {
    return [this.homeAccountId, this.environment].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
  }
  /**
   * Generate Account Cache Key as per the schema: <home_account_id>-<environment>-<realm*>
   */
  generateAccountKey() {
    return U.generateAccountCacheKey({
      homeAccountId: this.homeAccountId,
      environment: this.environment,
      tenantId: this.realm,
      username: this.username,
      localAccountId: this.localAccountId
    });
  }
  /**
   * Returns the AccountInfo interface for this account.
   */
  getAccountInfo() {
    return {
      homeAccountId: this.homeAccountId,
      environment: this.environment,
      tenantId: this.realm,
      username: this.username,
      localAccountId: this.localAccountId,
      name: this.name,
      nativeAccountId: this.nativeAccountId,
      authorityType: this.authorityType,
      // Deserialize tenant profiles array into a Map
      tenantProfiles: new Map((this.tenantProfiles || []).map((e) => [e.tenantId, e]))
    };
  }
  /**
   * Returns true if the account entity is in single tenant format (outdated), false otherwise
   */
  isSingleTenant() {
    return !this.tenantProfiles;
  }
  /**
   * Generates account key from interface
   * @param accountInterface
   */
  static generateAccountCacheKey(e) {
    const t = e.homeAccountId.split(".")[1];
    return [
      e.homeAccountId,
      e.environment || "",
      t || e.tenantId || ""
    ].join(B.CACHE_KEY_SEPARATOR).toLowerCase();
  }
  /**
   * Build Account cache from IdToken, clientInfo and authority/policy. Associated with AAD.
   * @param accountDetails
   */
  static createAccount(e, t, n) {
    var h, d, g, f, T, E;
    const o = new U();
    t.authorityType === W.Adfs ? o.authorityType = Je.ADFS_ACCOUNT_TYPE : t.protocolMode === le.AAD ? o.authorityType = Je.MSSTS_ACCOUNT_TYPE : o.authorityType = Je.GENERIC_ACCOUNT_TYPE;
    let r;
    e.clientInfo && n && (r = at(e.clientInfo, n)), o.clientInfo = e.clientInfo, o.homeAccountId = e.homeAccountId, o.nativeAccountId = e.nativeAccountId;
    const i = e.environment || t && t.getPreferredCache();
    if (!i)
      throw p(Xt);
    o.environment = i, o.realm = (r == null ? void 0 : r.utid) || Jo(e.idTokenClaims) || "", o.localAccountId = (r == null ? void 0 : r.uid) || ((h = e.idTokenClaims) == null ? void 0 : h.oid) || ((d = e.idTokenClaims) == null ? void 0 : d.sub) || "";
    const s = ((g = e.idTokenClaims) == null ? void 0 : g.preferred_username) || ((f = e.idTokenClaims) == null ? void 0 : f.upn), c = (T = e.idTokenClaims) != null && T.emails ? e.idTokenClaims.emails[0] : null;
    if (o.username = s || c || "", o.name = ((E = e.idTokenClaims) == null ? void 0 : E.name) || "", o.cloudGraphHostName = e.cloudGraphHostName, o.msGraphHost = e.msGraphHost, e.tenantProfiles)
      o.tenantProfiles = e.tenantProfiles;
    else {
      const v = cn(e.homeAccountId, o.localAccountId, o.realm, e.idTokenClaims);
      o.tenantProfiles = [v];
    }
    return o;
  }
  /**
   * Creates an AccountEntity object from AccountInfo
   * @param accountInfo
   * @param cloudGraphHostName
   * @param msGraphHost
   * @returns
   */
  static createFromAccountInfo(e, t, n) {
    var r;
    const o = new U();
    return o.authorityType = e.authorityType || Je.GENERIC_ACCOUNT_TYPE, o.homeAccountId = e.homeAccountId, o.localAccountId = e.localAccountId, o.nativeAccountId = e.nativeAccountId, o.realm = e.tenantId, o.environment = e.environment, o.username = e.username, o.name = e.name, o.cloudGraphHostName = t, o.msGraphHost = n, o.tenantProfiles = Array.from(((r = e.tenantProfiles) == null ? void 0 : r.values()) || []), o;
  }
  /**
   * Generate HomeAccountId from server response
   * @param serverClientInfo
   * @param authType
   */
  static generateHomeAccountId(e, t, n, o, r) {
    if (!(t === W.Adfs || t === W.Dsts)) {
      if (e)
        try {
          const i = at(e, o.base64Decode);
          if (i.uid && i.utid)
            return `${i.uid}.${i.utid}`;
        } catch {
        }
      n.warning("No client info in response");
    }
    return (r == null ? void 0 : r.sub) || "";
  }
  /**
   * Validates an entity: checks for all expected params
   * @param entity
   */
  static isAccountEntity(e) {
    return e ? e.hasOwnProperty("homeAccountId") && e.hasOwnProperty("environment") && e.hasOwnProperty("realm") && e.hasOwnProperty("localAccountId") && e.hasOwnProperty("username") && e.hasOwnProperty("authorityType") : !1;
  }
  /**
   * Helper function to determine whether 2 accountInfo objects represent the same account
   * @param accountA
   * @param accountB
   * @param compareClaims - If set to true idTokenClaims will also be compared to determine account equality
   */
  static accountInfoIsEqual(e, t, n) {
    if (!e || !t)
      return !1;
    let o = !0;
    if (n) {
      const r = e.idTokenClaims || {}, i = t.idTokenClaims || {};
      o = r.iat === i.iat && r.nonce === i.nonce;
    }
    return e.homeAccountId === t.homeAccountId && e.localAccountId === t.localAccountId && e.username === t.username && e.tenantId === t.tenantId && e.environment === t.environment && e.nativeAccountId === t.nativeAccountId && o;
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Xo(a) {
  return a.startsWith("#/") ? a.substring(2) : a.startsWith("#") || a.startsWith("?") ? a.substring(1) : a;
}
function ct(a) {
  if (!a || a.indexOf("=") < 0)
    return null;
  try {
    const e = Xo(a), t = Object.fromEntries(new URLSearchParams(e));
    if (t.code || t.error || t.error_description || t.state)
      return t;
  } catch {
    throw p(Eo);
  }
  return null;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class w {
  get urlString() {
    return this._urlString;
  }
  constructor(e) {
    if (this._urlString = e, !this._urlString)
      throw R(xo);
    e.includes("#") || (this._urlString = w.canonicalizeUri(e));
  }
  /**
   * Ensure urls are lower case and end with a / character.
   * @param url
   */
  static canonicalizeUri(e) {
    if (e) {
      let t = e.toLowerCase();
      return j.endsWith(t, "?") ? t = t.slice(0, -1) : j.endsWith(t, "?/") && (t = t.slice(0, -2)), j.endsWith(t, "/") || (t += "/"), t;
    }
    return e;
  }
  /**
   * Throws if urlString passed is not a valid authority URI string.
   */
  validateAsUri() {
    let e;
    try {
      e = this.getUrlComponents();
    } catch {
      throw R(xe);
    }
    if (!e.HostNameAndPort || !e.PathSegments)
      throw R(xe);
    if (!e.Protocol || e.Protocol.toLowerCase() !== "https:")
      throw R(Ko);
  }
  /**
   * Given a url and a query string return the url with provided query string appended
   * @param url
   * @param queryString
   */
  static appendQueryString(e, t) {
    return t ? e.indexOf("?") < 0 ? `${e}?${t}` : `${e}&${t}` : e;
  }
  /**
   * Returns a url with the hash removed
   * @param url
   */
  static removeHashFromUrl(e) {
    return w.canonicalizeUri(e.split("#")[0]);
  }
  /**
   * Given a url like https://a:b/common/d?e=f#g, and a tenantId, returns https://a:b/tenantId/d
   * @param href The url
   * @param tenantId The tenant id to replace
   */
  replaceTenantPath(e) {
    const t = this.getUrlComponents(), n = t.PathSegments;
    return e && n.length !== 0 && (n[0] === ge.COMMON || n[0] === ge.ORGANIZATIONS) && (n[0] = e), w.constructAuthorityUriFromObject(t);
  }
  /**
   * Parses out the components from a url string.
   * @returns An object with the various components. Please cache this value insted of calling this multiple times on the same url.
   */
  getUrlComponents() {
    const e = RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"), t = this.urlString.match(e);
    if (!t)
      throw R(xe);
    const n = {
      Protocol: t[1],
      HostNameAndPort: t[4],
      AbsolutePath: t[5],
      QueryString: t[7]
    };
    let o = n.AbsolutePath.split("/");
    return o = o.filter((r) => r && r.length > 0), n.PathSegments = o, n.QueryString && n.QueryString.endsWith("/") && (n.QueryString = n.QueryString.substring(0, n.QueryString.length - 1)), n;
  }
  static getDomainFromUrl(e) {
    const t = RegExp("^([^:/?#]+://)?([^/?#]*)"), n = e.match(t);
    if (!n)
      throw R(xe);
    return n[2];
  }
  static getAbsoluteUrl(e, t) {
    if (e[0] === u.FORWARD_SLASH) {
      const o = new w(t).getUrlComponents();
      return o.Protocol + "//" + o.HostNameAndPort + e;
    }
    return e;
  }
  static constructAuthorityUriFromObject(e) {
    return new w(e.Protocol + "//" + e.HostNameAndPort + "/" + e.PathSegments.join("/"));
  }
  /**
   * Check if the hash of the URL string contains known properties
   * @deprecated This API will be removed in a future version
   */
  static hashContainsKnownProperties(e) {
    return !!ct(e);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Zo = {
  endpointMetadata: {
    "login.microsoftonline.com": {
      token_endpoint: "https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/token",
      jwks_uri: "https://login.microsoftonline.com/{tenantid}/discovery/v2.0/keys",
      issuer: "https://login.microsoftonline.com/{tenantid}/v2.0",
      authorization_endpoint: "https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/authorize",
      end_session_endpoint: "https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/logout"
    },
    "login.chinacloudapi.cn": {
      token_endpoint: "https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/token",
      jwks_uri: "https://login.chinacloudapi.cn/{tenantid}/discovery/v2.0/keys",
      issuer: "https://login.partner.microsoftonline.cn/{tenantid}/v2.0",
      authorization_endpoint: "https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/authorize",
      end_session_endpoint: "https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/logout"
    },
    "login.microsoftonline.us": {
      token_endpoint: "https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/token",
      jwks_uri: "https://login.microsoftonline.us/{tenantid}/discovery/v2.0/keys",
      issuer: "https://login.microsoftonline.us/{tenantid}/v2.0",
      authorization_endpoint: "https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/authorize",
      end_session_endpoint: "https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/logout"
    }
  },
  instanceDiscoveryMetadata: {
    metadata: [
      {
        preferred_network: "login.microsoftonline.com",
        preferred_cache: "login.windows.net",
        aliases: [
          "login.microsoftonline.com",
          "login.windows.net",
          "login.microsoft.com",
          "sts.windows.net"
        ]
      },
      {
        preferred_network: "login.partner.microsoftonline.cn",
        preferred_cache: "login.partner.microsoftonline.cn",
        aliases: [
          "login.partner.microsoftonline.cn",
          "login.chinacloudapi.cn"
        ]
      },
      {
        preferred_network: "login.microsoftonline.de",
        preferred_cache: "login.microsoftonline.de",
        aliases: ["login.microsoftonline.de"]
      },
      {
        preferred_network: "login.microsoftonline.us",
        preferred_cache: "login.microsoftonline.us",
        aliases: [
          "login.microsoftonline.us",
          "login.usgovcloudapi.net"
        ]
      },
      {
        preferred_network: "login-us.microsoftonline.com",
        preferred_cache: "login-us.microsoftonline.com",
        aliases: ["login-us.microsoftonline.com"]
      }
    ]
  }
}, Yn = Zo.endpointMetadata, ln = Zo.instanceDiscoveryMetadata, er = /* @__PURE__ */ new Set();
ln.metadata.forEach((a) => {
  a.aliases.forEach((e) => {
    er.add(e);
  });
});
function xi(a, e) {
  var o;
  let t;
  const n = a.canonicalAuthority;
  if (n) {
    const r = new w(n).getUrlComponents().HostNameAndPort;
    t = Wn(r, (o = a.cloudDiscoveryMetadata) == null ? void 0 : o.metadata, $.CONFIG, e) || Wn(r, ln.metadata, $.HARDCODED_VALUES, e) || a.knownAuthorities;
  }
  return t || [];
}
function Wn(a, e, t, n) {
  if (n == null || n.trace(`getAliasesFromMetadata called with source: ${t}`), a && e) {
    const o = ht(e, a);
    if (o)
      return n == null || n.trace(`getAliasesFromMetadata: found cloud discovery metadata in ${t}, returning aliases`), o.aliases;
    n == null || n.trace(`getAliasesFromMetadata: did not find cloud discovery metadata in ${t}`);
  }
  return null;
}
function Bi(a) {
  return ht(ln.metadata, a);
}
function ht(a, e) {
  for (let t = 0; t < a.length; t++) {
    const n = a[t];
    if (n.aliases.includes(e))
      return n;
  }
  return null;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const dn = "cache_quota_exceeded", un = "cache_error_unknown";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const kt = {
  [dn]: "Exceeded cache storage capacity.",
  [un]: "Unexpected error occurred when using cache storage."
};
class Oe extends Error {
  constructor(e, t) {
    const n = t || (kt[e] ? kt[e] : kt[un]);
    super(`${e}: ${n}`), Object.setPrototypeOf(this, Oe.prototype), this.name = "CacheError", this.errorCode = e, this.errorMessage = n;
  }
}
function tr(a) {
  return a instanceof Error ? a.name === "QuotaExceededError" || a.name === "NS_ERROR_DOM_QUOTA_REACHED" || a.message.includes("exceeded the quota") ? new Oe(dn) : new Oe(a.name, a.message) : new Oe(un);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class Me {
  constructor(e, t, n, o) {
    this.clientId = e, this.cryptoImpl = t, this.commonLogger = n.clone(Uo, en), this.staticAuthorityOptions = o;
  }
  /**
   * Returns all the accounts in the cache that match the optional filter. If no filter is provided, all accounts are returned.
   * @param accountFilter - (Optional) filter to narrow down the accounts returned
   * @returns Array of AccountInfo objects in cache
   */
  getAllAccounts(e, t) {
    return this.buildTenantProfiles(this.getAccountsFilteredBy(t || {}, e), e, t);
  }
  /**
   * Gets first tenanted AccountInfo object found based on provided filters
   */
  getAccountInfoFilteredBy(e, t) {
    const n = this.getAllAccounts(t, e);
    return n.length > 1 ? n.sort((r) => r.idTokenClaims ? -1 : 1)[0] : n.length === 1 ? n[0] : null;
  }
  /**
   * Returns a single matching
   * @param accountFilter
   * @returns
   */
  getBaseAccountInfo(e, t) {
    const n = this.getAccountsFilteredBy(e, t);
    return n.length > 0 ? n[0].getAccountInfo() : null;
  }
  /**
   * Matches filtered account entities with cached ID tokens that match the tenant profile-specific account filters
   * and builds the account info objects from the matching ID token's claims
   * @param cachedAccounts
   * @param accountFilter
   * @returns Array of AccountInfo objects that match account and tenant profile filters
   */
  buildTenantProfiles(e, t, n) {
    return e.flatMap((o) => this.getTenantProfilesFromAccountEntity(o, t, n == null ? void 0 : n.tenantId, n));
  }
  getTenantedAccountInfoByFilter(e, t, n, o, r) {
    let i = null, s;
    if (r && !this.tenantProfileMatchesFilter(n, r))
      return null;
    const c = this.getIdToken(e, o, t, n.tenantId);
    return c && (s = Ae(c.secret, this.cryptoImpl.base64Decode), !this.idTokenClaimsMatchTenantProfileFilter(s, r)) ? null : (i = hn(e, n, s, c == null ? void 0 : c.secret), i);
  }
  getTenantProfilesFromAccountEntity(e, t, n, o) {
    const r = e.getAccountInfo();
    let i = r.tenantProfiles || /* @__PURE__ */ new Map();
    const s = this.getTokenKeys();
    if (n) {
      const h = i.get(n);
      if (h)
        i = /* @__PURE__ */ new Map([
          [n, h]
        ]);
      else
        return [];
    }
    const c = [];
    return i.forEach((h) => {
      const d = this.getTenantedAccountInfoByFilter(r, s, h, t, o);
      d && c.push(d);
    }), c;
  }
  tenantProfileMatchesFilter(e, t) {
    return !(t.localAccountId && !this.matchLocalAccountIdFromTenantProfile(e, t.localAccountId) || t.name && e.name !== t.name || t.isHomeTenant !== void 0 && e.isHomeTenant !== t.isHomeTenant);
  }
  idTokenClaimsMatchTenantProfileFilter(e, t) {
    return !(t && (t.localAccountId && !this.matchLocalAccountIdFromTokenClaims(e, t.localAccountId) || t.loginHint && !this.matchLoginHintFromTokenClaims(e, t.loginHint) || t.username && !this.matchUsername(e.preferred_username, t.username) || t.name && !this.matchName(e, t.name) || t.sid && !this.matchSid(e, t.sid)));
  }
  /**
   * saves a cache record
   * @param cacheRecord {CacheRecord}
   * @param storeInCache {?StoreInCache}
   * @param correlationId {?string} correlation id
   */
  async saveCacheRecord(e, t, n) {
    var o;
    if (!e)
      throw p(Oo);
    try {
      e.account && this.setAccount(e.account, t), e.idToken && (n == null ? void 0 : n.idToken) !== !1 && this.setIdTokenCredential(e.idToken, t), e.accessToken && (n == null ? void 0 : n.accessToken) !== !1 && await this.saveAccessToken(e.accessToken, t), e.refreshToken && (n == null ? void 0 : n.refreshToken) !== !1 && this.setRefreshTokenCredential(e.refreshToken, t), e.appMetadata && this.setAppMetadata(e.appMetadata, t);
    } catch (r) {
      throw (o = this.commonLogger) == null || o.error("CacheManager.saveCacheRecord: failed"), r instanceof O ? r : tr(r);
    }
  }
  /**
   * saves access token credential
   * @param credential
   */
  async saveAccessToken(e, t) {
    const n = {
      clientId: e.clientId,
      credentialType: e.credentialType,
      environment: e.environment,
      homeAccountId: e.homeAccountId,
      realm: e.realm,
      tokenType: e.tokenType,
      requestedClaimsHash: e.requestedClaimsHash
    }, o = this.getTokenKeys(), r = P.fromString(e.target);
    o.accessToken.forEach((i) => {
      if (!this.accessTokenKeyMatchesFilter(i, n, !1))
        return;
      const s = this.getAccessTokenCredential(i, t);
      s && this.credentialMatchesFilter(s, n) && P.fromString(s.target).intersectingScopeSets(r) && this.removeAccessToken(i, t);
    }), this.setAccessTokenCredential(e, t);
  }
  /**
   * Retrieve account entities matching all provided tenant-agnostic filters; if no filter is set, get all account entities in the cache
   * Not checking for casing as keys are all generated in lower case, remember to convert to lower case if object properties are compared
   * @param accountFilter - An object containing Account properties to filter by
   */
  getAccountsFilteredBy(e, t) {
    const n = this.getAccountKeys(), o = [];
    return n.forEach((r) => {
      var h;
      if (!this.isAccountKey(r, e.homeAccountId))
        return;
      const i = this.getAccount(r, t, this.commonLogger);
      if (!i || e.homeAccountId && !this.matchHomeAccountId(i, e.homeAccountId) || e.username && !this.matchUsername(i.username, e.username) || e.environment && !this.matchEnvironment(i, e.environment) || e.realm && !this.matchRealm(i, e.realm) || e.nativeAccountId && !this.matchNativeAccountId(i, e.nativeAccountId) || e.authorityType && !this.matchAuthorityType(i, e.authorityType))
        return;
      const s = {
        localAccountId: e == null ? void 0 : e.localAccountId,
        name: e == null ? void 0 : e.name
      }, c = (h = i.tenantProfiles) == null ? void 0 : h.filter((d) => this.tenantProfileMatchesFilter(d, s));
      c && c.length === 0 || o.push(i);
    }), o;
  }
  /**
   * Returns true if the given key matches our account key schema. Also matches homeAccountId and/or tenantId if provided
   * @param key
   * @param homeAccountId
   * @param tenantId
   * @returns
   */
  isAccountKey(e, t, n) {
    return !(e.split(B.CACHE_KEY_SEPARATOR).length < 3 || t && !e.toLowerCase().includes(t.toLowerCase()) || n && !e.toLowerCase().includes(n.toLowerCase()));
  }
  /**
   * Returns true if the given key matches our credential key schema.
   * @param key
   */
  isCredentialKey(e) {
    if (e.split(B.CACHE_KEY_SEPARATOR).length < 6)
      return !1;
    const t = e.toLowerCase();
    if (t.indexOf(A.ID_TOKEN.toLowerCase()) === -1 && t.indexOf(A.ACCESS_TOKEN.toLowerCase()) === -1 && t.indexOf(A.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()) === -1 && t.indexOf(A.REFRESH_TOKEN.toLowerCase()) === -1)
      return !1;
    if (t.indexOf(A.REFRESH_TOKEN.toLowerCase()) > -1) {
      const n = `${A.REFRESH_TOKEN}${B.CACHE_KEY_SEPARATOR}${this.clientId}${B.CACHE_KEY_SEPARATOR}`, o = `${A.REFRESH_TOKEN}${B.CACHE_KEY_SEPARATOR}${Be}${B.CACHE_KEY_SEPARATOR}`;
      if (t.indexOf(n.toLowerCase()) === -1 && t.indexOf(o.toLowerCase()) === -1)
        return !1;
    } else if (t.indexOf(this.clientId.toLowerCase()) === -1)
      return !1;
    return !0;
  }
  /**
   * Returns whether or not the given credential entity matches the filter
   * @param entity
   * @param filter
   * @returns
   */
  credentialMatchesFilter(e, t) {
    return !(t.clientId && !this.matchClientId(e, t.clientId) || t.userAssertionHash && !this.matchUserAssertionHash(e, t.userAssertionHash) || typeof t.homeAccountId == "string" && !this.matchHomeAccountId(e, t.homeAccountId) || t.environment && !this.matchEnvironment(e, t.environment) || t.realm && !this.matchRealm(e, t.realm) || t.credentialType && !this.matchCredentialType(e, t.credentialType) || t.familyId && !this.matchFamilyId(e, t.familyId) || t.target && !this.matchTarget(e, t.target) || (t.requestedClaimsHash || e.requestedClaimsHash) && e.requestedClaimsHash !== t.requestedClaimsHash || e.credentialType === A.ACCESS_TOKEN_WITH_AUTH_SCHEME && (t.tokenType && !this.matchTokenType(e, t.tokenType) || t.tokenType === k.SSH && t.keyId && !this.matchKeyId(e, t.keyId)));
  }
  /**
   * retrieve appMetadata matching all provided filters; if no filter is set, get all appMetadata
   * @param filter
   */
  getAppMetadataFilteredBy(e) {
    const t = this.getKeys(), n = {};
    return t.forEach((o) => {
      if (!this.isAppMetadata(o))
        return;
      const r = this.getAppMetadata(o);
      r && (e.environment && !this.matchEnvironment(r, e.environment) || e.clientId && !this.matchClientId(r, e.clientId) || (n[o] = r));
    }), n;
  }
  /**
   * retrieve authorityMetadata that contains a matching alias
   * @param filter
   */
  getAuthorityMetadataByAlias(e) {
    const t = this.getAuthorityMetadataKeys();
    let n = null;
    return t.forEach((o) => {
      if (!this.isAuthorityMetadata(o) || o.indexOf(this.clientId) === -1)
        return;
      const r = this.getAuthorityMetadata(o);
      r && r.aliases.indexOf(e) !== -1 && (n = r);
    }), n;
  }
  /**
   * Removes all accounts and related tokens from cache.
   */
  async removeAllAccounts(e) {
    const t = this.getAccountKeys(), n = [];
    t.forEach((o) => {
      n.push(this.removeAccount(o, e));
    }), await Promise.all(n);
  }
  /**
   * Removes the account and related tokens for a given account key
   * @param account
   */
  async removeAccount(e, t) {
    const n = this.getAccount(e, t, this.commonLogger);
    n && (await this.removeAccountContext(n, t), this.removeItem(e, t));
  }
  /**
   * Removes credentials associated with the provided account
   * @param account
   */
  async removeAccountContext(e, t) {
    const n = this.getTokenKeys(), o = e.generateAccountId();
    n.idToken.forEach((r) => {
      r.indexOf(o) === 0 && this.removeIdToken(r, t);
    }), n.accessToken.forEach((r) => {
      r.indexOf(o) === 0 && this.removeAccessToken(r, t);
    }), n.refreshToken.forEach((r) => {
      r.indexOf(o) === 0 && this.removeRefreshToken(r, t);
    }), this.getKeys().forEach((r) => {
      r.includes(o) && this.removeItem(r, t);
    });
  }
  /**
   * Migrates a single-tenant account and all it's associated alternate cross-tenant account objects in the
   * cache into a condensed multi-tenant account object with tenant profiles.
   * @param accountKey
   * @param accountEntity
   * @param logger
   * @returns
   */
  updateOutdatedCachedAccount(e, t, n, o) {
    var r;
    if (t && t.isSingleTenant()) {
      (r = this.commonLogger) == null || r.verbose("updateOutdatedCachedAccount: Found a single-tenant (outdated) account entity in the cache, migrating to multi-tenant account entity");
      const i = this.getAccountKeys().filter((g) => g.startsWith(t.homeAccountId)), s = [];
      i.forEach((g) => {
        const f = this.getCachedAccountEntity(g, n);
        f && s.push(f);
      });
      const c = s.find((g) => st(g.realm, g.homeAccountId)) || s[0];
      c.tenantProfiles = s.map((g) => ({
        tenantId: g.realm,
        localAccountId: g.localAccountId,
        name: g.name,
        isHomeTenant: st(g.realm, g.homeAccountId)
      }));
      const h = Me.toObject(new U(), {
        ...c
      }), d = h.generateAccountKey();
      return i.forEach((g) => {
        g !== d && this.removeOutdatedAccount(e, n);
      }), this.setAccount(h, n), o == null || o.verbose("Updated an outdated account entity in the cache"), h;
    }
    return t;
  }
  /**
   * returns a boolean if the given credential is removed
   * @param credential
   */
  removeAccessToken(e, t) {
    const n = this.getAccessTokenCredential(e, t);
    if (this.removeItem(e, t), !n || n.credentialType.toLowerCase() !== A.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase() || n.tokenType !== k.POP)
      return;
    const o = n.keyId;
    o && this.cryptoImpl.removeTokenBindingKey(o).catch(() => {
      this.commonLogger.error("Binding key could not be removed");
    });
  }
  /**
   * Removes all app metadata objects from cache.
   */
  removeAppMetadata(e) {
    return this.getKeys().forEach((n) => {
      this.isAppMetadata(n) && this.removeItem(n, e);
    }), !0;
  }
  /**
   * Retrieve AccountEntity from cache
   * @param account
   */
  readAccountFromCache(e, t) {
    const n = U.generateAccountCacheKey(e);
    return this.getAccount(n, t, this.commonLogger);
  }
  /**
   * Retrieve IdTokenEntity from cache
   * @param account {AccountInfo}
   * @param tokenKeys {?TokenKeys}
   * @param targetRealm {?string}
   * @param performanceClient {?IPerformanceClient}
   * @param correlationId {?string}
   */
  getIdToken(e, t, n, o, r) {
    this.commonLogger.trace("CacheManager - getIdToken called");
    const i = {
      homeAccountId: e.homeAccountId,
      environment: e.environment,
      credentialType: A.ID_TOKEN,
      clientId: this.clientId,
      realm: o
    }, s = this.getIdTokensByFilter(i, t, n), c = s.size;
    if (c < 1)
      return this.commonLogger.info("CacheManager:getIdToken - No token found"), null;
    if (c > 1) {
      let h = s;
      if (!o) {
        const d = /* @__PURE__ */ new Map();
        s.forEach((f, T) => {
          f.realm === e.tenantId && d.set(T, f);
        });
        const g = d.size;
        if (g < 1)
          return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account but none match account entity tenant id, returning first result"), s.values().next().value;
        if (g === 1)
          return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account, defaulting to home tenant profile"), d.values().next().value;
        h = d;
      }
      return this.commonLogger.info("CacheManager:getIdToken - Multiple matching ID tokens found, clearing them"), h.forEach((d, g) => {
        this.removeIdToken(g, t);
      }), r && t && r.addFields({ multiMatchedID: s.size }, t), null;
    }
    return this.commonLogger.info("CacheManager:getIdToken - Returning ID token"), s.values().next().value;
  }
  /**
   * Gets all idTokens matching the given filter
   * @param filter
   * @returns
   */
  getIdTokensByFilter(e, t, n) {
    const o = n && n.idToken || this.getTokenKeys().idToken, r = /* @__PURE__ */ new Map();
    return o.forEach((i) => {
      if (!this.idTokenKeyMatchesFilter(i, {
        clientId: this.clientId,
        ...e
      }))
        return;
      const s = this.getIdTokenCredential(i, t);
      s && this.credentialMatchesFilter(s, e) && r.set(i, s);
    }), r;
  }
  /**
   * Validate the cache key against filter before retrieving and parsing cache value
   * @param key
   * @param filter
   * @returns
   */
  idTokenKeyMatchesFilter(e, t) {
    const n = e.toLowerCase();
    return !(t.clientId && n.indexOf(t.clientId.toLowerCase()) === -1 || t.homeAccountId && n.indexOf(t.homeAccountId.toLowerCase()) === -1);
  }
  /**
   * Removes idToken from the cache
   * @param key
   */
  removeIdToken(e, t) {
    this.removeItem(e, t);
  }
  /**
   * Removes refresh token from the cache
   * @param key
   */
  removeRefreshToken(e, t) {
    this.removeItem(e, t);
  }
  /**
   * Retrieve AccessTokenEntity from cache
   * @param account {AccountInfo}
   * @param request {BaseAuthRequest}
   * @param tokenKeys {?TokenKeys}
   * @param performanceClient {?IPerformanceClient}
   * @param correlationId {?string}
   */
  getAccessToken(e, t, n, o, r) {
    this.commonLogger.trace("CacheManager - getAccessToken called");
    const i = P.createSearchScopes(t.scopes), s = t.authenticationScheme || k.BEARER, c = s.toLowerCase() !== k.BEARER.toLowerCase() ? A.ACCESS_TOKEN_WITH_AUTH_SCHEME : A.ACCESS_TOKEN, h = {
      homeAccountId: e.homeAccountId,
      environment: e.environment,
      credentialType: c,
      clientId: this.clientId,
      realm: o || e.tenantId,
      target: i,
      tokenType: s,
      keyId: t.sshKid,
      requestedClaimsHash: t.requestedClaimsHash
    }, d = n && n.accessToken || this.getTokenKeys().accessToken, g = [];
    d.forEach((T) => {
      if (this.accessTokenKeyMatchesFilter(T, h, !0)) {
        const E = this.getAccessTokenCredential(T, t.correlationId);
        E && this.credentialMatchesFilter(E, h) && g.push(E);
      }
    });
    const f = g.length;
    return f < 1 ? (this.commonLogger.info("CacheManager:getAccessToken - No token found"), null) : f > 1 ? (this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"), g.forEach((T) => {
      this.removeAccessToken(ke(T), t.correlationId);
    }), r && t.correlationId && r.addFields({ multiMatchedAT: g.length }, t.correlationId), null) : (this.commonLogger.info("CacheManager:getAccessToken - Returning access token"), g[0]);
  }
  /**
   * Validate the cache key against filter before retrieving and parsing cache value
   * @param key
   * @param filter
   * @param keyMustContainAllScopes
   * @returns
   */
  accessTokenKeyMatchesFilter(e, t, n) {
    const o = e.toLowerCase();
    if (t.clientId && o.indexOf(t.clientId.toLowerCase()) === -1 || t.homeAccountId && o.indexOf(t.homeAccountId.toLowerCase()) === -1 || t.realm && o.indexOf(t.realm.toLowerCase()) === -1 || t.requestedClaimsHash && o.indexOf(t.requestedClaimsHash.toLowerCase()) === -1)
      return !1;
    if (t.target) {
      const r = t.target.asArray();
      for (let i = 0; i < r.length; i++) {
        if (n && !o.includes(r[i].toLowerCase()))
          return !1;
        if (!n && o.includes(r[i].toLowerCase()))
          return !0;
      }
    }
    return !0;
  }
  /**
   * Gets all access tokens matching the filter
   * @param filter
   * @returns
   */
  getAccessTokensByFilter(e, t) {
    const n = this.getTokenKeys(), o = [];
    return n.accessToken.forEach((r) => {
      if (!this.accessTokenKeyMatchesFilter(r, e, !0))
        return;
      const i = this.getAccessTokenCredential(r, t);
      i && this.credentialMatchesFilter(i, e) && o.push(i);
    }), o;
  }
  /**
   * Helper to retrieve the appropriate refresh token from cache
   * @param account {AccountInfo}
   * @param familyRT {boolean}
   * @param tokenKeys {?TokenKeys}
   * @param performanceClient {?IPerformanceClient}
   * @param correlationId {?string}
   */
  getRefreshToken(e, t, n, o, r) {
    this.commonLogger.trace("CacheManager - getRefreshToken called");
    const i = t ? Be : void 0, s = {
      homeAccountId: e.homeAccountId,
      environment: e.environment,
      credentialType: A.REFRESH_TOKEN,
      clientId: this.clientId,
      familyId: i
    }, c = o && o.refreshToken || this.getTokenKeys().refreshToken, h = [];
    c.forEach((g) => {
      if (this.refreshTokenKeyMatchesFilter(g, s)) {
        const f = this.getRefreshTokenCredential(g, n);
        f && this.credentialMatchesFilter(f, s) && h.push(f);
      }
    });
    const d = h.length;
    return d < 1 ? (this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."), null) : (d > 1 && r && n && r.addFields({ multiMatchedRT: d }, n), this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"), h[0]);
  }
  /**
   * Validate the cache key against filter before retrieving and parsing cache value
   * @param key
   * @param filter
   */
  refreshTokenKeyMatchesFilter(e, t) {
    const n = e.toLowerCase();
    return !(t.familyId && n.indexOf(t.familyId.toLowerCase()) === -1 || !t.familyId && t.clientId && n.indexOf(t.clientId.toLowerCase()) === -1 || t.homeAccountId && n.indexOf(t.homeAccountId.toLowerCase()) === -1);
  }
  /**
   * Retrieve AppMetadataEntity from cache
   */
  readAppMetadataFromCache(e) {
    const t = {
      environment: e,
      clientId: this.clientId
    }, n = this.getAppMetadataFilteredBy(t), o = Object.keys(n).map((i) => n[i]), r = o.length;
    if (r < 1)
      return null;
    if (r > 1)
      throw p(_o);
    return o[0];
  }
  /**
   * Return the family_id value associated  with FOCI
   * @param environment
   * @param clientId
   */
  isAppMetadataFOCI(e) {
    const t = this.readAppMetadataFromCache(e);
    return !!(t && t.familyId === Be);
  }
  /**
   * helper to match account ids
   * @param value
   * @param homeAccountId
   */
  matchHomeAccountId(e, t) {
    return typeof e.homeAccountId == "string" && t === e.homeAccountId;
  }
  /**
   * helper to match account ids
   * @param entity
   * @param localAccountId
   * @returns
   */
  matchLocalAccountIdFromTokenClaims(e, t) {
    const n = e.oid || e.sub;
    return t === n;
  }
  matchLocalAccountIdFromTenantProfile(e, t) {
    return e.localAccountId === t;
  }
  /**
   * helper to match names
   * @param entity
   * @param name
   * @returns true if the downcased name properties are present and match in the filter and the entity
   */
  matchName(e, t) {
    var n;
    return t.toLowerCase() === ((n = e.name) == null ? void 0 : n.toLowerCase());
  }
  /**
   * helper to match usernames
   * @param entity
   * @param username
   * @returns
   */
  matchUsername(e, t) {
    return !!(e && typeof e == "string" && (t == null ? void 0 : t.toLowerCase()) === e.toLowerCase());
  }
  /**
   * helper to match assertion
   * @param value
   * @param oboAssertion
   */
  matchUserAssertionHash(e, t) {
    return !!(e.userAssertionHash && t === e.userAssertionHash);
  }
  /**
   * helper to match environment
   * @param value
   * @param environment
   */
  matchEnvironment(e, t) {
    if (this.staticAuthorityOptions) {
      const o = xi(this.staticAuthorityOptions, this.commonLogger);
      if (o.includes(t) && o.includes(e.environment))
        return !0;
    }
    const n = this.getAuthorityMetadataByAlias(t);
    return !!(n && n.aliases.indexOf(e.environment) > -1);
  }
  /**
   * helper to match credential type
   * @param entity
   * @param credentialType
   */
  matchCredentialType(e, t) {
    return e.credentialType && t.toLowerCase() === e.credentialType.toLowerCase();
  }
  /**
   * helper to match client ids
   * @param entity
   * @param clientId
   */
  matchClientId(e, t) {
    return !!(e.clientId && t === e.clientId);
  }
  /**
   * helper to match family ids
   * @param entity
   * @param familyId
   */
  matchFamilyId(e, t) {
    return !!(e.familyId && t === e.familyId);
  }
  /**
   * helper to match realm
   * @param entity
   * @param realm
   */
  matchRealm(e, t) {
    var n;
    return ((n = e.realm) == null ? void 0 : n.toLowerCase()) === t.toLowerCase();
  }
  /**
   * helper to match nativeAccountId
   * @param entity
   * @param nativeAccountId
   * @returns boolean indicating the match result
   */
  matchNativeAccountId(e, t) {
    return !!(e.nativeAccountId && t === e.nativeAccountId);
  }
  /**
   * helper to match loginHint which can be either:
   * 1. login_hint ID token claim
   * 2. username in cached account object
   * 3. upn in ID token claims
   * @param entity
   * @param loginHint
   * @returns
   */
  matchLoginHintFromTokenClaims(e, t) {
    return e.login_hint === t || e.preferred_username === t || e.upn === t;
  }
  /**
   * Helper to match sid
   * @param entity
   * @param sid
   * @returns true if the sid claim is present and matches the filter
   */
  matchSid(e, t) {
    return e.sid === t;
  }
  matchAuthorityType(e, t) {
    return !!(e.authorityType && t.toLowerCase() === e.authorityType.toLowerCase());
  }
  /**
   * Returns true if the target scopes are a subset of the current entity's scopes, false otherwise.
   * @param entity
   * @param target
   */
  matchTarget(e, t) {
    return e.credentialType !== A.ACCESS_TOKEN && e.credentialType !== A.ACCESS_TOKEN_WITH_AUTH_SCHEME || !e.target ? !1 : P.fromString(e.target).containsScopeSet(t);
  }
  /**
   * Returns true if the credential's tokenType or Authentication Scheme matches the one in the request, false otherwise
   * @param entity
   * @param tokenType
   */
  matchTokenType(e, t) {
    return !!(e.tokenType && e.tokenType === t);
  }
  /**
   * Returns true if the credential's keyId matches the one in the request, false otherwise
   * @param entity
   * @param keyId
   */
  matchKeyId(e, t) {
    return !!(e.keyId && e.keyId === t);
  }
  /**
   * returns if a given cache entity is of the type appmetadata
   * @param key
   */
  isAppMetadata(e) {
    return e.indexOf(Vt) !== -1;
  }
  /**
   * returns if a given cache entity is of the type authoritymetadata
   * @param key
   */
  isAuthorityMetadata(e) {
    return e.indexOf(ot.CACHE_KEY) !== -1;
  }
  /**
   * returns cache key used for cloud instance metadata
   */
  generateAuthorityMetadataCacheKey(e) {
    return `${ot.CACHE_KEY}-${this.clientId}-${e}`;
  }
  /**
   * Helper to convert serialized data to object
   * @param obj
   * @param json
   */
  static toObject(e, t) {
    for (const n in t)
      e[n] = t[n];
    return e;
  }
}
class Fi extends Me {
  setAccount() {
    throw p(S);
  }
  getAccount() {
    throw p(S);
  }
  getCachedAccountEntity() {
    throw p(S);
  }
  setIdTokenCredential() {
    throw p(S);
  }
  getIdTokenCredential() {
    throw p(S);
  }
  setAccessTokenCredential() {
    throw p(S);
  }
  getAccessTokenCredential() {
    throw p(S);
  }
  setRefreshTokenCredential() {
    throw p(S);
  }
  getRefreshTokenCredential() {
    throw p(S);
  }
  setAppMetadata() {
    throw p(S);
  }
  getAppMetadata() {
    throw p(S);
  }
  setServerTelemetry() {
    throw p(S);
  }
  getServerTelemetry() {
    throw p(S);
  }
  setAuthorityMetadata() {
    throw p(S);
  }
  getAuthorityMetadata() {
    throw p(S);
  }
  getAuthorityMetadataKeys() {
    throw p(S);
  }
  setThrottlingCache() {
    throw p(S);
  }
  getThrottlingCache() {
    throw p(S);
  }
  removeItem() {
    throw p(S);
  }
  getKeys() {
    throw p(S);
  }
  getAccountKeys() {
    throw p(S);
  }
  getTokenKeys() {
    throw p(S);
  }
  updateCredentialCacheKey() {
    throw p(S);
  }
  removeOutdatedAccount() {
    throw p(S);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const nr = {
  tokenRenewalOffsetSeconds: ci,
  preventCorsPreflight: !1
}, Gi = {
  loggerCallback: () => {
  },
  piiLoggingEnabled: !1,
  logLevel: N.Info,
  correlationId: u.EMPTY_STRING
}, qi = {
  claimsBasedCachingEnabled: !1
}, $i = {
  async sendGetRequestAsync() {
    throw p(S);
  },
  async sendPostRequestAsync() {
    throw p(S);
  }
}, zi = {
  sku: u.SKU,
  version: en,
  cpu: u.EMPTY_STRING,
  os: u.EMPTY_STRING
}, Vi = {
  clientSecret: u.EMPTY_STRING,
  clientAssertion: void 0
}, Qi = {
  azureCloudInstance: tn.None,
  tenant: `${u.DEFAULT_COMMON_TENANT}`
}, Yi = {
  application: {
    appName: "",
    appVersion: ""
  }
};
function Wi({ authOptions: a, systemOptions: e, loggerOptions: t, cacheOptions: n, storageInterface: o, networkInterface: r, cryptoInterface: i, clientCredentials: s, libraryInfo: c, telemetry: h, serverTelemetryManager: d, persistencePlugin: g, serializableCache: f }) {
  const T = {
    ...Gi,
    ...t
  };
  return {
    authOptions: ji(a),
    systemOptions: { ...nr, ...e },
    loggerOptions: T,
    cacheOptions: { ...qi, ...n },
    storageInterface: o || new Fi(a.clientId, it, new pe(T)),
    networkInterface: r || $i,
    cryptoInterface: i || it,
    clientCredentials: s || Vi,
    libraryInfo: { ...zi, ...c },
    telemetry: { ...Yi, ...h },
    serverTelemetryManager: d || null,
    persistencePlugin: g || null,
    serializableCache: f || null
  };
}
function ji(a) {
  return {
    clientCapabilities: [],
    azureCloudOptions: Qi,
    skipAuthorityMetadataCache: !1,
    instanceAware: !1,
    ...a
  };
}
function Lt(a) {
  return a.authOptions.authority.options.protocolMode === le.OIDC;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Q = {
  HOME_ACCOUNT_ID: "home_account_id",
  UPN: "UPN"
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Ce = "client_id", or = "redirect_uri", jn = "response_type", Ji = "response_mode", Xi = "grant_type", Zi = "claims", ea = "scope", ta = "refresh_token", na = "state", oa = "nonce", ra = "prompt", ia = "code", aa = "code_challenge", sa = "code_challenge_method", ca = "code_verifier", ha = "client-request-id", la = "x-client-SKU", da = "x-client-VER", ua = "x-client-OS", ga = "x-client-CPU", pa = "x-client-current-telemetry", ma = "x-client-last-telemetry", fa = "x-ms-lib-capability", Ca = "x-app-name", ya = "x-app-ver", Ta = "post_logout_redirect_uri", Ia = "id_token_hint", Aa = "device_code", Ea = "client_secret", Sa = "client_assertion", va = "client_assertion_type", Jn = "token_type", Xn = "req_cnf", wa = "assertion", _a = "requested_token_use", Zn = "return_spa_code", ka = "nativebroker", Ra = "logout_hint", ba = "sid", Oa = "login_hint", Na = "domain_hint", Pa = "x-client-xtra-sku", gn = "brk_client_id", Ht = "brk_redirect_uri";
/*! @azure/msal-common v14.16.1 2025-08-05 */
class ve {
  /**
   * Utility to check if the `redirectUri` in the request is a non-null value
   * @param redirectUri
   */
  static validateRedirectUri(e) {
    if (!e)
      throw R(Do);
  }
  /**
   * Utility to validate prompt sent by the user in the request
   * @param prompt
   */
  static validatePrompt(e) {
    const t = [];
    for (const n in D)
      t.push(D[n]);
    if (t.indexOf(e) < 0)
      throw R(Fo);
  }
  static validateClaims(e) {
    try {
      JSON.parse(e);
    } catch {
      throw R(on);
    }
  }
  /**
   * Utility to validate code_challenge and code_challenge_method
   * @param codeChallenge
   * @param codeChallengeMethod
   */
  static validateCodeChallengeParams(e, t) {
    if (!e || !t)
      throw R(rn);
    this.validateCodeChallengeMethod(t);
  }
  /**
   * Utility to validate code_challenge_method
   * @param codeChallengeMethod
   */
  static validateCodeChallengeMethod(e) {
    if ([
      Kn.PLAIN,
      Kn.S256
    ].indexOf(e) < 0)
      throw R($o);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Ma(a, e, t) {
  if (!e)
    return;
  const n = a.get(Ce);
  n && a.has(gn) && (t == null || t.addFields({
    embeddedClientId: n,
    embeddedRedirectUri: a.get(or)
  }, e));
}
class Ge {
  constructor(e, t) {
    this.parameters = /* @__PURE__ */ new Map(), this.performanceClient = t, this.correlationId = e;
  }
  /**
   * add response_type = code
   */
  addResponseTypeCode() {
    this.parameters.set(jn, encodeURIComponent(u.CODE_RESPONSE_TYPE));
  }
  /**
   * add response_type = token id_token
   */
  addResponseTypeForTokenAndIdToken() {
    this.parameters.set(jn, encodeURIComponent(`${u.TOKEN_RESPONSE_TYPE} ${u.ID_TOKEN_RESPONSE_TYPE}`));
  }
  /**
   * add response_mode. defaults to query.
   * @param responseMode
   */
  addResponseMode(e) {
    this.parameters.set(Ji, encodeURIComponent(e || ii.QUERY));
  }
  /**
   * Add flag to indicate STS should attempt to use WAM if available
   */
  addNativeBroker() {
    this.parameters.set(ka, encodeURIComponent("1"));
  }
  /**
   * add scopes. set addOidcScopes to false to prevent default scopes in non-user scenarios
   * @param scopeSet
   * @param addOidcScopes
   */
  addScopes(e, t = !0, n = De) {
    t && !n.includes("openid") && !e.includes("openid") && n.push("openid");
    const o = t ? [...e || [], ...n] : e || [], r = new P(o);
    this.parameters.set(ea, encodeURIComponent(r.printScopes()));
  }
  /**
   * add clientId
   * @param clientId
   */
  addClientId(e) {
    this.parameters.set(Ce, encodeURIComponent(e));
  }
  /**
   * add redirect_uri
   * @param redirectUri
   */
  addRedirectUri(e) {
    ve.validateRedirectUri(e), this.parameters.set(or, encodeURIComponent(e));
  }
  /**
   * add post logout redirectUri
   * @param redirectUri
   */
  addPostLogoutRedirectUri(e) {
    ve.validateRedirectUri(e), this.parameters.set(Ta, encodeURIComponent(e));
  }
  /**
   * add id_token_hint to logout request
   * @param idTokenHint
   */
  addIdTokenHint(e) {
    this.parameters.set(Ia, encodeURIComponent(e));
  }
  /**
   * add domain_hint
   * @param domainHint
   */
  addDomainHint(e) {
    this.parameters.set(Na, encodeURIComponent(e));
  }
  /**
   * add login_hint
   * @param loginHint
   */
  addLoginHint(e) {
    this.parameters.set(Oa, encodeURIComponent(e));
  }
  /**
   * Adds the CCS (Cache Credential Service) query parameter for login_hint
   * @param loginHint
   */
  addCcsUpn(e) {
    this.parameters.set(F.CCS_HEADER, encodeURIComponent(`UPN:${e}`));
  }
  /**
   * Adds the CCS (Cache Credential Service) query parameter for account object
   * @param loginHint
   */
  addCcsOid(e) {
    this.parameters.set(F.CCS_HEADER, encodeURIComponent(`Oid:${e.uid}@${e.utid}`));
  }
  /**
   * add sid
   * @param sid
   */
  addSid(e) {
    this.parameters.set(ba, encodeURIComponent(e));
  }
  /**
   * add claims
   * @param claims
   */
  addClaims(e, t) {
    const n = this.addClientCapabilitiesToClaims(e, t);
    ve.validateClaims(n), this.parameters.set(Zi, encodeURIComponent(n));
  }
  /**
   * add correlationId
   * @param correlationId
   */
  addCorrelationId(e) {
    this.parameters.set(ha, encodeURIComponent(e));
  }
  /**
   * add library info query params
   * @param libraryInfo
   */
  addLibraryInfo(e) {
    this.parameters.set(la, e.sku), this.parameters.set(da, e.version), e.os && this.parameters.set(ua, e.os), e.cpu && this.parameters.set(ga, e.cpu);
  }
  /**
   * Add client telemetry parameters
   * @param appTelemetry
   */
  addApplicationTelemetry(e) {
    e != null && e.appName && this.parameters.set(Ca, e.appName), e != null && e.appVersion && this.parameters.set(ya, e.appVersion);
  }
  /**
   * add prompt
   * @param prompt
   */
  addPrompt(e) {
    ve.validatePrompt(e), this.parameters.set(`${ra}`, encodeURIComponent(e));
  }
  /**
   * add state
   * @param state
   */
  addState(e) {
    e && this.parameters.set(na, encodeURIComponent(e));
  }
  /**
   * add nonce
   * @param nonce
   */
  addNonce(e) {
    this.parameters.set(oa, encodeURIComponent(e));
  }
  /**
   * add code_challenge and code_challenge_method
   * - throw if either of them are not passed
   * @param codeChallenge
   * @param codeChallengeMethod
   */
  addCodeChallengeParams(e, t) {
    if (ve.validateCodeChallengeParams(e, t), e && t)
      this.parameters.set(aa, encodeURIComponent(e)), this.parameters.set(sa, encodeURIComponent(t));
    else
      throw R(rn);
  }
  /**
   * add the `authorization_code` passed by the user to exchange for a token
   * @param code
   */
  addAuthorizationCode(e) {
    this.parameters.set(ia, encodeURIComponent(e));
  }
  /**
   * add the `authorization_code` passed by the user to exchange for a token
   * @param code
   */
  addDeviceCode(e) {
    this.parameters.set(Aa, encodeURIComponent(e));
  }
  /**
   * add the `refreshToken` passed by the user
   * @param refreshToken
   */
  addRefreshToken(e) {
    this.parameters.set(ta, encodeURIComponent(e));
  }
  /**
   * add the `code_verifier` passed by the user to exchange for a token
   * @param codeVerifier
   */
  addCodeVerifier(e) {
    this.parameters.set(ca, encodeURIComponent(e));
  }
  /**
   * add client_secret
   * @param clientSecret
   */
  addClientSecret(e) {
    this.parameters.set(Ea, encodeURIComponent(e));
  }
  /**
   * add clientAssertion for confidential client flows
   * @param clientAssertion
   */
  addClientAssertion(e) {
    e && this.parameters.set(Sa, encodeURIComponent(e));
  }
  /**
   * add clientAssertionType for confidential client flows
   * @param clientAssertionType
   */
  addClientAssertionType(e) {
    e && this.parameters.set(va, encodeURIComponent(e));
  }
  /**
   * add OBO assertion for confidential client flows
   * @param clientAssertion
   */
  addOboAssertion(e) {
    this.parameters.set(wa, encodeURIComponent(e));
  }
  /**
   * add grant type
   * @param grantType
   */
  addRequestTokenUse(e) {
    this.parameters.set(_a, encodeURIComponent(e));
  }
  /**
   * add grant type
   * @param grantType
   */
  addGrantType(e) {
    this.parameters.set(Xi, encodeURIComponent(e));
  }
  /**
   * add client info
   *
   */
  addClientInfo() {
    this.parameters.set(ai, "1");
  }
  /**
   * add extraQueryParams
   * @param eQParams
   */
  addExtraQueryParameters(e) {
    Object.entries(e).forEach(([t, n]) => {
      !this.parameters.has(t) && n && this.parameters.set(t, n);
    });
  }
  addClientCapabilitiesToClaims(e, t) {
    let n;
    if (!e)
      n = {};
    else
      try {
        n = JSON.parse(e);
      } catch {
        throw R(on);
      }
    return t && t.length > 0 && (n.hasOwnProperty(je.ACCESS_TOKEN) || (n[je.ACCESS_TOKEN] = {}), n[je.ACCESS_TOKEN][je.XMS_CC] = {
      values: t
    }), JSON.stringify(n);
  }
  /**
   * adds `username` for Password Grant flow
   * @param username
   */
  addUsername(e) {
    this.parameters.set(Bn.username, encodeURIComponent(e));
  }
  /**
   * adds `password` for Password Grant flow
   * @param password
   */
  addPassword(e) {
    this.parameters.set(Bn.password, encodeURIComponent(e));
  }
  /**
   * add pop_jwk to query params
   * @param cnfString
   */
  addPopToken(e) {
    e && (this.parameters.set(Jn, k.POP), this.parameters.set(Xn, encodeURIComponent(e)));
  }
  /**
   * add SSH JWK and key ID to query params
   */
  addSshJwk(e) {
    e && (this.parameters.set(Jn, k.SSH), this.parameters.set(Xn, encodeURIComponent(e)));
  }
  /**
   * add server telemetry fields
   * @param serverTelemetryManager
   */
  addServerTelemetry(e) {
    this.parameters.set(pa, e.generateCurrentRequestHeaderValue()), this.parameters.set(ma, e.generateLastRequestHeaderValue());
  }
  /**
   * Adds parameter that indicates to the server that throttling is supported
   */
  addThrottling() {
    this.parameters.set(fa, Fe.X_MS_LIB_CAPABILITY_VALUE);
  }
  /**
   * Adds logout_hint parameter for "silent" logout which prevent server account picker
   */
  addLogoutHint(e) {
    this.parameters.set(Ra, encodeURIComponent(e));
  }
  addBrokerParameters(e) {
    const t = {};
    t[gn] = e.brokerClientId, t[Ht] = e.brokerRedirectUri, this.addExtraQueryParameters(t);
  }
  /**
   * Utility to create a URL from the params map
   */
  createQueryString() {
    const e = new Array();
    return this.parameters.forEach((t, n) => {
      e.push(`${n}=${t}`);
    }), Ma(this.parameters, this.correlationId, this.performanceClient), e.join("&");
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Ua(a) {
  return a.hasOwnProperty("authorization_endpoint") && a.hasOwnProperty("token_endpoint") && a.hasOwnProperty("issuer") && a.hasOwnProperty("jwks_uri");
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function La(a) {
  return a.hasOwnProperty("tenant_discovery_endpoint") && a.hasOwnProperty("metadata");
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Ha(a) {
  return a.hasOwnProperty("error") && a.hasOwnProperty("error_description");
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const l = {
  /**
   * acquireTokenByCode API (msal-browser and msal-node).
   * Used to acquire tokens by trading an authorization code against the token endpoint.
   */
  AcquireTokenByCode: "acquireTokenByCode",
  /**
   * acquireTokenByRefreshToken API (msal-browser and msal-node).
   * Used to renew an access token using a refresh token against the token endpoint.
   */
  AcquireTokenByRefreshToken: "acquireTokenByRefreshToken",
  /**
   * acquireTokenSilent API (msal-browser and msal-node).
   * Used to silently acquire a new access token (from the cache or the network).
   */
  AcquireTokenSilent: "acquireTokenSilent",
  /**
   * acquireTokenSilentAsync (msal-browser).
   * Internal API for acquireTokenSilent.
   */
  AcquireTokenSilentAsync: "acquireTokenSilentAsync",
  /**
   * acquireTokenPopup (msal-browser).
   * Used to acquire a new access token interactively through pop ups
   */
  AcquireTokenPopup: "acquireTokenPopup",
  /**
   * acquireTokenPreRedirect (msal-browser).
   * First part of the redirect flow.
   * Used to acquire a new access token interactively through redirects.
   */
  AcquireTokenPreRedirect: "acquireTokenPreRedirect",
  /**
   * acquireTokenRedirect (msal-browser).
   * Second part of the redirect flow.
   * Used to acquire a new access token interactively through redirects.
   */
  AcquireTokenRedirect: "acquireTokenRedirect",
  /**
   * getPublicKeyThumbprint API in CryptoOpts class (msal-browser).
   * Used to generate a public/private keypair and generate a public key thumbprint for pop requests.
   */
  CryptoOptsGetPublicKeyThumbprint: "cryptoOptsGetPublicKeyThumbprint",
  /**
   * signJwt API in CryptoOpts class (msal-browser).
   * Used to signed a pop token.
   */
  CryptoOptsSignJwt: "cryptoOptsSignJwt",
  /**
   * acquireToken API in the SilentCacheClient class (msal-browser).
   * Used to read access tokens from the cache.
   */
  SilentCacheClientAcquireToken: "silentCacheClientAcquireToken",
  /**
   * acquireToken API in the SilentIframeClient class (msal-browser).
   * Used to acquire a new set of tokens from the authorize endpoint in a hidden iframe.
   */
  SilentIframeClientAcquireToken: "silentIframeClientAcquireToken",
  AwaitConcurrentIframe: "awaitConcurrentIframe",
  /**
   * acquireToken API in SilentRereshClient (msal-browser).
   * Used to acquire a new set of tokens from the token endpoint using a refresh token.
   */
  SilentRefreshClientAcquireToken: "silentRefreshClientAcquireToken",
  /**
   * ssoSilent API (msal-browser).
   * Used to silently acquire an authorization code and set of tokens using a hidden iframe.
   */
  SsoSilent: "ssoSilent",
  /**
   * getDiscoveredAuthority API in StandardInteractionClient class (msal-browser).
   * Used to load authority metadata for a request.
   */
  StandardInteractionClientGetDiscoveredAuthority: "standardInteractionClientGetDiscoveredAuthority",
  /**
   * acquireToken APIs in msal-browser.
   * Used to make an /authorize endpoint call with native brokering enabled.
   */
  FetchAccountIdWithNativeBroker: "fetchAccountIdWithNativeBroker",
  /**
   * acquireToken API in NativeInteractionClient class (msal-browser).
   * Used to acquire a token from Native component when native brokering is enabled.
   */
  NativeInteractionClientAcquireToken: "nativeInteractionClientAcquireToken",
  /**
   * Time spent creating default headers for requests to token endpoint
   */
  BaseClientCreateTokenRequestHeaders: "baseClientCreateTokenRequestHeaders",
  /**
   * Time spent sending/waiting for the response of a request to the token endpoint
   */
  NetworkClientSendPostRequestAsync: "networkClientSendPostRequestAsync",
  RefreshTokenClientExecutePostToTokenEndpoint: "refreshTokenClientExecutePostToTokenEndpoint",
  AuthorizationCodeClientExecutePostToTokenEndpoint: "authorizationCodeClientExecutePostToTokenEndpoint",
  /**
   * Used to measure the time taken for completing embedded-broker handshake (PW-Broker).
   */
  BrokerHandhshake: "brokerHandshake",
  /**
   * acquireTokenByRefreshToken API in BrokerClientApplication (PW-Broker) .
   */
  AcquireTokenByRefreshTokenInBroker: "acquireTokenByRefreshTokenInBroker",
  /**
   * Time taken for token acquisition by broker
   */
  AcquireTokenByBroker: "acquireTokenByBroker",
  /**
   * Time spent on the network for refresh token acquisition
   */
  RefreshTokenClientExecuteTokenRequest: "refreshTokenClientExecuteTokenRequest",
  /**
   * Time taken for acquiring refresh token , records RT size
   */
  RefreshTokenClientAcquireToken: "refreshTokenClientAcquireToken",
  /**
   * Time taken for acquiring cached refresh token
   */
  RefreshTokenClientAcquireTokenWithCachedRefreshToken: "refreshTokenClientAcquireTokenWithCachedRefreshToken",
  /**
   * acquireTokenByRefreshToken API in RefreshTokenClient (msal-common).
   */
  RefreshTokenClientAcquireTokenByRefreshToken: "refreshTokenClientAcquireTokenByRefreshToken",
  /**
   * Helper function to create token request body in RefreshTokenClient (msal-common).
   */
  RefreshTokenClientCreateTokenRequestBody: "refreshTokenClientCreateTokenRequestBody",
  /**
   * acquireTokenFromCache (msal-browser).
   * Internal API for acquiring token from cache
   */
  AcquireTokenFromCache: "acquireTokenFromCache",
  SilentFlowClientAcquireCachedToken: "silentFlowClientAcquireCachedToken",
  SilentFlowClientGenerateResultFromCacheRecord: "silentFlowClientGenerateResultFromCacheRecord",
  /**
   * acquireTokenBySilentIframe (msal-browser).
   * Internal API for acquiring token by silent Iframe
   */
  AcquireTokenBySilentIframe: "acquireTokenBySilentIframe",
  /**
   * Internal API for initializing base request in BaseInteractionClient (msal-browser)
   */
  InitializeBaseRequest: "initializeBaseRequest",
  /**
   * Internal API for initializing silent request in SilentCacheClient (msal-browser)
   */
  InitializeSilentRequest: "initializeSilentRequest",
  InitializeClientApplication: "initializeClientApplication",
  /**
   * Helper function in SilentIframeClient class (msal-browser).
   */
  SilentIframeClientTokenHelper: "silentIframeClientTokenHelper",
  /**
   * SilentHandler
   */
  SilentHandlerInitiateAuthRequest: "silentHandlerInitiateAuthRequest",
  SilentHandlerMonitorIframeForHash: "silentHandlerMonitorIframeForHash",
  SilentHandlerLoadFrame: "silentHandlerLoadFrame",
  SilentHandlerLoadFrameSync: "silentHandlerLoadFrameSync",
  /**
   * Helper functions in StandardInteractionClient class (msal-browser)
   */
  StandardInteractionClientCreateAuthCodeClient: "standardInteractionClientCreateAuthCodeClient",
  StandardInteractionClientGetClientConfiguration: "standardInteractionClientGetClientConfiguration",
  StandardInteractionClientInitializeAuthorizationRequest: "standardInteractionClientInitializeAuthorizationRequest",
  StandardInteractionClientInitializeAuthorizationCodeRequest: "standardInteractionClientInitializeAuthorizationCodeRequest",
  /**
   * getAuthCodeUrl API (msal-browser and msal-node).
   */
  GetAuthCodeUrl: "getAuthCodeUrl",
  /**
   * Functions from InteractionHandler (msal-browser)
   */
  HandleCodeResponseFromServer: "handleCodeResponseFromServer",
  HandleCodeResponse: "handleCodeResponse",
  UpdateTokenEndpointAuthority: "updateTokenEndpointAuthority",
  /**
   * APIs in Authorization Code Client (msal-common)
   */
  AuthClientAcquireToken: "authClientAcquireToken",
  AuthClientExecuteTokenRequest: "authClientExecuteTokenRequest",
  AuthClientCreateTokenRequestBody: "authClientCreateTokenRequestBody",
  AuthClientCreateQueryString: "authClientCreateQueryString",
  /**
   * Generate functions in PopTokenGenerator (msal-common)
   */
  PopTokenGenerateCnf: "popTokenGenerateCnf",
  PopTokenGenerateKid: "popTokenGenerateKid",
  /**
   * handleServerTokenResponse API in ResponseHandler (msal-common)
   */
  HandleServerTokenResponse: "handleServerTokenResponse",
  DeserializeResponse: "deserializeResponse",
  /**
   * Authority functions
   */
  AuthorityFactoryCreateDiscoveredInstance: "authorityFactoryCreateDiscoveredInstance",
  AuthorityResolveEndpointsAsync: "authorityResolveEndpointsAsync",
  AuthorityResolveEndpointsFromLocalSources: "authorityResolveEndpointsFromLocalSources",
  AuthorityGetCloudDiscoveryMetadataFromNetwork: "authorityGetCloudDiscoveryMetadataFromNetwork",
  AuthorityUpdateCloudDiscoveryMetadata: "authorityUpdateCloudDiscoveryMetadata",
  AuthorityGetEndpointMetadataFromNetwork: "authorityGetEndpointMetadataFromNetwork",
  AuthorityUpdateEndpointMetadata: "authorityUpdateEndpointMetadata",
  AuthorityUpdateMetadataWithRegionalInformation: "authorityUpdateMetadataWithRegionalInformation",
  /**
   * Region Discovery functions
   */
  RegionDiscoveryDetectRegion: "regionDiscoveryDetectRegion",
  RegionDiscoveryGetRegionFromIMDS: "regionDiscoveryGetRegionFromIMDS",
  RegionDiscoveryGetCurrentVersion: "regionDiscoveryGetCurrentVersion",
  AcquireTokenByCodeAsync: "acquireTokenByCodeAsync",
  GetEndpointMetadataFromNetwork: "getEndpointMetadataFromNetwork",
  GetCloudDiscoveryMetadataFromNetworkMeasurement: "getCloudDiscoveryMetadataFromNetworkMeasurement",
  HandleRedirectPromiseMeasurement: "handleRedirectPromise",
  HandleNativeRedirectPromiseMeasurement: "handleNativeRedirectPromise",
  UpdateCloudDiscoveryMetadataMeasurement: "updateCloudDiscoveryMetadataMeasurement",
  UsernamePasswordClientAcquireToken: "usernamePasswordClientAcquireToken",
  NativeMessageHandlerHandshake: "nativeMessageHandlerHandshake",
  NativeGenerateAuthResult: "nativeGenerateAuthResult",
  RemoveHiddenIframe: "removeHiddenIframe",
  /**
   * Cache operations
   */
  ClearTokensAndKeysWithClaims: "clearTokensAndKeysWithClaims",
  CacheManagerGetRefreshToken: "cacheManagerGetRefreshToken",
  /**
   * Crypto Operations
   */
  GeneratePkceCodes: "generatePkceCodes",
  GenerateCodeVerifier: "generateCodeVerifier",
  GenerateCodeChallengeFromVerifier: "generateCodeChallengeFromVerifier",
  Sha256Digest: "sha256Digest",
  GetRandomValues: "getRandomValues"
}, Da = {
  InProgress: 1
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Ee = (a, e, t, n, o) => (...r) => {
  t.trace(`Executing function ${e}`);
  const i = n == null ? void 0 : n.startMeasurement(e, o);
  if (o) {
    const s = e + "CallCount";
    n == null || n.incrementFields({ [s]: 1 }, o);
  }
  try {
    const s = a(...r);
    return i == null || i.end({
      success: !0
    }), t.trace(`Returning result from ${e}`), s;
  } catch (s) {
    t.trace(`Error occurred in ${e}`);
    try {
      t.trace(JSON.stringify(s));
    } catch {
      t.trace("Unable to print error message.");
    }
    throw i == null || i.end({
      success: !1
    }, s), s;
  }
}, m = (a, e, t, n, o) => (...r) => {
  t.trace(`Executing function ${e}`);
  const i = n == null ? void 0 : n.startMeasurement(e, o);
  if (o) {
    const s = e + "CallCount";
    n == null || n.incrementFields({ [s]: 1 }, o);
  }
  return n == null || n.setPreQueueTime(e, o), a(...r).then((s) => (t.trace(`Returning result from ${e}`), i == null || i.end({
    success: !0
  }), s)).catch((s) => {
    t.trace(`Error occurred in ${e}`);
    try {
      t.trace(JSON.stringify(s));
    } catch {
      t.trace("Unable to print error message.");
    }
    throw i == null || i.end({
      success: !1
    }, s), s;
  });
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
class Ct {
  constructor(e, t, n, o) {
    this.networkInterface = e, this.logger = t, this.performanceClient = n, this.correlationId = o;
  }
  /**
   * Detect the region from the application's environment.
   *
   * @returns Promise<string | null>
   */
  async detectRegion(e, t) {
    var o;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.RegionDiscoveryDetectRegion, this.correlationId);
    let n = e;
    if (n)
      t.region_source = Se.ENVIRONMENT_VARIABLE;
    else {
      const r = Ct.IMDS_OPTIONS;
      try {
        const i = await m(this.getRegionFromIMDS.bind(this), l.RegionDiscoveryGetRegionFromIMDS, this.logger, this.performanceClient, this.correlationId)(u.IMDS_VERSION, r);
        if (i.status === Xe.httpSuccess && (n = i.body, t.region_source = Se.IMDS), i.status === Xe.httpBadRequest) {
          const s = await m(this.getCurrentVersion.bind(this), l.RegionDiscoveryGetCurrentVersion, this.logger, this.performanceClient, this.correlationId)(r);
          if (!s)
            return t.region_source = Se.FAILED_AUTO_DETECTION, null;
          const c = await m(this.getRegionFromIMDS.bind(this), l.RegionDiscoveryGetRegionFromIMDS, this.logger, this.performanceClient, this.correlationId)(s, r);
          c.status === Xe.httpSuccess && (n = c.body, t.region_source = Se.IMDS);
        }
      } catch {
        return t.region_source = Se.FAILED_AUTO_DETECTION, null;
      }
    }
    return n || (t.region_source = Se.FAILED_AUTO_DETECTION), n || null;
  }
  /**
   * Make the call to the IMDS endpoint
   *
   * @param imdsEndpointUrl
   * @returns Promise<NetworkResponse<string>>
   */
  async getRegionFromIMDS(e, t) {
    var n;
    return (n = this.performanceClient) == null || n.addQueueMeasurement(l.RegionDiscoveryGetRegionFromIMDS, this.correlationId), this.networkInterface.sendGetRequestAsync(`${u.IMDS_ENDPOINT}?api-version=${e}&format=text`, t, u.IMDS_TIMEOUT);
  }
  /**
   * Get the most recent version of the IMDS endpoint available
   *
   * @returns Promise<string | null>
   */
  async getCurrentVersion(e) {
    var t;
    (t = this.performanceClient) == null || t.addQueueMeasurement(l.RegionDiscoveryGetCurrentVersion, this.correlationId);
    try {
      const n = await this.networkInterface.sendGetRequestAsync(`${u.IMDS_ENDPOINT}?format=json`, e);
      return n.status === Xe.httpBadRequest && n.body && n.body["newest-versions"] && n.body["newest-versions"].length > 0 ? n.body["newest-versions"][0] : null;
    } catch {
      return null;
    }
  }
}
Ct.IMDS_OPTIONS = {
  headers: {
    Metadata: "true"
  }
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
class x {
  constructor(e, t, n, o, r, i, s, c) {
    this.canonicalAuthority = e, this._canonicalAuthority.validateAsUri(), this.networkInterface = t, this.cacheManager = n, this.authorityOptions = o, this.regionDiscoveryMetadata = {
      region_used: void 0,
      region_source: void 0,
      region_outcome: void 0
    }, this.logger = r, this.performanceClient = s, this.correlationId = i, this.managedIdentity = c || !1, this.regionDiscovery = new Ct(t, this.logger, this.performanceClient, this.correlationId);
  }
  /**
   * Get {@link AuthorityType}
   * @param authorityUri {@link IUri}
   * @private
   */
  getAuthorityType(e) {
    if (e.HostNameAndPort.endsWith(u.CIAM_AUTH_URL))
      return W.Ciam;
    const t = e.PathSegments;
    if (t.length)
      switch (t[0].toLowerCase()) {
        case u.ADFS:
          return W.Adfs;
        case u.DSTS:
          return W.Dsts;
      }
    return W.Default;
  }
  // See above for AuthorityType
  get authorityType() {
    return this.getAuthorityType(this.canonicalAuthorityUrlComponents);
  }
  /**
   * ProtocolMode enum representing the way endpoints are constructed.
   */
  get protocolMode() {
    return this.authorityOptions.protocolMode;
  }
  /**
   * Returns authorityOptions which can be used to reinstantiate a new authority instance
   */
  get options() {
    return this.authorityOptions;
  }
  /**
   * A URL that is the authority set by the developer
   */
  get canonicalAuthority() {
    return this._canonicalAuthority.urlString;
  }
  /**
   * Sets canonical authority.
   */
  set canonicalAuthority(e) {
    this._canonicalAuthority = new w(e), this._canonicalAuthority.validateAsUri(), this._canonicalAuthorityUrlComponents = null;
  }
  /**
   * Get authority components.
   */
  get canonicalAuthorityUrlComponents() {
    return this._canonicalAuthorityUrlComponents || (this._canonicalAuthorityUrlComponents = this._canonicalAuthority.getUrlComponents()), this._canonicalAuthorityUrlComponents;
  }
  /**
   * Get hostname and port i.e. login.microsoftonline.com
   */
  get hostnameAndPort() {
    return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase();
  }
  /**
   * Get tenant for authority.
   */
  get tenant() {
    return this.canonicalAuthorityUrlComponents.PathSegments[0];
  }
  /**
   * OAuth /authorize endpoint for requests
   */
  get authorizationEndpoint() {
    if (this.discoveryComplete())
      return this.replacePath(this.metadata.authorization_endpoint);
    throw p(se);
  }
  /**
   * OAuth /token endpoint for requests
   */
  get tokenEndpoint() {
    if (this.discoveryComplete())
      return this.replacePath(this.metadata.token_endpoint);
    throw p(se);
  }
  get deviceCodeEndpoint() {
    if (this.discoveryComplete())
      return this.replacePath(this.metadata.token_endpoint.replace("/token", "/devicecode"));
    throw p(se);
  }
  /**
   * OAuth logout endpoint for requests
   */
  get endSessionEndpoint() {
    if (this.discoveryComplete()) {
      if (!this.metadata.end_session_endpoint)
        throw p(Mo);
      return this.replacePath(this.metadata.end_session_endpoint);
    } else
      throw p(se);
  }
  /**
   * OAuth issuer for requests
   */
  get selfSignedJwtAudience() {
    if (this.discoveryComplete())
      return this.replacePath(this.metadata.issuer);
    throw p(se);
  }
  /**
   * Jwks_uri for token signing keys
   */
  get jwksUri() {
    if (this.discoveryComplete())
      return this.replacePath(this.metadata.jwks_uri);
    throw p(se);
  }
  /**
   * Returns a flag indicating that tenant name can be replaced in authority {@link IUri}
   * @param authorityUri {@link IUri}
   * @private
   */
  canReplaceTenant(e) {
    return e.PathSegments.length === 1 && !x.reservedTenantDomains.has(e.PathSegments[0]) && this.getAuthorityType(e) === W.Default && this.protocolMode === le.AAD;
  }
  /**
   * Replaces tenant in url path with current tenant. Defaults to common.
   * @param urlString
   */
  replaceTenant(e) {
    return e.replace(/{tenant}|{tenantid}/g, this.tenant);
  }
  /**
   * Replaces path such as tenant or policy with the current tenant or policy.
   * @param urlString
   */
  replacePath(e) {
    let t = e;
    const o = new w(this.metadata.canonical_authority).getUrlComponents(), r = o.PathSegments;
    return this.canonicalAuthorityUrlComponents.PathSegments.forEach((s, c) => {
      let h = r[c];
      if (c === 0 && this.canReplaceTenant(o)) {
        const d = new w(this.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];
        h !== d && (this.logger.verbose(`Replacing tenant domain name ${h} with id ${d}`), h = d);
      }
      s !== h && (t = t.replace(`/${h}/`, `/${s}/`));
    }), this.replaceTenant(t);
  }
  /**
   * The default open id configuration endpoint for any canonical authority.
   */
  get defaultOpenIdConfigurationEndpoint() {
    const e = this.hostnameAndPort;
    return this.canonicalAuthority.endsWith("v2.0/") || this.authorityType === W.Adfs || this.protocolMode !== le.AAD && !this.isAliasOfKnownMicrosoftAuthority(e) ? `${this.canonicalAuthority}.well-known/openid-configuration` : `${this.canonicalAuthority}v2.0/.well-known/openid-configuration`;
  }
  /**
   * Boolean that returns whether or not tenant discovery has been completed.
   */
  discoveryComplete() {
    return !!this.metadata;
  }
  /**
   * Perform endpoint discovery to discover aliases, preferred_cache, preferred_network
   * and the /authorize, /token and logout endpoints.
   */
  async resolveEndpointsAsync() {
    var o, r;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.AuthorityResolveEndpointsAsync, this.correlationId);
    const e = this.getCurrentMetadataEntity(), t = await m(this.updateCloudDiscoveryMetadata.bind(this), l.AuthorityUpdateCloudDiscoveryMetadata, this.logger, this.performanceClient, this.correlationId)(e);
    this.canonicalAuthority = this.canonicalAuthority.replace(this.hostnameAndPort, e.preferred_network);
    const n = await m(this.updateEndpointMetadata.bind(this), l.AuthorityUpdateEndpointMetadata, this.logger, this.performanceClient, this.correlationId)(e);
    this.updateCachedMetadata(e, t, {
      source: n
    }), (r = this.performanceClient) == null || r.addFields({
      cloudDiscoverySource: t,
      authorityEndpointSource: n
    }, this.correlationId);
  }
  /**
   * Returns metadata entity from cache if it exists, otherwiser returns a new metadata entity built
   * from the configured canonical authority
   * @returns
   */
  getCurrentMetadataEntity() {
    let e = this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);
    return e || (e = {
      aliases: [],
      preferred_cache: this.hostnameAndPort,
      preferred_network: this.hostnameAndPort,
      canonical_authority: this.canonicalAuthority,
      authorization_endpoint: "",
      token_endpoint: "",
      end_session_endpoint: "",
      issuer: "",
      aliasesFromNetwork: !1,
      endpointsFromNetwork: !1,
      expiresAt: Vn(),
      jwks_uri: ""
    }), e;
  }
  /**
   * Updates cached metadata based on metadata source and sets the instance's metadata
   * property to the same value
   * @param metadataEntity
   * @param cloudDiscoverySource
   * @param endpointMetadataResult
   */
  updateCachedMetadata(e, t, n) {
    t !== $.CACHE && (n == null ? void 0 : n.source) !== $.CACHE && (e.expiresAt = Vn(), e.canonical_authority = this.canonicalAuthority);
    const o = this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache);
    this.cacheManager.setAuthorityMetadata(o, e), this.metadata = e;
  }
  /**
   * Update AuthorityMetadataEntity with new endpoints and return where the information came from
   * @param metadataEntity
   */
  async updateEndpointMetadata(e) {
    var o, r, i;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.AuthorityUpdateEndpointMetadata, this.correlationId);
    const t = this.updateEndpointMetadataFromLocalSources(e);
    if (t) {
      if (t.source === $.HARDCODED_VALUES && (r = this.authorityOptions.azureRegionConfiguration) != null && r.azureRegion && t.metadata) {
        const s = await m(this.updateMetadataWithRegionalInformation.bind(this), l.AuthorityUpdateMetadataWithRegionalInformation, this.logger, this.performanceClient, this.correlationId)(t.metadata);
        Ze(e, s, !1), e.canonical_authority = this.canonicalAuthority;
      }
      return t.source;
    }
    let n = await m(this.getEndpointMetadataFromNetwork.bind(this), l.AuthorityGetEndpointMetadataFromNetwork, this.logger, this.performanceClient, this.correlationId)();
    if (n)
      return (i = this.authorityOptions.azureRegionConfiguration) != null && i.azureRegion && (n = await m(this.updateMetadataWithRegionalInformation.bind(this), l.AuthorityUpdateMetadataWithRegionalInformation, this.logger, this.performanceClient, this.correlationId)(n)), Ze(e, n, !0), $.NETWORK;
    throw p(Ao, this.defaultOpenIdConfigurationEndpoint);
  }
  /**
   * Updates endpoint metadata from local sources and returns where the information was retrieved from and the metadata config
   * response if the source is hardcoded metadata
   * @param metadataEntity
   * @returns
   */
  updateEndpointMetadataFromLocalSources(e) {
    this.logger.verbose("Attempting to get endpoint metadata from authority configuration");
    const t = this.getEndpointMetadataFromConfig();
    if (t)
      return this.logger.verbose("Found endpoint metadata in authority configuration"), Ze(e, t, !1), {
        source: $.CONFIG
      };
    if (this.logger.verbose("Did not find endpoint metadata in the config... Attempting to get endpoint metadata from the hardcoded values."), this.authorityOptions.skipAuthorityMetadataCache)
      this.logger.verbose("Skipping hardcoded metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get endpoint metadata from the network metadata cache.");
    else {
      const o = this.getEndpointMetadataFromHardcodedValues();
      if (o)
        return Ze(e, o, !1), {
          source: $.HARDCODED_VALUES,
          metadata: o
        };
      this.logger.verbose("Did not find endpoint metadata in hardcoded values... Attempting to get endpoint metadata from the network metadata cache.");
    }
    const n = Qn(e);
    return this.isAuthoritySameType(e) && e.endpointsFromNetwork && !n ? (this.logger.verbose("Found endpoint metadata in the cache."), { source: $.CACHE }) : (n && this.logger.verbose("The metadata entity is expired."), null);
  }
  /**
   * Compares the number of url components after the domain to determine if the cached
   * authority metadata can be used for the requested authority. Protects against same domain different
   * authority such as login.microsoftonline.com/tenant and login.microsoftonline.com/tfp/tenant/policy
   * @param metadataEntity
   */
  isAuthoritySameType(e) {
    return new w(e.canonical_authority).getUrlComponents().PathSegments.length === this.canonicalAuthorityUrlComponents.PathSegments.length;
  }
  /**
   * Parse authorityMetadata config option
   */
  getEndpointMetadataFromConfig() {
    if (this.authorityOptions.authorityMetadata)
      try {
        return JSON.parse(this.authorityOptions.authorityMetadata);
      } catch {
        throw R(zo);
      }
    return null;
  }
  /**
   * Gets OAuth endpoints from the given OpenID configuration endpoint.
   *
   * @param hasHardcodedMetadata boolean
   */
  async getEndpointMetadataFromNetwork() {
    var n;
    (n = this.performanceClient) == null || n.addQueueMeasurement(l.AuthorityGetEndpointMetadataFromNetwork, this.correlationId);
    const e = {}, t = this.defaultOpenIdConfigurationEndpoint;
    this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: attempting to retrieve OAuth endpoints from ${t}`);
    try {
      const o = await this.networkInterface.sendGetRequestAsync(t, e);
      return Ua(o.body) ? o.body : (this.logger.verbose("Authority.getEndpointMetadataFromNetwork: could not parse response as OpenID configuration"), null);
    } catch (o) {
      return this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: ${o}`), null;
    }
  }
  /**
   * Get OAuth endpoints for common authorities.
   */
  getEndpointMetadataFromHardcodedValues() {
    return this.hostnameAndPort in Yn ? Yn[this.hostnameAndPort] : null;
  }
  /**
   * Update the retrieved metadata with regional information.
   * User selected Azure region will be used if configured.
   */
  async updateMetadataWithRegionalInformation(e) {
    var n, o, r;
    (n = this.performanceClient) == null || n.addQueueMeasurement(l.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);
    const t = (o = this.authorityOptions.azureRegionConfiguration) == null ? void 0 : o.azureRegion;
    if (t) {
      if (t !== u.AZURE_REGION_AUTO_DISCOVER_FLAG)
        return this.regionDiscoveryMetadata.region_outcome = wt.CONFIGURED_NO_AUTO_DETECTION, this.regionDiscoveryMetadata.region_used = t, x.replaceWithRegionalInformation(e, t);
      const i = await m(this.regionDiscovery.detectRegion.bind(this.regionDiscovery), l.RegionDiscoveryDetectRegion, this.logger, this.performanceClient, this.correlationId)((r = this.authorityOptions.azureRegionConfiguration) == null ? void 0 : r.environmentRegion, this.regionDiscoveryMetadata);
      if (i)
        return this.regionDiscoveryMetadata.region_outcome = wt.AUTO_DETECTION_REQUESTED_SUCCESSFUL, this.regionDiscoveryMetadata.region_used = i, x.replaceWithRegionalInformation(e, i);
      this.regionDiscoveryMetadata.region_outcome = wt.AUTO_DETECTION_REQUESTED_FAILED;
    }
    return e;
  }
  /**
   * Updates the AuthorityMetadataEntity with new aliases, preferred_network and preferred_cache
   * and returns where the information was retrieved from
   * @param metadataEntity
   * @returns AuthorityMetadataSource
   */
  async updateCloudDiscoveryMetadata(e) {
    var o;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.AuthorityUpdateCloudDiscoveryMetadata, this.correlationId);
    const t = this.updateCloudDiscoveryMetadataFromLocalSources(e);
    if (t)
      return t;
    const n = await m(this.getCloudDiscoveryMetadataFromNetwork.bind(this), l.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.logger, this.performanceClient, this.correlationId)();
    if (n)
      return _t(e, n, !0), $.NETWORK;
    throw R(Vo);
  }
  updateCloudDiscoveryMetadataFromLocalSources(e) {
    this.logger.verbose("Attempting to get cloud discovery metadata  from authority configuration"), this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities || u.NOT_APPLICABLE}`), this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata || u.NOT_APPLICABLE}`), this.logger.verbosePii(`Canonical Authority: ${e.canonical_authority || u.NOT_APPLICABLE}`);
    const t = this.getCloudDiscoveryMetadataFromConfig();
    if (t)
      return this.logger.verbose("Found cloud discovery metadata in authority configuration"), _t(e, t, !1), $.CONFIG;
    if (this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the hardcoded values."), this.options.skipAuthorityMetadataCache)
      this.logger.verbose("Skipping hardcoded cloud discovery metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get cloud discovery metadata from the network metadata cache.");
    else {
      const o = Bi(this.hostnameAndPort);
      if (o)
        return this.logger.verbose("Found cloud discovery metadata from hardcoded values."), _t(e, o, !1), $.HARDCODED_VALUES;
      this.logger.verbose("Did not find cloud discovery metadata in hardcoded values... Attempting to get cloud discovery metadata from the network metadata cache.");
    }
    const n = Qn(e);
    return this.isAuthoritySameType(e) && e.aliasesFromNetwork && !n ? (this.logger.verbose("Found cloud discovery metadata in the cache."), $.CACHE) : (n && this.logger.verbose("The metadata entity is expired."), null);
  }
  /**
   * Parse cloudDiscoveryMetadata config or check knownAuthorities
   */
  getCloudDiscoveryMetadataFromConfig() {
    if (this.authorityType === W.Ciam)
      return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."), x.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);
    if (this.authorityOptions.cloudDiscoveryMetadata) {
      this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");
      try {
        this.logger.verbose("Attempting to parse the cloud discovery metadata.");
        const e = JSON.parse(this.authorityOptions.cloudDiscoveryMetadata), t = ht(e.metadata, this.hostnameAndPort);
        if (this.logger.verbose("Parsed the cloud discovery metadata."), t)
          return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."), t;
        this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.");
      } catch {
        throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."), R(an);
      }
    }
    return this.isInKnownAuthorities() ? (this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."), x.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)) : null;
  }
  /**
   * Called to get metadata from network if CloudDiscoveryMetadata was not populated by config
   *
   * @param hasHardcodedMetadata boolean
   */
  async getCloudDiscoveryMetadataFromNetwork() {
    var o;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.correlationId);
    const e = `${u.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`, t = {};
    let n = null;
    try {
      const r = await this.networkInterface.sendGetRequestAsync(e, t);
      let i, s;
      if (La(r.body))
        i = r.body, s = i.metadata, this.logger.verbosePii(`tenant_discovery_endpoint is: ${i.tenant_discovery_endpoint}`);
      else if (Ha(r.body)) {
        if (this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${r.status}`), i = r.body, i.error === u.INVALID_INSTANCE)
          return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."), null;
        this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${i.error}`), this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${i.error_description}`), this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"), s = [];
      } else
        return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"), null;
      this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."), n = ht(s, this.hostnameAndPort);
    } catch (r) {
      if (r instanceof O)
        this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.
Error: ${r.errorCode}
Error Description: ${r.errorMessage}`);
      else {
        const i = r;
        this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.
Error: ${i.name}
Error Description: ${i.message}`);
      }
      return null;
    }
    return n || (this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."), this.logger.verbose("Creating custom Authority for custom domain scenario."), n = x.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)), n;
  }
  /**
   * Helper function to determine if this host is included in the knownAuthorities config option
   */
  isInKnownAuthorities() {
    return this.authorityOptions.knownAuthorities.filter((t) => t && w.getDomainFromUrl(t).toLowerCase() === this.hostnameAndPort).length > 0;
  }
  /**
   * helper function to populate the authority based on azureCloudOptions
   * @param authorityString
   * @param azureCloudOptions
   */
  static generateAuthority(e, t) {
    let n;
    if (t && t.azureCloudInstance !== tn.None) {
      const o = t.tenant ? t.tenant : u.DEFAULT_COMMON_TENANT;
      n = `${t.azureCloudInstance}/${o}/`;
    }
    return n || e;
  }
  /**
   * Creates cloud discovery metadata object from a given host
   * @param host
   */
  static createCloudDiscoveryMetadataFromHost(e) {
    return {
      preferred_network: e,
      preferred_cache: e,
      aliases: [e]
    };
  }
  /**
   * helper function to generate environment from authority object
   */
  getPreferredCache() {
    if (this.managedIdentity)
      return u.DEFAULT_AUTHORITY_HOST;
    if (this.discoveryComplete())
      return this.metadata.preferred_cache;
    throw p(se);
  }
  /**
   * Returns whether or not the provided host is an alias of this authority instance
   * @param host
   */
  isAlias(e) {
    return this.metadata.aliases.indexOf(e) > -1;
  }
  /**
   * Returns whether or not the provided host is an alias of a known Microsoft authority for purposes of endpoint discovery
   * @param host
   */
  isAliasOfKnownMicrosoftAuthority(e) {
    return er.has(e);
  }
  /**
   * Checks whether the provided host is that of a public cloud authority
   *
   * @param authority string
   * @returns bool
   */
  static isPublicCloudAuthority(e) {
    return u.KNOWN_PUBLIC_CLOUDS.indexOf(e) >= 0;
  }
  /**
   * Rebuild the authority string with the region
   *
   * @param host string
   * @param region string
   */
  static buildRegionalAuthorityString(e, t, n) {
    const o = new w(e);
    o.validateAsUri();
    const r = o.getUrlComponents();
    let i = `${t}.${r.HostNameAndPort}`;
    this.isPublicCloudAuthority(r.HostNameAndPort) && (i = `${t}.${u.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`);
    const s = w.constructAuthorityUriFromObject({
      ...o.getUrlComponents(),
      HostNameAndPort: i
    }).urlString;
    return n ? `${s}?${n}` : s;
  }
  /**
   * Replace the endpoints in the metadata object with their regional equivalents.
   *
   * @param metadata OpenIdConfigResponse
   * @param azureRegion string
   */
  static replaceWithRegionalInformation(e, t) {
    const n = { ...e };
    return n.authorization_endpoint = x.buildRegionalAuthorityString(n.authorization_endpoint, t), n.token_endpoint = x.buildRegionalAuthorityString(n.token_endpoint, t), n.end_session_endpoint && (n.end_session_endpoint = x.buildRegionalAuthorityString(n.end_session_endpoint, t)), n;
  }
  /**
   * Transform CIAM_AUTHORIY as per the below rules:
   * If no path segments found and it is a CIAM authority (hostname ends with .ciamlogin.com), then transform it
   *
   * NOTE: The transformation path should go away once STS supports CIAM with the format: `tenantIdorDomain.ciamlogin.com`
   * `ciamlogin.com` can also change in the future and we should accommodate the same
   *
   * @param authority
   */
  static transformCIAMAuthority(e) {
    let t = e;
    const o = new w(e).getUrlComponents();
    if (o.PathSegments.length === 0 && o.HostNameAndPort.endsWith(u.CIAM_AUTH_URL)) {
      const r = o.HostNameAndPort.split(".")[0];
      t = `${t}${r}${u.AAD_TENANT_DOMAIN_SUFFIX}`;
    }
    return t;
  }
}
x.reservedTenantDomains = /* @__PURE__ */ new Set([
  "{tenant}",
  "{tenantid}",
  ge.COMMON,
  ge.CONSUMERS,
  ge.ORGANIZATIONS
]);
function Ka(a) {
  var o;
  const n = (o = new w(a).getUrlComponents().PathSegments.slice(-1)[0]) == null ? void 0 : o.toLowerCase();
  switch (n) {
    case ge.COMMON:
    case ge.ORGANIZATIONS:
    case ge.CONSUMERS:
      return;
    default:
      return n;
  }
}
function rr(a) {
  return a.endsWith(u.FORWARD_SLASH) ? a : `${a}${u.FORWARD_SLASH}`;
}
function xa(a) {
  const e = a.cloudDiscoveryMetadata;
  let t;
  if (e)
    try {
      t = JSON.parse(e);
    } catch {
      throw R(an);
    }
  return {
    canonicalAuthority: a.authority ? rr(a.authority) : void 0,
    knownAuthorities: a.knownAuthorities,
    cloudDiscoveryMetadata: t
  };
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
async function ir(a, e, t, n, o, r, i) {
  i == null || i.addQueueMeasurement(l.AuthorityFactoryCreateDiscoveredInstance, r);
  const s = x.transformCIAMAuthority(rr(a)), c = new x(s, e, t, n, o, r, i);
  try {
    return await m(c.resolveEndpointsAsync.bind(c), l.AuthorityResolveEndpointsAsync, o, i, r)(), c;
  } catch {
    throw p(se);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class me extends O {
  constructor(e, t, n, o, r) {
    super(e, t, n), this.name = "ServerError", this.errorNo = o, this.status = r, Object.setPrototypeOf(this, me.prototype);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class ee {
  /**
   * Prepares a RequestThumbprint to be stored as a key.
   * @param thumbprint
   */
  static generateThrottlingStorageKey(e) {
    return `${Fe.THROTTLING_PREFIX}.${JSON.stringify(e)}`;
  }
  /**
   * Performs necessary throttling checks before a network request.
   * @param cacheManager
   * @param thumbprint
   */
  static preProcess(e, t, n) {
    var i;
    const o = ee.generateThrottlingStorageKey(t), r = e.getThrottlingCache(o);
    if (r) {
      if (r.throttleTime < Date.now()) {
        e.removeItem(o, n);
        return;
      }
      throw new me(((i = r.errorCodes) == null ? void 0 : i.join(" ")) || u.EMPTY_STRING, r.errorMessage, r.subError);
    }
  }
  /**
   * Performs necessary throttling checks after a network request.
   * @param cacheManager
   * @param thumbprint
   * @param response
   */
  static postProcess(e, t, n, o) {
    if (ee.checkResponseStatus(n) || ee.checkResponseForRetryAfter(n)) {
      const r = {
        throttleTime: ee.calculateThrottleTime(parseInt(n.headers[F.RETRY_AFTER])),
        error: n.body.error,
        errorCodes: n.body.error_codes,
        errorMessage: n.body.error_description,
        subError: n.body.suberror
      };
      e.setThrottlingCache(ee.generateThrottlingStorageKey(t), r, o);
    }
  }
  /**
   * Checks a NetworkResponse object's status codes against 429 or 5xx
   * @param response
   */
  static checkResponseStatus(e) {
    return e.status === 429 || e.status >= 500 && e.status < 600;
  }
  /**
   * Checks a NetworkResponse object's RetryAfter header
   * @param response
   */
  static checkResponseForRetryAfter(e) {
    return e.headers ? e.headers.hasOwnProperty(F.RETRY_AFTER) && (e.status < 200 || e.status >= 300) : !1;
  }
  /**
   * Calculates the Unix-time value for a throttle to expire given throttleTime in seconds.
   * @param throttleTime
   */
  static calculateThrottleTime(e) {
    const t = e <= 0 ? 0 : e, n = Date.now() / 1e3;
    return Math.floor(Math.min(n + (t || Fe.DEFAULT_THROTTLE_TIME_SECONDS), n + Fe.DEFAULT_MAX_THROTTLE_TIME_SECONDS) * 1e3);
  }
  static removeThrottle(e, t, n, o) {
    const r = {
      clientId: t,
      authority: n.authority,
      scopes: n.scopes,
      homeAccountIdentifier: o,
      claims: n.claims,
      authenticationScheme: n.authenticationScheme,
      resourceRequestMethod: n.resourceRequestMethod,
      resourceRequestUri: n.resourceRequestUri,
      shrClaims: n.shrClaims,
      sshKid: n.sshKid
    }, i = this.generateThrottlingStorageKey(r);
    e.removeItem(i, n.correlationId);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class yt extends O {
  constructor(e, t, n) {
    super(e.errorCode, e.errorMessage, e.subError), Object.setPrototypeOf(this, yt.prototype), this.name = "NetworkError", this.error = e, this.httpStatus = t, this.responseHeaders = n;
  }
}
function eo(a, e, t) {
  return new yt(a, e, t);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class pn {
  constructor(e, t) {
    this.config = Wi(e), this.logger = new pe(this.config.loggerOptions, Uo, en), this.cryptoUtils = this.config.cryptoInterface, this.cacheManager = this.config.storageInterface, this.networkClient = this.config.networkInterface, this.serverTelemetryManager = this.config.serverTelemetryManager, this.authority = this.config.authOptions.authority, this.performanceClient = t;
  }
  /**
   * Creates default headers for requests to token endpoint
   */
  createTokenRequestHeaders(e) {
    const t = {};
    if (t[F.CONTENT_TYPE] = u.URL_FORM_CONTENT_TYPE, !this.config.systemOptions.preventCorsPreflight && e)
      switch (e.type) {
        case Q.HOME_ACCOUNT_ID:
          try {
            const n = Re(e.credential);
            t[F.CCS_HEADER] = `Oid:${n.uid}@${n.utid}`;
          } catch (n) {
            this.logger.verbose("Could not parse home account ID for CCS Header: " + n);
          }
          break;
        case Q.UPN:
          t[F.CCS_HEADER] = `UPN: ${e.credential}`;
          break;
      }
    return t;
  }
  /**
   * Http post to token endpoint
   * @param tokenEndpoint
   * @param queryString
   * @param headers
   * @param thumbprint
   */
  async executePostToTokenEndpoint(e, t, n, o, r, i) {
    var c;
    i && ((c = this.performanceClient) == null || c.addQueueMeasurement(i, r));
    const s = await this.sendPostRequest(o, e, { body: t, headers: n }, r);
    return this.config.serverTelemetryManager && s.status < 500 && s.status !== 429 && this.config.serverTelemetryManager.clearTelemetryCache(), s;
  }
  /**
   * Wraps sendPostRequestAsync with necessary preflight and postflight logic
   * @param thumbprint - Request thumbprint for throttling
   * @param tokenEndpoint - Endpoint to make the POST to
   * @param options - Body and Headers to include on the POST request
   * @param correlationId - CorrelationId for telemetry
   */
  async sendPostRequest(e, t, n, o) {
    var i, s, c;
    ee.preProcess(this.cacheManager, e, o);
    let r;
    try {
      r = await m(this.networkClient.sendPostRequestAsync.bind(this.networkClient), l.NetworkClientSendPostRequestAsync, this.logger, this.performanceClient, o)(t, n);
      const h = r.headers || {};
      (s = this.performanceClient) == null || s.addFields({
        refreshTokenSize: ((i = r.body.refresh_token) == null ? void 0 : i.length) || 0,
        httpVerToken: h[F.X_MS_HTTP_VERSION] || "",
        requestId: h[F.X_MS_REQUEST_ID] || ""
      }, o);
    } catch (h) {
      if (h instanceof yt) {
        const d = h.responseHeaders;
        throw d && ((c = this.performanceClient) == null || c.addFields({
          httpVerToken: d[F.X_MS_HTTP_VERSION] || "",
          requestId: d[F.X_MS_REQUEST_ID] || "",
          contentTypeHeader: d[F.CONTENT_TYPE] || void 0,
          contentLengthHeader: d[F.CONTENT_LENGTH] || void 0,
          httpStatus: h.httpStatus
        }, o)), h.error;
      }
      throw h instanceof O ? h : p(Io);
    }
    return ee.postProcess(this.cacheManager, e, r, o), r;
  }
  /**
   * Updates the authority object of the client. Endpoint discovery must be completed.
   * @param updatedAuthority
   */
  async updateAuthority(e, t) {
    var r;
    (r = this.performanceClient) == null || r.addQueueMeasurement(l.UpdateTokenEndpointAuthority, t);
    const n = `https://${e}/${this.authority.tenant}/`, o = await ir(n, this.networkClient, this.cacheManager, this.authority.options, this.logger, t, this.performanceClient);
    this.authority = o;
  }
  /**
   * Creates query string for the /token request
   * @param request
   */
  createTokenQueryParameters(e) {
    const t = new Ge(e.correlationId, this.performanceClient);
    return e.embeddedClientId && t.addBrokerParameters({
      brokerClientId: this.config.authOptions.clientId,
      brokerRedirectUri: this.config.authOptions.redirectUri
    }), e.tokenQueryParameters && t.addExtraQueryParameters(e.tokenQueryParameters), t.addCorrelationId(e.correlationId), t.createQueryString();
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const lt = "no_tokens_found", ar = "native_account_unavailable", mn = "refresh_token_expired", Ba = "interaction_required", Fa = "consent_required", Ga = "login_required", Tt = "bad_token";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const to = [
  Ba,
  Fa,
  Ga,
  Tt
], qa = [
  "message_only",
  "additional_action",
  "basic_action",
  "user_password_expired",
  "consent_required",
  "bad_token"
], $a = {
  [lt]: "No refresh token found in the cache. Please sign-in.",
  [ar]: "The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.",
  [mn]: "Refresh token has expired.",
  [Tt]: "Identity provider returned bad_token due to an expired or invalid refresh token. Please invoke an interactive API to resolve."
};
class X extends O {
  constructor(e, t, n, o, r, i, s, c) {
    super(e, t, n), Object.setPrototypeOf(this, X.prototype), this.timestamp = o || u.EMPTY_STRING, this.traceId = r || u.EMPTY_STRING, this.correlationId = i || u.EMPTY_STRING, this.claims = s || u.EMPTY_STRING, this.name = "InteractionRequiredAuthError", this.errorNo = c;
  }
}
function no(a, e, t) {
  const n = !!a && to.indexOf(a) > -1, o = !!t && qa.indexOf(t) > -1, r = !!e && to.some((i) => e.indexOf(i) > -1);
  return n || r || o;
}
function Dt(a) {
  return new X(a, $a[a]);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class J {
  /**
   * Appends user state with random guid, or returns random guid.
   * @param userState
   * @param randomGuid
   */
  static setRequestState(e, t, n) {
    const o = J.generateLibraryState(e, n);
    return t ? `${o}${u.RESOURCE_DELIM}${t}` : o;
  }
  /**
   * Generates the state value used by the common library.
   * @param randomGuid
   * @param cryptoObj
   */
  static generateLibraryState(e, t) {
    if (!e)
      throw p(Pt);
    const n = {
      id: e.createNewGuid()
    };
    t && (n.meta = t);
    const o = JSON.stringify(n);
    return e.base64Encode(o);
  }
  /**
   * Parses the state into the RequestStateObject, which contains the LibraryState info and the state passed by the user.
   * @param state
   * @param cryptoObj
   */
  static parseRequestState(e, t) {
    if (!e)
      throw p(Pt);
    if (!t)
      throw p(Pe);
    try {
      const n = t.split(u.RESOURCE_DELIM), o = n[0], r = n.length > 1 ? n.slice(1).join(u.RESOURCE_DELIM) : u.EMPTY_STRING, i = e.base64Decode(o), s = JSON.parse(i);
      return {
        userRequestState: r || u.EMPTY_STRING,
        libraryState: s
      };
    } catch {
      throw p(Pe);
    }
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const za = {
  SW: "sw"
};
class Ue {
  constructor(e, t) {
    this.cryptoUtils = e, this.performanceClient = t;
  }
  /**
   * Generates the req_cnf validated at the RP in the POP protocol for SHR parameters
   * and returns an object containing the keyid, the full req_cnf string and the req_cnf string hash
   * @param request
   * @returns
   */
  async generateCnf(e, t) {
    var r;
    (r = this.performanceClient) == null || r.addQueueMeasurement(l.PopTokenGenerateCnf, e.correlationId);
    const n = await m(this.generateKid.bind(this), l.PopTokenGenerateCnf, t, this.performanceClient, e.correlationId)(e), o = this.cryptoUtils.base64UrlEncode(JSON.stringify(n));
    return {
      kid: n.kid,
      reqCnfString: o
    };
  }
  /**
   * Generates key_id for a SHR token request
   * @param request
   * @returns
   */
  async generateKid(e) {
    var n;
    return (n = this.performanceClient) == null || n.addQueueMeasurement(l.PopTokenGenerateKid, e.correlationId), {
      kid: await this.cryptoUtils.getPublicKeyThumbprint(e),
      xms_ksl: za.SW
    };
  }
  /**
   * Signs the POP access_token with the local generated key-pair
   * @param accessToken
   * @param request
   * @returns
   */
  async signPopToken(e, t, n) {
    return this.signPayload(e, t, n);
  }
  /**
   * Utility function to generate the signed JWT for an access_token
   * @param payload
   * @param kid
   * @param request
   * @param claims
   * @returns
   */
  async signPayload(e, t, n, o) {
    const { resourceRequestMethod: r, resourceRequestUri: i, shrClaims: s, shrNonce: c, shrOptions: h } = n, d = i ? new w(i) : void 0, g = d == null ? void 0 : d.getUrlComponents();
    return this.cryptoUtils.signJwt({
      at: e,
      ts: re(),
      m: r == null ? void 0 : r.toUpperCase(),
      u: g == null ? void 0 : g.HostNameAndPort,
      nonce: c || this.cryptoUtils.createNewGuid(),
      p: g == null ? void 0 : g.AbsolutePath,
      q: g != null && g.QueryString ? [[], g.QueryString] : void 0,
      client_claims: s || void 0,
      ...o
    }, t, h, n.correlationId);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class Va {
  constructor(e, t) {
    this.cache = e, this.hasChanged = t;
  }
  /**
   * boolean which indicates the changes in cache
   */
  get cacheHasChanged() {
    return this.hasChanged;
  }
  /**
   * function to retrieve the token cache
   */
  get tokenCache() {
    return this.cache;
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
function Qa(a) {
  var n, o;
  const e = "code=", t = (n = a.error_uri) == null ? void 0 : n.lastIndexOf(e);
  return t && t >= 0 ? (o = a.error_uri) == null ? void 0 : o.substring(t + e.length) : void 0;
}
class ye {
  constructor(e, t, n, o, r, i, s) {
    this.clientId = e, this.cacheStorage = t, this.cryptoObj = n, this.logger = o, this.serializableCache = r, this.persistencePlugin = i, this.performanceClient = s;
  }
  /**
   * Function which validates server authorization code response.
   * @param serverResponseHash
   * @param requestState
   * @param cryptoObj
   */
  validateServerAuthorizationCodeResponse(e, t) {
    if (!e.state || !t)
      throw e.state ? p(rt, "Cached State") : p(rt, "Server State");
    let n, o;
    try {
      n = decodeURIComponent(e.state);
    } catch {
      throw p(Pe, e.state);
    }
    try {
      o = decodeURIComponent(t);
    } catch {
      throw p(Pe, e.state);
    }
    if (n !== o)
      throw p(So);
    if (e.error || e.error_description || e.suberror) {
      const r = Qa(e);
      throw no(e.error, e.error_description, e.suberror) ? new X(e.error || "", e.error_description, e.suberror, e.timestamp || "", e.trace_id || "", e.correlation_id || "", e.claims || "", r) : new me(e.error || "", e.error_description, e.suberror, r);
    }
  }
  /**
   * Function which validates server authorization token response.
   * @param serverResponse
   * @param refreshAccessToken
   */
  validateTokenResponse(e, t) {
    var n;
    if (e.error || e.error_description || e.suberror) {
      const o = `Error(s): ${e.error_codes || u.NOT_AVAILABLE} - Timestamp: ${e.timestamp || u.NOT_AVAILABLE} - Description: ${e.error_description || u.NOT_AVAILABLE} - Correlation ID: ${e.correlation_id || u.NOT_AVAILABLE} - Trace ID: ${e.trace_id || u.NOT_AVAILABLE}`, r = (n = e.error_codes) != null && n.length ? e.error_codes[0] : void 0, i = new me(e.error, o, e.suberror, r, e.status);
      if (t && e.status && e.status >= We.SERVER_ERROR_RANGE_START && e.status <= We.SERVER_ERROR_RANGE_END) {
        this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently unavailable and the access token is unable to be refreshed.
${i}`);
        return;
      } else if (t && e.status && e.status >= We.CLIENT_ERROR_RANGE_START && e.status <= We.CLIENT_ERROR_RANGE_END) {
        this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently available but is unable to refresh the access token.
${i}`);
        return;
      }
      throw no(e.error, e.error_description, e.suberror) ? new X(e.error, e.error_description, e.suberror, e.timestamp || u.EMPTY_STRING, e.trace_id || u.EMPTY_STRING, e.correlation_id || u.EMPTY_STRING, e.claims || u.EMPTY_STRING, r) : i;
    }
  }
  /**
   * Returns a constructed token response based on given string. Also manages the cache updates and cleanups.
   * @param serverTokenResponse
   * @param authority
   */
  async handleServerTokenResponse(e, t, n, o, r, i, s, c, h) {
    var E;
    (E = this.performanceClient) == null || E.addQueueMeasurement(l.HandleServerTokenResponse, e.correlation_id);
    let d;
    if (e.id_token) {
      if (d = Ae(e.id_token || u.EMPTY_STRING, this.cryptoObj.base64Decode), r && r.nonce && d.nonce !== r.nonce)
        throw p(vo);
      if (o.maxAge || o.maxAge === 0) {
        const v = d.auth_time;
        if (!v)
          throw p(jt);
        Lo(v, o.maxAge);
      }
    }
    this.homeAccountIdentifier = U.generateHomeAccountId(e.client_info || u.EMPTY_STRING, t.authorityType, this.logger, this.cryptoObj, d);
    let g;
    r && r.state && (g = J.parseRequestState(this.cryptoObj, r.state)), e.key_id = e.key_id || o.sshKid || void 0;
    const f = this.generateCacheRecord(e, t, n, o, d, i, r);
    let T;
    try {
      if (this.persistencePlugin && this.serializableCache && (this.logger.verbose("Persistence enabled, calling beforeCacheAccess"), T = new Va(this.serializableCache, !0), await this.persistencePlugin.beforeCacheAccess(T)), s && !c && f.account) {
        const v = f.account.generateAccountKey();
        if (!this.cacheStorage.getAccount(v, o.correlationId, this.logger))
          return this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"), await ye.generateAuthenticationResult(this.cryptoObj, t, f, !1, o, d, g, void 0, h);
      }
      await this.cacheStorage.saveCacheRecord(f, o.correlationId, o.storeInCache);
    } finally {
      this.persistencePlugin && this.serializableCache && T && (this.logger.verbose("Persistence enabled, calling afterCacheAccess"), await this.persistencePlugin.afterCacheAccess(T));
    }
    return ye.generateAuthenticationResult(this.cryptoObj, t, f, !1, o, d, g, e, h);
  }
  /**
   * Generates CacheRecord
   * @param serverTokenResponse
   * @param idTokenObj
   * @param authority
   */
  generateCacheRecord(e, t, n, o, r, i, s) {
    const c = t.getPreferredCache();
    if (!c)
      throw p(Xt);
    const h = Jo(r);
    let d, g;
    e.id_token && r && (d = pt(this.homeAccountIdentifier, c, e.id_token, this.clientId, h || ""), g = fn(
      this.cacheStorage,
      t,
      this.homeAccountIdentifier,
      this.cryptoObj.base64Decode,
      o.correlationId,
      r,
      e.client_info,
      c,
      h,
      s,
      void 0,
      // nativeAccountId
      this.logger
    ));
    let f = null;
    if (e.access_token) {
      const v = e.scope ? P.fromString(e.scope) : new P(o.scopes || []), M = (typeof e.expires_in == "string" ? parseInt(e.expires_in, 10) : e.expires_in) || 0, G = (typeof e.ext_expires_in == "string" ? parseInt(e.ext_expires_in, 10) : e.ext_expires_in) || 0, L = (typeof e.refresh_in == "string" ? parseInt(e.refresh_in, 10) : e.refresh_in) || void 0, de = n + M, Qe = de + G, Ye = L && L > 0 ? n + L : void 0;
      f = mt(this.homeAccountIdentifier, c, e.access_token, this.clientId, h || t.tenant || "", v.printScopes(), de, Qe, this.cryptoObj.base64Decode, Ye, e.token_type, i, e.key_id, o.claims, o.requestedClaimsHash);
    }
    let T = null;
    if (e.refresh_token) {
      let v;
      if (e.refresh_token_expires_in) {
        const M = typeof e.refresh_token_expires_in == "string" ? parseInt(e.refresh_token_expires_in, 10) : e.refresh_token_expires_in;
        v = n + M;
      }
      T = Ho(this.homeAccountIdentifier, c, e.refresh_token, this.clientId, e.foci, i, v);
    }
    let E = null;
    return e.foci && (E = {
      clientId: this.clientId,
      environment: c,
      familyId: e.foci
    }), {
      account: g,
      idToken: d,
      accessToken: f,
      refreshToken: T,
      appMetadata: E
    };
  }
  /**
   * Creates an @AuthenticationResult from @CacheRecord , @IdToken , and a boolean that states whether or not the result is from cache.
   *
   * Optionally takes a state string that is set as-is in the response.
   *
   * @param cacheRecord
   * @param idTokenObj
   * @param fromTokenCache
   * @param stateString
   */
  static async generateAuthenticationResult(e, t, n, o, r, i, s, c, h) {
    var de, Qe, Ye, Un, Ln;
    let d = u.EMPTY_STRING, g = [], f = null, T, E, v = u.EMPTY_STRING;
    if (n.accessToken) {
      if (n.accessToken.tokenType === k.POP && !r.popKid) {
        const oi = new Ue(e), { secret: ri, keyId: Hn } = n.accessToken;
        if (!Hn)
          throw p(Zt);
        d = await oi.signPopToken(ri, Hn, r);
      } else
        d = n.accessToken.secret;
      g = P.fromString(n.accessToken.target).asArray(), f = new Date(Number(n.accessToken.expiresOn) * 1e3), T = new Date(Number(n.accessToken.extendedExpiresOn) * 1e3), n.accessToken.refreshOn && (E = new Date(Number(n.accessToken.refreshOn) * 1e3));
    }
    n.appMetadata && (v = n.appMetadata.familyId === Be ? Be : "");
    const M = (i == null ? void 0 : i.oid) || (i == null ? void 0 : i.sub) || "", G = (i == null ? void 0 : i.tid) || "";
    c != null && c.spa_accountid && n.account && (n.account.nativeAccountId = c == null ? void 0 : c.spa_accountid);
    const L = n.account ? hn(
      n.account.getAccountInfo(),
      void 0,
      // tenantProfile optional
      i,
      (de = n.idToken) == null ? void 0 : de.secret
    ) : null;
    return {
      authority: t.canonicalAuthority,
      uniqueId: M,
      tenantId: G,
      scopes: g,
      account: L,
      idToken: ((Qe = n == null ? void 0 : n.idToken) == null ? void 0 : Qe.secret) || "",
      idTokenClaims: i || {},
      accessToken: d,
      fromCache: o,
      expiresOn: f,
      extExpiresOn: T,
      refreshOn: E,
      correlationId: r.correlationId,
      requestId: h || u.EMPTY_STRING,
      familyId: v,
      tokenType: ((Ye = n.accessToken) == null ? void 0 : Ye.tokenType) || u.EMPTY_STRING,
      state: s ? s.userRequestState : u.EMPTY_STRING,
      cloudGraphHostName: ((Un = n.account) == null ? void 0 : Un.cloudGraphHostName) || u.EMPTY_STRING,
      msGraphHost: ((Ln = n.account) == null ? void 0 : Ln.msGraphHost) || u.EMPTY_STRING,
      code: c == null ? void 0 : c.spa_code,
      fromNativeBroker: !1
    };
  }
}
function fn(a, e, t, n, o, r, i, s, c, h, d, g) {
  g == null || g.verbose("setCachedAccount called");
  const T = a.getAccountKeys().find((L) => L.startsWith(t));
  let E = null;
  T && (E = a.getAccount(T, o, g));
  const v = E || U.createAccount({
    homeAccountId: t,
    idTokenClaims: r,
    clientInfo: i,
    environment: s,
    cloudGraphHostName: h == null ? void 0 : h.cloud_graph_host_name,
    msGraphHost: h == null ? void 0 : h.msgraph_host,
    nativeAccountId: d
  }, e, n), M = v.tenantProfiles || [], G = c || v.realm;
  if (G && !M.find((L) => L.tenantId === G)) {
    const L = cn(t, v.localAccountId, G, r);
    M.push(L);
  }
  return v.tenantProfiles = M, v;
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
async function sr(a, e, t) {
  return typeof a == "string" ? a : a({
    clientId: e,
    tokenEndpoint: t
  });
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class cr extends pn {
  constructor(e, t) {
    var n;
    super(e, t), this.includeRedirectUri = !0, this.oidcDefaultScopes = (n = this.config.authOptions.authority.options.OIDCOptions) == null ? void 0 : n.defaultScopes;
  }
  /**
   * Creates the URL of the authorization request letting the user input credentials and consent to the
   * application. The URL target the /authorize endpoint of the authority configured in the
   * application object.
   *
   * Once the user inputs their credentials and consents, the authority will send a response to the redirect URI
   * sent in the request and should contain an authorization code, which can then be used to acquire tokens via
   * acquireToken(AuthorizationCodeRequest)
   * @param request
   */
  async getAuthCodeUrl(e) {
    var n;
    (n = this.performanceClient) == null || n.addQueueMeasurement(l.GetAuthCodeUrl, e.correlationId);
    const t = await m(this.createAuthCodeUrlQueryString.bind(this), l.AuthClientCreateQueryString, this.logger, this.performanceClient, e.correlationId)(e);
    return w.appendQueryString(this.authority.authorizationEndpoint, t);
  }
  /**
   * API to acquire a token in exchange of 'authorization_code` acquired by the user in the first leg of the
   * authorization_code_grant
   * @param request
   */
  async acquireToken(e, t) {
    var s, c;
    if ((s = this.performanceClient) == null || s.addQueueMeasurement(l.AuthClientAcquireToken, e.correlationId), !e.code)
      throw p(ko);
    const n = re(), o = await m(this.executeTokenRequest.bind(this), l.AuthClientExecuteTokenRequest, this.logger, this.performanceClient, e.correlationId)(this.authority, e), r = (c = o.headers) == null ? void 0 : c[F.X_MS_REQUEST_ID], i = new ye(this.config.authOptions.clientId, this.cacheManager, this.cryptoUtils, this.logger, this.config.serializableCache, this.config.persistencePlugin, this.performanceClient);
    return i.validateTokenResponse(o.body), m(i.handleServerTokenResponse.bind(i), l.HandleServerTokenResponse, this.logger, this.performanceClient, e.correlationId)(o.body, this.authority, n, e, t, void 0, void 0, void 0, r);
  }
  /**
   * Handles the hash fragment response from public client code request. Returns a code response used by
   * the client to exchange for a token in acquireToken.
   * @param hashFragment
   */
  handleFragmentResponse(e, t) {
    if (new ye(this.config.authOptions.clientId, this.cacheManager, this.cryptoUtils, this.logger, null, null).validateServerAuthorizationCodeResponse(e, t), !e.code)
      throw p(Po);
    return e;
  }
  /**
   * Used to log out the current user, and redirect the user to the postLogoutRedirectUri.
   * Default behaviour is to redirect the user to `window.location.href`.
   * @param authorityUri
   */
  getLogoutUri(e) {
    if (!e)
      throw R(qo);
    const t = this.createLogoutUrlQueryString(e);
    return w.appendQueryString(this.authority.endSessionEndpoint, t);
  }
  /**
   * Executes POST request to token endpoint
   * @param authority
   * @param request
   */
  async executeTokenRequest(e, t) {
    var h, d;
    (h = this.performanceClient) == null || h.addQueueMeasurement(l.AuthClientExecuteTokenRequest, t.correlationId);
    const n = this.createTokenQueryParameters(t), o = w.appendQueryString(e.tokenEndpoint, n), r = await m(this.createTokenRequestBody.bind(this), l.AuthClientCreateTokenRequestBody, this.logger, this.performanceClient, t.correlationId)(t);
    let i;
    if (t.clientInfo)
      try {
        const g = at(t.clientInfo, this.cryptoUtils.base64Decode);
        i = {
          credential: `${g.uid}${B.CLIENT_INFO_SEPARATOR}${g.utid}`,
          type: Q.HOME_ACCOUNT_ID
        };
      } catch (g) {
        this.logger.verbose("Could not parse client info for CCS Header: " + g);
      }
    const s = this.createTokenRequestHeaders(i || t.ccsCredential), c = {
      clientId: ((d = t.tokenBodyParameters) == null ? void 0 : d.clientId) || this.config.authOptions.clientId,
      authority: e.canonicalAuthority,
      scopes: t.scopes,
      claims: t.claims,
      authenticationScheme: t.authenticationScheme,
      resourceRequestMethod: t.resourceRequestMethod,
      resourceRequestUri: t.resourceRequestUri,
      shrClaims: t.shrClaims,
      sshKid: t.sshKid
    };
    return m(this.executePostToTokenEndpoint.bind(this), l.AuthorizationCodeClientExecutePostToTokenEndpoint, this.logger, this.performanceClient, t.correlationId)(o, r, s, c, t.correlationId, l.AuthorizationCodeClientExecutePostToTokenEndpoint);
  }
  /**
   * Generates a map for all the params to be sent to the service
   * @param request
   */
  async createTokenRequestBody(e) {
    var o, r;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.AuthClientCreateTokenRequestBody, e.correlationId);
    const t = new Ge(e.correlationId, this.performanceClient);
    if (t.addClientId(e.embeddedClientId || ((r = e.tokenBodyParameters) == null ? void 0 : r[Ce]) || this.config.authOptions.clientId), this.includeRedirectUri ? t.addRedirectUri(e.redirectUri) : ve.validateRedirectUri(e.redirectUri), t.addScopes(e.scopes, !0, this.oidcDefaultScopes), t.addAuthorizationCode(e.code), t.addLibraryInfo(this.config.libraryInfo), t.addApplicationTelemetry(this.config.telemetry.application), t.addThrottling(), this.serverTelemetryManager && !Lt(this.config) && t.addServerTelemetry(this.serverTelemetryManager), e.codeVerifier && t.addCodeVerifier(e.codeVerifier), this.config.clientCredentials.clientSecret && t.addClientSecret(this.config.clientCredentials.clientSecret), this.config.clientCredentials.clientAssertion) {
      const i = this.config.clientCredentials.clientAssertion;
      t.addClientAssertion(await sr(i.assertion, this.config.authOptions.clientId, e.resourceRequestUri)), t.addClientAssertionType(i.assertionType);
    }
    if (t.addGrantType(fo.AUTHORIZATION_CODE_GRANT), t.addClientInfo(), e.authenticationScheme === k.POP) {
      const i = new Ue(this.cryptoUtils, this.performanceClient);
      let s;
      e.popKid ? s = this.cryptoUtils.encodeKid(e.popKid) : s = (await m(i.generateCnf.bind(i), l.PopTokenGenerateCnf, this.logger, this.performanceClient, e.correlationId)(e, this.logger)).reqCnfString, t.addPopToken(s);
    } else if (e.authenticationScheme === k.SSH)
      if (e.sshJwk)
        t.addSshJwk(e.sshJwk);
      else
        throw R(ft);
    (!j.isEmptyObj(e.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) && t.addClaims(e.claims, this.config.authOptions.clientCapabilities);
    let n;
    if (e.clientInfo)
      try {
        const i = at(e.clientInfo, this.cryptoUtils.base64Decode);
        n = {
          credential: `${i.uid}${B.CLIENT_INFO_SEPARATOR}${i.utid}`,
          type: Q.HOME_ACCOUNT_ID
        };
      } catch (i) {
        this.logger.verbose("Could not parse client info for CCS Header: " + i);
      }
    else
      n = e.ccsCredential;
    if (this.config.systemOptions.preventCorsPreflight && n)
      switch (n.type) {
        case Q.HOME_ACCOUNT_ID:
          try {
            const i = Re(n.credential);
            t.addCcsOid(i);
          } catch (i) {
            this.logger.verbose("Could not parse home account ID for CCS Header: " + i);
          }
          break;
        case Q.UPN:
          t.addCcsUpn(n.credential);
          break;
      }
    return e.embeddedClientId && t.addBrokerParameters({
      brokerClientId: this.config.authOptions.clientId,
      brokerRedirectUri: this.config.authOptions.redirectUri
    }), e.tokenBodyParameters && t.addExtraQueryParameters(e.tokenBodyParameters), e.enableSpaAuthorizationCode && (!e.tokenBodyParameters || !e.tokenBodyParameters[Zn]) && t.addExtraQueryParameters({
      [Zn]: "1"
    }), t.createQueryString();
  }
  /**
   * This API validates the `AuthorizationCodeUrlRequest` and creates a URL
   * @param request
   */
  async createAuthCodeUrlQueryString(e) {
    var r, i;
    const t = e.correlationId || this.config.cryptoInterface.createNewGuid();
    (r = this.performanceClient) == null || r.addQueueMeasurement(l.AuthClientCreateQueryString, t);
    const n = new Ge(t, this.performanceClient);
    n.addClientId(e.embeddedClientId || ((i = e.extraQueryParameters) == null ? void 0 : i[Ce]) || this.config.authOptions.clientId);
    const o = [
      ...e.scopes || [],
      ...e.extraScopesToConsent || []
    ];
    if (n.addScopes(o, !0, this.oidcDefaultScopes), n.addRedirectUri(e.redirectUri), n.addCorrelationId(t), n.addResponseMode(e.responseMode), n.addResponseTypeCode(), n.addLibraryInfo(this.config.libraryInfo), Lt(this.config) || n.addApplicationTelemetry(this.config.telemetry.application), n.addClientInfo(), e.codeChallenge && e.codeChallengeMethod && n.addCodeChallengeParams(e.codeChallenge, e.codeChallengeMethod), e.prompt && n.addPrompt(e.prompt), e.domainHint && n.addDomainHint(e.domainHint), e.prompt !== D.SELECT_ACCOUNT)
      if (e.sid && e.prompt === D.NONE)
        this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"), n.addSid(e.sid);
      else if (e.account) {
        const s = this.extractAccountSid(e.account);
        let c = this.extractLoginHint(e.account);
        if (c && e.domainHint && (this.logger.warning('AuthorizationCodeClient.createAuthCodeUrlQueryString: "domainHint" param is set, skipping opaque "login_hint" claim. Please consider not passing domainHint'), c = null), c) {
          this.logger.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"), n.addLoginHint(c);
          try {
            const h = Re(e.account.homeAccountId);
            n.addCcsOid(h);
          } catch {
            this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header");
          }
        } else if (s && e.prompt === D.NONE) {
          this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"), n.addSid(s);
          try {
            const h = Re(e.account.homeAccountId);
            n.addCcsOid(h);
          } catch {
            this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header");
          }
        } else if (e.loginHint)
          this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"), n.addLoginHint(e.loginHint), n.addCcsUpn(e.loginHint);
        else if (e.account.username) {
          this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"), n.addLoginHint(e.account.username);
          try {
            const h = Re(e.account.homeAccountId);
            n.addCcsOid(h);
          } catch {
            this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header");
          }
        }
      } else e.loginHint && (this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"), n.addLoginHint(e.loginHint), n.addCcsUpn(e.loginHint));
    else
      this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");
    if (e.nonce && n.addNonce(e.nonce), e.state && n.addState(e.state), (e.claims || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) && n.addClaims(e.claims, this.config.authOptions.clientCapabilities), e.embeddedClientId && n.addBrokerParameters({
      brokerClientId: this.config.authOptions.clientId,
      brokerRedirectUri: this.config.authOptions.redirectUri
    }), this.addExtraQueryParams(e, n), e.nativeBroker && (n.addNativeBroker(), e.authenticationScheme === k.POP)) {
      const s = new Ue(this.cryptoUtils);
      let c;
      e.popKid ? c = this.cryptoUtils.encodeKid(e.popKid) : c = (await m(s.generateCnf.bind(s), l.PopTokenGenerateCnf, this.logger, this.performanceClient, e.correlationId)(e, this.logger)).reqCnfString, n.addPopToken(c);
    }
    return n.createQueryString();
  }
  /**
   * This API validates the `EndSessionRequest` and creates a URL
   * @param request
   */
  createLogoutUrlQueryString(e) {
    const t = new Ge(e.correlationId, this.performanceClient);
    return e.postLogoutRedirectUri && t.addPostLogoutRedirectUri(e.postLogoutRedirectUri), e.correlationId && t.addCorrelationId(e.correlationId), e.idTokenHint && t.addIdTokenHint(e.idTokenHint), e.state && t.addState(e.state), e.logoutHint && t.addLogoutHint(e.logoutHint), this.addExtraQueryParams(e, t), t.createQueryString();
  }
  addExtraQueryParams(e, t) {
    !(e.extraQueryParameters && e.extraQueryParameters.hasOwnProperty("instance_aware")) && this.config.authOptions.instanceAware && (e.extraQueryParameters = e.extraQueryParameters || {}, e.extraQueryParameters.instance_aware = "true"), e.extraQueryParameters && t.addExtraQueryParameters(e.extraQueryParameters);
  }
  /**
   * Helper to get sid from account. Returns null if idTokenClaims are not present or sid is not present.
   * @param account
   */
  extractAccountSid(e) {
    var t;
    return ((t = e.idTokenClaims) == null ? void 0 : t.sid) || null;
  }
  extractLoginHint(e) {
    var t;
    return ((t = e.idTokenClaims) == null ? void 0 : t.login_hint) || null;
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Ya = 300;
class Kt extends pn {
  constructor(e, t) {
    super(e, t);
  }
  async acquireToken(e) {
    var i, s;
    (i = this.performanceClient) == null || i.addQueueMeasurement(l.RefreshTokenClientAcquireToken, e.correlationId);
    const t = re(), n = await m(this.executeTokenRequest.bind(this), l.RefreshTokenClientExecuteTokenRequest, this.logger, this.performanceClient, e.correlationId)(e, this.authority), o = (s = n.headers) == null ? void 0 : s[F.X_MS_REQUEST_ID], r = new ye(this.config.authOptions.clientId, this.cacheManager, this.cryptoUtils, this.logger, this.config.serializableCache, this.config.persistencePlugin);
    return r.validateTokenResponse(n.body), m(r.handleServerTokenResponse.bind(r), l.HandleServerTokenResponse, this.logger, this.performanceClient, e.correlationId)(n.body, this.authority, t, e, void 0, void 0, !0, e.forceCache, o);
  }
  /**
   * Gets cached refresh token and attaches to request, then calls acquireToken API
   * @param request
   */
  async acquireTokenByRefreshToken(e) {
    var n;
    if (!e)
      throw R(Go);
    if ((n = this.performanceClient) == null || n.addQueueMeasurement(l.RefreshTokenClientAcquireTokenByRefreshToken, e.correlationId), !e.account)
      throw p(Jt);
    if (this.cacheManager.isAppMetadataFOCI(e.account.environment))
      try {
        return await m(this.acquireTokenWithCachedRefreshToken.bind(this), l.RefreshTokenClientAcquireTokenWithCachedRefreshToken, this.logger, this.performanceClient, e.correlationId)(e, !0);
      } catch (o) {
        const r = o instanceof X && o.errorCode === lt, i = o instanceof me && o.errorCode === xn.INVALID_GRANT_ERROR && o.subError === xn.CLIENT_MISMATCH_ERROR;
        if (r || i)
          return m(this.acquireTokenWithCachedRefreshToken.bind(this), l.RefreshTokenClientAcquireTokenWithCachedRefreshToken, this.logger, this.performanceClient, e.correlationId)(e, !1);
        throw o;
      }
    return m(this.acquireTokenWithCachedRefreshToken.bind(this), l.RefreshTokenClientAcquireTokenWithCachedRefreshToken, this.logger, this.performanceClient, e.correlationId)(e, !1);
  }
  /**
   * makes a network call to acquire tokens by exchanging RefreshToken available in userCache; throws if refresh token is not cached
   * @param request
   */
  async acquireTokenWithCachedRefreshToken(e, t) {
    var r;
    (r = this.performanceClient) == null || r.addQueueMeasurement(l.RefreshTokenClientAcquireTokenWithCachedRefreshToken, e.correlationId);
    const n = Ee(this.cacheManager.getRefreshToken.bind(this.cacheManager), l.CacheManagerGetRefreshToken, this.logger, this.performanceClient, e.correlationId)(e.account, t, e.correlationId, void 0, this.performanceClient);
    if (!n)
      throw Dt(lt);
    if (n.expiresOn && Ut(n.expiresOn, e.refreshTokenExpirationOffsetSeconds || Ya))
      throw Dt(mn);
    const o = {
      ...e,
      refreshToken: n.secret,
      authenticationScheme: e.authenticationScheme || k.BEARER,
      ccsCredential: {
        credential: e.account.homeAccountId,
        type: Q.HOME_ACCOUNT_ID
      }
    };
    try {
      return await m(this.acquireToken.bind(this), l.RefreshTokenClientAcquireToken, this.logger, this.performanceClient, e.correlationId)(o);
    } catch (i) {
      if (i instanceof X && i.subError === Tt) {
        this.logger.verbose("acquireTokenWithRefreshToken: bad refresh token, removing from cache");
        const s = ke(n);
        this.cacheManager.removeRefreshToken(s, e.correlationId);
      }
      throw i;
    }
  }
  /**
   * Constructs the network message and makes a NW call to the underlying secure token service
   * @param request
   * @param authority
   */
  async executeTokenRequest(e, t) {
    var c, h;
    (c = this.performanceClient) == null || c.addQueueMeasurement(l.RefreshTokenClientExecuteTokenRequest, e.correlationId);
    const n = this.createTokenQueryParameters(e), o = w.appendQueryString(t.tokenEndpoint, n), r = await m(this.createTokenRequestBody.bind(this), l.RefreshTokenClientCreateTokenRequestBody, this.logger, this.performanceClient, e.correlationId)(e), i = this.createTokenRequestHeaders(e.ccsCredential), s = {
      clientId: ((h = e.tokenBodyParameters) == null ? void 0 : h.clientId) || this.config.authOptions.clientId,
      authority: t.canonicalAuthority,
      scopes: e.scopes,
      claims: e.claims,
      authenticationScheme: e.authenticationScheme,
      resourceRequestMethod: e.resourceRequestMethod,
      resourceRequestUri: e.resourceRequestUri,
      shrClaims: e.shrClaims,
      sshKid: e.sshKid
    };
    return m(this.executePostToTokenEndpoint.bind(this), l.RefreshTokenClientExecutePostToTokenEndpoint, this.logger, this.performanceClient, e.correlationId)(o, r, i, s, e.correlationId, l.RefreshTokenClientExecutePostToTokenEndpoint);
  }
  /**
   * Helper function to create the token request body
   * @param request
   */
  async createTokenRequestBody(e) {
    var o, r, i;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.RefreshTokenClientCreateTokenRequestBody, e.correlationId);
    const t = e.correlationId, n = new Ge(t, this.performanceClient);
    if (n.addClientId(e.embeddedClientId || ((r = e.tokenBodyParameters) == null ? void 0 : r[Ce]) || this.config.authOptions.clientId), e.redirectUri && n.addRedirectUri(e.redirectUri), n.addScopes(e.scopes, !0, (i = this.config.authOptions.authority.options.OIDCOptions) == null ? void 0 : i.defaultScopes), n.addGrantType(fo.REFRESH_TOKEN_GRANT), n.addClientInfo(), n.addLibraryInfo(this.config.libraryInfo), n.addApplicationTelemetry(this.config.telemetry.application), n.addThrottling(), this.serverTelemetryManager && !Lt(this.config) && n.addServerTelemetry(this.serverTelemetryManager), n.addRefreshToken(e.refreshToken), this.config.clientCredentials.clientSecret && n.addClientSecret(this.config.clientCredentials.clientSecret), this.config.clientCredentials.clientAssertion) {
      const s = this.config.clientCredentials.clientAssertion;
      n.addClientAssertion(await sr(s.assertion, this.config.authOptions.clientId, e.resourceRequestUri)), n.addClientAssertionType(s.assertionType);
    }
    if (e.authenticationScheme === k.POP) {
      const s = new Ue(this.cryptoUtils, this.performanceClient);
      let c;
      e.popKid ? c = this.cryptoUtils.encodeKid(e.popKid) : c = (await m(s.generateCnf.bind(s), l.PopTokenGenerateCnf, this.logger, this.performanceClient, e.correlationId)(e, this.logger)).reqCnfString, n.addPopToken(c);
    } else if (e.authenticationScheme === k.SSH)
      if (e.sshJwk)
        n.addSshJwk(e.sshJwk);
      else
        throw R(ft);
    if ((!j.isEmptyObj(e.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) && n.addClaims(e.claims, this.config.authOptions.clientCapabilities), this.config.systemOptions.preventCorsPreflight && e.ccsCredential)
      switch (e.ccsCredential.type) {
        case Q.HOME_ACCOUNT_ID:
          try {
            const s = Re(e.ccsCredential.credential);
            n.addCcsOid(s);
          } catch (s) {
            this.logger.verbose("Could not parse home account ID for CCS Header: " + s);
          }
          break;
        case Q.UPN:
          n.addCcsUpn(e.ccsCredential.credential);
          break;
      }
    return e.embeddedClientId && n.addBrokerParameters({
      brokerClientId: this.config.authOptions.clientId,
      brokerRedirectUri: this.config.authOptions.redirectUri
    }), e.tokenBodyParameters && n.addExtraQueryParameters(e.tokenBodyParameters), n.createQueryString();
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class Wa extends pn {
  constructor(e, t) {
    super(e, t);
  }
  /**
   * Retrieves a token from cache if it is still valid, or uses the cached refresh token to renew
   * the given token and returns the renewed token
   * @param request
   */
  async acquireToken(e) {
    var t;
    try {
      const [n, o] = await this.acquireCachedToken({
        ...e,
        scopes: (t = e.scopes) != null && t.length ? e.scopes : [...De]
      });
      return o === ue.PROACTIVELY_REFRESHED && (this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'. It's not expired, but must be refreshed."), new Kt(this.config, this.performanceClient).acquireTokenByRefreshToken(e).catch(() => {
      })), n;
    } catch (n) {
      if (n instanceof gt && n.errorCode === ce)
        return new Kt(this.config, this.performanceClient).acquireTokenByRefreshToken(e);
      throw n;
    }
  }
  /**
   * Retrieves token from cache or throws an error if it must be refreshed.
   * @param request
   */
  async acquireCachedToken(e) {
    var c;
    (c = this.performanceClient) == null || c.addQueueMeasurement(l.SilentFlowClientAcquireCachedToken, e.correlationId);
    let t = ue.NOT_APPLICABLE;
    if (e.forceRefresh || !this.config.cacheOptions.claimsBasedCachingEnabled && !j.isEmptyObj(e.claims))
      throw this.setCacheOutcome(ue.FORCE_REFRESH_OR_CLAIMS, e.correlationId), p(ce);
    if (!e.account)
      throw p(Jt);
    const n = e.account.tenantId || Ka(e.authority), o = this.cacheManager.getTokenKeys(), r = this.cacheManager.getAccessToken(e.account, e, o, n, this.performanceClient);
    if (r) {
      if (vi(r.cachedAt) || Ut(r.expiresOn, this.config.systemOptions.tokenRenewalOffsetSeconds))
        throw this.setCacheOutcome(ue.CACHED_ACCESS_TOKEN_EXPIRED, e.correlationId), p(ce);
      r.refreshOn && Ut(r.refreshOn, 0) && (t = ue.PROACTIVELY_REFRESHED);
    } else throw this.setCacheOutcome(ue.NO_CACHED_ACCESS_TOKEN, e.correlationId), p(ce);
    const i = e.authority || this.authority.getPreferredCache(), s = {
      account: this.cacheManager.readAccountFromCache(e.account, e.correlationId),
      accessToken: r,
      idToken: this.cacheManager.getIdToken(e.account, e.correlationId, o, n, this.performanceClient),
      refreshToken: null,
      appMetadata: this.cacheManager.readAppMetadataFromCache(i)
    };
    return this.setCacheOutcome(t, e.correlationId), this.config.serverTelemetryManager && this.config.serverTelemetryManager.incrementCacheHits(), [
      await m(this.generateResultFromCacheRecord.bind(this), l.SilentFlowClientGenerateResultFromCacheRecord, this.logger, this.performanceClient, e.correlationId)(s, e),
      t
    ];
  }
  setCacheOutcome(e, t) {
    var n, o;
    (n = this.serverTelemetryManager) == null || n.setCacheOutcome(e), (o = this.performanceClient) == null || o.addFields({
      cacheOutcome: e
    }, t), e !== ue.NOT_APPLICABLE && this.logger.info(`Token refresh is required due to cache outcome: ${e}`);
  }
  /**
   * Helper function to build response object from the CacheRecord
   * @param cacheRecord
   */
  async generateResultFromCacheRecord(e, t) {
    var o;
    (o = this.performanceClient) == null || o.addQueueMeasurement(l.SilentFlowClientGenerateResultFromCacheRecord, t.correlationId);
    let n;
    if (e.idToken && (n = Ae(e.idToken.secret, this.config.cryptoInterface.base64Decode)), t.maxAge || t.maxAge === 0) {
      const r = n == null ? void 0 : n.auth_time;
      if (!r)
        throw p(jt);
      Lo(r, t.maxAge);
    }
    return ye.generateAuthenticationResult(this.cryptoUtils, this.authority, e, !0, t, n);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const ja = {
  sendGetRequestAsync: () => Promise.reject(p(S)),
  sendPostRequestAsync: () => Promise.reject(p(S))
};
/*! @azure/msal-common v14.16.1 2025-08-05 */
const oo = ",", hr = "|";
function Ja(a) {
  const { skus: e, libraryName: t, libraryVersion: n, extensionName: o, extensionVersion: r } = a, i = /* @__PURE__ */ new Map([
    [0, [t, n]],
    [2, [o, r]]
  ]);
  let s = [];
  if (e != null && e.length) {
    if (s = e.split(oo), s.length < 4)
      return e;
  } else
    s = Array.from({ length: 4 }, () => hr);
  return i.forEach((c, h) => {
    var d, g;
    c.length === 2 && ((d = c[0]) != null && d.length) && ((g = c[1]) != null && g.length) && Xa({
      skuArr: s,
      index: h,
      skuName: c[0],
      skuVersion: c[1]
    });
  }), s.join(oo);
}
function Xa(a) {
  const { skuArr: e, index: t, skuName: n, skuVersion: o } = a;
  t >= e.length || (e[t] = [n, o].join(hr));
}
class qe {
  constructor(e, t) {
    this.cacheOutcome = ue.NOT_APPLICABLE, this.cacheManager = t, this.apiId = e.apiId, this.correlationId = e.correlationId, this.wrapperSKU = e.wrapperSKU || u.EMPTY_STRING, this.wrapperVer = e.wrapperVer || u.EMPTY_STRING, this.telemetryCacheKey = K.CACHE_KEY + B.CACHE_KEY_SEPARATOR + e.clientId;
  }
  /**
   * API to add MSER Telemetry to request
   */
  generateCurrentRequestHeaderValue() {
    const e = `${this.apiId}${K.VALUE_SEPARATOR}${this.cacheOutcome}`, t = [this.wrapperSKU, this.wrapperVer], n = this.getNativeBrokerErrorCode();
    n != null && n.length && t.push(`broker_error=${n}`);
    const o = t.join(K.VALUE_SEPARATOR), r = this.getRegionDiscoveryFields(), i = [
      e,
      r
    ].join(K.VALUE_SEPARATOR);
    return [
      K.SCHEMA_VERSION,
      i,
      o
    ].join(K.CATEGORY_SEPARATOR);
  }
  /**
   * API to add MSER Telemetry for the last failed request
   */
  generateLastRequestHeaderValue() {
    const e = this.getLastRequests(), t = qe.maxErrorsToSend(e), n = e.failedRequests.slice(0, 2 * t).join(K.VALUE_SEPARATOR), o = e.errors.slice(0, t).join(K.VALUE_SEPARATOR), r = e.errors.length, i = t < r ? K.OVERFLOW_TRUE : K.OVERFLOW_FALSE, s = [r, i].join(K.VALUE_SEPARATOR);
    return [
      K.SCHEMA_VERSION,
      e.cacheHits,
      n,
      o,
      s
    ].join(K.CATEGORY_SEPARATOR);
  }
  /**
   * API to cache token failures for MSER data capture
   * @param error
   */
  cacheFailedRequest(e) {
    const t = this.getLastRequests();
    t.errors.length >= K.MAX_CACHED_ERRORS && (t.failedRequests.shift(), t.failedRequests.shift(), t.errors.shift()), t.failedRequests.push(this.apiId, this.correlationId), e instanceof Error && e && e.toString() ? e instanceof O ? e.subError ? t.errors.push(e.subError) : e.errorCode ? t.errors.push(e.errorCode) : t.errors.push(e.toString()) : t.errors.push(e.toString()) : t.errors.push(K.UNKNOWN_ERROR), this.cacheManager.setServerTelemetry(this.telemetryCacheKey, t, this.correlationId);
  }
  /**
   * Update server telemetry cache entry by incrementing cache hit counter
   */
  incrementCacheHits() {
    const e = this.getLastRequests();
    return e.cacheHits += 1, this.cacheManager.setServerTelemetry(this.telemetryCacheKey, e, this.correlationId), e.cacheHits;
  }
  /**
   * Get the server telemetry entity from cache or initialize a new one
   */
  getLastRequests() {
    const e = {
      failedRequests: [],
      errors: [],
      cacheHits: 0
    };
    return this.cacheManager.getServerTelemetry(this.telemetryCacheKey) || e;
  }
  /**
   * Remove server telemetry cache entry
   */
  clearTelemetryCache() {
    const e = this.getLastRequests(), t = qe.maxErrorsToSend(e), n = e.errors.length;
    if (t === n)
      this.cacheManager.removeItem(this.telemetryCacheKey, this.correlationId);
    else {
      const o = {
        failedRequests: e.failedRequests.slice(t * 2),
        errors: e.errors.slice(t),
        cacheHits: 0
      };
      this.cacheManager.setServerTelemetry(this.telemetryCacheKey, o, this.correlationId);
    }
  }
  /**
   * Returns the maximum number of errors that can be flushed to the server in the next network request
   * @param serverTelemetryEntity
   */
  static maxErrorsToSend(e) {
    let t, n = 0, o = 0;
    const r = e.errors.length;
    for (t = 0; t < r; t++) {
      const i = e.failedRequests[2 * t] || u.EMPTY_STRING, s = e.failedRequests[2 * t + 1] || u.EMPTY_STRING, c = e.errors[t] || u.EMPTY_STRING;
      if (o += i.toString().length + s.toString().length + c.length + 3, o < K.MAX_LAST_HEADER_BYTES)
        n += 1;
      else
        break;
    }
    return n;
  }
  /**
   * Get the region discovery fields
   *
   * @returns string
   */
  getRegionDiscoveryFields() {
    const e = [];
    return e.push(this.regionUsed || u.EMPTY_STRING), e.push(this.regionSource || u.EMPTY_STRING), e.push(this.regionOutcome || u.EMPTY_STRING), e.join(",");
  }
  /**
   * Update the region discovery metadata
   *
   * @param regionDiscoveryMetadata
   * @returns void
   */
  updateRegionDiscoveryMetadata(e) {
    this.regionUsed = e.region_used, this.regionSource = e.region_source, this.regionOutcome = e.region_outcome;
  }
  /**
   * Set cache outcome
   */
  setCacheOutcome(e) {
    this.cacheOutcome = e;
  }
  setNativeBrokerErrorCode(e) {
    const t = this.getLastRequests();
    t.nativeBrokerErrorCode = e, this.cacheManager.setServerTelemetry(this.telemetryCacheKey, t, this.correlationId);
  }
  getNativeBrokerErrorCode() {
    return this.getLastRequests().nativeBrokerErrorCode;
  }
  clearNativeBrokerErrorCode() {
    const e = this.getLastRequests();
    delete e.nativeBrokerErrorCode, this.cacheManager.setServerTelemetry(this.telemetryCacheKey, e, this.correlationId);
  }
  static makeExtraSkuString(e) {
    return Ja(e);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
const lr = "missing_kid_error", dr = "missing_alg_error";
/*! @azure/msal-common v14.16.1 2025-08-05 */
const Za = {
  [lr]: "The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.",
  [dr]: "The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."
};
class Cn extends O {
  constructor(e, t) {
    super(e, t), this.name = "JoseHeaderError", Object.setPrototypeOf(this, Cn.prototype);
  }
}
function ro(a) {
  return new Cn(a, Za[a]);
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class yn {
  constructor(e) {
    this.typ = e.typ, this.alg = e.alg, this.kid = e.kid;
  }
  /**
   * Builds SignedHttpRequest formatted JOSE Header from the
   * JOSE Header options provided or previously set on the object and returns
   * the stringified header object.
   * Throws if keyId or algorithm aren't provided since they are required for Access Token Binding.
   * @param shrHeaderOptions
   * @returns
   */
  static getShrHeaderString(e) {
    if (!e.kid)
      throw ro(lr);
    if (!e.alg)
      throw ro(dr);
    const t = new yn({
      // Access Token PoP headers must have type pop, but the type header can be overriden for special cases
      typ: e.typ || si.Pop,
      kid: e.kid,
      alg: e.alg
    });
    return JSON.stringify(t);
  }
}
/*! @azure/msal-common v14.16.1 2025-08-05 */
class io {
  startMeasurement() {
  }
  endMeasurement() {
  }
  flushMeasurement() {
    return null;
  }
}
class es {
  generateId() {
    return "callback-id";
  }
  startMeasurement(e, t) {
    return {
      end: () => null,
      discard: () => {
      },
      add: () => {
      },
      increment: () => {
      },
      event: {
        eventId: this.generateId(),
        status: Da.InProgress,
        authority: "",
        libraryName: "",
        libraryVersion: "",
        clientId: "",
        name: e,
        startTimeMs: Date.now(),
        correlationId: t || ""
      },
      measurement: new io()
    };
  }
  startPerformanceMeasurement() {
    return new io();
  }
  calculateQueuedTime() {
    return 0;
  }
  addQueueMeasurement() {
  }
  setPreQueueTime() {
  }
  endMeasurement() {
    return null;
  }
  discardMeasurements() {
  }
  removePerformanceCallback() {
    return !0;
  }
  addPerformanceCallback() {
    return "";
  }
  emitEvents() {
  }
  addFields() {
  }
  incrementFields() {
  }
  cacheEventByCorrelationId() {
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const Tn = "pkce_not_created", xt = "crypto_nonexistent", It = "empty_navigate_uri", ur = "hash_empty_error", In = "no_state_in_hash", gr = "hash_does_not_contain_known_properties", pr = "unable_to_parse_state", mr = "state_interaction_type_mismatch", fr = "interaction_in_progress", Cr = "popup_window_error", yr = "empty_window_error", Te = "user_cancelled", ts = "monitor_popup_timeout", Tr = "monitor_window_timeout", Ir = "redirect_in_iframe", Ar = "block_iframe_reload", Er = "block_nested_popups", ns = "iframe_closed_prematurely", At = "silent_logout_unsupported", Sr = "no_account_error", os = "silent_prompt_value_error", vr = "no_token_request_cache_error", wr = "unable_to_parse_token_request_cache_error", An = "no_cached_authority_error", rs = "auth_request_not_set_error", is = "invalid_cache_type", Et = "non_browser_environment", we = "database_not_open", dt = "no_network_connectivity", _r = "post_request_failed", kr = "get_request_failed", Bt = "failed_to_parse_response", Rr = "unable_to_load_token", En = "crypto_key_not_found", br = "auth_code_required", Or = "auth_code_or_nativeAccountId_required", Nr = "spa_code_and_nativeAccountId_present", Sn = "database_unavailable", Pr = "unable_to_acquire_token_from_native_platform", Mr = "native_handshake_timeout", Ur = "native_extension_not_installed", ze = "native_connection_not_established", Lr = "uninitialized_public_client_application", Hr = "native_prompt_not_supported", Dr = "invalid_base64_string", Kr = "invalid_pop_token_request", xr = "failed_to_build_headers", Br = "failed_to_parse_headers";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const ae = "For more visit: aka.ms/msaljs/browser-errors", as = {
  [Tn]: "The PKCE code challenge and verifier could not be generated.",
  [xt]: "The crypto object or function is not available.",
  [It]: "Navigation URI is empty. Please check stack trace for more info.",
  [ur]: `Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. ${ae}`,
  [In]: "Hash does not contain state. Please verify that the request originated from msal.",
  [gr]: `Hash does not contain known properites. Please verify that your redirectUri is not changing the hash.  ${ae}`,
  [pr]: "Unable to parse state. Please verify that the request originated from msal.",
  [mr]: "Hash contains state but the interaction type does not match the caller.",
  [fr]: `Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.   ${ae}`,
  [Cr]: "Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.",
  [yr]: "window.open returned null or undefined window object.",
  [Te]: "User cancelled the flow.",
  [ts]: `Token acquisition in popup failed due to timeout.  ${ae}`,
  [Tr]: `Token acquisition in iframe failed due to timeout.  ${ae}`,
  [Ir]: "Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.",
  [Ar]: `Request was blocked inside an iframe because MSAL detected an authentication response.  ${ae}`,
  [Er]: "Request was blocked inside a popup because MSAL detected it was running in a popup.",
  [ns]: "The iframe being monitored was closed prematurely.",
  [At]: "Silent logout not supported. Please call logoutRedirect or logoutPopup instead.",
  [Sr]: "No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.",
  [os]: "The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.",
  [vr]: "No token request found in cache.",
  [wr]: "The cached token request could not be parsed.",
  [An]: "No cached authority found.",
  [rs]: "Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler",
  [is]: "Invalid cache type",
  [Et]: "Login and token requests are not supported in non-browser environments.",
  [we]: "Database is not open!",
  [dt]: "No network connectivity. Check your internet connection.",
  [_r]: "Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'",
  [kr]: "Network request failed. Please check the network trace to determine root cause.",
  [Bt]: "Failed to parse network response. Check network trace.",
  [Rr]: "Error loading token to cache.",
  [En]: "Cryptographic Key or Keypair not found in browser storage.",
  [br]: "An authorization code must be provided (as the `code` property on the request) to this flow.",
  [Or]: "An authorization code or nativeAccountId must be provided to this flow.",
  [Nr]: "Request cannot contain both spa code and native account id.",
  [Sn]: "IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.",
  [Pr]: `Unable to acquire token from native platform.  ${ae}`,
  [Mr]: "Timed out while attempting to establish connection to browser extension",
  [Ur]: "Native extension is not installed. If you think this is a mistake call the initialize function.",
  [ze]: `Connection to native platform has not been established. Please install a compatible browser extension and run initialize().  ${ae}`,
  [Lr]: `You must call and await the initialize function before attempting to call any other MSAL API.  ${ae}`,
  [Hr]: "The provided prompt is not supported by the native platform. This request should be routed to the web based flow.",
  [Dr]: "Invalid base64 encoded string.",
  [Kr]: "Invalid PoP token request. The request should not have both a popKid value and signPopToken set to true.",
  [xr]: "Failed to build request headers object.",
  [Br]: "Failed to parse response headers"
};
class Ve extends O {
  constructor(e, t) {
    super(e, as[e], t), Object.setPrototypeOf(this, Ve.prototype), this.name = "BrowserAuthError";
  }
}
function C(a, e) {
  return new Ve(a, e);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const V = {
  /**
   * Invalid grant error code
   */
  INVALID_GRANT_ERROR: "invalid_grant",
  /**
   * Default popup window width
   */
  POPUP_WIDTH: 483,
  /**
   * Default popup window height
   */
  POPUP_HEIGHT: 600,
  /**
   * Name of the popup window starts with
   */
  POPUP_NAME_PREFIX: "msal",
  /**
   * Default popup monitor poll interval in milliseconds
   */
  DEFAULT_POLL_INTERVAL_MS: 30,
  /**
   * Msal-browser SKU
   */
  MSAL_SKU: "msal.js.browser"
}, be = {
  CHANNEL_ID: "53ee284d-920a-4b59-9d30-a60315b26836",
  PREFERRED_EXTENSION_ID: "ppnbnpeolgkicgegkbkbjmhlideopiji",
  MATS_TELEMETRY: "MATS"
}, fe = {
  HandshakeRequest: "Handshake",
  HandshakeResponse: "HandshakeResponse",
  GetToken: "GetToken",
  Response: "Response"
}, q = {
  LocalStorage: "localStorage",
  SessionStorage: "sessionStorage",
  MemoryStorage: "memoryStorage"
}, ao = {
  GET: "GET",
  POST: "POST"
}, _ = {
  AUTHORITY: "authority",
  ACQUIRE_TOKEN_ACCOUNT: "acquireToken.account",
  SESSION_STATE: "session.state",
  REQUEST_STATE: "request.state",
  NONCE_IDTOKEN: "nonce.id_token",
  ORIGIN_URI: "request.origin",
  RENEW_STATUS: "token.renew.status",
  URL_HASH: "urlHash",
  REQUEST_PARAMS: "request.params",
  SCOPES: "scopes",
  INTERACTION_STATUS_KEY: "interaction.status",
  CCS_CREDENTIAL: "ccs.credential",
  CORRELATION_ID: "request.correlationId",
  NATIVE_REQUEST: "request.native",
  REDIRECT_CONTEXT: "request.redirect.context"
}, Y = {
  ACCOUNT_KEYS: "msal.account.keys",
  TOKEN_KEYS: "msal.token.keys",
  VERSION: "msal.version"
}, et = {
  WRAPPER_SKU: "wrapper.sku",
  WRAPPER_VER: "wrapper.version"
}, b = {
  acquireTokenRedirect: 861,
  acquireTokenPopup: 862,
  ssoSilent: 863,
  acquireTokenSilent_authCode: 864,
  handleRedirectPromise: 865,
  acquireTokenByCode: 866,
  acquireTokenSilent_silentFlow: 61,
  logout: 961,
  logoutPopup: 962
};
var y;
(function(a) {
  a.Redirect = "redirect", a.Popup = "popup", a.Silent = "silent", a.None = "none";
})(y || (y = {}));
const so = {
  scopes: De
}, Fr = "jwk", Ft = "msal.db", ss = 1, cs = `${Ft}.keys`, z = {
  /*
   * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired
   * or cannot be found the refresh token will be used to acquire a new one. Finally, if the refresh token
   * is expired acquireTokenSilent will attempt to acquire new access and refresh tokens.
   */
  Default: 0,
  /*
   * acquireTokenSilent will only look for access tokens in the cache. It will not attempt to renew access or
   * refresh tokens.
   */
  AccessToken: 1,
  /*
   * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired or
   * cannot be found, the refresh token will be used to acquire a new one. If the refresh token is expired, it
   * will not be renewed and acquireTokenSilent will fail.
   */
  AccessTokenAndRefreshToken: 2,
  /*
   * acquireTokenSilent will not attempt to retrieve access tokens from the cache and will instead attempt to
   * exchange the cached refresh token for a new access token. If the refresh token is expired, it will not be
   * renewed and acquireTokenSilent will fail.
   */
  RefreshToken: 3,
  /*
   * acquireTokenSilent will not look in the cache for the access token. It will go directly to network with the
   * cached refresh token. If the refresh token is expired an attempt will be made to renew it. This is equivalent to
   * setting "forceRefresh: true".
   */
  RefreshTokenAndNetwork: 4,
  /*
   * acquireTokenSilent will attempt to renew both access and refresh tokens. It will not look in the cache. This will
   * always fail if 3rd party cookies are blocked by the browser.
   */
  Skip: 5
}, hs = [
  z.Default,
  z.Skip,
  z.RefreshTokenAndNetwork
], ls = "msal.browser.log.level", ds = "msal.browser.log.pii";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function tt(a) {
  return encodeURIComponent(vn(a).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_"));
}
function St(a) {
  return Gr(a).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
}
function vn(a) {
  return Gr(new TextEncoder().encode(a));
}
function Gr(a) {
  const e = Array.from(a, (t) => String.fromCodePoint(t)).join("");
  return btoa(e);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const us = "RSASSA-PKCS1-v1_5", qr = "SHA-256", gs = 2048, ps = new Uint8Array([1, 0, 1]), co = "0123456789abcdef", ho = new Uint32Array(1), ms = "crypto_subtle_undefined", wn = {
  name: us,
  hash: qr,
  modulusLength: gs,
  publicExponent: ps
};
function fs(a) {
  if (!window)
    throw C(Et);
  if (!window.crypto)
    throw C(xt);
  if (!a && !window.crypto.subtle)
    throw C(xt, ms);
}
async function $r(a, e, t) {
  e == null || e.addQueueMeasurement(l.Sha256Digest, t);
  const o = new TextEncoder().encode(a);
  return window.crypto.subtle.digest(qr, o);
}
function Cs(a) {
  return window.crypto.getRandomValues(a);
}
function Rt() {
  return window.crypto.getRandomValues(ho), ho[0];
}
function ie() {
  const a = Date.now(), e = Rt() * 1024 + (Rt() & 1023), t = new Uint8Array(16), n = Math.trunc(e / 2 ** 30), o = e & 2 ** 30 - 1, r = Rt();
  t[0] = a / 2 ** 40, t[1] = a / 2 ** 32, t[2] = a / 2 ** 24, t[3] = a / 2 ** 16, t[4] = a / 2 ** 8, t[5] = a, t[6] = 112 | n >>> 8, t[7] = n, t[8] = 128 | o >>> 24, t[9] = o >>> 16, t[10] = o >>> 8, t[11] = o, t[12] = r >>> 24, t[13] = r >>> 16, t[14] = r >>> 8, t[15] = r;
  let i = "";
  for (let s = 0; s < t.length; s++)
    i += co.charAt(t[s] >>> 4), i += co.charAt(t[s] & 15), (s === 3 || s === 5 || s === 7 || s === 9) && (i += "-");
  return i;
}
async function ys(a, e) {
  return window.crypto.subtle.generateKey(wn, a, e);
}
async function bt(a) {
  return window.crypto.subtle.exportKey(Fr, a);
}
async function Ts(a, e, t) {
  return window.crypto.subtle.importKey(Fr, a, wn, e, t);
}
async function Is(a, e) {
  return window.crypto.subtle.sign(wn, a, e);
}
async function zr(a) {
  const e = await $r(a), t = new Uint8Array(e);
  return St(t);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const _n = "storage_not_supported", As = "stubbed_public_client_application_called", Vr = "in_mem_redirect_unavailable";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const Es = {
  [_n]: "Given storage configuration option was not supported.",
  [As]: "Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors",
  [Vr]: "Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."
};
class kn extends O {
  constructor(e, t) {
    super(e, t), this.name = "BrowserConfigurationAuthError", Object.setPrototypeOf(this, kn.prototype);
  }
}
function Rn(a) {
  return new kn(a, Es[a]);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function Ss(a) {
  a.location.hash = "", typeof a.history.replaceState == "function" && a.history.replaceState(null, "", `${a.location.origin}${a.location.pathname}${a.location.search}`);
}
function vs(a) {
  const e = a.split("#");
  e.shift(), window.location.hash = e.length > 0 ? e.join("#") : "";
}
function bn() {
  return window.parent !== window;
}
function ws() {
  return typeof window < "u" && !!window.opener && window.opener !== window && typeof window.name == "string" && window.name.indexOf(`${V.POPUP_NAME_PREFIX}.`) === 0;
}
function he() {
  return typeof window < "u" && window.location ? window.location.href.split("?")[0].split("#")[0] : "";
}
function _s() {
  const e = new w(window.location.href).getUrlComponents();
  return `${e.Protocol}//${e.HostNameAndPort}/`;
}
function ks() {
  if (w.hashContainsKnownProperties(window.location.hash) && bn())
    throw C(Ar);
}
function Rs(a) {
  if (bn() && !a)
    throw C(Ir);
}
function bs() {
  if (ws())
    throw C(Er);
}
function Qr() {
  if (typeof window > "u")
    throw C(Et);
}
function Yr(a) {
  if (!a)
    throw C(Lr);
}
function On(a) {
  Qr(), ks(), bs(), Yr(a);
}
function lo(a, e) {
  if (On(a), Rs(e.system.allowRedirectInIframe), e.cache.cacheLocation === q.MemoryStorage && !e.cache.storeAuthStateInCookie)
    throw Rn(Vr);
}
function Wr(a) {
  const e = document.createElement("link");
  e.rel = "preconnect", e.href = new URL(a).origin, e.crossOrigin = "anonymous", document.head.appendChild(e), window.setTimeout(() => {
    try {
      document.head.removeChild(e);
    } catch {
    }
  }, 1e4);
}
function Os() {
  return ie();
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class ut {
  /**
   * Navigates to other pages within the same web application
   * @param url
   * @param options
   */
  navigateInternal(e, t) {
    return ut.defaultNavigateWindow(e, t);
  }
  /**
   * Navigates to other pages outside the web application i.e. the Identity Provider
   * @param url
   * @param options
   */
  navigateExternal(e, t) {
    return ut.defaultNavigateWindow(e, t);
  }
  /**
   * Default navigation implementation invoked by the internal and external functions
   * @param url
   * @param options
   */
  static defaultNavigateWindow(e, t) {
    return t.noHistory ? window.location.replace(e) : window.location.assign(e), new Promise((n) => {
      setTimeout(() => {
        n(!0);
      }, t.timeout);
    });
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Ns {
  /**
   * Fetch Client for REST endpoints - Get request
   * @param url
   * @param headers
   * @param body
   */
  async sendGetRequestAsync(e, t) {
    let n, o = {}, r = 0;
    const i = uo(t);
    try {
      n = await fetch(e, {
        method: ao.GET,
        headers: i
      });
    } catch {
      throw C(window.navigator.onLine ? kr : dt);
    }
    o = go(n.headers);
    try {
      return r = n.status, {
        headers: o,
        body: await n.json(),
        status: r
      };
    } catch {
      throw eo(C(Bt), r, o);
    }
  }
  /**
   * Fetch Client for REST endpoints - Post request
   * @param url
   * @param headers
   * @param body
   */
  async sendPostRequestAsync(e, t) {
    const n = t && t.body || "", o = uo(t);
    let r, i = 0, s = {};
    try {
      r = await fetch(e, {
        method: ao.POST,
        headers: o,
        body: n
      });
    } catch {
      throw C(window.navigator.onLine ? _r : dt);
    }
    s = go(r.headers);
    try {
      return i = r.status, {
        headers: s,
        body: await r.json(),
        status: i
      };
    } catch {
      throw eo(C(Bt), i, s);
    }
  }
}
function uo(a) {
  try {
    const e = new Headers();
    if (!(a && a.headers))
      return e;
    const t = a.headers;
    return Object.entries(t).forEach(([n, o]) => {
      e.append(n, o);
    }), e;
  } catch {
    throw C(xr);
  }
}
function go(a) {
  try {
    const e = {};
    return a.forEach((t, n) => {
      e[n] = t;
    }), e;
  } catch {
    throw C(Br);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const Ps = 6e4, Gt = 1e4, Ms = 3e4, Us = 2e3;
function Ls({ auth: a, cache: e, system: t, telemetry: n }, o) {
  const r = {
    clientId: u.EMPTY_STRING,
    authority: `${u.DEFAULT_AUTHORITY}`,
    knownAuthorities: [],
    cloudDiscoveryMetadata: u.EMPTY_STRING,
    authorityMetadata: u.EMPTY_STRING,
    redirectUri: typeof window < "u" ? he() : "",
    postLogoutRedirectUri: u.EMPTY_STRING,
    navigateToLoginRequestUrl: !0,
    clientCapabilities: [],
    protocolMode: le.AAD,
    OIDCOptions: {
      serverResponseType: $e.FRAGMENT,
      defaultScopes: [
        u.OPENID_SCOPE,
        u.PROFILE_SCOPE,
        u.OFFLINE_ACCESS_SCOPE
      ]
    },
    azureCloudOptions: {
      azureCloudInstance: tn.None,
      tenant: u.EMPTY_STRING
    },
    skipAuthorityMetadataCache: !1,
    supportsNestedAppAuth: !1,
    instanceAware: !1
  }, i = {
    cacheLocation: q.SessionStorage,
    temporaryCacheLocation: q.SessionStorage,
    storeAuthStateInCookie: !1,
    secureCookies: !1,
    // Default cache migration to true if cache location is localStorage since entries are preserved across tabs/windows. Migration has little to no benefit in sessionStorage and memoryStorage
    cacheMigrationEnabled: !!(e && e.cacheLocation === q.LocalStorage),
    claimsBasedCachingEnabled: !1
  }, s = {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    loggerCallback: () => {
    },
    logLevel: N.Info,
    piiLoggingEnabled: !1
  }, h = {
    ...{
      ...nr,
      loggerOptions: s,
      networkClient: o ? new Ns() : ja,
      navigationClient: new ut(),
      loadFrameTimeout: 0,
      // If loadFrameTimeout is provided, use that as default.
      windowHashTimeout: (t == null ? void 0 : t.loadFrameTimeout) || Ps,
      iframeHashTimeout: (t == null ? void 0 : t.loadFrameTimeout) || Gt,
      navigateFrameWait: 0,
      redirectNavigationTimeout: Ms,
      asyncPopups: !1,
      allowRedirectInIframe: !1,
      allowNativeBroker: !1,
      nativeBrokerHandshakeTimeout: (t == null ? void 0 : t.nativeBrokerHandshakeTimeout) || Us,
      pollIntervalMilliseconds: V.DEFAULT_POLL_INTERVAL_MS
    },
    ...t,
    loggerOptions: (t == null ? void 0 : t.loggerOptions) || s
  }, d = {
    application: {
      appName: u.EMPTY_STRING,
      appVersion: u.EMPTY_STRING
    },
    client: new es()
  };
  if ((a == null ? void 0 : a.protocolMode) !== le.OIDC && (a != null && a.OIDCOptions) && new pe(h.loggerOptions).warning(JSON.stringify(R(Yo))), a != null && a.protocolMode && a.protocolMode !== le.AAD && (h != null && h.allowNativeBroker))
    throw R(Wo);
  return {
    auth: {
      ...r,
      ...a,
      OIDCOptions: {
        ...r.OIDCOptions,
        ...a == null ? void 0 : a.OIDCOptions
      }
    },
    cache: { ...i, ...e },
    system: h,
    telemetry: { ...d, ...n }
  };
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const Hs = "@azure/msal-browser", Le = "3.30.0";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Nn {
  static loggerCallback(e, t) {
    switch (e) {
      case N.Error:
        console.error(t);
        return;
      case N.Info:
        console.info(t);
        return;
      case N.Verbose:
        console.debug(t);
        return;
      case N.Warning:
        console.warn(t);
        return;
      default:
        console.log(t);
        return;
    }
  }
  constructor(e) {
    var c;
    this.browserEnvironment = typeof window < "u", this.config = Ls(e, this.browserEnvironment);
    let t;
    try {
      t = window[q.SessionStorage];
    } catch {
    }
    const n = t == null ? void 0 : t.getItem(ls), o = (c = t == null ? void 0 : t.getItem(ds)) == null ? void 0 : c.toLowerCase(), r = o === "true" ? !0 : o === "false" ? !1 : void 0, i = { ...this.config.system.loggerOptions }, s = n && Object.keys(N).includes(n) ? N[n] : void 0;
    s && (i.loggerCallback = Nn.loggerCallback, i.logLevel = s), r !== void 0 && (i.piiLoggingEnabled = r), this.logger = new pe(i, Hs, Le), this.available = !1;
  }
  /**
   * Return the MSAL config
   * @returns BrowserConfiguration
   */
  getConfig() {
    return this.config;
  }
  /**
   * Returns the MSAL Logger
   * @returns Logger
   */
  getLogger() {
    return this.logger;
  }
  isAvailable() {
    return this.available;
  }
  isBrowserEnvironment() {
    return this.browserEnvironment;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Ie extends Nn {
  /**
   * Return the module name.  Intended for use with import() to enable dynamic import
   * of the implementation associated with this operating context
   * @returns
   */
  getModuleName() {
    return Ie.MODULE_NAME;
  }
  /**
   * Returns the unique identifier for this operating context
   * @returns string
   */
  getId() {
    return Ie.ID;
  }
  /**
   * Checks whether the operating context is available.
   * Confirms that the code is running a browser rather.  This is required.
   * @returns Promise<boolean> indicating whether this operating context is currently available.
   */
  async initialize() {
    return this.available = typeof window < "u", this.available;
  }
}
Ie.MODULE_NAME = "";
Ie.ID = "StandardOperatingContext";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function oe(a) {
  return new TextDecoder().decode(Ds(a));
}
function Ds(a) {
  let e = a.replace(/-/g, "+").replace(/_/g, "/");
  switch (e.length % 4) {
    case 0:
      break;
    case 2:
      e += "==";
      break;
    case 3:
      e += "=";
      break;
    default:
      throw C(Dr);
  }
  const t = atob(e);
  return Uint8Array.from(t, (n) => n.codePointAt(0) || 0);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Ks {
  constructor() {
    this.dbName = Ft, this.version = ss, this.tableName = cs, this.dbOpen = !1;
  }
  /**
   * Opens IndexedDB instance.
   */
  async open() {
    return new Promise((e, t) => {
      const n = window.indexedDB.open(this.dbName, this.version);
      n.addEventListener("upgradeneeded", (o) => {
        o.target.result.createObjectStore(this.tableName);
      }), n.addEventListener("success", (o) => {
        const r = o;
        this.db = r.target.result, this.dbOpen = !0, e();
      }), n.addEventListener("error", () => t(C(Sn)));
    });
  }
  /**
   * Closes the connection to IndexedDB database when all pending transactions
   * complete.
   */
  closeConnection() {
    const e = this.db;
    e && this.dbOpen && (e.close(), this.dbOpen = !1);
  }
  /**
   * Opens database if it's not already open
   */
  async validateDbIsOpen() {
    if (!this.dbOpen)
      return this.open();
  }
  /**
   * Retrieves item from IndexedDB instance.
   * @param key
   */
  async getItem(e) {
    return await this.validateDbIsOpen(), new Promise((t, n) => {
      if (!this.db)
        return n(C(we));
      const i = this.db.transaction([this.tableName], "readonly").objectStore(this.tableName).get(e);
      i.addEventListener("success", (s) => {
        const c = s;
        this.closeConnection(), t(c.target.result);
      }), i.addEventListener("error", (s) => {
        this.closeConnection(), n(s);
      });
    });
  }
  /**
   * Adds item to IndexedDB under given key
   * @param key
   * @param payload
   */
  async setItem(e, t) {
    return await this.validateDbIsOpen(), new Promise((n, o) => {
      if (!this.db)
        return o(C(we));
      const s = this.db.transaction([this.tableName], "readwrite").objectStore(this.tableName).put(t, e);
      s.addEventListener("success", () => {
        this.closeConnection(), n();
      }), s.addEventListener("error", (c) => {
        this.closeConnection(), o(c);
      });
    });
  }
  /**
   * Removes item from IndexedDB under given key
   * @param key
   */
  async removeItem(e) {
    return await this.validateDbIsOpen(), new Promise((t, n) => {
      if (!this.db)
        return n(C(we));
      const i = this.db.transaction([this.tableName], "readwrite").objectStore(this.tableName).delete(e);
      i.addEventListener("success", () => {
        this.closeConnection(), t();
      }), i.addEventListener("error", (s) => {
        this.closeConnection(), n(s);
      });
    });
  }
  /**
   * Get all the keys from the storage object as an iterable array of strings.
   */
  async getKeys() {
    return await this.validateDbIsOpen(), new Promise((e, t) => {
      if (!this.db)
        return t(C(we));
      const r = this.db.transaction([this.tableName], "readonly").objectStore(this.tableName).getAllKeys();
      r.addEventListener("success", (i) => {
        const s = i;
        this.closeConnection(), e(s.target.result);
      }), r.addEventListener("error", (i) => {
        this.closeConnection(), t(i);
      });
    });
  }
  /**
   *
   * Checks whether there is an object under the search key in the object store
   */
  async containsKey(e) {
    return await this.validateDbIsOpen(), new Promise((t, n) => {
      if (!this.db)
        return n(C(we));
      const i = this.db.transaction([this.tableName], "readonly").objectStore(this.tableName).count(e);
      i.addEventListener("success", (s) => {
        const c = s;
        this.closeConnection(), t(c.target.result === 1);
      }), i.addEventListener("error", (s) => {
        this.closeConnection(), n(s);
      });
    });
  }
  /**
   * Deletes the MSAL database. The database is deleted rather than cleared to make it possible
   * for client applications to downgrade to a previous MSAL version without worrying about forward compatibility issues
   * with IndexedDB database versions.
   */
  async deleteDatabase() {
    return this.db && this.dbOpen && this.closeConnection(), new Promise((e, t) => {
      const n = window.indexedDB.deleteDatabase(Ft), o = setTimeout(() => t(!1), 200);
      n.addEventListener("success", () => (clearTimeout(o), e(!0))), n.addEventListener("blocked", () => (clearTimeout(o), e(!0))), n.addEventListener("error", () => (clearTimeout(o), t(!1)));
    });
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class qt {
  constructor() {
    this.cache = /* @__PURE__ */ new Map();
  }
  getItem(e) {
    return this.cache.get(e) || null;
  }
  setItem(e, t) {
    this.cache.set(e, t);
  }
  removeItem(e) {
    this.cache.delete(e);
  }
  getKeys() {
    const e = [];
    return this.cache.forEach((t, n) => {
      e.push(n);
    }), e;
  }
  containsKey(e) {
    return this.cache.has(e);
  }
  clear() {
    this.cache.clear();
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class xs {
  constructor(e) {
    this.inMemoryCache = new qt(), this.indexedDBCache = new Ks(), this.logger = e;
  }
  handleDatabaseAccessError(e) {
    if (e instanceof Ve && e.errorCode === Sn)
      this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.");
    else
      throw e;
  }
  /**
   * Get the item matching the given key. Tries in-memory cache first, then in the asynchronous
   * storage object if item isn't found in-memory.
   * @param key
   */
  async getItem(e) {
    const t = this.inMemoryCache.getItem(e);
    if (!t)
      try {
        return this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."), await this.indexedDBCache.getItem(e);
      } catch (n) {
        this.handleDatabaseAccessError(n);
      }
    return t;
  }
  /**
   * Sets the item in the in-memory cache and then tries to set it in the asynchronous
   * storage object with the given key.
   * @param key
   * @param value
   */
  async setItem(e, t) {
    this.inMemoryCache.setItem(e, t);
    try {
      await this.indexedDBCache.setItem(e, t);
    } catch (n) {
      this.handleDatabaseAccessError(n);
    }
  }
  /**
   * Removes the item matching the key from the in-memory cache, then tries to remove it from the asynchronous storage object.
   * @param key
   */
  async removeItem(e) {
    this.inMemoryCache.removeItem(e);
    try {
      await this.indexedDBCache.removeItem(e);
    } catch (t) {
      this.handleDatabaseAccessError(t);
    }
  }
  /**
   * Get all the keys from the in-memory cache as an iterable array of strings. If no keys are found, query the keys in the
   * asynchronous storage object.
   */
  async getKeys() {
    const e = this.inMemoryCache.getKeys();
    if (e.length === 0)
      try {
        return this.logger.verbose("In-memory cache is empty, now querying persistent storage."), await this.indexedDBCache.getKeys();
      } catch (t) {
        this.handleDatabaseAccessError(t);
      }
    return e;
  }
  /**
   * Returns true or false if the given key is present in the cache.
   * @param key
   */
  async containsKey(e) {
    const t = this.inMemoryCache.containsKey(e);
    if (!t)
      try {
        return this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."), await this.indexedDBCache.containsKey(e);
      } catch (n) {
        this.handleDatabaseAccessError(n);
      }
    return t;
  }
  /**
   * Clears in-memory Map
   */
  clearInMemory() {
    this.logger.verbose("Deleting in-memory keystore"), this.inMemoryCache.clear(), this.logger.verbose("In-memory keystore deleted");
  }
  /**
   * Tries to delete the IndexedDB database
   * @returns
   */
  async clearPersistent() {
    try {
      this.logger.verbose("Deleting persistent keystore");
      const e = await this.indexedDBCache.deleteDatabase();
      return e && this.logger.verbose("Persistent keystore deleted"), e;
    } catch (e) {
      return this.handleDatabaseAccessError(e), !1;
    }
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class He {
  constructor(e, t, n) {
    this.logger = e, fs(n ?? !1), this.cache = new xs(this.logger), this.performanceClient = t;
  }
  /**
   * Creates a new random GUID - used to populate state and nonce.
   * @returns string (GUID)
   */
  createNewGuid() {
    return ie();
  }
  /**
   * Encodes input string to base64.
   * @param input
   */
  base64Encode(e) {
    return vn(e);
  }
  /**
   * Decodes input string from base64.
   * @param input
   */
  base64Decode(e) {
    return oe(e);
  }
  /**
   * Encodes input string to base64 URL safe string.
   * @param input
   */
  base64UrlEncode(e) {
    return tt(e);
  }
  /**
   * Stringifies and base64Url encodes input public key
   * @param inputKid
   * @returns Base64Url encoded public key
   */
  encodeKid(e) {
    return this.base64UrlEncode(JSON.stringify({ kid: e }));
  }
  /**
   * Generates a keypair, stores it and returns a thumbprint
   * @param request
   */
  async getPublicKeyThumbprint(e) {
    var d;
    const t = (d = this.performanceClient) == null ? void 0 : d.startMeasurement(l.CryptoOptsGetPublicKeyThumbprint, e.correlationId), n = await ys(He.EXTRACTABLE, He.POP_KEY_USAGES), o = await bt(n.publicKey), r = {
      e: o.e,
      kty: o.kty,
      n: o.n
    }, i = po(r), s = await this.hashString(i), c = await bt(n.privateKey), h = await Ts(c, !1, ["sign"]);
    return await this.cache.setItem(s, {
      privateKey: h,
      publicKey: n.publicKey,
      requestMethod: e.resourceRequestMethod,
      requestUri: e.resourceRequestUri
    }), t && t.end({
      success: !0
    }), s;
  }
  /**
   * Removes cryptographic keypair from key store matching the keyId passed in
   * @param kid
   */
  async removeTokenBindingKey(e) {
    return await this.cache.removeItem(e), !await this.cache.containsKey(e);
  }
  /**
   * Removes all cryptographic keys from IndexedDB storage
   */
  async clearKeystore() {
    this.cache.clearInMemory();
    try {
      return await this.cache.clearPersistent(), !0;
    } catch (e) {
      return e instanceof Error ? this.logger.error(`Clearing keystore failed with error: ${e.message}`) : this.logger.error("Clearing keystore failed with unknown error"), !1;
    }
  }
  /**
   * Signs the given object as a jwt payload with private key retrieved by given kid.
   * @param payload
   * @param kid
   */
  async signJwt(e, t, n, o) {
    var de;
    const r = (de = this.performanceClient) == null ? void 0 : de.startMeasurement(l.CryptoOptsSignJwt, o), i = await this.cache.getItem(t);
    if (!i)
      throw C(En);
    const s = await bt(i.publicKey), c = po(s), h = tt(JSON.stringify({ kid: t })), d = yn.getShrHeaderString({
      ...n == null ? void 0 : n.header,
      alg: s.alg,
      kid: h
    }), g = tt(d);
    e.cnf = {
      jwk: JSON.parse(c)
    };
    const f = tt(JSON.stringify(e)), T = `${g}.${f}`, v = new TextEncoder().encode(T), M = await Is(i.privateKey, v), G = St(new Uint8Array(M)), L = `${T}.${G}`;
    return r && r.end({
      success: !0
    }), L;
  }
  /**
   * Returns the SHA-256 hash of an input string
   * @param plainText
   */
  async hashString(e) {
    return zr(e);
  }
}
He.POP_KEY_USAGES = ["sign", "verify"];
He.EXTRACTABLE = !0;
function po(a) {
  return JSON.stringify(a, Object.keys(a).sort());
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Bs {
  constructor() {
    if (!window.localStorage)
      throw Rn(_n);
  }
  getItem(e) {
    return window.localStorage.getItem(e);
  }
  setItem(e, t) {
    window.localStorage.setItem(e, t);
  }
  removeItem(e) {
    window.localStorage.removeItem(e);
  }
  getKeys() {
    return Object.keys(window.localStorage);
  }
  containsKey(e) {
    return window.localStorage.hasOwnProperty(e);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Fs {
  constructor() {
    if (!window.sessionStorage)
      throw Rn(_n);
  }
  getItem(e) {
    return window.sessionStorage.getItem(e);
  }
  setItem(e, t) {
    window.sessionStorage.setItem(e, t);
  }
  removeItem(e) {
    window.sessionStorage.removeItem(e);
  }
  getKeys() {
    return Object.keys(window.sessionStorage);
  }
  containsKey(e) {
    return window.sessionStorage.hasOwnProperty(e);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function jr(a, e) {
  if (!e)
    return null;
  try {
    return J.parseRequestState(a, e).libraryState.meta;
  } catch {
    throw p(Pe);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const Gs = 24 * 60 * 60 * 1e3;
class qs {
  getItem(e) {
    const t = `${encodeURIComponent(e)}`, n = document.cookie.split(";");
    for (let o = 0; o < n.length; o++) {
      const r = n[o], [i, ...s] = decodeURIComponent(r).trim().split("="), c = s.join("=");
      if (i === t)
        return c;
    }
    return "";
  }
  setItem(e, t, n, o = !0) {
    let r = `${encodeURIComponent(e)}=${encodeURIComponent(t)};path=/;SameSite=Lax;`;
    if (n) {
      const i = $s(n);
      r += `expires=${i};`;
    }
    o && (r += "Secure;"), document.cookie = r;
  }
  removeItem(e) {
    this.setItem(e, "", -1);
  }
  getKeys() {
    const e = document.cookie.split(";"), t = [];
    return e.forEach((n) => {
      const o = decodeURIComponent(n).trim().split("=");
      t.push(o[0]);
    }), t;
  }
  containsKey(e) {
    return this.getKeys().includes(e);
  }
}
function $s(a) {
  const e = /* @__PURE__ */ new Date();
  return new Date(e.getTime() + a * Gs).toUTCString();
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class $t extends Me {
  constructor(e, t, n, o, r, i) {
    super(e, n, o, r), this.cacheConfig = t, this.logger = o, this.internalStorage = new qt(), this.browserStorage = this.setupBrowserStorage(this.cacheConfig.cacheLocation), this.temporaryCacheStorage = this.setupBrowserStorage(this.cacheConfig.temporaryCacheLocation), this.cookieStorage = new qs(), t.cacheMigrationEnabled && (this.migrateCacheEntries(), this.createKeyMaps()), this.performanceClient = i;
  }
  /**
   * Returns a window storage class implementing the IWindowStorage interface that corresponds to the configured cacheLocation.
   * @param cacheLocation
   */
  setupBrowserStorage(e) {
    try {
      switch (e) {
        case q.LocalStorage:
          return new Bs();
        case q.SessionStorage:
          return new Fs();
        case q.MemoryStorage:
        default:
          break;
      }
    } catch (t) {
      this.logger.error(t);
    }
    return this.cacheConfig.cacheLocation = q.MemoryStorage, new qt();
  }
  /**
   * Migrate all old cache entries to new schema. No rollback supported.
   * @param storeAuthStateInCookie
   */
  migrateCacheEntries() {
    const e = this.browserStorage.getItem(Y.VERSION);
    e && this.logger.info(`MSAL.js was last initialized with version ${e}`), e !== Le && this.browserStorage.setItem(Y.VERSION, Le);
    const t = `${u.CACHE_PREFIX}.${H.ID_TOKEN}`, n = `${u.CACHE_PREFIX}.${H.CLIENT_INFO}`, o = `${u.CACHE_PREFIX}.${H.ERROR}`, r = `${u.CACHE_PREFIX}.${H.ERROR_DESC}`, i = this.browserStorage.getItem(t), s = this.browserStorage.getItem(n), c = this.browserStorage.getItem(o), h = this.browserStorage.getItem(r), d = [
      i,
      s,
      c,
      h
    ];
    [
      H.ID_TOKEN,
      H.CLIENT_INFO,
      H.ERROR,
      H.ERROR_DESC
    ].forEach((f, T) => {
      const E = d[T];
      E && this.setTemporaryCache(f, E, !0);
    });
  }
  /**
   * Searches all cache entries for MSAL accounts and creates the account key map
   * This is used to migrate users from older versions of MSAL which did not create the map.
   * @returns
   */
  createKeyMaps() {
    this.logger.trace("BrowserCacheManager - createKeyMaps called.");
    const e = this.cryptoImpl.createNewGuid(), t = this.getItem(Y.ACCOUNT_KEYS), n = this.getItem(`${Y.TOKEN_KEYS}.${this.clientId}`);
    if (t && n) {
      this.logger.verbose("BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration.");
      return;
    }
    this.browserStorage.getKeys().forEach((r) => {
      if (this.isCredentialKey(r)) {
        const i = this.getItem(r);
        if (i) {
          const s = this.validateAndParseJson(i);
          if (s && s.hasOwnProperty("credentialType"))
            switch (s.credentialType) {
              case A.ID_TOKEN:
                if ($n(s)) {
                  this.logger.trace("BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map"), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - idToken with key: ${r} found, saving key to token key map`);
                  const c = s, h = this.updateCredentialCacheKey(r, c, e);
                  this.addTokenKey(h, A.ID_TOKEN, e);
                  return;
                } else
                  this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping."), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed idToken validation on key: ${r}`);
                break;
              case A.ACCESS_TOKEN:
              case A.ACCESS_TOKEN_WITH_AUTH_SCHEME:
                if (qn(s)) {
                  this.logger.trace("BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map"), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - accessToken with key: ${r} found, saving key to token key map`);
                  const c = s, h = this.updateCredentialCacheKey(r, c, e);
                  this.addTokenKey(h, A.ACCESS_TOKEN, e);
                  return;
                } else
                  this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping."), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed accessToken validation on key: ${r}`);
                break;
              case A.REFRESH_TOKEN:
                if (zn(s)) {
                  this.logger.trace("BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map"), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - refreshToken with key: ${r} found, saving key to token key map`);
                  const c = s, h = this.updateCredentialCacheKey(r, c, e);
                  this.addTokenKey(h, A.REFRESH_TOKEN, e);
                  return;
                } else
                  this.logger.trace("BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping."), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: ${r}`);
                break;
            }
        }
      }
      if (this.isAccountKey(r)) {
        const i = this.getItem(r);
        if (i) {
          const s = this.validateAndParseJson(i);
          s && U.isAccountEntity(s) && (this.logger.trace("BrowserCacheManager:createKeyMaps - account found, saving key to account key map"), this.logger.tracePii(`BrowserCacheManager:createKeyMaps - account with key: ${r} found, saving key to account key map`), this.addAccountKeyToMap(r, e));
        }
      }
    });
  }
  /**
   * Parses passed value as JSON object, JSON.parse() will throw an error.
   * @param input
   */
  validateAndParseJson(e) {
    try {
      const t = JSON.parse(e);
      return t && typeof t == "object" ? t : null;
    } catch {
      return null;
    }
  }
  /**
   * fetches the entry from the browser storage based off the key
   * @param key
   */
  getItem(e) {
    return this.browserStorage.getItem(e);
  }
  /**
   * sets the entry in the browser storage
   * @param key
   * @param value
   */
  setItem(e, t, n) {
    let o = [];
    for (let i = 0; i <= 20; i++)
      try {
        this.browserStorage.setItem(e, t), i > 0 && this.removeAccessTokenKeys(o.slice(0, i), n);
        break;
      } catch (s) {
        const c = tr(s);
        if (c.errorCode === dn && i < 20) {
          if (o.length || (e === `${Y.TOKEN_KEYS}.${this.clientId}` ? o = JSON.parse(t).accessToken : o = this.getTokenKeys().accessToken), o.length <= i)
            throw c;
          this.removeAccessToken(
            o[i],
            n,
            !1
            // Don't save token keys yet, do it at the end
          );
        } else
          throw c;
      }
  }
  /**
   * fetch the account entity from the platform cache
   * @param accountKey
   */
  getAccount(e, t, n) {
    this.logger.trace("BrowserCacheManager.getAccount called");
    const o = this.getCachedAccountEntity(e, t);
    return this.updateOutdatedCachedAccount(e, o, t, n);
  }
  /**
   * Reads account from cache, deserializes it into an account entity and returns it.
   * If account is not found from the key, returns null and removes key from map.
   * @param accountKey
   * @returns
   */
  getCachedAccountEntity(e, t) {
    const n = this.getItem(e);
    if (!n)
      return this.removeAccountKeyFromMap(e, t), null;
    const o = this.validateAndParseJson(n);
    return !o || !U.isAccountEntity(o) ? null : Me.toObject(new U(), o);
  }
  /**
   * set account entity in the platform cache
   * @param account
   */
  setAccount(e, t) {
    this.logger.trace("BrowserCacheManager.setAccount called");
    const n = e.generateAccountKey();
    e.lastUpdatedAt = Date.now().toString(), this.setItem(n, JSON.stringify(e), t), this.addAccountKeyToMap(n, t);
  }
  /**
   * Returns the array of account keys currently cached
   * @returns
   */
  getAccountKeys() {
    this.logger.trace("BrowserCacheManager.getAccountKeys called");
    const e = this.getItem(Y.ACCOUNT_KEYS);
    return e ? JSON.parse(e) : (this.logger.verbose("BrowserCacheManager.getAccountKeys - No account keys found"), []);
  }
  /**
   * Add a new account to the key map
   * @param key
   */
  addAccountKeyToMap(e, t) {
    this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"), this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${e}`);
    const n = this.getAccountKeys();
    n.indexOf(e) === -1 ? (n.push(e), this.setItem(Y.ACCOUNT_KEYS, JSON.stringify(n), t), this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added")) : this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map");
  }
  /**
   * Remove an account from the key map
   * @param key
   */
  removeAccountKeyFromMap(e, t) {
    this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"), this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${e}`);
    const n = this.getAccountKeys(), o = n.indexOf(e);
    if (o > -1) {
      if (n.splice(o, 1), n.length === 0) {
        this.removeItem(Y.ACCOUNT_KEYS);
        return;
      } else
        this.setItem(Y.ACCOUNT_KEYS, JSON.stringify(n), t);
      this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed");
    } else
      this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map");
  }
  /**
   * Extends inherited removeAccount function to include removal of the account key from the map
   * @param key
   */
  async removeAccount(e, t) {
    super.removeAccount(e, t), this.removeAccountKeyFromMap(e, t);
  }
  /**
   * Remove account entity from the platform cache if it's outdated
   * @param accountKey
   */
  removeOutdatedAccount(e, t) {
    this.removeItem(e), this.removeAccountKeyFromMap(e, t);
  }
  /**
   * Removes given idToken from the cache and from the key map
   * @param key
   */
  removeIdToken(e, t) {
    super.removeIdToken(e, t), this.removeTokenKey(e, A.ID_TOKEN, t);
  }
  /**
   * Removes given accessToken from the cache and from the key map
   * @param key
   */
  removeAccessToken(e, t, n = !0) {
    var o;
    super.removeAccessToken(e, t), (o = this.performanceClient) == null || o.incrementFields({ accessTokensRemoved: 1 }, t), n && this.removeTokenKey(e, A.ACCESS_TOKEN, t);
  }
  removeAccessTokenKeys(e, t) {
    this.logger.trace("removeAccessTokenKey called");
    const n = this.getTokenKeys();
    let o = 0;
    if (e.forEach((r) => {
      const i = n.accessToken.indexOf(r);
      i > -1 && (n.accessToken.splice(i, 1), o++);
    }), o > 0) {
      this.logger.info(`removed ${o} accessToken keys from tokenKeys map`), this.setTokenKeys(n, t);
      return;
    }
  }
  /**
   * Removes given refreshToken from the cache and from the key map
   * @param key
   */
  removeRefreshToken(e, t) {
    super.removeRefreshToken(e, t), this.removeTokenKey(e, A.REFRESH_TOKEN, t);
  }
  /**
   * Gets the keys for the cached tokens associated with this clientId
   * @returns
   */
  getTokenKeys() {
    this.logger.trace("BrowserCacheManager.getTokenKeys called");
    const e = this.getItem(`${Y.TOKEN_KEYS}.${this.clientId}`);
    if (e) {
      const t = this.validateAndParseJson(e);
      if (t && t.hasOwnProperty("idToken") && t.hasOwnProperty("accessToken") && t.hasOwnProperty("refreshToken"))
        return t;
      this.logger.error("BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.");
    } else
      this.logger.verbose("BrowserCacheManager.getTokenKeys - No token keys found");
    return {
      idToken: [],
      accessToken: [],
      refreshToken: []
    };
  }
  /**
   * Stores the token keys in the cache
   * @param tokenKeys
   * @param correlationId
   * @returns
   */
  setTokenKeys(e, t) {
    if (e.idToken.length === 0 && e.accessToken.length === 0 && e.refreshToken.length === 0) {
      this.removeItem(`${Y.TOKEN_KEYS}.${this.clientId}`);
      return;
    } else
      this.setItem(`${Y.TOKEN_KEYS}.${this.clientId}`, JSON.stringify(e), t);
  }
  /**
   * Adds the given key to the token key map
   * @param key
   * @param type
   */
  addTokenKey(e, t, n) {
    this.logger.trace("BrowserCacheManager addTokenKey called");
    const o = this.getTokenKeys();
    switch (t) {
      case A.ID_TOKEN:
        o.idToken.indexOf(e) === -1 && (this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"), o.idToken.push(e));
        break;
      case A.ACCESS_TOKEN:
        const r = o.accessToken.indexOf(e);
        r !== -1 && o.accessToken.splice(r, 1), this.logger.trace(`access token ${r === -1 ? "added to" : "updated in"} map`), o.accessToken.push(e);
        break;
      case A.REFRESH_TOKEN:
        o.refreshToken.indexOf(e) === -1 && (this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"), o.refreshToken.push(e));
        break;
      default:
        throw this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${t}`), p(Mt);
    }
    this.setTokenKeys(o, n);
  }
  /**
   * Removes the given key from the token key map
   * @param key
   * @param type
   */
  removeTokenKey(e, t, n, o = this.getTokenKeys()) {
    switch (this.logger.trace("BrowserCacheManager removeTokenKey called"), t) {
      case A.ID_TOKEN:
        this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${e} from map`);
        const r = o.idToken.indexOf(e);
        r > -1 ? (this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"), o.idToken.splice(r, 1)) : this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");
        break;
      case A.ACCESS_TOKEN:
        this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${e} from map`);
        const i = o.accessToken.indexOf(e);
        i > -1 ? (this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"), o.accessToken.splice(i, 1)) : this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");
        break;
      case A.REFRESH_TOKEN:
        this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${e} from map`);
        const s = o.refreshToken.indexOf(e);
        s > -1 ? (this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"), o.refreshToken.splice(s, 1)) : this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");
        break;
      default:
        throw this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${t}`), p(Mt);
    }
    this.setTokenKeys(o, n);
  }
  /**
   * generates idToken entity from a string
   * @param idTokenKey
   */
  getIdTokenCredential(e, t) {
    const n = this.getItem(e);
    if (!n)
      return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"), this.removeIdToken(e, t), null;
    const o = this.validateAndParseJson(n);
    return !o || !$n(o) ? (this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"), o);
  }
  /**
   * set IdToken credential to the platform cache
   * @param idToken
   */
  setIdTokenCredential(e, t) {
    this.logger.trace("BrowserCacheManager.setIdTokenCredential called");
    const n = ke(e);
    e.lastUpdatedAt = Date.now().toString(), this.setItem(n, JSON.stringify(e), t), this.addTokenKey(n, A.ID_TOKEN, t);
  }
  /**
   * generates accessToken entity from a string
   * @param key
   */
  getAccessTokenCredential(e, t) {
    const n = this.getItem(e);
    if (!n)
      return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"), this.removeTokenKey(e, A.ACCESS_TOKEN, t), null;
    const o = this.validateAndParseJson(n);
    return !o || !qn(o) ? (this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"), o);
  }
  /**
   * set accessToken credential to the platform cache
   * @param accessToken
   */
  setAccessTokenCredential(e, t) {
    this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");
    const n = ke(e);
    e.lastUpdatedAt = Date.now().toString(), this.setItem(n, JSON.stringify(e), t), this.addTokenKey(n, A.ACCESS_TOKEN, t);
  }
  /**
   * generates refreshToken entity from a string
   * @param refreshTokenKey
   */
  getRefreshTokenCredential(e, t) {
    const n = this.getItem(e);
    if (!n)
      return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"), this.removeTokenKey(e, A.REFRESH_TOKEN, t), null;
    const o = this.validateAndParseJson(n);
    return !o || !zn(o) ? (this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"), o);
  }
  /**
   * set refreshToken credential to the platform cache
   * @param refreshToken
   */
  setRefreshTokenCredential(e, t) {
    this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");
    const n = ke(e);
    e.lastUpdatedAt = Date.now().toString(), this.setItem(n, JSON.stringify(e), t), this.addTokenKey(n, A.REFRESH_TOKEN, t);
  }
  /**
   * fetch appMetadata entity from the platform cache
   * @param appMetadataKey
   */
  getAppMetadata(e) {
    const t = this.getItem(e);
    if (!t)
      return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"), null;
    const n = this.validateAndParseJson(t);
    return !n || !Mi(e, n) ? (this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"), n);
  }
  /**
   * set appMetadata entity to the platform cache
   * @param appMetadata
   */
  setAppMetadata(e, t) {
    this.logger.trace("BrowserCacheManager.setAppMetadata called");
    const n = Pi(e);
    this.setItem(n, JSON.stringify(e), t);
  }
  /**
   * fetch server telemetry entity from the platform cache
   * @param serverTelemetryKey
   */
  getServerTelemetry(e) {
    const t = this.getItem(e);
    if (!t)
      return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"), null;
    const n = this.validateAndParseJson(t);
    return !n || !Oi(e, n) ? (this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"), n);
  }
  /**
   * set server telemetry entity to the platform cache
   * @param serverTelemetryKey
   * @param serverTelemetry
   */
  setServerTelemetry(e, t, n) {
    this.logger.trace("BrowserCacheManager.setServerTelemetry called"), this.setItem(e, JSON.stringify(t), n);
  }
  /**
   *
   */
  getAuthorityMetadata(e) {
    const t = this.internalStorage.getItem(e);
    if (!t)
      return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"), null;
    const n = this.validateAndParseJson(t);
    return n && Ui(e, n) ? (this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"), n) : null;
  }
  /**
   *
   */
  getAuthorityMetadataKeys() {
    return this.internalStorage.getKeys().filter((t) => this.isAuthorityMetadata(t));
  }
  /**
   * Sets wrapper metadata in memory
   * @param wrapperSKU
   * @param wrapperVersion
   */
  setWrapperMetadata(e, t) {
    this.internalStorage.setItem(et.WRAPPER_SKU, e), this.internalStorage.setItem(et.WRAPPER_VER, t);
  }
  /**
   * Returns wrapper metadata from in-memory storage
   */
  getWrapperMetadata() {
    const e = this.internalStorage.getItem(et.WRAPPER_SKU) || u.EMPTY_STRING, t = this.internalStorage.getItem(et.WRAPPER_VER) || u.EMPTY_STRING;
    return [e, t];
  }
  /**
   *
   * @param entity
   */
  setAuthorityMetadata(e, t) {
    this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"), this.internalStorage.setItem(e, JSON.stringify(t));
  }
  /**
   * Gets the active account
   */
  getActiveAccount(e) {
    const t = this.generateCacheKey(H.ACTIVE_ACCOUNT_FILTERS), n = this.getItem(t);
    if (!n) {
      this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema");
      const r = this.generateCacheKey(H.ACTIVE_ACCOUNT), i = this.getItem(r);
      if (!i)
        return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"), null;
      const s = this.getAccountInfoFilteredBy({
        localAccountId: i
      }, e);
      return s ? (this.logger.trace("BrowserCacheManager.getActiveAccount: Legacy active account cache schema found"), this.logger.trace("BrowserCacheManager.getActiveAccount: Adding active account filters cache schema"), this.setActiveAccount(s, e), s) : null;
    }
    const o = this.validateAndParseJson(n);
    return o ? (this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"), this.getAccountInfoFilteredBy({
      homeAccountId: o.homeAccountId,
      localAccountId: o.localAccountId,
      tenantId: o.tenantId
    }, e)) : (this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"), null);
  }
  /**
   * Sets the active account's localAccountId in cache
   * @param account
   */
  setActiveAccount(e, t) {
    const n = this.generateCacheKey(H.ACTIVE_ACCOUNT_FILTERS), o = this.generateCacheKey(H.ACTIVE_ACCOUNT);
    if (e) {
      this.logger.verbose("setActiveAccount: Active account set");
      const r = {
        homeAccountId: e.homeAccountId,
        localAccountId: e.localAccountId,
        tenantId: e.tenantId,
        lastUpdatedAt: Date.now().toString()
      };
      this.setItem(n, JSON.stringify(r), t), this.setItem(o, e.localAccountId, t);
    } else
      this.logger.verbose("setActiveAccount: No account passed, active account not set"), this.browserStorage.removeItem(n), this.browserStorage.removeItem(o);
  }
  /**
   * fetch throttling entity from the platform cache
   * @param throttlingCacheKey
   */
  getThrottlingCache(e) {
    const t = this.getItem(e);
    if (!t)
      return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"), null;
    const n = this.validateAndParseJson(t);
    return !n || !Ni(e, n) ? (this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"), null) : (this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"), n);
  }
  /**
   * set throttling entity to the platform cache
   * @param throttlingCacheKey
   * @param throttlingCache
   */
  setThrottlingCache(e, t, n) {
    this.logger.trace("BrowserCacheManager.setThrottlingCache called"), this.setItem(e, JSON.stringify(t), n);
  }
  /**
   * Gets cache item with given key.
   * Will retrieve from cookies if storeAuthStateInCookie is set to true.
   * @param key
   */
  getTemporaryCache(e, t) {
    const n = t ? this.generateCacheKey(e) : e;
    if (this.cacheConfig.storeAuthStateInCookie) {
      const r = this.cookieStorage.getItem(n);
      if (r)
        return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"), r;
    }
    const o = this.temporaryCacheStorage.getItem(n);
    if (!o) {
      if (this.cacheConfig.cacheLocation === q.LocalStorage) {
        const r = this.browserStorage.getItem(n);
        if (r)
          return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"), r;
      }
      return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"), null;
    }
    return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"), o;
  }
  /**
   * Sets the cache item with the key and value given.
   * Stores in cookie if storeAuthStateInCookie is set to true.
   * This can cause cookie overflow if used incorrectly.
   * @param key
   * @param value
   */
  setTemporaryCache(e, t, n) {
    const o = n ? this.generateCacheKey(e) : e;
    this.temporaryCacheStorage.setItem(o, t), this.cacheConfig.storeAuthStateInCookie && (this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"), this.cookieStorage.setItem(o, t, void 0, this.cacheConfig.secureCookies));
  }
  /**
   * Removes the cache item with the given key.
   * @param key
   */
  removeItem(e) {
    this.browserStorage.removeItem(e);
  }
  /**
   * Removes the temporary cache item with the given key.
   * Will also clear the cookie item if storeAuthStateInCookie is set to true.
   * @param key
   */
  removeTemporaryItem(e) {
    this.temporaryCacheStorage.removeItem(e), this.cacheConfig.storeAuthStateInCookie && (this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"), this.cookieStorage.removeItem(e));
  }
  /**
   * Gets all keys in window.
   */
  getKeys() {
    return this.browserStorage.getKeys();
  }
  /**
   * Clears all cache entries created by MSAL.
   */
  async clear(e) {
    await this.removeAllAccounts(e), this.removeAppMetadata(e), this.temporaryCacheStorage.getKeys().forEach((t) => {
      (t.indexOf(u.CACHE_PREFIX) !== -1 || t.indexOf(this.clientId) !== -1) && this.removeTemporaryItem(t);
    }), this.browserStorage.getKeys().forEach((t) => {
      (t.indexOf(u.CACHE_PREFIX) !== -1 || t.indexOf(this.clientId) !== -1) && this.browserStorage.removeItem(t);
    }), this.internalStorage.clear();
  }
  /**
   * Clears all access tokes that have claims prior to saving the current one
   * @param performanceClient {IPerformanceClient}
   * @param correlationId {string} correlation id
   * @returns
   */
  async clearTokensAndKeysWithClaims(e, t) {
    e.addQueueMeasurement(l.ClearTokensAndKeysWithClaims, t);
    const n = this.getTokenKeys();
    let o = 0;
    n.accessToken.forEach((r) => {
      const i = this.getAccessTokenCredential(r, t);
      i != null && i.requestedClaimsHash && r.includes(i.requestedClaimsHash.toLowerCase()) && (this.removeAccessToken(r, t), o++);
    }), o > 0 && this.logger.warning(`${o} access tokens with claims in the cache keys have been removed from the cache.`);
  }
  /**
   * Prepend msal.<client-id> to each key; Skip for any JSON object as Key (defined schemas do not need the key appended: AccessToken Keys or the upcoming schema)
   * @param key
   * @param addInstanceId
   */
  generateCacheKey(e) {
    return this.validateAndParseJson(e) ? JSON.stringify(e) : j.startsWith(e, u.CACHE_PREFIX) || j.startsWith(e, H.ADAL_ID_TOKEN) ? e : `${u.CACHE_PREFIX}.${this.clientId}.${e}`;
  }
  /**
   * Create authorityKey to cache authority
   * @param state
   */
  generateAuthorityKey(e) {
    const { libraryState: { id: t } } = J.parseRequestState(this.cryptoImpl, e);
    return this.generateCacheKey(`${_.AUTHORITY}.${t}`);
  }
  /**
   * Create Nonce key to cache nonce
   * @param state
   */
  generateNonceKey(e) {
    const { libraryState: { id: t } } = J.parseRequestState(this.cryptoImpl, e);
    return this.generateCacheKey(`${_.NONCE_IDTOKEN}.${t}`);
  }
  /**
   * Creates full cache key for the request state
   * @param stateString State string for the request
   */
  generateStateKey(e) {
    const { libraryState: { id: t } } = J.parseRequestState(this.cryptoImpl, e);
    return this.generateCacheKey(`${_.REQUEST_STATE}.${t}`);
  }
  /**
   * Gets the cached authority based on the cached state. Returns empty if no cached state found.
   */
  getCachedAuthority(e) {
    const t = this.generateStateKey(e), n = this.getTemporaryCache(t);
    if (!n)
      return null;
    const o = this.generateAuthorityKey(n);
    return this.getTemporaryCache(o);
  }
  /**
   * Updates account, authority, and state in cache
   * @param serverAuthenticationRequest
   * @param account
   */
  updateCacheEntries(e, t, n, o, r) {
    this.logger.trace("BrowserCacheManager.updateCacheEntries called");
    const i = this.generateStateKey(e);
    this.setTemporaryCache(i, e, !1);
    const s = this.generateNonceKey(e);
    this.setTemporaryCache(s, t, !1);
    const c = this.generateAuthorityKey(e);
    if (this.setTemporaryCache(c, n, !1), r) {
      const h = {
        credential: r.homeAccountId,
        type: Q.HOME_ACCOUNT_ID
      };
      this.setTemporaryCache(_.CCS_CREDENTIAL, JSON.stringify(h), !0);
    } else if (o) {
      const h = {
        credential: o,
        type: Q.UPN
      };
      this.setTemporaryCache(_.CCS_CREDENTIAL, JSON.stringify(h), !0);
    }
  }
  /**
   * Reset all temporary cache items
   * @param state
   */
  resetRequestCache(e) {
    this.logger.trace("BrowserCacheManager.resetRequestCache called"), e && (this.temporaryCacheStorage.getKeys().forEach((t) => {
      t.indexOf(e) !== -1 && this.removeTemporaryItem(t);
    }), this.removeTemporaryItem(this.generateStateKey(e)), this.removeTemporaryItem(this.generateNonceKey(e)), this.removeTemporaryItem(this.generateAuthorityKey(e))), this.removeTemporaryItem(this.generateCacheKey(_.REQUEST_PARAMS)), this.removeTemporaryItem(this.generateCacheKey(_.ORIGIN_URI)), this.removeTemporaryItem(this.generateCacheKey(_.URL_HASH)), this.removeTemporaryItem(this.generateCacheKey(_.CORRELATION_ID)), this.removeTemporaryItem(this.generateCacheKey(_.CCS_CREDENTIAL)), this.removeTemporaryItem(this.generateCacheKey(_.NATIVE_REQUEST)), this.setInteractionInProgress(!1);
  }
  /**
   * Removes temporary cache for the provided state
   * @param stateString
   */
  cleanRequestByState(e) {
    if (this.logger.trace("BrowserCacheManager.cleanRequestByState called"), e) {
      const t = this.generateStateKey(e), n = this.temporaryCacheStorage.getItem(t);
      this.logger.infoPii(`BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: ${n}`), this.resetRequestCache(n || u.EMPTY_STRING);
    }
  }
  /**
   * Looks in temporary cache for any state values with the provided interactionType and removes all temporary cache items for that state
   * Used in scenarios where temp cache needs to be cleaned but state is not known, such as clicking browser back button.
   * @param interactionType
   */
  cleanRequestByInteractionType(e) {
    this.logger.trace("BrowserCacheManager.cleanRequestByInteractionType called"), this.temporaryCacheStorage.getKeys().forEach((t) => {
      if (t.indexOf(_.REQUEST_STATE) === -1)
        return;
      const n = this.temporaryCacheStorage.getItem(t);
      if (!n)
        return;
      const o = jr(this.cryptoImpl, n);
      o && o.interactionType === e && (this.logger.infoPii(`BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: ${n}`), this.resetRequestCache(n));
    }), this.setInteractionInProgress(!1);
  }
  cacheCodeRequest(e) {
    this.logger.trace("BrowserCacheManager.cacheCodeRequest called");
    const t = vn(JSON.stringify(e));
    this.setTemporaryCache(_.REQUEST_PARAMS, t, !0);
  }
  /**
   * Gets the token exchange parameters from the cache. Throws an error if nothing is found.
   */
  getCachedRequest(e) {
    this.logger.trace("BrowserCacheManager.getCachedRequest called");
    const t = this.getTemporaryCache(_.REQUEST_PARAMS, !0);
    if (!t)
      throw C(vr);
    let n;
    try {
      n = JSON.parse(oe(t));
    } catch (o) {
      throw this.logger.errorPii(`Attempted to parse: ${t}`), this.logger.error(`Parsing cached token request threw with error: ${o}`), C(wr);
    }
    if (this.removeTemporaryItem(this.generateCacheKey(_.REQUEST_PARAMS)), !n.authority) {
      const o = this.generateAuthorityKey(e), r = this.getTemporaryCache(o);
      if (!r)
        throw C(An);
      n.authority = r;
    }
    return n;
  }
  /**
   * Gets cached native request for redirect flows
   */
  getCachedNativeRequest() {
    this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");
    const e = this.getTemporaryCache(_.NATIVE_REQUEST, !0);
    if (!e)
      return this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"), null;
    const t = this.validateAndParseJson(e);
    return t || (this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"), null);
  }
  isInteractionInProgress(e) {
    const t = this.getInteractionInProgress();
    return e ? t === this.clientId : !!t;
  }
  getInteractionInProgress() {
    const e = `${u.CACHE_PREFIX}.${_.INTERACTION_STATUS_KEY}`;
    return this.getTemporaryCache(e, !1);
  }
  setInteractionInProgress(e) {
    const t = `${u.CACHE_PREFIX}.${_.INTERACTION_STATUS_KEY}`;
    if (e) {
      if (this.getInteractionInProgress())
        throw C(fr);
      this.setTemporaryCache(t, this.clientId, !1);
    } else !e && this.getInteractionInProgress() === this.clientId && this.removeTemporaryItem(t);
  }
  /**
   * Returns username retrieved from ADAL or MSAL v1 idToken
   * @deprecated
   */
  getLegacyLoginHint() {
    const e = this.getTemporaryCache(H.ADAL_ID_TOKEN);
    e && (this.browserStorage.removeItem(H.ADAL_ID_TOKEN), this.logger.verbose("Cached ADAL id token retrieved."));
    const t = this.getTemporaryCache(H.ID_TOKEN, !0);
    t && (this.browserStorage.removeItem(this.generateCacheKey(H.ID_TOKEN)), this.logger.verbose("Cached MSAL.js v1 id token retrieved"));
    const n = t || e;
    if (n) {
      const o = Ae(n, oe);
      if (o.preferred_username)
        return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint"), o.preferred_username;
      if (o.upn)
        return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint"), o.upn;
      this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.");
    }
    return null;
  }
  /**
   * Updates a credential's cache key if the current cache key is outdated
   */
  updateCredentialCacheKey(e, t, n) {
    const o = ke(t);
    if (e !== o) {
      const r = this.getItem(e);
      if (r)
        return this.browserStorage.removeItem(e), this.setItem(o, r, n), this.logger.verbose(`Updated an outdated ${t.credentialType} cache key`), o;
      this.logger.error(`Attempted to update an outdated ${t.credentialType} cache key but no item matching the outdated key was found in storage`);
    }
    return e;
  }
  /**
   * Builds credential entities from AuthenticationResult object and saves the resulting credentials to the cache
   * @param result
   * @param request
   */
  async hydrateCache(e, t) {
    var s, c, h;
    const n = pt((s = e.account) == null ? void 0 : s.homeAccountId, (c = e.account) == null ? void 0 : c.environment, e.idToken, this.clientId, e.tenantId);
    let o;
    t.claims && (o = await this.cryptoImpl.hashString(t.claims));
    const r = mt(
      (h = e.account) == null ? void 0 : h.homeAccountId,
      e.account.environment,
      e.accessToken,
      this.clientId,
      e.tenantId,
      e.scopes.join(" "),
      e.expiresOn ? e.expiresOn.getTime() / 1e3 : 0,
      e.extExpiresOn ? e.extExpiresOn.getTime() / 1e3 : 0,
      oe,
      void 0,
      // refreshOn
      e.tokenType,
      void 0,
      // userAssertionHash
      t.sshKid,
      t.claims,
      o
    ), i = {
      idToken: n,
      accessToken: r
    };
    return this.saveCacheRecord(i, e.correlationId);
  }
  /**
   * saves a cache record
   * @param cacheRecord {CacheRecord}
   * @param storeInCache {?StoreInCache}
   * @param correlationId {?string} correlation id
   */
  async saveCacheRecord(e, t, n) {
    try {
      await super.saveCacheRecord(e, t, n);
    } catch (o) {
      if (o instanceof Oe && this.performanceClient && t)
        try {
          const r = this.getTokenKeys();
          this.performanceClient.addFields({
            cacheRtCount: r.refreshToken.length,
            cacheIdCount: r.idToken.length,
            cacheAtCount: r.accessToken.length
          }, t);
        } catch {
        }
      throw o;
    }
  }
}
const zs = (a, e) => {
  const t = {
    cacheLocation: q.MemoryStorage,
    temporaryCacheLocation: q.MemoryStorage,
    storeAuthStateInCookie: !1,
    secureCookies: !1,
    cacheMigrationEnabled: !1,
    claimsBasedCachingEnabled: !1
  };
  return new $t(a, t, it, e);
};
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function Vs(a, e, t, n, o) {
  return a.verbose("getAllAccounts called"), t ? e.getAllAccounts(n, o) : [];
}
function Qs(a, e, t, n) {
  if (e.trace("getAccount called"), Object.keys(a).length === 0)
    return e.warning("getAccount: No accountFilter provided"), null;
  const o = t.getAccountInfoFilteredBy(a, n);
  return o ? (e.verbose("getAccount: Account matching provided filter found, returning"), o) : (e.verbose("getAccount: No matching account found, returning null"), null);
}
function Ys(a, e, t, n) {
  if (e.trace("getAccountByUsername called"), !a)
    return e.warning("getAccountByUsername: No username provided"), null;
  const o = t.getAccountInfoFilteredBy({
    username: a
  }, n);
  return o ? (e.verbose("getAccountByUsername: Account matching username found, returning"), e.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${a}`), o) : (e.verbose("getAccountByUsername: No matching account found, returning null"), null);
}
function Ws(a, e, t, n) {
  if (e.trace("getAccountByHomeId called"), !a)
    return e.warning("getAccountByHomeId: No homeAccountId provided"), null;
  const o = t.getAccountInfoFilteredBy({
    homeAccountId: a
  }, n);
  return o ? (e.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"), e.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${a}`), o) : (e.verbose("getAccountByHomeId: No matching account found, returning null"), null);
}
function js(a, e, t, n) {
  if (e.trace("getAccountByLocalId called"), !a)
    return e.warning("getAccountByLocalId: No localAccountId provided"), null;
  const o = t.getAccountInfoFilteredBy({
    localAccountId: a
  }, n);
  return o ? (e.verbose("getAccountByLocalId: Account matching localAccountId found, returning"), e.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${a}`), o) : (e.verbose("getAccountByLocalId: No matching account found, returning null"), null);
}
function Js(a, e, t) {
  e.setActiveAccount(a, t);
}
function Xs(a, e) {
  return a.getActiveAccount(e);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const I = {
  INITIALIZE_START: "msal:initializeStart",
  INITIALIZE_END: "msal:initializeEnd",
  ACCOUNT_ADDED: "msal:accountAdded",
  ACCOUNT_REMOVED: "msal:accountRemoved",
  ACTIVE_ACCOUNT_CHANGED: "msal:activeAccountChanged",
  LOGIN_START: "msal:loginStart",
  LOGIN_SUCCESS: "msal:loginSuccess",
  LOGIN_FAILURE: "msal:loginFailure",
  ACQUIRE_TOKEN_START: "msal:acquireTokenStart",
  ACQUIRE_TOKEN_SUCCESS: "msal:acquireTokenSuccess",
  ACQUIRE_TOKEN_FAILURE: "msal:acquireTokenFailure",
  ACQUIRE_TOKEN_NETWORK_START: "msal:acquireTokenFromNetworkStart",
  SSO_SILENT_START: "msal:ssoSilentStart",
  SSO_SILENT_SUCCESS: "msal:ssoSilentSuccess",
  SSO_SILENT_FAILURE: "msal:ssoSilentFailure",
  ACQUIRE_TOKEN_BY_CODE_START: "msal:acquireTokenByCodeStart",
  ACQUIRE_TOKEN_BY_CODE_SUCCESS: "msal:acquireTokenByCodeSuccess",
  ACQUIRE_TOKEN_BY_CODE_FAILURE: "msal:acquireTokenByCodeFailure",
  HANDLE_REDIRECT_START: "msal:handleRedirectStart",
  HANDLE_REDIRECT_END: "msal:handleRedirectEnd",
  POPUP_OPENED: "msal:popupOpened",
  LOGOUT_START: "msal:logoutStart",
  LOGOUT_SUCCESS: "msal:logoutSuccess",
  LOGOUT_FAILURE: "msal:logoutFailure",
  LOGOUT_END: "msal:logoutEnd",
  RESTORE_FROM_BFCACHE: "msal:restoreFromBFCache"
};
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Zs {
  constructor(e) {
    this.eventCallbacks = /* @__PURE__ */ new Map(), this.logger = e || new pe({});
  }
  /**
   * Adds event callbacks to array
   * @param callback - callback to be invoked when an event is raised
   * @param eventTypes - list of events that this callback will be invoked for, if not provided callback will be invoked for all events
   * @param callbackId - Identifier for the callback, used to locate and remove the callback when no longer required
   */
  addEventCallback(e, t, n) {
    if (typeof window < "u") {
      const o = n || Os();
      return this.eventCallbacks.has(o) ? (this.logger.error(`Event callback with id: ${o} is already registered. Please provide a unique id or remove the existing callback and try again.`), null) : (this.eventCallbacks.set(o, [e, t || []]), this.logger.verbose(`Event callback registered with id: ${o}`), o);
    }
    return null;
  }
  /**
   * Removes callback with provided id from callback array
   * @param callbackId
   */
  removeEventCallback(e) {
    this.eventCallbacks.delete(e), this.logger.verbose(`Event callback ${e} removed.`);
  }
  /**
   * Emits events by calling callback with event message
   * @param eventType
   * @param interactionType
   * @param payload
   * @param error
   */
  emitEvent(e, t, n, o) {
    if (typeof window < "u") {
      const r = {
        eventType: e,
        interactionType: t || null,
        payload: n || null,
        error: o || null,
        timestamp: Date.now()
      };
      this.eventCallbacks.forEach(([i, s], c) => {
        (s.length === 0 || s.includes(e)) && (this.logger.verbose(`Emitting event to callback ${c}: ${e}`), i.apply(null, [r]));
      });
    }
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Jr {
  constructor(e, t, n, o, r, i, s, c, h) {
    this.config = e, this.browserStorage = t, this.browserCrypto = n, this.networkClient = this.config.system.networkClient, this.eventHandler = r, this.navigationClient = i, this.nativeMessageHandler = c, this.correlationId = h || ie(), this.logger = o.clone(V.MSAL_SKU, Le, this.correlationId), this.performanceClient = s;
  }
  async clearCacheOnLogout(e) {
    if (e) {
      U.accountInfoIsEqual(e, this.browserStorage.getActiveAccount(this.correlationId), !1) && (this.logger.verbose("Setting active account to null"), this.browserStorage.setActiveAccount(null, this.correlationId));
      try {
        await this.browserStorage.removeAccount(U.generateAccountCacheKey(e), this.correlationId), this.logger.verbose("Cleared cache items belonging to the account provided in the logout request.");
      } catch {
        this.logger.error("Account provided in logout request was not found. Local cache unchanged.");
      }
    } else
      try {
        this.logger.verbose("No account provided in logout request, clearing all cache items.", this.correlationId), await this.browserStorage.clear(this.correlationId), await this.browserCrypto.clearKeystore();
      } catch {
        this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged.");
      }
  }
  /**
   *
   * Use to get the redirect uri configured in MSAL or null.
   * @param requestRedirectUri
   * @returns Redirect URL
   *
   */
  getRedirectUri(e) {
    this.logger.verbose("getRedirectUri called");
    const t = e || this.config.auth.redirectUri;
    return w.getAbsoluteUrl(t, he());
  }
  /**
   *
   * @param apiId
   * @param correlationId
   * @param forceRefresh
   */
  initializeServerTelemetryManager(e, t) {
    this.logger.verbose("initializeServerTelemetryManager called");
    const n = {
      clientId: this.config.auth.clientId,
      correlationId: this.correlationId,
      apiId: e,
      forceRefresh: t || !1,
      wrapperSKU: this.browserStorage.getWrapperMetadata()[0],
      wrapperVer: this.browserStorage.getWrapperMetadata()[1]
    };
    return new qe(n, this.browserStorage);
  }
  /**
   * Used to get a discovered version of the default authority.
   * @param params {
   *         requestAuthority?: string;
   *         requestAzureCloudOptions?: AzureCloudOptions;
   *         requestExtraQueryParameters?: StringDict;
   *         account?: AccountInfo;
   *        }
   */
  async getDiscoveredAuthority(e) {
    const { account: t } = e, n = e.requestExtraQueryParameters && e.requestExtraQueryParameters.hasOwnProperty("instance_aware") ? e.requestExtraQueryParameters.instance_aware : void 0;
    this.performanceClient.addQueueMeasurement(l.StandardInteractionClientGetDiscoveredAuthority, this.correlationId);
    const o = {
      protocolMode: this.config.auth.protocolMode,
      OIDCOptions: this.config.auth.OIDCOptions,
      knownAuthorities: this.config.auth.knownAuthorities,
      cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,
      authorityMetadata: this.config.auth.authorityMetadata,
      skipAuthorityMetadataCache: this.config.auth.skipAuthorityMetadataCache
    }, r = e.requestAuthority || this.config.auth.authority, i = n != null && n.length ? n === "true" : this.config.auth.instanceAware, s = t && i ? this.config.auth.authority.replace(w.getDomainFromUrl(r), t.environment) : r, c = x.generateAuthority(s, e.requestAzureCloudOptions || this.config.auth.azureCloudOptions), h = await m(ir, l.AuthorityFactoryCreateDiscoveredInstance, this.logger, this.performanceClient, this.correlationId)(c, this.config.system.networkClient, this.browserStorage, o, this.logger, this.correlationId, this.performanceClient);
    if (t && !h.isAlias(t.environment))
      throw R(jo);
    return h;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const ec = 32;
async function tc(a, e, t) {
  a.addQueueMeasurement(l.GeneratePkceCodes, t);
  const n = Ee(nc, l.GenerateCodeVerifier, e, a, t)(a, e, t), o = await m(oc, l.GenerateCodeChallengeFromVerifier, e, a, t)(n, a, e, t);
  return {
    verifier: n,
    challenge: o
  };
}
function nc(a, e, t) {
  try {
    const n = new Uint8Array(ec);
    return Ee(Cs, l.GetRandomValues, e, a, t)(n), St(n);
  } catch {
    throw C(Tn);
  }
}
async function oc(a, e, t, n) {
  e.addQueueMeasurement(l.GenerateCodeChallengeFromVerifier, n);
  try {
    const o = await m($r, l.Sha256Digest, t, e, n)(a, e, n);
    return St(new Uint8Array(o));
  } catch {
    throw C(Tn);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
async function Pn(a, e, t, n) {
  t.addQueueMeasurement(l.InitializeBaseRequest, a.correlationId);
  const o = a.authority || e.auth.authority, r = [...a && a.scopes || []], i = {
    ...a,
    correlationId: a.correlationId,
    authority: o,
    scopes: r
  };
  if (!i.authenticationScheme)
    i.authenticationScheme = k.BEARER, n.verbose(`Authentication Scheme wasn't explicitly set in request, defaulting to "Bearer" request`);
  else {
    if (i.authenticationScheme === k.SSH) {
      if (!a.sshJwk)
        throw R(ft);
      if (!a.sshKid)
        throw R(Qo);
    }
    n.verbose(`Authentication Scheme set to "${i.authenticationScheme}" as configured in Auth request`);
  }
  return e.cache.claimsBasedCachingEnabled && a.claims && // Checks for empty stringified object "{}" which doesn't qualify as requested claims
  !j.isEmptyObj(a.claims) && (i.requestedClaimsHash = await zr(a.claims)), i;
}
async function rc(a, e, t, n, o) {
  n.addQueueMeasurement(l.InitializeSilentRequest, a.correlationId);
  const r = await m(Pn, l.InitializeBaseRequest, o, n, a.correlationId)(a, t, n, o);
  return {
    ...a,
    ...r,
    account: e,
    forceRefresh: a.forceRefresh || !1
  };
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Ke extends Jr {
  /**
   * Generates an auth code request tied to the url request.
   * @param request
   */
  async initializeAuthorizationCodeRequest(e) {
    this.performanceClient.addQueueMeasurement(l.StandardInteractionClientInitializeAuthorizationCodeRequest, this.correlationId);
    const t = await m(tc, l.GeneratePkceCodes, this.logger, this.performanceClient, this.correlationId)(this.performanceClient, this.logger, this.correlationId), n = {
      ...e,
      redirectUri: e.redirectUri,
      code: u.EMPTY_STRING,
      codeVerifier: t.verifier
    };
    return e.codeChallenge = t.challenge, e.codeChallengeMethod = u.S256_CODE_CHALLENGE_METHOD, n;
  }
  /**
   * Initializer for the logout request.
   * @param logoutRequest
   */
  initializeLogoutRequest(e) {
    this.logger.verbose("initializeLogoutRequest called", e == null ? void 0 : e.correlationId);
    const t = {
      correlationId: this.correlationId || ie(),
      ...e
    };
    if (e)
      if (e.logoutHint)
        this.logger.verbose("logoutHint has already been set in logoutRequest");
      else if (e.account) {
        const n = this.getLogoutHintFromIdTokenClaims(e.account);
        n && (this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"), t.logoutHint = n);
      } else
        this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set");
    else
      this.logger.verbose("logoutHint will not be set since no logout request was configured");
    return !e || e.postLogoutRedirectUri !== null ? e && e.postLogoutRedirectUri ? (this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request", t.correlationId), t.postLogoutRedirectUri = w.getAbsoluteUrl(e.postLogoutRedirectUri, he())) : this.config.auth.postLogoutRedirectUri === null ? this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect", t.correlationId) : this.config.auth.postLogoutRedirectUri ? (this.logger.verbose("Setting postLogoutRedirectUri to configured uri", t.correlationId), t.postLogoutRedirectUri = w.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri, he())) : (this.logger.verbose("Setting postLogoutRedirectUri to current page", t.correlationId), t.postLogoutRedirectUri = w.getAbsoluteUrl(he(), he())) : this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri", t.correlationId), t;
  }
  /**
   * Parses login_hint ID Token Claim out of AccountInfo object to be used as
   * logout_hint in end session request.
   * @param account
   */
  getLogoutHintFromIdTokenClaims(e) {
    const t = e.idTokenClaims;
    if (t) {
      if (t.login_hint)
        return t.login_hint;
      this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request");
    } else
      this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");
    return null;
  }
  /**
   * Creates an Authorization Code Client with the given authority, or the default authority.
   * @param params {
   *         serverTelemetryManager: ServerTelemetryManager;
   *         authorityUrl?: string;
   *         requestAzureCloudOptions?: AzureCloudOptions;
   *         requestExtraQueryParameters?: StringDict;
   *         account?: AccountInfo;
   *        }
   */
  async createAuthCodeClient(e) {
    this.performanceClient.addQueueMeasurement(l.StandardInteractionClientCreateAuthCodeClient, this.correlationId);
    const t = await m(this.getClientConfiguration.bind(this), l.StandardInteractionClientGetClientConfiguration, this.logger, this.performanceClient, this.correlationId)(e);
    return new cr(t, this.performanceClient);
  }
  /**
   * Creates a Client Configuration object with the given request authority, or the default authority.
   * @param params {
   *         serverTelemetryManager: ServerTelemetryManager;
   *         requestAuthority?: string;
   *         requestAzureCloudOptions?: AzureCloudOptions;
   *         requestExtraQueryParameters?: boolean;
   *         account?: AccountInfo;
   *        }
   */
  async getClientConfiguration(e) {
    const { serverTelemetryManager: t, requestAuthority: n, requestAzureCloudOptions: o, requestExtraQueryParameters: r, account: i } = e;
    this.performanceClient.addQueueMeasurement(l.StandardInteractionClientGetClientConfiguration, this.correlationId);
    const s = await m(this.getDiscoveredAuthority.bind(this), l.StandardInteractionClientGetDiscoveredAuthority, this.logger, this.performanceClient, this.correlationId)({
      requestAuthority: n,
      requestAzureCloudOptions: o,
      requestExtraQueryParameters: r,
      account: i
    }), c = this.config.system.loggerOptions;
    return {
      authOptions: {
        clientId: this.config.auth.clientId,
        authority: s,
        clientCapabilities: this.config.auth.clientCapabilities,
        redirectUri: this.config.auth.redirectUri
      },
      systemOptions: {
        tokenRenewalOffsetSeconds: this.config.system.tokenRenewalOffsetSeconds,
        preventCorsPreflight: !0
      },
      loggerOptions: {
        loggerCallback: c.loggerCallback,
        piiLoggingEnabled: c.piiLoggingEnabled,
        logLevel: c.logLevel,
        correlationId: this.correlationId
      },
      cacheOptions: {
        claimsBasedCachingEnabled: this.config.cache.claimsBasedCachingEnabled
      },
      cryptoInterface: this.browserCrypto,
      networkInterface: this.networkClient,
      storageInterface: this.browserStorage,
      serverTelemetryManager: t,
      libraryInfo: {
        sku: V.MSAL_SKU,
        version: Le,
        cpu: u.EMPTY_STRING,
        os: u.EMPTY_STRING
      },
      telemetry: this.config.telemetry
    };
  }
  /**
   * Helper to initialize required request parameters for interactive APIs and ssoSilent()
   * @param request
   * @param interactionType
   */
  async initializeAuthorizationRequest(e, t) {
    this.performanceClient.addQueueMeasurement(l.StandardInteractionClientInitializeAuthorizationRequest, this.correlationId);
    const n = this.getRedirectUri(e.redirectUri), o = {
      interactionType: t
    }, r = J.setRequestState(this.browserCrypto, e && e.state || u.EMPTY_STRING, o), s = {
      ...await m(Pn, l.InitializeBaseRequest, this.logger, this.performanceClient, this.correlationId)({ ...e, correlationId: this.correlationId }, this.config, this.performanceClient, this.logger),
      redirectUri: n,
      state: r,
      nonce: e.nonce || ie(),
      responseMode: this.config.auth.OIDCOptions.serverResponseType
    };
    if (e.loginHint || e.sid)
      return s;
    const c = e.account || this.browserStorage.getActiveAccount(this.correlationId);
    if (c && (this.logger.verbose("Setting validated request account", this.correlationId), this.logger.verbosePii(`Setting validated request account: ${c.homeAccountId}`, this.correlationId), s.account = c), !s.loginHint && !c) {
      const h = this.browserStorage.getLegacyLoginHint();
      h && (s.loginHint = h);
    }
    return s;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const ic = "ContentError", Xr = "user_switch";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const ac = "USER_INTERACTION_REQUIRED", sc = "USER_CANCEL", cc = "NO_NETWORK", hc = "PERSISTENT_ERROR", lc = "DISABLED", dc = "ACCOUNT_UNAVAILABLE";
/*! @azure/msal-browser v3.30.0 2025-08-05 */
const uc = -**********, gc = {
  [Xr]: "User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."
};
class te extends O {
  constructor(e, t, n) {
    super(e, t), Object.setPrototypeOf(this, te.prototype), this.name = "NativeAuthError", this.ext = n;
  }
}
function _e(a) {
  if (a.ext && a.ext.status && (a.ext.status === hc || a.ext.status === lc) || a.ext && a.ext.error && a.ext.error === uc)
    return !0;
  switch (a.errorCode) {
    case ic:
      return !0;
    default:
      return !1;
  }
}
function zt(a, e, t) {
  if (t && t.status)
    switch (t.status) {
      case dc:
        return Dt(ar);
      case ac:
        return new X(a, e);
      case sc:
        return C(Te);
      case cc:
        return C(dt);
    }
  return new te(a, gc[a] || e, t);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Zr extends Ke {
  /**
   * Returns unexpired tokens from the cache, if available
   * @param silentRequest
   */
  async acquireToken(e) {
    this.performanceClient.addQueueMeasurement(l.SilentCacheClientAcquireToken, e.correlationId);
    const t = this.initializeServerTelemetryManager(b.acquireTokenSilent_silentFlow), n = await m(this.getClientConfiguration.bind(this), l.StandardInteractionClientGetClientConfiguration, this.logger, this.performanceClient, this.correlationId)({
      serverTelemetryManager: t,
      requestAuthority: e.authority,
      requestAzureCloudOptions: e.azureCloudOptions,
      account: e.account
    }), o = new Wa(n, this.performanceClient);
    this.logger.verbose("Silent auth client created");
    try {
      const i = (await m(o.acquireCachedToken.bind(o), l.SilentFlowClientAcquireCachedToken, this.logger, this.performanceClient, e.correlationId)(e))[0];
      return this.performanceClient.addFields({
        fromCache: !0
      }, e.correlationId), i;
    } catch (r) {
      throw r instanceof Ve && r.errorCode === En && this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."), r;
    }
  }
  /**
   * API to silenty clear the browser cache.
   * @param logoutRequest
   */
  logout(e) {
    this.logger.verbose("logoutRedirect called");
    const t = this.initializeLogoutRequest(e);
    return this.clearCacheOnLogout(t == null ? void 0 : t.account);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Ne extends Jr {
  constructor(e, t, n, o, r, i, s, c, h, d, g, f) {
    var E;
    super(e, t, n, o, r, i, c, h, f), this.apiId = s, this.accountId = d, this.nativeMessageHandler = h, this.nativeStorageManager = g, this.silentCacheClient = new Zr(e, this.nativeStorageManager, n, o, r, i, c, h, f), this.serverTelemetryManager = this.initializeServerTelemetryManager(this.apiId);
    const T = this.nativeMessageHandler.getExtensionId() === be.PREFERRED_EXTENSION_ID ? "chrome" : (E = this.nativeMessageHandler.getExtensionId()) != null && E.length ? "unknown" : void 0;
    this.skus = qe.makeExtraSkuString({
      libraryName: V.MSAL_SKU,
      libraryVersion: Le,
      extensionName: T,
      extensionVersion: this.nativeMessageHandler.getExtensionVersion()
    });
  }
  /**
   * Adds SKUs to request extra query parameters
   * @param request {NativeTokenRequest}
   * @private
   */
  addRequestSKUs(e) {
    e.extraParameters = {
      ...e.extraParameters,
      [Pa]: this.skus
    };
  }
  /**
   * Acquire token from native platform via browser extension
   * @param request
   */
  async acquireToken(e) {
    this.performanceClient.addQueueMeasurement(l.NativeInteractionClientAcquireToken, e.correlationId), this.logger.trace("NativeInteractionClient - acquireToken called.");
    const t = this.performanceClient.startMeasurement(l.NativeInteractionClientAcquireToken, e.correlationId), n = re();
    try {
      const o = await this.initializeNativeRequest(e);
      try {
        const h = await this.acquireTokensFromCache(this.accountId, o);
        return t.end({
          success: !0,
          isNativeBroker: !1,
          fromCache: !0
        }), h;
      } catch {
        this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call");
      }
      const { ...r } = o, i = {
        method: fe.GetToken,
        request: r
      }, s = await this.nativeMessageHandler.sendMessage(i), c = this.validateNativeResponse(s);
      return await this.handleNativeResponse(c, o, n).then((h) => (t.end({
        success: !0,
        isNativeBroker: !0,
        requestId: h.requestId
      }), this.serverTelemetryManager.clearNativeBrokerErrorCode(), h)).catch((h) => {
        throw t.end({
          success: !1,
          errorCode: h.errorCode,
          subErrorCode: h.subError,
          isNativeBroker: !0
        }), h;
      });
    } catch (o) {
      throw o instanceof te && this.serverTelemetryManager.setNativeBrokerErrorCode(o.errorCode), o;
    }
  }
  /**
   * Creates silent flow request
   * @param request
   * @param cachedAccount
   * @returns CommonSilentFlowRequest
   */
  createSilentCacheRequest(e, t) {
    return {
      authority: e.authority,
      correlationId: this.correlationId,
      scopes: P.fromString(e.scope).asArray(),
      account: t,
      forceRefresh: !1
    };
  }
  /**
   * Fetches the tokens from the cache if un-expired
   * @param nativeAccountId
   * @param request
   * @returns authenticationResult
   */
  async acquireTokensFromCache(e, t) {
    if (!e)
      throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"), p(Nt);
    const n = this.browserStorage.getBaseAccountInfo({
      nativeAccountId: e
    }, t.correlationId);
    if (!n)
      throw p(Nt);
    try {
      const o = this.createSilentCacheRequest(t, n), r = await this.silentCacheClient.acquireToken(o), i = {
        ...n,
        idTokenClaims: r == null ? void 0 : r.idTokenClaims,
        idToken: r == null ? void 0 : r.idToken
      };
      return {
        ...r,
        account: i
      };
    } catch (o) {
      throw o;
    }
  }
  /**
   * Acquires a token from native platform then redirects to the redirectUri instead of returning the response
   * @param {RedirectRequest} request
   * @param {InProgressPerformanceEvent} rootMeasurement
   */
  async acquireTokenRedirect(e, t) {
    this.logger.trace("NativeInteractionClient - acquireTokenRedirect called.");
    const { ...n } = e;
    delete n.onRedirectNavigate;
    const o = await this.initializeNativeRequest(n), r = {
      method: fe.GetToken,
      request: o
    };
    try {
      const c = await this.nativeMessageHandler.sendMessage(r);
      this.validateNativeResponse(c);
    } catch (c) {
      if (c instanceof te && (this.serverTelemetryManager.setNativeBrokerErrorCode(c.errorCode), _e(c)))
        throw c;
    }
    this.browserStorage.setTemporaryCache(_.NATIVE_REQUEST, JSON.stringify(o), !0);
    const i = {
      apiId: b.acquireTokenRedirect,
      timeout: this.config.system.redirectNavigationTimeout,
      noHistory: !1
    }, s = this.config.auth.navigateToLoginRequestUrl ? window.location.href : this.getRedirectUri(e.redirectUri);
    t.end({ success: !0 }), await this.navigationClient.navigateExternal(s, i);
  }
  /**
   * If the previous page called native platform for a token using redirect APIs, send the same request again and return the response
   * @param performanceClient {IPerformanceClient?}
   * @param correlationId {string?} correlation identifier
   */
  async handleRedirectPromise(e, t) {
    if (this.logger.trace("NativeInteractionClient - handleRedirectPromise called."), !this.browserStorage.isInteractionInProgress(!0))
      return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."), null;
    const n = this.browserStorage.getCachedNativeRequest();
    if (!n)
      return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."), e && t && (e == null || e.addFields({ errorCode: "no_cached_request" }, t)), null;
    const { prompt: o, ...r } = n;
    o && this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."), this.browserStorage.removeItem(this.browserStorage.generateCacheKey(_.NATIVE_REQUEST));
    const i = {
      method: fe.GetToken,
      request: r
    }, s = re();
    try {
      this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker.");
      const c = await this.nativeMessageHandler.sendMessage(i);
      this.validateNativeResponse(c);
      const h = this.handleNativeResponse(c, r, s);
      this.browserStorage.setInteractionInProgress(!1);
      const d = await h;
      return this.serverTelemetryManager.clearNativeBrokerErrorCode(), d;
    } catch (c) {
      throw this.browserStorage.setInteractionInProgress(!1), c;
    }
  }
  /**
   * Logout from native platform via browser extension
   * @param request
   */
  logout() {
    return this.logger.trace("NativeInteractionClient - logout called."), Promise.reject("Logout not implemented yet");
  }
  /**
   * Transform response from native platform into AuthenticationResult object which will be returned to the end user
   * @param response
   * @param request
   * @param reqTimestamp
   */
  async handleNativeResponse(e, t, n) {
    var d;
    this.logger.trace("NativeInteractionClient - handleNativeResponse called.");
    const o = Ae(e.id_token, oe), r = this.createHomeAccountIdentifier(e, o), i = (d = this.browserStorage.getAccountInfoFilteredBy({
      nativeAccountId: t.accountId
    }, this.correlationId)) == null ? void 0 : d.homeAccountId;
    if (r !== i && e.account.id !== t.accountId)
      throw zt(Xr);
    const s = await this.getDiscoveredAuthority({
      requestAuthority: t.authority
    }), c = fn(
      this.browserStorage,
      s,
      r,
      oe,
      this.correlationId,
      o,
      e.client_info,
      void 0,
      // environment
      o.tid,
      void 0,
      // auth code payload
      e.account.id,
      this.logger
    ), h = await this.generateAuthenticationResult(e, t, o, c, s.canonicalAuthority, n);
    return this.cacheAccount(c), this.cacheNativeTokens(e, t, r, o, e.access_token, h.tenantId, n), h;
  }
  /**
   * creates an homeAccountIdentifier for the account
   * @param response
   * @param idTokenObj
   * @returns
   */
  createHomeAccountIdentifier(e, t) {
    return U.generateHomeAccountId(e.client_info || u.EMPTY_STRING, W.Default, this.logger, this.browserCrypto, t);
  }
  /**
   * Helper to generate scopes
   * @param response
   * @param request
   * @returns
   */
  generateScopes(e, t) {
    return e.scope ? P.fromString(e.scope) : P.fromString(t.scope);
  }
  /**
   * If PoP token is requesred, records the PoP token if returned from the WAM, else generates one in the browser
   * @param request
   * @param response
   */
  async generatePopAccessToken(e, t) {
    if (t.tokenType === k.POP && t.signPopToken) {
      if (e.shr)
        return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"), e.shr;
      const n = new Ue(this.browserCrypto), o = {
        resourceRequestMethod: t.resourceRequestMethod,
        resourceRequestUri: t.resourceRequestUri,
        shrClaims: t.shrClaims,
        shrNonce: t.shrNonce
      };
      if (!t.keyId)
        throw p(Zt);
      return n.signPopToken(e.access_token, t.keyId, o);
    } else
      return e.access_token;
  }
  /**
   * Generates authentication result
   * @param response
   * @param request
   * @param idTokenObj
   * @param accountEntity
   * @param authority
   * @param reqTimestamp
   * @returns
   */
  async generateAuthenticationResult(e, t, n, o, r, i) {
    const s = this.addTelemetryFromNativeResponse(e), c = e.scope ? P.fromString(e.scope) : P.fromString(t.scope), h = e.account.properties || {}, d = h.UID || n.oid || n.sub || u.EMPTY_STRING, g = h.TenantId || n.tid || u.EMPTY_STRING, f = hn(
      o.getAccountInfo(),
      void 0,
      // tenantProfile optional
      n,
      e.id_token
    );
    f.nativeAccountId !== e.account.id && (f.nativeAccountId = e.account.id);
    const T = await this.generatePopAccessToken(e, t), E = t.tokenType === k.POP ? k.POP : k.BEARER;
    return {
      authority: r,
      uniqueId: d,
      tenantId: g,
      scopes: c.asArray(),
      account: f,
      idToken: e.id_token,
      idTokenClaims: n,
      accessToken: T,
      fromCache: s ? this.isResponseFromCache(s) : !1,
      expiresOn: new Date(Number(i + e.expires_in) * 1e3),
      tokenType: E,
      correlationId: this.correlationId,
      state: e.state,
      fromNativeBroker: !0
    };
  }
  /**
   * cache the account entity in browser storage
   * @param accountEntity
   */
  cacheAccount(e) {
    this.browserStorage.setAccount(e, this.correlationId), this.browserStorage.removeAccountContext(e, this.correlationId).catch((t) => {
      this.logger.error(`Error occurred while removing account context from browser storage. ${t}`);
    });
  }
  /**
   * Stores the access_token and id_token in inmemory storage
   * @param response
   * @param request
   * @param homeAccountIdentifier
   * @param idTokenObj
   * @param responseAccessToken
   * @param tenantId
   * @param reqTimestamp
   */
  cacheNativeTokens(e, t, n, o, r, i, s) {
    const c = pt(n, t.authority, e.id_token || "", t.clientId, o.tid || ""), h = t.tokenType === k.POP ? u.SHR_NONCE_VALIDITY : (typeof e.expires_in == "string" ? parseInt(e.expires_in, 10) : e.expires_in) || 0, d = s + h, g = this.generateScopes(e, t), f = mt(n, t.authority, r, t.clientId, o.tid || i, g.printScopes(), d, 0, oe, void 0, t.tokenType, void 0, t.keyId), T = {
      idToken: c,
      accessToken: f
    };
    this.nativeStorageManager.saveCacheRecord(T, t.correlationId, t.storeInCache);
  }
  addTelemetryFromNativeResponse(e) {
    const t = this.getMATSFromResponse(e);
    return t ? (this.performanceClient.addFields({
      extensionId: this.nativeMessageHandler.getExtensionId(),
      extensionVersion: this.nativeMessageHandler.getExtensionVersion(),
      matsBrokerVersion: t.broker_version,
      matsAccountJoinOnStart: t.account_join_on_start,
      matsAccountJoinOnEnd: t.account_join_on_end,
      matsDeviceJoin: t.device_join,
      matsPromptBehavior: t.prompt_behavior,
      matsApiErrorCode: t.api_error_code,
      matsUiVisible: t.ui_visible,
      matsSilentCode: t.silent_code,
      matsSilentBiSubCode: t.silent_bi_sub_code,
      matsSilentMessage: t.silent_message,
      matsSilentStatus: t.silent_status,
      matsHttpStatus: t.http_status,
      matsHttpEventCount: t.http_event_count
    }, this.correlationId), t) : null;
  }
  /**
   * Validates native platform response before processing
   * @param response
   */
  validateNativeResponse(e) {
    if (e.hasOwnProperty("access_token") && e.hasOwnProperty("id_token") && e.hasOwnProperty("client_info") && e.hasOwnProperty("account") && e.hasOwnProperty("scope") && e.hasOwnProperty("expires_in"))
      return e;
    throw Co(Qt, "Response missing expected properties.");
  }
  /**
   * Gets MATS telemetry from native response
   * @param response
   * @returns
   */
  getMATSFromResponse(e) {
    if (e.properties.MATS)
      try {
        return JSON.parse(e.properties.MATS);
      } catch {
        this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead");
      }
    return null;
  }
  /**
   * Returns whether or not response came from native cache
   * @param response
   * @returns
   */
  isResponseFromCache(e) {
    return typeof e.is_cached > "u" ? (this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."), !1) : !!e.is_cached;
  }
  /**
   * Translates developer provided request object into NativeRequest object
   * @param request
   */
  async initializeNativeRequest(e) {
    this.logger.trace("NativeInteractionClient - initializeNativeRequest called");
    const t = e.authority || this.config.auth.authority;
    e.account && await this.getDiscoveredAuthority({
      requestAuthority: t,
      requestAzureCloudOptions: e.azureCloudOptions,
      account: e.account
    });
    const n = new w(t);
    n.validateAsUri();
    const { scopes: o, ...r } = e, i = new P(o || []);
    i.appendScopes(De);
    const s = () => {
      switch (this.apiId) {
        case b.ssoSilent:
        case b.acquireTokenSilent_silentFlow:
          return this.logger.trace("initializeNativeRequest: silent request sets prompt to none"), D.NONE;
      }
      if (!e.prompt) {
        this.logger.trace("initializeNativeRequest: prompt was not provided");
        return;
      }
      switch (e.prompt) {
        case D.NONE:
        case D.CONSENT:
        case D.LOGIN:
          return this.logger.trace("initializeNativeRequest: prompt is compatible with native flow"), e.prompt;
        default:
          throw this.logger.trace(`initializeNativeRequest: prompt = ${e.prompt} is not compatible with native flow`), C(Hr);
      }
    }, c = {
      ...r,
      accountId: this.accountId,
      clientId: this.config.auth.clientId,
      authority: n.urlString,
      scope: i.printScopes(),
      redirectUri: this.getRedirectUri(e.redirectUri),
      prompt: s(),
      correlationId: this.correlationId,
      tokenType: e.authenticationScheme,
      windowTitleSubstring: document.title,
      extraParameters: {
        ...e.extraQueryParameters,
        ...e.tokenQueryParameters
      },
      extendedExpiryToken: !1,
      keyId: e.popKid
    };
    if (c.signPopToken && e.popKid)
      throw C(Kr);
    if (this.handleExtraBrokerParams(c), c.extraParameters = c.extraParameters || {}, c.extraParameters.telemetry = be.MATS_TELEMETRY, e.authenticationScheme === k.POP) {
      const h = {
        resourceRequestUri: e.resourceRequestUri,
        resourceRequestMethod: e.resourceRequestMethod,
        shrClaims: e.shrClaims,
        shrNonce: e.shrNonce
      }, d = new Ue(this.browserCrypto);
      let g;
      if (c.keyId)
        g = this.browserCrypto.base64UrlEncode(JSON.stringify({ kid: c.keyId })), c.signPopToken = !1;
      else {
        const f = await m(d.generateCnf.bind(d), l.PopTokenGenerateCnf, this.logger, this.performanceClient, e.correlationId)(h, this.logger);
        g = f.reqCnfString, c.keyId = f.kid, c.signPopToken = !0;
      }
      c.reqCnf = g;
    }
    return this.addRequestSKUs(c), c;
  }
  /**
   * Handles extra broker request parameters
   * @param request {NativeTokenRequest}
   * @private
   */
  handleExtraBrokerParams(e) {
    var r;
    const t = e.extraParameters && e.extraParameters.hasOwnProperty(gn) && e.extraParameters.hasOwnProperty(Ht) && e.extraParameters.hasOwnProperty(Ce);
    if (!e.embeddedClientId && !t)
      return;
    let n = "";
    const o = e.redirectUri;
    e.embeddedClientId ? (e.redirectUri = this.config.auth.redirectUri, n = e.embeddedClientId) : e.extraParameters && (e.redirectUri = e.extraParameters[Ht], n = e.extraParameters[Ce]), e.extraParameters = {
      child_client_id: n,
      child_redirect_uri: o
    }, (r = this.performanceClient) == null || r.addFields({
      embeddedClientId: n,
      embeddedRedirectUri: o
    }, e.correlationId);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class ne {
  constructor(e, t, n, o) {
    this.logger = e, this.handshakeTimeoutMs = t, this.extensionId = o, this.resolvers = /* @__PURE__ */ new Map(), this.handshakeResolvers = /* @__PURE__ */ new Map(), this.messageChannel = new MessageChannel(), this.windowListener = this.onWindowMessage.bind(this), this.performanceClient = n, this.handshakeEvent = n.startMeasurement(l.NativeMessageHandlerHandshake);
  }
  /**
   * Sends a given message to the extension and resolves with the extension response
   * @param body
   */
  async sendMessage(e) {
    this.logger.trace("NativeMessageHandler - sendMessage called.");
    const t = {
      channel: be.CHANNEL_ID,
      extensionId: this.extensionId,
      responseId: ie(),
      body: e
    };
    return this.logger.trace("NativeMessageHandler - Sending request to browser extension"), this.logger.tracePii(`NativeMessageHandler - Sending request to browser extension: ${JSON.stringify(t)}`), this.messageChannel.port1.postMessage(t), new Promise((n, o) => {
      this.resolvers.set(t.responseId, { resolve: n, reject: o });
    });
  }
  /**
   * Returns an instance of the MessageHandler that has successfully established a connection with an extension
   * @param {Logger} logger
   * @param {number} handshakeTimeoutMs
   * @param {IPerformanceClient} performanceClient
   * @param {ICrypto} crypto
   */
  static async createProvider(e, t, n) {
    e.trace("NativeMessageHandler - createProvider called.");
    try {
      const o = new ne(e, t, n, be.PREFERRED_EXTENSION_ID);
      return await o.sendHandshakeRequest(), o;
    } catch {
      const r = new ne(e, t, n);
      return await r.sendHandshakeRequest(), r;
    }
  }
  /**
   * Send handshake request helper.
   */
  async sendHandshakeRequest() {
    this.logger.trace("NativeMessageHandler - sendHandshakeRequest called."), window.addEventListener("message", this.windowListener, !1);
    const e = {
      channel: be.CHANNEL_ID,
      extensionId: this.extensionId,
      responseId: ie(),
      body: {
        method: fe.HandshakeRequest
      }
    };
    return this.handshakeEvent.add({
      extensionId: this.extensionId,
      extensionHandshakeTimeoutMs: this.handshakeTimeoutMs
    }), this.messageChannel.port1.onmessage = (t) => {
      this.onChannelMessage(t);
    }, window.postMessage(e, window.origin, [this.messageChannel.port2]), new Promise((t, n) => {
      this.handshakeResolvers.set(e.responseId, { resolve: t, reject: n }), this.timeoutId = window.setTimeout(() => {
        window.removeEventListener("message", this.windowListener, !1), this.messageChannel.port1.close(), this.messageChannel.port2.close(), this.handshakeEvent.end({
          extensionHandshakeTimedOut: !0,
          success: !1
        }), n(C(Mr)), this.handshakeResolvers.delete(e.responseId);
      }, this.handshakeTimeoutMs);
    });
  }
  /**
   * Invoked when a message is posted to the window. If a handshake request is received it means the extension is not installed.
   * @param event
   */
  onWindowMessage(e) {
    if (this.logger.trace("NativeMessageHandler - onWindowMessage called"), e.source !== window)
      return;
    const t = e.data;
    if (!(!t.channel || t.channel !== be.CHANNEL_ID) && !(t.extensionId && t.extensionId !== this.extensionId) && t.body.method === fe.HandshakeRequest) {
      const n = this.handshakeResolvers.get(t.responseId);
      if (!n) {
        this.logger.trace(`NativeMessageHandler.onWindowMessage - resolver can't be found for request ${t.responseId}`);
        return;
      }
      this.logger.verbose(t.extensionId ? `Extension with id: ${t.extensionId} not installed` : "No extension installed"), clearTimeout(this.timeoutId), this.messageChannel.port1.close(), this.messageChannel.port2.close(), window.removeEventListener("message", this.windowListener, !1), this.handshakeEvent.end({
        success: !1,
        extensionInstalled: !1
      }), n.reject(C(Ur));
    }
  }
  /**
   * Invoked when a message is received from the extension on the MessageChannel port
   * @param event
   */
  onChannelMessage(e) {
    this.logger.trace("NativeMessageHandler - onChannelMessage called.");
    const t = e.data, n = this.resolvers.get(t.responseId), o = this.handshakeResolvers.get(t.responseId);
    try {
      const r = t.body.method;
      if (r === fe.Response) {
        if (!n)
          return;
        const i = t.body.response;
        if (this.logger.trace("NativeMessageHandler - Received response from browser extension"), this.logger.tracePii(`NativeMessageHandler - Received response from browser extension: ${JSON.stringify(i)}`), i.status !== "Success")
          n.reject(zt(i.code, i.description, i.ext));
        else if (i.result)
          i.result.code && i.result.description ? n.reject(zt(i.result.code, i.result.description, i.result.ext)) : n.resolve(i.result);
        else
          throw Co(Qt, "Event does not contain result.");
        this.resolvers.delete(t.responseId);
      } else if (r === fe.HandshakeResponse) {
        if (!o) {
          this.logger.trace(`NativeMessageHandler.onChannelMessage - resolver can't be found for request ${t.responseId}`);
          return;
        }
        clearTimeout(this.timeoutId), window.removeEventListener("message", this.windowListener, !1), this.extensionId = t.extensionId, this.extensionVersion = t.body.version, this.logger.verbose(`NativeMessageHandler - Received HandshakeResponse from extension: ${this.extensionId}`), this.handshakeEvent.end({
          extensionInstalled: !0,
          success: !0
        }), o.resolve(), this.handshakeResolvers.delete(t.responseId);
      }
    } catch (r) {
      this.logger.error("Error parsing response from WAM Extension"), this.logger.errorPii(`Error parsing response from WAM Extension: ${r}`), this.logger.errorPii(`Unable to parse ${e}`), n ? n.reject(r) : o && o.reject(r);
    }
  }
  /**
   * Returns the Id for the browser extension this handler is communicating with
   * @returns
   */
  getExtensionId() {
    return this.extensionId;
  }
  /**
   * Returns the version for the browser extension this handler is communicating with
   * @returns
   */
  getExtensionVersion() {
    return this.extensionVersion;
  }
  /**
   * Returns boolean indicating whether or not the request should attempt to use native broker
   * @param logger
   * @param config
   * @param nativeExtensionProvider
   * @param authenticationScheme
   */
  static isNativeAvailable(e, t, n, o) {
    if (t.trace("isNativeAvailable called"), !e.system.allowNativeBroker)
      return t.trace("isNativeAvailable: allowNativeBroker is not enabled, returning false"), !1;
    if (!n)
      return t.trace("isNativeAvailable: WAM extension provider is not initialized, returning false"), !1;
    if (o)
      switch (o) {
        case k.BEARER:
        case k.POP:
          return t.trace("isNativeAvailable: authenticationScheme is supported, returning true"), !0;
        default:
          return t.trace("isNativeAvailable: authenticationScheme is not supported, returning false"), !1;
      }
    return !0;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Mn {
  constructor(e, t, n, o, r) {
    this.authModule = e, this.browserStorage = t, this.authCodeRequest = n, this.logger = o, this.performanceClient = r;
  }
  /**
   * Function to handle response parameters from hash.
   * @param locationHash
   */
  async handleCodeResponse(e, t) {
    this.performanceClient.addQueueMeasurement(l.HandleCodeResponse, t.correlationId);
    let n;
    try {
      n = this.authModule.handleFragmentResponse(e, t.state);
    } catch (o) {
      throw o instanceof me && o.subError === Te ? C(Te) : o;
    }
    return m(this.handleCodeResponseFromServer.bind(this), l.HandleCodeResponseFromServer, this.logger, this.performanceClient, t.correlationId)(n, t);
  }
  /**
   * Process auth code response from AAD
   * @param authCodeResponse
   * @param state
   * @param authority
   * @param networkModule
   * @returns
   */
  async handleCodeResponseFromServer(e, t, n = !0) {
    if (this.performanceClient.addQueueMeasurement(l.HandleCodeResponseFromServer, t.correlationId), this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"), this.authCodeRequest.code = e.code, e.cloud_instance_host_name && await m(this.authModule.updateAuthority.bind(this.authModule), l.UpdateTokenEndpointAuthority, this.logger, this.performanceClient, t.correlationId)(e.cloud_instance_host_name, t.correlationId), n && (e.nonce = t.nonce || void 0), e.state = t.state, e.client_info)
      this.authCodeRequest.clientInfo = e.client_info;
    else {
      const r = this.createCcsCredentials(t);
      r && (this.authCodeRequest.ccsCredential = r);
    }
    return await m(this.authModule.acquireToken.bind(this.authModule), l.AuthClientAcquireToken, this.logger, this.performanceClient, t.correlationId)(this.authCodeRequest, e);
  }
  /**
   * Build ccs creds if available
   */
  createCcsCredentials(e) {
    return e.account ? {
      credential: e.account.homeAccountId,
      type: Q.HOME_ACCOUNT_ID
    } : e.loginHint ? {
      credential: e.loginHint,
      type: Q.UPN
    } : null;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function ei(a, e, t) {
  const n = ct(a);
  if (!n)
    throw Xo(a) ? (t.error(`A ${e} is present in the iframe but it does not contain known properties. It's likely that the ${e} has been replaced by code running on the redirectUri page.`), t.errorPii(`The ${e} detected is: ${a}`), C(gr)) : (t.error(`The request has returned to the redirectUri but a ${e} is not present. It's likely that the ${e} has been removed or the page has been redirected by code running on the redirectUri page.`), C(ur));
  return n;
}
function pc(a, e, t) {
  if (!a.state)
    throw C(In);
  const n = jr(e, a.state);
  if (!n)
    throw C(pr);
  if (n.interactionType !== t)
    throw C(mr);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class mc extends Ke {
  constructor(e, t, n, o, r, i, s, c, h, d) {
    super(e, t, n, o, r, i, s, h, d), this.unloadWindow = this.unloadWindow.bind(this), this.nativeStorage = c;
  }
  /**
   * Acquires tokens by opening a popup window to the /authorize endpoint of the authority
   * @param request
   */
  acquireToken(e) {
    try {
      const n = {
        popupName: this.generatePopupName(e.scopes || De, e.authority || this.config.auth.authority),
        popupWindowAttributes: e.popupWindowAttributes || {},
        popupWindowParent: e.popupWindowParent ?? window
      };
      return this.config.system.asyncPopups ? (this.logger.verbose("asyncPopups set to true, acquiring token"), this.acquireTokenPopupAsync(e, n)) : (this.logger.verbose("asyncPopup set to false, opening popup before acquiring token"), n.popup = this.openSizedPopup("about:blank", n), this.acquireTokenPopupAsync(e, n));
    } catch (t) {
      return Promise.reject(t);
    }
  }
  /**
   * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server
   * @param logoutRequest
   */
  logout(e) {
    try {
      this.logger.verbose("logoutPopup called");
      const t = this.initializeLogoutRequest(e), n = {
        popupName: this.generateLogoutPopupName(t),
        popupWindowAttributes: (e == null ? void 0 : e.popupWindowAttributes) || {},
        popupWindowParent: (e == null ? void 0 : e.popupWindowParent) ?? window
      }, o = e && e.authority, r = e && e.mainWindowRedirectUri;
      return this.config.system.asyncPopups ? (this.logger.verbose("asyncPopups set to true"), this.logoutPopupAsync(t, n, o, r)) : (this.logger.verbose("asyncPopup set to false, opening popup"), n.popup = this.openSizedPopup("about:blank", n), this.logoutPopupAsync(t, n, o, r));
    } catch (t) {
      return Promise.reject(t);
    }
  }
  /**
   * Helper which obtains an access_token for your API via opening a popup window in the user's browser
   * @param validRequest
   * @param popupName
   * @param popup
   * @param popupWindowAttributes
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  async acquireTokenPopupAsync(e, t) {
    var r;
    this.logger.verbose("acquireTokenPopupAsync called");
    const n = this.initializeServerTelemetryManager(b.acquireTokenPopup), o = await m(this.initializeAuthorizationRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationRequest, this.logger, this.performanceClient, this.correlationId)(e, y.Popup);
    Wr(o.authority);
    try {
      const i = await m(this.initializeAuthorizationCodeRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationCodeRequest, this.logger, this.performanceClient, this.correlationId)(o), s = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, this.correlationId)({
        serverTelemetryManager: n,
        requestAuthority: o.authority,
        requestAzureCloudOptions: o.azureCloudOptions,
        requestExtraQueryParameters: o.extraQueryParameters,
        account: o.account
      }), c = ne.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, e.authenticationScheme);
      let h;
      c && (h = this.performanceClient.startMeasurement(l.FetchAccountIdWithNativeBroker, e.correlationId));
      const d = await s.getAuthCodeUrl({
        ...o,
        nativeBroker: c
      }), g = new Mn(s, this.browserStorage, i, this.logger, this.performanceClient), f = this.initiateAuthRequest(d, t);
      this.eventHandler.emitEvent(I.POPUP_OPENED, y.Popup, { popupWindow: f }, null);
      const T = await this.monitorPopupForHash(f, t.popupWindowParent), E = Ee(ei, l.DeserializeResponse, this.logger, this.performanceClient, this.correlationId)(T, this.config.auth.OIDCOptions.serverResponseType, this.logger);
      if (ee.removeThrottle(this.browserStorage, this.config.auth.clientId, i), E.accountId) {
        if (this.logger.verbose("Account id found in hash, calling WAM for token"), h && h.end({
          success: !0,
          isNativeBroker: !0
        }), !this.nativeMessageHandler)
          throw C(ze);
        const M = new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.acquireTokenPopup, this.performanceClient, this.nativeMessageHandler, E.accountId, this.nativeStorage, o.correlationId), { userRequestState: G } = J.parseRequestState(this.browserCrypto, o.state);
        return await M.acquireToken({
          ...o,
          state: G,
          prompt: void 0
          // Server should handle the prompt, ideally native broker can do this part silently
        });
      }
      return await g.handleCodeResponse(E, o);
    } catch (i) {
      throw (r = t.popup) == null || r.close(), i instanceof O && (i.setCorrelationId(this.correlationId), n.cacheFailedRequest(i)), i;
    }
  }
  /**
   *
   * @param validRequest
   * @param popupName
   * @param requestAuthority
   * @param popup
   * @param mainWindowRedirectUri
   * @param popupWindowAttributes
   */
  async logoutPopupAsync(e, t, n, o) {
    var i, s, c, h;
    this.logger.verbose("logoutPopupAsync called"), this.eventHandler.emitEvent(I.LOGOUT_START, y.Popup, e);
    const r = this.initializeServerTelemetryManager(b.logoutPopup);
    try {
      await this.clearCacheOnLogout(e.account);
      const d = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, this.correlationId)({
        serverTelemetryManager: r,
        requestAuthority: n,
        account: e.account || void 0
      });
      try {
        d.authority.endSessionEndpoint;
      } catch {
        if ((i = e.account) != null && i.homeAccountId && e.postLogoutRedirectUri && d.authority.protocolMode === le.OIDC) {
          if (this.browserStorage.removeAccount((s = e.account) == null ? void 0 : s.homeAccountId, this.correlationId), this.eventHandler.emitEvent(I.LOGOUT_SUCCESS, y.Popup, e), o) {
            const T = {
              apiId: b.logoutPopup,
              timeout: this.config.system.redirectNavigationTimeout,
              noHistory: !1
            }, E = w.getAbsoluteUrl(o, he());
            await this.navigationClient.navigateInternal(E, T);
          }
          (c = t.popup) == null || c.close();
          return;
        }
      }
      const g = d.getLogoutUri(e);
      this.eventHandler.emitEvent(I.LOGOUT_SUCCESS, y.Popup, e);
      const f = this.openPopup(g, t);
      if (this.eventHandler.emitEvent(I.POPUP_OPENED, y.Popup, { popupWindow: f }, null), await this.monitorPopupForHash(f, t.popupWindowParent).catch(() => {
      }), o) {
        const T = {
          apiId: b.logoutPopup,
          timeout: this.config.system.redirectNavigationTimeout,
          noHistory: !1
        }, E = w.getAbsoluteUrl(o, he());
        this.logger.verbose("Redirecting main window to url specified in the request"), this.logger.verbosePii(`Redirecting main window to: ${E}`), await this.navigationClient.navigateInternal(E, T);
      } else
        this.logger.verbose("No main window navigation requested");
    } catch (d) {
      throw (h = t.popup) == null || h.close(), d instanceof O && (d.setCorrelationId(this.correlationId), r.cacheFailedRequest(d)), this.browserStorage.setInteractionInProgress(!1), this.eventHandler.emitEvent(I.LOGOUT_FAILURE, y.Popup, null, d), this.eventHandler.emitEvent(I.LOGOUT_END, y.Popup), d;
    }
    this.eventHandler.emitEvent(I.LOGOUT_END, y.Popup);
  }
  /**
   * Opens a popup window with given request Url.
   * @param requestUrl
   */
  initiateAuthRequest(e, t) {
    if (e)
      return this.logger.infoPii(`Navigate to: ${e}`), this.openPopup(e, t);
    throw this.logger.error("Navigate url is empty"), C(It);
  }
  /**
   * Monitors a window until it loads a url with the same origin.
   * @param popupWindow - window that is being monitored
   * @param timeout - timeout for processing hash once popup is redirected back to application
   */
  monitorPopupForHash(e, t) {
    return new Promise((n, o) => {
      this.logger.verbose("PopupHandler.monitorPopupForHash - polling started");
      const r = setInterval(() => {
        if (e.closed) {
          this.logger.error("PopupHandler.monitorPopupForHash - window closed"), clearInterval(r), o(C(Te));
          return;
        }
        let i = "";
        try {
          i = e.location.href;
        } catch {
        }
        if (!i || i === "about:blank")
          return;
        clearInterval(r);
        let s = "";
        const c = this.config.auth.OIDCOptions.serverResponseType;
        e && (c === $e.QUERY ? s = e.location.search : s = e.location.hash), this.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"), n(s);
      }, this.config.system.pollIntervalMilliseconds);
    }).finally(() => {
      this.cleanPopup(e, t);
    });
  }
  /**
   * @hidden
   *
   * Configures popup window for login.
   *
   * @param urlNavigate
   * @param title
   * @param popUpWidth
   * @param popUpHeight
   * @param popupWindowAttributes
   * @ignore
   * @hidden
   */
  openPopup(e, t) {
    try {
      let n;
      if (t.popup ? (n = t.popup, this.logger.verbosePii(`Navigating popup window to: ${e}`), n.location.assign(e)) : typeof t.popup > "u" && (this.logger.verbosePii(`Opening popup window to: ${e}`), n = this.openSizedPopup(e, t)), !n)
        throw C(yr);
      return n.focus && n.focus(), this.currentWindow = n, t.popupWindowParent.addEventListener("beforeunload", this.unloadWindow), n;
    } catch (n) {
      throw this.logger.error("error opening popup " + n.message), this.browserStorage.setInteractionInProgress(!1), C(Cr);
    }
  }
  /**
   * Helper function to set popup window dimensions and position
   * @param urlNavigate
   * @param popupName
   * @param popupWindowAttributes
   * @returns
   */
  openSizedPopup(e, { popupName: t, popupWindowAttributes: n, popupWindowParent: o }) {
    var T, E, v, M;
    const r = o.screenLeft ? o.screenLeft : o.screenX, i = o.screenTop ? o.screenTop : o.screenY, s = o.innerWidth || document.documentElement.clientWidth || document.body.clientWidth, c = o.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    let h = (T = n.popupSize) == null ? void 0 : T.width, d = (E = n.popupSize) == null ? void 0 : E.height, g = (v = n.popupPosition) == null ? void 0 : v.top, f = (M = n.popupPosition) == null ? void 0 : M.left;
    return (!h || h < 0 || h > s) && (this.logger.verbose("Default popup window width used. Window width not configured or invalid."), h = V.POPUP_WIDTH), (!d || d < 0 || d > c) && (this.logger.verbose("Default popup window height used. Window height not configured or invalid."), d = V.POPUP_HEIGHT), (!g || g < 0 || g > c) && (this.logger.verbose("Default popup window top position used. Window top not configured or invalid."), g = Math.max(0, c / 2 - V.POPUP_HEIGHT / 2 + i)), (!f || f < 0 || f > s) && (this.logger.verbose("Default popup window left position used. Window left not configured or invalid."), f = Math.max(0, s / 2 - V.POPUP_WIDTH / 2 + r)), o.open(e, t, `width=${h}, height=${d}, top=${g}, left=${f}, scrollbars=yes`);
  }
  /**
   * Event callback to unload main window.
   */
  unloadWindow(e) {
    this.browserStorage.cleanRequestByInteractionType(y.Popup), this.currentWindow && this.currentWindow.close(), e.preventDefault();
  }
  /**
   * Closes popup, removes any state vars created during popup calls.
   * @param popupWindow
   */
  cleanPopup(e, t) {
    e.close(), t.removeEventListener("beforeunload", this.unloadWindow), this.browserStorage.setInteractionInProgress(!1);
  }
  /**
   * Generates the name for the popup based on the client id and request
   * @param clientId
   * @param request
   */
  generatePopupName(e, t) {
    return `${V.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${e.join("-")}.${t}.${this.correlationId}`;
  }
  /**
   * Generates the name for the popup based on the client id and request for logouts
   * @param clientId
   * @param request
   */
  generateLogoutPopupName(e) {
    const t = e.account && e.account.homeAccountId;
    return `${V.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${t}.${this.correlationId}`;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class mo {
  constructor(e, t, n, o, r) {
    this.authModule = e, this.browserStorage = t, this.authCodeRequest = n, this.logger = o, this.performanceClient = r;
  }
  /**
   * Redirects window to given URL.
   * @param urlNavigate
   */
  async initiateAuthRequest(e, t) {
    if (this.logger.verbose("RedirectHandler.initiateAuthRequest called"), e) {
      t.redirectStartPage && (this.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page"), this.browserStorage.setTemporaryCache(_.ORIGIN_URI, t.redirectStartPage, !0)), this.browserStorage.setTemporaryCache(_.CORRELATION_ID, this.authCodeRequest.correlationId, !0), this.browserStorage.cacheCodeRequest(this.authCodeRequest), this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${e}`);
      const n = {
        apiId: b.acquireTokenRedirect,
        timeout: t.redirectTimeout,
        noHistory: !1
      };
      if (typeof t.onRedirectNavigate == "function")
        if (this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback"), t.onRedirectNavigate(e) !== !1) {
          this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"), await t.navigationClient.navigateExternal(e, n);
          return;
        } else {
          this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation");
          return;
        }
      else {
        this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"), await t.navigationClient.navigateExternal(e, n);
        return;
      }
    } else
      throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"), C(It);
  }
  /**
   * Handle authorization code response in the window.
   * @param hash
   */
  async handleCodeResponse(e, t) {
    this.logger.verbose("RedirectHandler.handleCodeResponse called"), this.browserStorage.setInteractionInProgress(!1);
    const n = this.browserStorage.generateStateKey(t), o = this.browserStorage.getTemporaryCache(n);
    if (!o)
      throw p(rt, "Cached State");
    let r;
    try {
      r = this.authModule.handleFragmentResponse(e, o);
    } catch (h) {
      throw h instanceof me && h.subError === Te ? C(Te) : h;
    }
    const i = this.browserStorage.generateNonceKey(o), s = this.browserStorage.getTemporaryCache(i);
    if (this.authCodeRequest.code = r.code, r.cloud_instance_host_name && await m(this.authModule.updateAuthority.bind(this.authModule), l.UpdateTokenEndpointAuthority, this.logger, this.performanceClient, this.authCodeRequest.correlationId)(r.cloud_instance_host_name, this.authCodeRequest.correlationId), r.nonce = s || void 0, r.state = o, r.client_info)
      this.authCodeRequest.clientInfo = r.client_info;
    else {
      const h = this.checkCcsCredentials();
      h && (this.authCodeRequest.ccsCredential = h);
    }
    const c = await this.authModule.acquireToken(this.authCodeRequest, r);
    return this.browserStorage.cleanRequestByState(t), c;
  }
  /**
   * Looks up ccs creds in the cache
   */
  checkCcsCredentials() {
    const e = this.browserStorage.getTemporaryCache(_.CCS_CREDENTIAL, !0);
    if (e)
      try {
        return JSON.parse(e);
      } catch {
        this.authModule.logger.error("Cache credential could not be parsed"), this.authModule.logger.errorPii(`Cache credential could not be parsed: ${e}`);
      }
    return null;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function fc() {
  if (typeof window > "u" || typeof window.performance > "u" || typeof window.performance.getEntriesByType != "function")
    return;
  const a = window.performance.getEntriesByType("navigation"), e = a.length ? a[0] : void 0;
  return e == null ? void 0 : e.type;
}
class Cc extends Ke {
  constructor(e, t, n, o, r, i, s, c, h, d) {
    super(e, t, n, o, r, i, s, h, d), this.nativeStorage = c;
  }
  /**
   * Redirects the page to the /authorize endpoint of the IDP
   * @param request
   */
  async acquireToken(e) {
    const t = await m(this.initializeAuthorizationRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationRequest, this.logger, this.performanceClient, this.correlationId)(e, y.Redirect);
    this.browserStorage.updateCacheEntries(t.state, t.nonce, t.authority, t.loginHint || "", t.account || null);
    const n = this.initializeServerTelemetryManager(b.acquireTokenRedirect), o = (r) => {
      r.persisted && (this.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."), this.browserStorage.cleanRequestByState(t.state), this.eventHandler.emitEvent(I.RESTORE_FROM_BFCACHE, y.Redirect));
    };
    try {
      const r = await m(this.initializeAuthorizationCodeRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationCodeRequest, this.logger, this.performanceClient, this.correlationId)(t), i = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, this.correlationId)({
        serverTelemetryManager: n,
        requestAuthority: t.authority,
        requestAzureCloudOptions: t.azureCloudOptions,
        requestExtraQueryParameters: t.extraQueryParameters,
        account: t.account
      }), s = new mo(i, this.browserStorage, r, this.logger, this.performanceClient), c = await i.getAuthCodeUrl({
        ...t,
        nativeBroker: ne.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, e.authenticationScheme)
      }), h = this.getRedirectStartPage(e.redirectStartPage);
      return this.logger.verbosePii(`Redirect start page: ${h}`), window.addEventListener("pageshow", o), await s.initiateAuthRequest(c, {
        navigationClient: this.navigationClient,
        redirectTimeout: this.config.system.redirectNavigationTimeout,
        redirectStartPage: h,
        onRedirectNavigate: e.onRedirectNavigate || this.config.auth.onRedirectNavigate
      });
    } catch (r) {
      throw r instanceof O && (r.setCorrelationId(this.correlationId), n.cacheFailedRequest(r)), window.removeEventListener("pageshow", o), this.browserStorage.cleanRequestByState(t.state), r;
    }
  }
  /**
   * Checks if navigateToLoginRequestUrl is set, and:
   * - if true, performs logic to cache and navigate
   * - if false, handles hash string and parses response
   * @param hash {string} url hash
   * @param parentMeasurement {InProgressPerformanceEvent} parent measurement
   */
  async handleRedirectPromise(e = "", t) {
    const n = this.initializeServerTelemetryManager(b.handleRedirectPromise);
    try {
      if (!this.browserStorage.isInteractionInProgress(!0))
        return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."), null;
      const [o, r] = this.getRedirectResponse(e || "");
      if (!o)
        return this.logger.info("handleRedirectPromise did not detect a response as a result of a redirect. Cleaning temporary cache."), this.browserStorage.cleanRequestByInteractionType(y.Redirect), fc() !== "back_forward" ? t.event.errorCode = "no_server_response" : this.logger.verbose("Back navigation event detected. Muting no_server_response error"), null;
      const i = this.browserStorage.getTemporaryCache(_.ORIGIN_URI, !0) || u.EMPTY_STRING, s = w.removeHashFromUrl(i), c = w.removeHashFromUrl(window.location.href);
      if (s === c && this.config.auth.navigateToLoginRequestUrl)
        return this.logger.verbose("Current page is loginRequestUrl, handling response"), i.indexOf("#") > -1 && vs(i), await this.handleResponse(o, n);
      if (this.config.auth.navigateToLoginRequestUrl) {
        if (!bn() || this.config.system.allowRedirectInIframe) {
          this.browserStorage.setTemporaryCache(_.URL_HASH, r, !0);
          const h = {
            apiId: b.handleRedirectPromise,
            timeout: this.config.system.redirectNavigationTimeout,
            noHistory: !0
          };
          let d = !0;
          if (!i || i === "null") {
            const g = _s();
            this.browserStorage.setTemporaryCache(_.ORIGIN_URI, g, !0), this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"), d = await this.navigationClient.navigateInternal(g, h);
          } else
            this.logger.verbose(`Navigating to loginRequestUrl: ${i}`), d = await this.navigationClient.navigateInternal(i, h);
          if (!d)
            return await this.handleResponse(o, n);
        }
      } else return this.logger.verbose("NavigateToLoginRequestUrl set to false, handling response"), await this.handleResponse(o, n);
      return null;
    } catch (o) {
      throw o instanceof O && (o.setCorrelationId(this.correlationId), n.cacheFailedRequest(o)), this.browserStorage.cleanRequestByInteractionType(y.Redirect), o;
    }
  }
  /**
   * Gets the response hash for a redirect request
   * Returns null if interactionType in the state value is not "redirect" or the hash does not contain known properties
   * @param hash
   */
  getRedirectResponse(e) {
    this.logger.verbose("getRedirectResponseHash called");
    let t = e;
    t || (this.config.auth.OIDCOptions.serverResponseType === $e.QUERY ? t = window.location.search : t = window.location.hash);
    let n = ct(t);
    if (n) {
      try {
        pc(n, this.browserCrypto, y.Redirect);
      } catch (r) {
        return r instanceof O && this.logger.error(`Interaction type validation failed due to ${r.errorCode}: ${r.errorMessage}`), [null, ""];
      }
      return Ss(window), this.logger.verbose("Hash contains known properties, returning response hash"), [n, t];
    }
    const o = this.browserStorage.getTemporaryCache(_.URL_HASH, !0);
    return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(_.URL_HASH)), o && (n = ct(o), n) ? (this.logger.verbose("Hash does not contain known properties, returning cached hash"), [n, o]) : [null, ""];
  }
  /**
   * Checks if hash exists and handles in window.
   * @param hash
   * @param state
   */
  async handleResponse(e, t) {
    const n = e.state;
    if (!n)
      throw C(In);
    const o = this.browserStorage.getCachedRequest(n);
    if (this.logger.verbose("handleResponse called, retrieved cached request"), e.accountId) {
      if (this.logger.verbose("Account id found in hash, calling WAM for token"), !this.nativeMessageHandler)
        throw C(ze);
      const c = new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.acquireTokenPopup, this.performanceClient, this.nativeMessageHandler, e.accountId, this.nativeStorage, o.correlationId), { userRequestState: h } = J.parseRequestState(this.browserCrypto, n);
      return c.acquireToken({
        ...o,
        state: h,
        prompt: void 0
        // Server should handle the prompt, ideally native broker can do this part silently
      }).finally(() => {
        this.browserStorage.cleanRequestByState(n);
      });
    }
    const r = this.browserStorage.getCachedAuthority(n);
    if (!r)
      throw C(An);
    const i = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, this.correlationId)({ serverTelemetryManager: t, requestAuthority: r });
    return ee.removeThrottle(this.browserStorage, this.config.auth.clientId, o), new mo(i, this.browserStorage, o, this.logger, this.performanceClient).handleCodeResponse(e, n);
  }
  /**
   * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.
   * Default behaviour is to redirect the user to `window.location.href`.
   * @param logoutRequest
   */
  async logout(e) {
    var o, r;
    this.logger.verbose("logoutRedirect called");
    const t = this.initializeLogoutRequest(e), n = this.initializeServerTelemetryManager(b.logout);
    try {
      this.eventHandler.emitEvent(I.LOGOUT_START, y.Redirect, e), await this.clearCacheOnLogout(t.account);
      const i = {
        apiId: b.logout,
        timeout: this.config.system.redirectNavigationTimeout,
        noHistory: !1
      }, s = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, this.correlationId)({
        serverTelemetryManager: n,
        requestAuthority: e && e.authority,
        requestExtraQueryParameters: e == null ? void 0 : e.extraQueryParameters,
        account: e && e.account || void 0
      });
      if (s.authority.protocolMode === le.OIDC)
        try {
          s.authority.endSessionEndpoint;
        } catch {
          if ((o = t.account) != null && o.homeAccountId) {
            this.browserStorage.removeAccount((r = t.account) == null ? void 0 : r.homeAccountId, this.correlationId), this.eventHandler.emitEvent(I.LOGOUT_SUCCESS, y.Redirect, t);
            return;
          }
        }
      const c = s.getLogoutUri(t);
      if (this.eventHandler.emitEvent(I.LOGOUT_SUCCESS, y.Redirect, t), e && typeof e.onRedirectNavigate == "function")
        if (e.onRedirectNavigate(c) !== !1) {
          this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"), this.browserStorage.getInteractionInProgress() || this.browserStorage.setInteractionInProgress(!0), await this.navigationClient.navigateExternal(c, i);
          return;
        } else
          this.browserStorage.setInteractionInProgress(!1), this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation");
      else {
        this.browserStorage.getInteractionInProgress() || this.browserStorage.setInteractionInProgress(!0), await this.navigationClient.navigateExternal(c, i);
        return;
      }
    } catch (i) {
      throw i instanceof O && (i.setCorrelationId(this.correlationId), n.cacheFailedRequest(i)), this.eventHandler.emitEvent(I.LOGOUT_FAILURE, y.Redirect, null, i), this.eventHandler.emitEvent(I.LOGOUT_END, y.Redirect), i;
    }
    this.eventHandler.emitEvent(I.LOGOUT_END, y.Redirect);
  }
  /**
   * Use to get the redirectStartPage either from request or use current window
   * @param requestStartPage
   */
  getRedirectStartPage(e) {
    const t = e || window.location.href;
    return w.getAbsoluteUrl(t, he());
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
async function yc(a, e, t, n, o) {
  if (e.addQueueMeasurement(l.SilentHandlerInitiateAuthRequest, n), !a)
    throw t.info("Navigate url is empty"), C(It);
  return o ? m(Ic, l.SilentHandlerLoadFrame, t, e, n)(a, o, e, n) : Ee(Ac, l.SilentHandlerLoadFrameSync, t, e, n)(a);
}
async function Tc(a, e, t, n, o, r, i) {
  return n.addQueueMeasurement(l.SilentHandlerMonitorIframeForHash, r), new Promise((s, c) => {
    e < Gt && o.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${e}ms) than the default (${Gt}ms). This may result in timeouts.`);
    const h = window.setTimeout(() => {
      window.clearInterval(d), c(C(Tr));
    }, e), d = window.setInterval(() => {
      let g = "";
      const f = a.contentWindow;
      try {
        g = f ? f.location.href : "";
      } catch {
      }
      if (!g || g === "about:blank")
        return;
      let T = "";
      f && (i === $e.QUERY ? T = f.location.search : T = f.location.hash), window.clearTimeout(h), window.clearInterval(d), s(T);
    }, t);
  }).finally(() => {
    Ee(Ec, l.RemoveHiddenIframe, o, n, r)(a);
  });
}
function Ic(a, e, t, n) {
  return t.addQueueMeasurement(l.SilentHandlerLoadFrame, n), new Promise((o, r) => {
    const i = ti();
    window.setTimeout(() => {
      if (!i) {
        r("Unable to load iframe");
        return;
      }
      i.src = a, o(i);
    }, e);
  });
}
function Ac(a) {
  const e = ti();
  return e.src = a, e;
}
function ti() {
  const a = document.createElement("iframe");
  return a.className = "msalSilentIframe", a.style.visibility = "hidden", a.style.position = "absolute", a.style.width = a.style.height = "0", a.style.border = "0", a.setAttribute("sandbox", "allow-scripts allow-same-origin allow-forms"), document.body.appendChild(a), a;
}
function Ec(a) {
  document.body === a.parentNode && document.body.removeChild(a);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class Sc extends Ke {
  constructor(e, t, n, o, r, i, s, c, h, d, g) {
    super(e, t, n, o, r, i, c, d, g), this.apiId = s, this.nativeStorage = h;
  }
  /**
   * Acquires a token silently by opening a hidden iframe to the /authorize endpoint with prompt=none or prompt=no_session
   * @param request
   */
  async acquireToken(e) {
    this.performanceClient.addQueueMeasurement(l.SilentIframeClientAcquireToken, e.correlationId), !e.loginHint && !e.sid && (!e.account || !e.account.username) && this.logger.warning("No user hint provided. The authorization server may need more information to complete this request.");
    const t = { ...e };
    t.prompt ? t.prompt !== D.NONE && t.prompt !== D.NO_SESSION && (this.logger.warning(`SilentIframeClient. Replacing invalid prompt ${t.prompt} with ${D.NONE}`), t.prompt = D.NONE) : t.prompt = D.NONE;
    const n = await m(this.initializeAuthorizationRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationRequest, this.logger, this.performanceClient, e.correlationId)(t, y.Silent);
    Wr(n.authority);
    const o = this.initializeServerTelemetryManager(this.apiId);
    let r;
    try {
      return r = await m(this.createAuthCodeClient.bind(this), l.StandardInteractionClientCreateAuthCodeClient, this.logger, this.performanceClient, e.correlationId)({
        serverTelemetryManager: o,
        requestAuthority: n.authority,
        requestAzureCloudOptions: n.azureCloudOptions,
        requestExtraQueryParameters: n.extraQueryParameters,
        account: n.account
      }), await m(this.silentTokenHelper.bind(this), l.SilentIframeClientTokenHelper, this.logger, this.performanceClient, e.correlationId)(r, n);
    } catch (i) {
      if (i instanceof O && (i.setCorrelationId(this.correlationId), o.cacheFailedRequest(i)), !r || !(i instanceof O) || i.errorCode !== V.INVALID_GRANT_ERROR)
        throw i;
      this.performanceClient.addFields({
        retryError: i.errorCode
      }, this.correlationId);
      const s = await m(this.initializeAuthorizationRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationRequest, this.logger, this.performanceClient, e.correlationId)(t, y.Silent);
      return await m(this.silentTokenHelper.bind(this), l.SilentIframeClientTokenHelper, this.logger, this.performanceClient, this.correlationId)(r, s);
    }
  }
  /**
   * Currently Unsupported
   */
  logout() {
    return Promise.reject(C(At));
  }
  /**
   * Helper which acquires an authorization code silently using a hidden iframe from given url
   * using the scopes requested as part of the id, and exchanges the code for a set of OAuth tokens.
   * @param navigateUrl
   * @param userRequestScopes
   */
  async silentTokenHelper(e, t) {
    const n = t.correlationId;
    this.performanceClient.addQueueMeasurement(l.SilentIframeClientTokenHelper, n);
    const o = await m(this.initializeAuthorizationCodeRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationCodeRequest, this.logger, this.performanceClient, n)(t), r = await m(e.getAuthCodeUrl.bind(e), l.GetAuthCodeUrl, this.logger, this.performanceClient, n)({
      ...t,
      nativeBroker: ne.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, t.authenticationScheme)
    }), i = new Mn(e, this.browserStorage, o, this.logger, this.performanceClient), s = await m(yc, l.SilentHandlerInitiateAuthRequest, this.logger, this.performanceClient, n)(r, this.performanceClient, this.logger, n, this.config.system.navigateFrameWait), c = this.config.auth.OIDCOptions.serverResponseType, h = await m(Tc, l.SilentHandlerMonitorIframeForHash, this.logger, this.performanceClient, n)(s, this.config.system.iframeHashTimeout, this.config.system.pollIntervalMilliseconds, this.performanceClient, this.logger, n, c), d = Ee(ei, l.DeserializeResponse, this.logger, this.performanceClient, this.correlationId)(h, c, this.logger);
    if (d.accountId) {
      if (this.logger.verbose("Account id found in hash, calling WAM for token"), !this.nativeMessageHandler)
        throw C(ze);
      const g = new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.apiId, this.performanceClient, this.nativeMessageHandler, d.accountId, this.browserStorage, n), { userRequestState: f } = J.parseRequestState(this.browserCrypto, t.state);
      return m(g.acquireToken.bind(g), l.NativeInteractionClientAcquireToken, this.logger, this.performanceClient, n)({
        ...t,
        state: f,
        prompt: t.prompt || D.NONE
      });
    }
    return m(i.handleCodeResponse.bind(i), l.HandleCodeResponse, this.logger, this.performanceClient, n)(d, t);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class vc extends Ke {
  /**
   * Exchanges the refresh token for new tokens
   * @param request
   */
  async acquireToken(e) {
    this.performanceClient.addQueueMeasurement(l.SilentRefreshClientAcquireToken, e.correlationId);
    const t = await m(Pn, l.InitializeBaseRequest, this.logger, this.performanceClient, e.correlationId)(e, this.config, this.performanceClient, this.logger), n = {
      ...e,
      ...t
    };
    e.redirectUri && (n.redirectUri = this.getRedirectUri(e.redirectUri));
    const o = this.initializeServerTelemetryManager(b.acquireTokenSilent_silentFlow), r = await this.createRefreshTokenClient({
      serverTelemetryManager: o,
      authorityUrl: n.authority,
      azureCloudOptions: n.azureCloudOptions,
      account: n.account
    });
    return m(r.acquireTokenByRefreshToken.bind(r), l.RefreshTokenClientAcquireTokenByRefreshToken, this.logger, this.performanceClient, e.correlationId)(n).catch((i) => {
      throw i.setCorrelationId(this.correlationId), o.cacheFailedRequest(i), i;
    });
  }
  /**
   * Currently Unsupported
   */
  logout() {
    return Promise.reject(C(At));
  }
  /**
   * Creates a Refresh Client with the given authority, or the default authority.
   * @param params {
   *         serverTelemetryManager: ServerTelemetryManager;
   *         authorityUrl?: string;
   *         azureCloudOptions?: AzureCloudOptions;
   *         extraQueryParams?: StringDict;
   *         account?: AccountInfo;
   *        }
   */
  async createRefreshTokenClient(e) {
    const t = await m(this.getClientConfiguration.bind(this), l.StandardInteractionClientGetClientConfiguration, this.logger, this.performanceClient, this.correlationId)({
      serverTelemetryManager: e.serverTelemetryManager,
      requestAuthority: e.authorityUrl,
      requestAzureCloudOptions: e.azureCloudOptions,
      requestExtraQueryParameters: e.extraQueryParameters,
      account: e.account
    });
    return new Kt(t, this.performanceClient);
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class wc {
  constructor(e, t, n, o) {
    this.isBrowserEnvironment = typeof window < "u", this.config = e, this.storage = t, this.logger = n, this.cryptoObj = o;
  }
  // Move getAllAccounts here and cache utility APIs
  /**
   * API to load tokens to msal-browser cache.
   * @param request
   * @param response
   * @param options
   * @returns `AuthenticationResult` for the response that was loaded.
   */
  loadExternalTokens(e, t, n) {
    if (!this.isBrowserEnvironment)
      throw C(Et);
    const o = e.correlationId || ie(), r = t.id_token ? Ae(t.id_token, oe) : void 0, i = {
      protocolMode: this.config.auth.protocolMode,
      knownAuthorities: this.config.auth.knownAuthorities,
      cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,
      authorityMetadata: this.config.auth.authorityMetadata,
      skipAuthorityMetadataCache: this.config.auth.skipAuthorityMetadataCache
    }, s = e.authority ? new x(x.generateAuthority(e.authority, e.azureCloudOptions), this.config.system.networkClient, this.storage, i, this.logger, e.correlationId || ie()) : void 0, c = this.loadAccount(e, n.clientInfo || t.client_info || "", o, r, s), h = this.loadIdToken(t, c.homeAccountId, c.environment, c.realm, o), d = this.loadAccessToken(e, t, c.homeAccountId, c.environment, c.realm, n, o), g = this.loadRefreshToken(t, c.homeAccountId, c.environment, o);
    return this.generateAuthenticationResult(e, {
      account: c,
      idToken: h,
      accessToken: d,
      refreshToken: g
    }, r, s);
  }
  /**
   * Helper function to load account to msal-browser cache
   * @param idToken
   * @param environment
   * @param clientInfo
   * @param authorityType
   * @param requestHomeAccountId
   * @returns `AccountEntity`
   */
  loadAccount(e, t, n, o, r) {
    if (this.logger.verbose("TokenCache - loading account"), e.account) {
      const h = U.createFromAccountInfo(e.account);
      return this.storage.setAccount(h, n), h;
    } else if (!r || !t && !o)
      throw this.logger.error("TokenCache - if an account is not provided on the request, authority and either clientInfo or idToken must be provided instead."), C(Rr);
    const i = U.generateHomeAccountId(t, r.authorityType, this.logger, this.cryptoObj, o), s = o == null ? void 0 : o.tid, c = fn(
      this.storage,
      r,
      i,
      oe,
      n,
      o,
      t,
      r.hostnameAndPort,
      s,
      void 0,
      // authCodePayload
      void 0,
      // nativeAccountId
      this.logger
    );
    return this.storage.setAccount(c, n), c;
  }
  /**
   * Helper function to load id tokens to msal-browser cache
   * @param idToken
   * @param homeAccountId
   * @param environment
   * @param tenantId
   * @returns `IdTokenEntity`
   */
  loadIdToken(e, t, n, o, r) {
    if (!e.id_token)
      return this.logger.verbose("TokenCache - no id token found in response"), null;
    this.logger.verbose("TokenCache - loading id token");
    const i = pt(t, n, e.id_token, this.config.auth.clientId, o);
    return this.storage.setIdTokenCredential(i, r), i;
  }
  /**
   * Helper function to load access tokens to msal-browser cache
   * @param request
   * @param response
   * @param homeAccountId
   * @param environment
   * @param tenantId
   * @returns `AccessTokenEntity`
   */
  loadAccessToken(e, t, n, o, r, i, s) {
    if (t.access_token)
      if (t.expires_in) {
        if (!t.scope && (!e.scopes || !e.scopes.length))
          return this.logger.error("TokenCache - scopes not specified in the request or response. Cannot add token to the cache."), null;
      } else return this.logger.error("TokenCache - no expiration set on the access token. Cannot add it to the cache."), null;
    else return this.logger.verbose("TokenCache - no access token found in response"), null;
    this.logger.verbose("TokenCache - loading access token");
    const c = t.scope ? P.fromString(t.scope) : new P(e.scopes), h = i.expiresOn || t.expires_in + (/* @__PURE__ */ new Date()).getTime() / 1e3, d = i.extendedExpiresOn || (t.ext_expires_in || t.expires_in) + (/* @__PURE__ */ new Date()).getTime() / 1e3, g = mt(n, o, t.access_token, this.config.auth.clientId, r, c.printScopes(), h, d, oe);
    return this.storage.setAccessTokenCredential(g, s), g;
  }
  /**
   * Helper function to load refresh tokens to msal-browser cache
   * @param request
   * @param response
   * @param homeAccountId
   * @param environment
   * @returns `RefreshTokenEntity`
   */
  loadRefreshToken(e, t, n, o) {
    if (!e.refresh_token)
      return this.logger.verbose("TokenCache - no refresh token found in response"), null;
    this.logger.verbose("TokenCache - loading refresh token");
    const r = Ho(
      t,
      n,
      e.refresh_token,
      this.config.auth.clientId,
      e.foci,
      void 0,
      // userAssertionHash
      e.refresh_token_expires_in
    );
    return this.storage.setRefreshTokenCredential(r, o), r;
  }
  /**
   * Helper function to generate an `AuthenticationResult` for the result.
   * @param request
   * @param idTokenObj
   * @param cacheRecord
   * @param authority
   * @returns `AuthenticationResult`
   */
  generateAuthenticationResult(e, t, n, o) {
    var d, g, f;
    let r = "", i = [], s = null, c;
    t != null && t.accessToken && (r = t.accessToken.secret, i = P.fromString(t.accessToken.target).asArray(), s = new Date(Number(t.accessToken.expiresOn) * 1e3), c = new Date(Number(t.accessToken.extendedExpiresOn) * 1e3));
    const h = t.account;
    return {
      authority: o ? o.canonicalAuthority : "",
      uniqueId: t.account.localAccountId,
      tenantId: t.account.realm,
      scopes: i,
      account: h.getAccountInfo(),
      idToken: ((d = t.idToken) == null ? void 0 : d.secret) || "",
      idTokenClaims: n || {},
      accessToken: r,
      fromCache: !0,
      expiresOn: s,
      correlationId: e.correlationId || "",
      requestId: "",
      extExpiresOn: c,
      familyId: ((g = t.refreshToken) == null ? void 0 : g.familyId) || "",
      tokenType: ((f = t == null ? void 0 : t.accessToken) == null ? void 0 : f.tokenType) || "",
      state: e.state || "",
      cloudGraphHostName: h.cloudGraphHostName || "",
      msGraphHost: h.msGraphHost || "",
      fromNativeBroker: !1
    };
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class _c extends cr {
  constructor(e) {
    super(e), this.includeRedirectUri = !1;
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class kc extends Ke {
  constructor(e, t, n, o, r, i, s, c, h, d) {
    super(e, t, n, o, r, i, c, h, d), this.apiId = s;
  }
  /**
   * Acquires a token silently by redeeming an authorization code against the /token endpoint
   * @param request
   */
  async acquireToken(e) {
    if (!e.code)
      throw C(br);
    const t = await m(this.initializeAuthorizationRequest.bind(this), l.StandardInteractionClientInitializeAuthorizationRequest, this.logger, this.performanceClient, e.correlationId)(e, y.Silent), n = this.initializeServerTelemetryManager(this.apiId);
    try {
      const o = {
        ...t,
        code: e.code
      }, r = await m(this.getClientConfiguration.bind(this), l.StandardInteractionClientGetClientConfiguration, this.logger, this.performanceClient, e.correlationId)({
        serverTelemetryManager: n,
        requestAuthority: t.authority,
        requestAzureCloudOptions: t.azureCloudOptions,
        requestExtraQueryParameters: t.extraQueryParameters,
        account: t.account
      }), i = new _c(r);
      this.logger.verbose("Auth code client created");
      const s = new Mn(i, this.browserStorage, o, this.logger, this.performanceClient);
      return await m(s.handleCodeResponseFromServer.bind(s), l.HandleCodeResponseFromServer, this.logger, this.performanceClient, e.correlationId)({
        code: e.code,
        msgraph_host: e.msGraphHost,
        cloud_graph_host_name: e.cloudGraphHostName,
        cloud_instance_host_name: e.cloudInstanceHostName
      }, t, !1);
    } catch (o) {
      throw o instanceof O && (o.setCorrelationId(this.correlationId), n.cacheFailedRequest(o)), o;
    }
  }
  /**
   * Currently Unsupported
   */
  logout() {
    return Promise.reject(C(At));
  }
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
function Z(a) {
  const e = a == null ? void 0 : a.idTokenClaims;
  if (e != null && e.tfp || e != null && e.acr)
    return "B2C";
  if (e != null && e.tid) {
    if ((e == null ? void 0 : e.tid) === "9188040d-6c67-4c5b-b112-36a304b66dad")
      return "MSA";
  } else return;
  return "AAD";
}
function nt(a, e) {
  try {
    On(a);
  } catch (t) {
    throw e.end({ success: !1 }, t), t;
  }
}
class vt {
  /**
   * @constructor
   * Constructor for the PublicClientApplication used to instantiate the PublicClientApplication object
   *
   * Important attributes in the Configuration object for auth are:
   * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview
   * - authority: the authority URL for your application.
   * - redirect_uri: the uri of your application registered in the portal.
   *
   * In Azure AD, authority is a URL indicating the Azure active directory that MSAL uses to obtain tokens.
   * It is of the form https://login.microsoftonline.com/{Enter_the_Tenant_Info_Here}
   * If your application supports Accounts in one organizational directory, replace "Enter_the_Tenant_Info_Here" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).
   * If your application supports Accounts in any organizational directory, replace "Enter_the_Tenant_Info_Here" value with organizations.
   * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace "Enter_the_Tenant_Info_Here" value with common.
   * To restrict support to Personal Microsoft accounts only, replace "Enter_the_Tenant_Info_Here" value with consumers.
   *
   * In Azure B2C, authority is of the form https://{instance}/tfp/{tenant}/{policyName}/
   * Full B2C functionality will be available in this library in future versions.
   *
   * @param configuration Object for the MSAL PublicClientApplication instance
   */
  constructor(e) {
    this.operatingContext = e, this.isBrowserEnvironment = this.operatingContext.isBrowserEnvironment(), this.config = e.getConfig(), this.initialized = !1, this.logger = this.operatingContext.getLogger(), this.networkClient = this.config.system.networkClient, this.navigationClient = this.config.system.navigationClient, this.redirectResponse = /* @__PURE__ */ new Map(), this.hybridAuthCodeResponses = /* @__PURE__ */ new Map(), this.performanceClient = this.config.telemetry.client, this.browserCrypto = this.isBrowserEnvironment ? new He(this.logger, this.performanceClient) : it, this.eventHandler = new Zs(this.logger), this.browserStorage = this.isBrowserEnvironment ? new $t(this.config.auth.clientId, this.config.cache, this.browserCrypto, this.logger, xa(this.config.auth), this.performanceClient) : zs(this.config.auth.clientId, this.logger);
    const t = {
      cacheLocation: q.MemoryStorage,
      temporaryCacheLocation: q.MemoryStorage,
      storeAuthStateInCookie: !1,
      secureCookies: !1,
      cacheMigrationEnabled: !1,
      claimsBasedCachingEnabled: !1
    };
    this.nativeInternalStorage = new $t(this.config.auth.clientId, t, this.browserCrypto, this.logger, void 0, this.performanceClient), this.tokenCache = new wc(this.config, this.browserStorage, this.logger, this.browserCrypto), this.activeSilentTokenRequests = /* @__PURE__ */ new Map(), this.trackPageVisibility = this.trackPageVisibility.bind(this), this.trackPageVisibilityWithMeasurement = this.trackPageVisibilityWithMeasurement.bind(this), this.listeningToStorageEvents = !1, this.handleAccountCacheChange = this.handleAccountCacheChange.bind(this);
  }
  static async createController(e, t) {
    const n = new vt(e);
    return await n.initialize(t), n;
  }
  trackPageVisibility(e) {
    e && (this.logger.info("Perf: Visibility change detected"), this.performanceClient.incrementFields({ visibilityChangeCount: 1 }, e));
  }
  /**
   * Initializer function to perform async startup tasks such as connecting to WAM extension
   * @param request {?InitializeApplicationRequest} correlation id
   */
  async initialize(e) {
    if (this.logger.trace("initialize called"), this.initialized) {
      this.logger.info("initialize has already been called, exiting early.");
      return;
    }
    if (!this.isBrowserEnvironment) {
      this.logger.info("in non-browser environment, exiting early."), this.initialized = !0, this.eventHandler.emitEvent(I.INITIALIZE_END);
      return;
    }
    const t = (e == null ? void 0 : e.correlationId) || this.getRequestCorrelationId(), n = this.config.system.allowNativeBroker, o = this.performanceClient.startMeasurement(l.InitializeClientApplication, t);
    if (this.eventHandler.emitEvent(I.INITIALIZE_START), n)
      try {
        this.nativeExtensionProvider = await ne.createProvider(this.logger, this.config.system.nativeBrokerHandshakeTimeout, this.performanceClient);
      } catch (r) {
        this.logger.verbose(r);
      }
    this.config.cache.claimsBasedCachingEnabled || (this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"), await m(this.browserStorage.clearTokensAndKeysWithClaims.bind(this.browserStorage), l.ClearTokensAndKeysWithClaims, this.logger, this.performanceClient, t)(this.performanceClient, t)), this.initialized = !0, this.eventHandler.emitEvent(I.INITIALIZE_END), o.end({ allowNativeBroker: n, success: !0 });
  }
  // #region Redirect Flow
  /**
   * Event handler function which allows users to fire events after the PublicClientApplication object
   * has loaded during redirect flows. This should be invoked on all page loads involved in redirect
   * auth flows.
   * @param hash Hash to process. Defaults to the current value of window.location.hash. Only needs to be provided explicitly if the response to be handled is not contained in the current value.
   * @returns Token response or null. If the return value is null, then no auth redirect was detected.
   */
  async handleRedirectPromise(e) {
    if (this.logger.verbose("handleRedirectPromise called"), Yr(this.initialized), this.isBrowserEnvironment) {
      const t = e || "";
      let n = this.redirectResponse.get(t);
      return typeof n > "u" ? (n = this.handleRedirectPromiseInternal(e), this.redirectResponse.set(t, n), this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise")) : this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"), n;
    }
    return this.logger.verbose("handleRedirectPromise returns null, not browser environment"), null;
  }
  /**
   * The internal details of handleRedirectPromise. This is separated out to a helper to allow handleRedirectPromise to memoize requests
   * @param hash
   * @returns
   */
  async handleRedirectPromiseInternal(e) {
    const t = this.getAllAccounts(), n = this.browserStorage.getCachedNativeRequest(), o = n && ne.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider) && this.nativeExtensionProvider && !e, r = o ? n == null ? void 0 : n.correlationId : this.browserStorage.getTemporaryCache(_.CORRELATION_ID, !0) || "", i = this.performanceClient.startMeasurement(l.AcquireTokenRedirect, r);
    this.eventHandler.emitEvent(I.HANDLE_REDIRECT_START, y.Redirect);
    let s;
    if (o && this.nativeExtensionProvider) {
      this.logger.trace("handleRedirectPromise - acquiring token from native platform");
      const c = new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.handleRedirectPromise, this.performanceClient, this.nativeExtensionProvider, n.accountId, this.nativeInternalStorage, n.correlationId);
      s = m(c.handleRedirectPromise.bind(c), l.HandleNativeRedirectPromiseMeasurement, this.logger, this.performanceClient, i.event.correlationId)(this.performanceClient, i.event.correlationId);
    } else {
      this.logger.trace("handleRedirectPromise - acquiring token from web flow");
      const c = this.createRedirectClient(r);
      s = m(c.handleRedirectPromise.bind(c), l.HandleRedirectPromiseMeasurement, this.logger, this.performanceClient, i.event.correlationId)(e, i);
    }
    return s.then((c) => (c ? (t.length < this.getAllAccounts().length ? (this.eventHandler.emitEvent(I.LOGIN_SUCCESS, y.Redirect, c), this.logger.verbose("handleRedirectResponse returned result, login success")) : (this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_SUCCESS, y.Redirect, c), this.logger.verbose("handleRedirectResponse returned result, acquire token success")), i.end({
      success: !0,
      accountType: Z(c.account)
    })) : i.event.errorCode ? i.end({ success: !1 }) : i.discard(), this.eventHandler.emitEvent(I.HANDLE_REDIRECT_END, y.Redirect), c)).catch((c) => {
      const h = c;
      throw t.length > 0 ? this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_FAILURE, y.Redirect, null, h) : this.eventHandler.emitEvent(I.LOGIN_FAILURE, y.Redirect, null, h), this.eventHandler.emitEvent(I.HANDLE_REDIRECT_END, y.Redirect), i.end({
        success: !1
      }, h), c;
    });
  }
  /**
   * Use when you want to obtain an access_token for your API by redirecting the user's browser window to the authorization endpoint. This function redirects
   * the page, so any code that follows this function will not execute.
   *
   * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current
   * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.
   *
   * @param request
   */
  async acquireTokenRedirect(e) {
    const t = this.getRequestCorrelationId(e);
    this.logger.verbose("acquireTokenRedirect called", t);
    const n = this.performanceClient.startMeasurement(l.AcquireTokenPreRedirect, t);
    n.add({
      accountType: Z(e.account),
      scenarioId: e.scenarioId
    });
    const o = e.onRedirectNavigate;
    if (o)
      e.onRedirectNavigate = (i) => {
        const s = typeof o == "function" ? o(i) : void 0;
        return s !== !1 ? n.end({ success: !0 }) : n.discard(), s;
      };
    else {
      const i = this.config.auth.onRedirectNavigate;
      this.config.auth.onRedirectNavigate = (s) => {
        const c = typeof i == "function" ? i(s) : void 0;
        return c !== !1 ? n.end({ success: !0 }) : n.discard(), c;
      };
    }
    const r = this.getAllAccounts().length > 0;
    try {
      lo(this.initialized, this.config), this.browserStorage.setInteractionInProgress(!0), r ? this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_START, y.Redirect, e) : this.eventHandler.emitEvent(I.LOGIN_START, y.Redirect, e);
      let i;
      return this.nativeExtensionProvider && this.canUseNative(e) ? i = new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.acquireTokenRedirect, this.performanceClient, this.nativeExtensionProvider, this.getNativeAccountId(e), this.nativeInternalStorage, t).acquireTokenRedirect(e, n).catch((c) => {
        if (c instanceof te && _e(c))
          return this.nativeExtensionProvider = void 0, this.createRedirectClient(t).acquireToken(e);
        if (c instanceof X)
          return this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow"), this.createRedirectClient(t).acquireToken(e);
        throw this.browserStorage.setInteractionInProgress(!1), c;
      }) : i = this.createRedirectClient(t).acquireToken(e), await i;
    } catch (i) {
      throw n.end({ success: !1 }, i), r ? this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_FAILURE, y.Redirect, null, i) : this.eventHandler.emitEvent(I.LOGIN_FAILURE, y.Redirect, null, i), i;
    }
  }
  // #endregion
  // #region Popup Flow
  /**
   * Use when you want to obtain an access_token for your API via opening a popup window in the user's browser
   *
   * @param request
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  acquireTokenPopup(e) {
    const t = this.getRequestCorrelationId(e), n = this.performanceClient.startMeasurement(l.AcquireTokenPopup, t);
    n.add({
      scenarioId: e.scenarioId,
      accountType: Z(e.account)
    });
    try {
      this.logger.verbose("acquireTokenPopup called", t), nt(this.initialized, n), this.browserStorage.setInteractionInProgress(!0);
    } catch (i) {
      return Promise.reject(i);
    }
    const o = this.getAllAccounts();
    o.length > 0 ? this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_START, y.Popup, e) : this.eventHandler.emitEvent(I.LOGIN_START, y.Popup, e);
    let r;
    return this.canUseNative(e) ? r = this.acquireTokenNative({
      ...e,
      correlationId: t
    }, b.acquireTokenPopup).then((i) => (this.browserStorage.setInteractionInProgress(!1), n.end({
      success: !0,
      isNativeBroker: !0,
      accountType: Z(i.account)
    }), i)).catch((i) => {
      if (i instanceof te && _e(i))
        return this.nativeExtensionProvider = void 0, this.createPopupClient(t).acquireToken(e);
      if (i instanceof X)
        return this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow"), this.createPopupClient(t).acquireToken(e);
      throw this.browserStorage.setInteractionInProgress(!1), i;
    }) : r = this.createPopupClient(t).acquireToken(e), r.then((i) => (o.length < this.getAllAccounts().length ? this.eventHandler.emitEvent(I.LOGIN_SUCCESS, y.Popup, i) : this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_SUCCESS, y.Popup, i), n.end({
      success: !0,
      accessTokenSize: i.accessToken.length,
      idTokenSize: i.idToken.length,
      accountType: Z(i.account)
    }), i)).catch((i) => (o.length > 0 ? this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_FAILURE, y.Popup, null, i) : this.eventHandler.emitEvent(I.LOGIN_FAILURE, y.Popup, null, i), n.end({
      success: !1
    }, i), Promise.reject(i)));
  }
  trackPageVisibilityWithMeasurement() {
    const e = this.ssoSilentMeasurement || this.acquireTokenByCodeAsyncMeasurement;
    e && (this.logger.info("Perf: Visibility change detected in ", e.event.name), e.increment({
      visibilityChangeCount: 1
    }));
  }
  // #endregion
  // #region Silent Flow
  /**
   * This function uses a hidden iframe to fetch an authorization code from the eSTS. There are cases where this may not work:
   * - Any browser using a form of Intelligent Tracking Prevention
   * - If there is not an established session with the service
   *
   * In these cases, the request must be done inside a popup or full frame redirect.
   *
   * For the cases where interaction is required, you cannot send a request with prompt=none.
   *
   * If your refresh token has expired, you can use this function to fetch a new set of tokens silently as long as
   * you session on the server still exists.
   * @param request {@link SsoSilentRequest}
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  async ssoSilent(e) {
    var r, i;
    const t = this.getRequestCorrelationId(e), n = {
      ...e,
      // will be PromptValue.NONE or PromptValue.NO_SESSION
      prompt: e.prompt,
      correlationId: t
    };
    this.ssoSilentMeasurement = this.performanceClient.startMeasurement(l.SsoSilent, t), (r = this.ssoSilentMeasurement) == null || r.add({
      scenarioId: e.scenarioId,
      accountType: Z(e.account)
    }), nt(this.initialized, this.ssoSilentMeasurement), (i = this.ssoSilentMeasurement) == null || i.increment({
      visibilityChangeCount: 0
    }), document.addEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement), this.logger.verbose("ssoSilent called", t), this.eventHandler.emitEvent(I.SSO_SILENT_START, y.Silent, n);
    let o;
    return this.canUseNative(n) ? o = this.acquireTokenNative(n, b.ssoSilent).catch((s) => {
      if (s instanceof te && _e(s))
        return this.nativeExtensionProvider = void 0, this.createSilentIframeClient(n.correlationId).acquireToken(n);
      throw s;
    }) : o = this.createSilentIframeClient(n.correlationId).acquireToken(n), o.then((s) => {
      var c;
      return this.eventHandler.emitEvent(I.SSO_SILENT_SUCCESS, y.Silent, s), (c = this.ssoSilentMeasurement) == null || c.end({
        success: !0,
        isNativeBroker: s.fromNativeBroker,
        accessTokenSize: s.accessToken.length,
        idTokenSize: s.idToken.length,
        accountType: Z(s.account)
      }), s;
    }).catch((s) => {
      var c;
      throw this.eventHandler.emitEvent(I.SSO_SILENT_FAILURE, y.Silent, null, s), (c = this.ssoSilentMeasurement) == null || c.end({
        success: !1
      }, s), s;
    }).finally(() => {
      document.removeEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement);
    });
  }
  /**
   * This function redeems an authorization code (passed as code) from the eSTS token endpoint.
   * This authorization code should be acquired server-side using a confidential client to acquire a spa_code.
   * This API is not indended for normal authorization code acquisition and redemption.
   *
   * Redemption of this authorization code will not require PKCE, as it was acquired by a confidential client.
   *
   * @param request {@link AuthorizationCodeRequest}
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  async acquireTokenByCode(e) {
    const t = this.getRequestCorrelationId(e);
    this.logger.trace("acquireTokenByCode called", t);
    const n = this.performanceClient.startMeasurement(l.AcquireTokenByCode, t);
    nt(this.initialized, n), this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_BY_CODE_START, y.Silent, e), n.add({ scenarioId: e.scenarioId });
    try {
      if (e.code && e.nativeAccountId)
        throw C(Nr);
      if (e.code) {
        const o = e.code;
        let r = this.hybridAuthCodeResponses.get(o);
        return r ? (this.logger.verbose("Existing acquireTokenByCode request found", t), n.discard()) : (this.logger.verbose("Initiating new acquireTokenByCode request", t), r = this.acquireTokenByCodeAsync({
          ...e,
          correlationId: t
        }).then((i) => (this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_BY_CODE_SUCCESS, y.Silent, i), this.hybridAuthCodeResponses.delete(o), n.end({
          success: !0,
          isNativeBroker: i.fromNativeBroker,
          accessTokenSize: i.accessToken.length,
          idTokenSize: i.idToken.length,
          accountType: Z(i.account)
        }), i)).catch((i) => {
          throw this.hybridAuthCodeResponses.delete(o), this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_BY_CODE_FAILURE, y.Silent, null, i), n.end({
            success: !1
          }, i), i;
        }), this.hybridAuthCodeResponses.set(o, r)), await r;
      } else if (e.nativeAccountId)
        if (this.canUseNative(e, e.nativeAccountId)) {
          const o = await this.acquireTokenNative({
            ...e,
            correlationId: t
          }, b.acquireTokenByCode, e.nativeAccountId).catch((r) => {
            throw r instanceof te && _e(r) && (this.nativeExtensionProvider = void 0), r;
          });
          return n.end({
            accountType: Z(o.account),
            success: !0
          }), o;
        } else
          throw C(Pr);
      else
        throw C(Or);
    } catch (o) {
      throw this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_BY_CODE_FAILURE, y.Silent, null, o), n.end({
        success: !1
      }, o), o;
    }
  }
  /**
   * Creates a SilentAuthCodeClient to redeem an authorization code.
   * @param request
   * @returns Result of the operation to redeem the authorization code
   */
  async acquireTokenByCodeAsync(e) {
    var o;
    return this.logger.trace("acquireTokenByCodeAsync called", e.correlationId), this.acquireTokenByCodeAsyncMeasurement = this.performanceClient.startMeasurement(l.AcquireTokenByCodeAsync, e.correlationId), (o = this.acquireTokenByCodeAsyncMeasurement) == null || o.increment({
      visibilityChangeCount: 0
    }), document.addEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement), await this.createSilentAuthCodeClient(e.correlationId).acquireToken(e).then((r) => {
      var i;
      return (i = this.acquireTokenByCodeAsyncMeasurement) == null || i.end({
        success: !0,
        fromCache: r.fromCache,
        isNativeBroker: r.fromNativeBroker
      }), r;
    }).catch((r) => {
      var i;
      throw (i = this.acquireTokenByCodeAsyncMeasurement) == null || i.end({
        success: !1
      }, r), r;
    }).finally(() => {
      document.removeEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement);
    });
  }
  /**
   * Attempt to acquire an access token from the cache
   * @param silentCacheClient SilentCacheClient
   * @param commonRequest CommonSilentFlowRequest
   * @param silentRequest SilentRequest
   * @returns A promise that, when resolved, returns the access token
   */
  async acquireTokenFromCache(e, t) {
    switch (this.performanceClient.addQueueMeasurement(l.AcquireTokenFromCache, e.correlationId), t) {
      case z.Default:
      case z.AccessToken:
      case z.AccessTokenAndRefreshToken:
        const n = this.createSilentCacheClient(e.correlationId);
        return m(n.acquireToken.bind(n), l.SilentCacheClientAcquireToken, this.logger, this.performanceClient, e.correlationId)(e);
      default:
        throw p(ce);
    }
  }
  /**
   * Attempt to acquire an access token via a refresh token
   * @param commonRequest CommonSilentFlowRequest
   * @param cacheLookupPolicy CacheLookupPolicy
   * @returns A promise that, when resolved, returns the access token
   */
  async acquireTokenByRefreshToken(e, t) {
    switch (this.performanceClient.addQueueMeasurement(l.AcquireTokenByRefreshToken, e.correlationId), t) {
      case z.Default:
      case z.AccessTokenAndRefreshToken:
      case z.RefreshToken:
      case z.RefreshTokenAndNetwork:
        const n = this.createSilentRefreshClient(e.correlationId);
        return m(n.acquireToken.bind(n), l.SilentRefreshClientAcquireToken, this.logger, this.performanceClient, e.correlationId)(e);
      default:
        throw p(ce);
    }
  }
  /**
   * Attempt to acquire an access token via an iframe
   * @param request CommonSilentFlowRequest
   * @returns A promise that, when resolved, returns the access token
   */
  async acquireTokenBySilentIframe(e) {
    this.performanceClient.addQueueMeasurement(l.AcquireTokenBySilentIframe, e.correlationId);
    const t = this.createSilentIframeClient(e.correlationId);
    return m(t.acquireToken.bind(t), l.SilentIframeClientAcquireToken, this.logger, this.performanceClient, e.correlationId)(e);
  }
  // #endregion
  // #region Logout
  /**
   * Deprecated logout function. Use logoutRedirect or logoutPopup instead
   * @param logoutRequest
   * @deprecated
   */
  async logout(e) {
    const t = this.getRequestCorrelationId(e);
    return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.", t), this.logoutRedirect({
      correlationId: t,
      ...e
    });
  }
  /**
   * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.
   * Default behaviour is to redirect the user to `window.location.href`.
   * @param logoutRequest
   */
  async logoutRedirect(e) {
    const t = this.getRequestCorrelationId(e);
    return lo(this.initialized, this.config), this.browserStorage.setInteractionInProgress(!0), this.createRedirectClient(t).logout(e);
  }
  /**
   * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server
   * @param logoutRequest
   */
  logoutPopup(e) {
    try {
      const t = this.getRequestCorrelationId(e);
      return On(this.initialized), this.browserStorage.setInteractionInProgress(!0), this.createPopupClient(t).logout(e);
    } catch (t) {
      return Promise.reject(t);
    }
  }
  /**
   * Creates a cache interaction client to clear broswer cache.
   * @param logoutRequest
   */
  async clearCache(e) {
    if (!this.isBrowserEnvironment) {
      this.logger.info("in non-browser environment, returning early.");
      return;
    }
    const t = this.getRequestCorrelationId(e);
    return this.createSilentCacheClient(t).logout(e);
  }
  // #endregion
  // #region Account APIs
  /**
   * Returns all the accounts in the cache that match the optional filter. If no filter is provided, all accounts are returned.
   * @param accountFilter - (Optional) filter to narrow down the accounts returned
   * @returns Array of AccountInfo objects in cache
   */
  getAllAccounts(e) {
    const t = this.getRequestCorrelationId();
    return Vs(this.logger, this.browserStorage, this.isBrowserEnvironment, t, e);
  }
  /**
   * Returns the first account found in the cache that matches the account filter passed in.
   * @param accountFilter
   * @returns The first account found in the cache matching the provided filter or null if no account could be found.
   */
  getAccount(e) {
    const t = this.getRequestCorrelationId();
    return Qs(e, this.logger, this.browserStorage, t);
  }
  /**
   * Returns the signed in account matching username.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found.
   * This API is provided for convenience but getAccountById should be used for best reliability
   * @param username
   * @returns The account object stored in MSAL
   */
  getAccountByUsername(e) {
    const t = this.getRequestCorrelationId();
    return Ys(e, this.logger, this.browserStorage, t);
  }
  /**
   * Returns the signed in account matching homeAccountId.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found
   * @param homeAccountId
   * @returns The account object stored in MSAL
   */
  getAccountByHomeId(e) {
    const t = this.getRequestCorrelationId();
    return Ws(e, this.logger, this.browserStorage, t);
  }
  /**
   * Returns the signed in account matching localAccountId.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found
   * @param localAccountId
   * @returns The account object stored in MSAL
   */
  getAccountByLocalId(e) {
    const t = this.getRequestCorrelationId();
    return js(e, this.logger, this.browserStorage, t);
  }
  /**
   * Sets the account to use as the active account. If no account is passed to the acquireToken APIs, then MSAL will use this active account.
   * @param account
   */
  setActiveAccount(e) {
    const t = this.getRequestCorrelationId();
    Js(e, this.browserStorage, t);
  }
  /**
   * Gets the currently active account
   */
  getActiveAccount() {
    const e = this.getRequestCorrelationId();
    return Xs(this.browserStorage, e);
  }
  // #endregion
  /**
   * Hydrates the cache with the tokens from an AuthenticationResult
   * @param result
   * @param request
   * @returns
   */
  async hydrateCache(e, t) {
    this.logger.verbose("hydrateCache called");
    const n = U.createFromAccountInfo(e.account, e.cloudGraphHostName, e.msGraphHost);
    return this.browserStorage.setAccount(n, e.correlationId), e.fromNativeBroker ? (this.logger.verbose("Response was from native broker, storing in-memory"), this.nativeInternalStorage.hydrateCache(e, t)) : this.browserStorage.hydrateCache(e, t);
  }
  // #region Helpers
  /**
   * Acquire a token from native device (e.g. WAM)
   * @param request
   */
  async acquireTokenNative(e, t, n) {
    if (this.logger.trace("acquireTokenNative called"), !this.nativeExtensionProvider)
      throw C(ze);
    return new Ne(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, t, this.performanceClient, this.nativeExtensionProvider, n || this.getNativeAccountId(e), this.nativeInternalStorage, e.correlationId).acquireToken(e);
  }
  /**
   * Returns boolean indicating if this request can use the native broker
   * @param request
   */
  canUseNative(e, t) {
    if (this.logger.trace("canUseNative called"), !ne.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider, e.authenticationScheme))
      return this.logger.trace("canUseNative: isNativeAvailable returned false, returning false"), !1;
    if (e.prompt)
      switch (e.prompt) {
        case D.NONE:
        case D.CONSENT:
        case D.LOGIN:
          this.logger.trace("canUseNative: prompt is compatible with native flow");
          break;
        default:
          return this.logger.trace(`canUseNative: prompt = ${e.prompt} is not compatible with native flow, returning false`), !1;
      }
    return !t && !this.getNativeAccountId(e) ? (this.logger.trace("canUseNative: nativeAccountId is not available, returning false"), !1) : !0;
  }
  /**
   * Get the native accountId from the account
   * @param request
   * @returns
   */
  getNativeAccountId(e) {
    const t = e.account || this.getAccount({
      loginHint: e.loginHint,
      sid: e.sid
    }) || this.getActiveAccount();
    return t && t.nativeAccountId || "";
  }
  /**
   * Returns new instance of the Popup Interaction Client
   * @param correlationId
   */
  createPopupClient(e) {
    return new mc(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, e);
  }
  /**
   * Returns new instance of the Redirect Interaction Client
   * @param correlationId
   */
  createRedirectClient(e) {
    return new Cc(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, e);
  }
  /**
   * Returns new instance of the Silent Iframe Interaction Client
   * @param correlationId
   */
  createSilentIframeClient(e) {
    return new Sc(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.ssoSilent, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, e);
  }
  /**
   * Returns new instance of the Silent Cache Interaction Client
   */
  createSilentCacheClient(e) {
    return new Zr(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, e);
  }
  /**
   * Returns new instance of the Silent Refresh Interaction Client
   */
  createSilentRefreshClient(e) {
    return new vc(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, e);
  }
  /**
   * Returns new instance of the Silent AuthCode Interaction Client
   */
  createSilentAuthCodeClient(e) {
    return new kc(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, b.acquireTokenByCode, this.performanceClient, this.nativeExtensionProvider, e);
  }
  /**
   * Adds event callbacks to array
   * @param callback
   */
  addEventCallback(e, t) {
    return this.eventHandler.addEventCallback(e, t);
  }
  /**
   * Removes callback with provided id from callback array
   * @param callbackId
   */
  removeEventCallback(e) {
    this.eventHandler.removeEventCallback(e);
  }
  /**
   * Registers a callback to receive performance events.
   *
   * @param {PerformanceCallbackFunction} callback
   * @returns {string}
   */
  addPerformanceCallback(e) {
    return Qr(), this.performanceClient.addPerformanceCallback(e);
  }
  /**
   * Removes a callback registered with addPerformanceCallback.
   *
   * @param {string} callbackId
   * @returns {boolean}
   */
  removePerformanceCallback(e) {
    return this.performanceClient.removePerformanceCallback(e);
  }
  /**
   * Adds event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
   */
  enableAccountStorageEvents() {
    typeof window > "u" || (this.listeningToStorageEvents ? this.logger.verbose("Account storage listener already registered.") : (this.logger.verbose("Adding account storage listener."), this.listeningToStorageEvents = !0, window.addEventListener("storage", this.handleAccountCacheChange)));
  }
  /**
   * Removes event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
   */
  disableAccountStorageEvents() {
    typeof window > "u" || (this.listeningToStorageEvents ? (this.logger.verbose("Removing account storage listener."), window.removeEventListener("storage", this.handleAccountCacheChange), this.listeningToStorageEvents = !1) : this.logger.verbose("No account storage listener registered."));
  }
  /**
   * Emit account added/removed events when cached accounts are changed in a different tab or frame
   */
  handleAccountCacheChange(e) {
    var t;
    try {
      (t = e.key) != null && t.includes(H.ACTIVE_ACCOUNT_FILTERS) && this.eventHandler.emitEvent(I.ACTIVE_ACCOUNT_CHANGED);
      const n = e.newValue || e.oldValue;
      if (!n)
        return;
      const o = JSON.parse(n);
      if (typeof o != "object" || !U.isAccountEntity(o))
        return;
      const i = Me.toObject(new U(), o).getAccountInfo();
      !e.oldValue && e.newValue ? (this.logger.info("Account was added to cache in a different window"), this.eventHandler.emitEvent(I.ACCOUNT_ADDED, void 0, i)) : !e.newValue && e.oldValue && (this.logger.info("Account was removed from cache in a different window"), this.eventHandler.emitEvent(I.ACCOUNT_REMOVED, void 0, i));
    } catch {
      return;
    }
  }
  /**
   * Gets the token cache for the application.
   */
  getTokenCache() {
    return this.tokenCache;
  }
  /**
   * Returns the logger instance
   */
  getLogger() {
    return this.logger;
  }
  /**
   * Replaces the default logger set in configurations with new Logger with new configurations
   * @param logger Logger instance
   */
  setLogger(e) {
    this.logger = e;
  }
  /**
   * Called by wrapper libraries (Angular & React) to set SKU and Version passed down to telemetry, logger, etc.
   * @param sku
   * @param version
   */
  initializeWrapperLibrary(e, t) {
    this.browserStorage.setWrapperMetadata(e, t);
  }
  /**
   * Sets navigation client
   * @param navigationClient
   */
  setNavigationClient(e) {
    this.navigationClient = e;
  }
  /**
   * Returns the configuration object
   */
  getConfiguration() {
    return this.config;
  }
  /**
   * Returns the performance client
   */
  getPerformanceClient() {
    return this.performanceClient;
  }
  /**
   * Returns the browser env indicator
   */
  isBrowserEnv() {
    return this.isBrowserEnvironment;
  }
  /**
   * Generates a correlation id for a request if none is provided.
   *
   * @protected
   * @param {?Partial<BaseAuthRequest>} [request]
   * @returns {string}
   */
  getRequestCorrelationId(e) {
    return e != null && e.correlationId ? e.correlationId : this.isBrowserEnvironment ? ie() : u.EMPTY_STRING;
  }
  // #endregion
  /**
   * Use when initiating the login process by redirecting the user's browser to the authorization endpoint. This function redirects the page, so
   * any code that follows this function will not execute.
   *
   * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current
   * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.
   *
   * @param request
   */
  async loginRedirect(e) {
    const t = this.getRequestCorrelationId(e);
    return this.logger.verbose("loginRedirect called", t), this.acquireTokenRedirect({
      correlationId: t,
      ...e || so
    });
  }
  /**
   * Use when initiating the login process via opening a popup window in the user's browser
   *
   * @param request
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  loginPopup(e) {
    const t = this.getRequestCorrelationId(e);
    return this.logger.verbose("loginPopup called", t), this.acquireTokenPopup({
      correlationId: t,
      ...e || so
    });
  }
  /**
   * Silently acquire an access token for a given set of scopes. Returns currently processing promise if parallel requests are made.
   *
   * @param {@link (SilentRequest:type)}
   * @returns {Promise.<AuthenticationResult>} - a promise that is fulfilled when this function has completed, or rejected if an error was raised. Returns the {@link AuthResponse} object
   */
  async acquireTokenSilent(e) {
    const t = this.getRequestCorrelationId(e), n = this.performanceClient.startMeasurement(l.AcquireTokenSilent, t);
    n.add({
      cacheLookupPolicy: e.cacheLookupPolicy,
      scenarioId: e.scenarioId
    }), nt(this.initialized, n), this.logger.verbose("acquireTokenSilent called", t);
    const o = e.account || this.getActiveAccount();
    if (!o)
      throw C(Sr);
    n.add({ accountType: Z(o) });
    const r = {
      clientId: this.config.auth.clientId,
      authority: e.authority || u.EMPTY_STRING,
      scopes: e.scopes,
      homeAccountIdentifier: o.homeAccountId,
      claims: e.claims,
      authenticationScheme: e.authenticationScheme,
      resourceRequestMethod: e.resourceRequestMethod,
      resourceRequestUri: e.resourceRequestUri,
      shrClaims: e.shrClaims,
      sshKid: e.sshKid,
      shrOptions: e.shrOptions
    }, i = JSON.stringify(r), s = this.activeSilentTokenRequests.get(i);
    if (typeof s > "u") {
      this.logger.verbose("acquireTokenSilent called for the first time, storing active request", t);
      const c = m(this.acquireTokenSilentAsync.bind(this), l.AcquireTokenSilentAsync, this.logger, this.performanceClient, t)({
        ...e,
        correlationId: t
      }, o).then((h) => (this.activeSilentTokenRequests.delete(i), n.end({
        success: !0,
        fromCache: h.fromCache,
        isNativeBroker: h.fromNativeBroker,
        cacheLookupPolicy: e.cacheLookupPolicy,
        accessTokenSize: h.accessToken.length,
        idTokenSize: h.idToken.length
      }), h)).catch((h) => {
        throw this.activeSilentTokenRequests.delete(i), n.end({
          success: !1
        }, h), h;
      });
      return this.activeSilentTokenRequests.set(i, c), {
        ...await c,
        state: e.state
      };
    } else
      return this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call", t), n.discard(), {
        ...await s,
        state: e.state
      };
  }
  /**
   * Silently acquire an access token for a given set of scopes. Will use cached token if available, otherwise will attempt to acquire a new token from the network via refresh token.
   * @param {@link (SilentRequest:type)}
   * @param {@link (AccountInfo:type)}
   * @returns {Promise.<AuthenticationResult>} - a promise that is fulfilled when this function has completed, or rejected if an error was raised. Returns the {@link AuthResponse}
   */
  async acquireTokenSilentAsync(e, t) {
    const n = () => this.trackPageVisibility(e.correlationId);
    this.performanceClient.addQueueMeasurement(l.AcquireTokenSilentAsync, e.correlationId), this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_START, y.Silent, e), e.correlationId && this.performanceClient.incrementFields({ visibilityChangeCount: 0 }, e.correlationId), document.addEventListener("visibilitychange", n);
    const o = await m(rc, l.InitializeSilentRequest, this.logger, this.performanceClient, e.correlationId)(e, t, this.config, this.performanceClient, this.logger), r = e.cacheLookupPolicy || z.Default;
    return this.acquireTokenSilentNoIframe(o, r).catch(async (s) => {
      if (Rc(s, r))
        if (this.activeIframeRequest)
          if (r !== z.Skip) {
            const [h, d] = this.activeIframeRequest;
            this.logger.verbose(`Iframe request is already in progress, awaiting resolution for request with correlationId: ${d}`, o.correlationId);
            const g = this.performanceClient.startMeasurement(l.AwaitConcurrentIframe, o.correlationId);
            g.add({
              awaitIframeCorrelationId: d
            });
            const f = await h;
            if (g.end({
              success: f
            }), f)
              return this.logger.verbose(`Parallel iframe request with correlationId: ${d} succeeded. Retrying cache and/or RT redemption`, o.correlationId), this.acquireTokenSilentNoIframe(o, r);
            throw this.logger.info(`Iframe request with correlationId: ${d} failed. Interaction is required.`), s;
          } else
            return this.logger.warning("Another iframe request is currently in progress and CacheLookupPolicy is set to Skip. This may result in degraded performance and/or reliability for both calls. Please consider changing the CacheLookupPolicy to take advantage of request queuing and token cache.", o.correlationId), m(this.acquireTokenBySilentIframe.bind(this), l.AcquireTokenBySilentIframe, this.logger, this.performanceClient, o.correlationId)(o);
        else {
          let h;
          return this.activeIframeRequest = [
            new Promise((d) => {
              h = d;
            }),
            o.correlationId
          ], this.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.", o.correlationId), m(this.acquireTokenBySilentIframe.bind(this), l.AcquireTokenBySilentIframe, this.logger, this.performanceClient, o.correlationId)(o).then((d) => (h(!0), d)).catch((d) => {
            throw h(!1), d;
          }).finally(() => {
            this.activeIframeRequest = void 0;
          });
        }
      else
        throw s;
    }).then((s) => (this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_SUCCESS, y.Silent, s), e.correlationId && this.performanceClient.addFields({
      fromCache: s.fromCache,
      isNativeBroker: s.fromNativeBroker
    }, e.correlationId), s)).catch((s) => {
      throw this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_FAILURE, y.Silent, null, s), s;
    }).finally(() => {
      document.removeEventListener("visibilitychange", n);
    });
  }
  /**
   * AcquireTokenSilent without the iframe fallback. This is used to enable the correct fallbacks in cases where there's a potential for multiple silent requests to be made in parallel and prevent those requests from making concurrent iframe requests.
   * @param silentRequest
   * @param cacheLookupPolicy
   * @returns
   */
  async acquireTokenSilentNoIframe(e, t) {
    return ne.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider, e.authenticationScheme) && e.account.nativeAccountId ? (this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"), this.acquireTokenNative(e, b.acquireTokenSilent_silentFlow).catch(async (n) => {
      throw n instanceof te && _e(n) ? (this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"), this.nativeExtensionProvider = void 0, p(ce)) : n;
    })) : (this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"), m(this.acquireTokenFromCache.bind(this), l.AcquireTokenFromCache, this.logger, this.performanceClient, e.correlationId)(e, t).catch((n) => {
      if (t === z.AccessToken)
        throw n;
      return this.eventHandler.emitEvent(I.ACQUIRE_TOKEN_NETWORK_START, y.Silent, e), m(this.acquireTokenByRefreshToken.bind(this), l.AcquireTokenByRefreshToken, this.logger, this.performanceClient, e.correlationId)(e, t);
    }));
  }
}
function Rc(a, e) {
  const t = !(a instanceof X && // For refresh token errors, bad_token does not always require interaction (silently resolvable)
  a.subError !== Tt), n = a.errorCode === V.INVALID_GRANT_ERROR || a.errorCode === ce, o = t && n || a.errorCode === lt || a.errorCode === mn, r = hs.includes(e);
  return o && r;
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
async function bc(a, e) {
  const t = new Ie(a);
  return await t.initialize(), vt.createController(t, e);
}
/*! @azure/msal-browser v3.30.0 2025-08-05 */
class ni {
  /**
   * Creates StandardController and passes it to the PublicClientApplication
   *
   * @param configuration {Configuration}
   */
  static async createPublicClientApplication(e) {
    const t = await bc(e);
    return new ni(e, t);
  }
  /**
   * @constructor
   * Constructor for the PublicClientApplication used to instantiate the PublicClientApplication object
   *
   * Important attributes in the Configuration object for auth are:
   * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview
   * - authority: the authority URL for your application.
   * - redirect_uri: the uri of your application registered in the portal.
   *
   * In Azure AD, authority is a URL indicating the Azure active directory that MSAL uses to obtain tokens.
   * It is of the form https://login.microsoftonline.com/{Enter_the_Tenant_Info_Here}
   * If your application supports Accounts in one organizational directory, replace "Enter_the_Tenant_Info_Here" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).
   * If your application supports Accounts in any organizational directory, replace "Enter_the_Tenant_Info_Here" value with organizations.
   * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace "Enter_the_Tenant_Info_Here" value with common.
   * To restrict support to Personal Microsoft accounts only, replace "Enter_the_Tenant_Info_Here" value with consumers.
   *
   * In Azure B2C, authority is of the form https://{instance}/tfp/{tenant}/{policyName}/
   * Full B2C functionality will be available in this library in future versions.
   *
   * @param configuration Object for the MSAL PublicClientApplication instance
   * @param IController Optional parameter to explictly set the controller. (Will be removed when we remove public constructor)
   */
  constructor(e, t) {
    this.controller = t || new vt(new Ie(e));
  }
  /**
   * Initializer function to perform async startup tasks such as connecting to WAM extension
   * @param request {?InitializeApplicationRequest}
   */
  async initialize(e) {
    return this.controller.initialize(e);
  }
  /**
   * Use when you want to obtain an access_token for your API via opening a popup window in the user's browser
   *
   * @param request
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  async acquireTokenPopup(e) {
    return this.controller.acquireTokenPopup(e);
  }
  /**
   * Use when you want to obtain an access_token for your API by redirecting the user's browser window to the authorization endpoint. This function redirects
   * the page, so any code that follows this function will not execute.
   *
   * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current
   * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.
   *
   * @param request
   */
  acquireTokenRedirect(e) {
    return this.controller.acquireTokenRedirect(e);
  }
  /**
   * Silently acquire an access token for a given set of scopes. Returns currently processing promise if parallel requests are made.
   *
   * @param {@link (SilentRequest:type)}
   * @returns {Promise.<AuthenticationResult>} - a promise that is fulfilled when this function has completed, or rejected if an error was raised. Returns the {@link AuthenticationResult} object
   */
  acquireTokenSilent(e) {
    return this.controller.acquireTokenSilent(e);
  }
  /**
   * This function redeems an authorization code (passed as code) from the eSTS token endpoint.
   * This authorization code should be acquired server-side using a confidential client to acquire a spa_code.
   * This API is not indended for normal authorization code acquisition and redemption.
   *
   * Redemption of this authorization code will not require PKCE, as it was acquired by a confidential client.
   *
   * @param request {@link AuthorizationCodeRequest}
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  acquireTokenByCode(e) {
    return this.controller.acquireTokenByCode(e);
  }
  /**
   * Adds event callbacks to array
   * @param callback
   * @param eventTypes
   */
  addEventCallback(e, t) {
    return this.controller.addEventCallback(e, t);
  }
  /**
   * Removes callback with provided id from callback array
   * @param callbackId
   */
  removeEventCallback(e) {
    return this.controller.removeEventCallback(e);
  }
  /**
   * Registers a callback to receive performance events.
   *
   * @param {PerformanceCallbackFunction} callback
   * @returns {string}
   */
  addPerformanceCallback(e) {
    return this.controller.addPerformanceCallback(e);
  }
  /**
   * Removes a callback registered with addPerformanceCallback.
   *
   * @param {string} callbackId
   * @returns {boolean}
   */
  removePerformanceCallback(e) {
    return this.controller.removePerformanceCallback(e);
  }
  /**
   * Adds event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
   */
  enableAccountStorageEvents() {
    this.controller.enableAccountStorageEvents();
  }
  /**
   * Removes event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
   */
  disableAccountStorageEvents() {
    this.controller.disableAccountStorageEvents();
  }
  /**
   * Returns the first account found in the cache that matches the account filter passed in.
   * @param accountFilter
   * @returns The first account found in the cache matching the provided filter or null if no account could be found.
   */
  getAccount(e) {
    return this.controller.getAccount(e);
  }
  /**
   * Returns the signed in account matching homeAccountId.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found
   * @param homeAccountId
   * @returns The account object stored in MSAL
   * @deprecated - Use getAccount instead
   */
  getAccountByHomeId(e) {
    return this.controller.getAccountByHomeId(e);
  }
  /**
   * Returns the signed in account matching localAccountId.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found
   * @param localAccountId
   * @returns The account object stored in MSAL
   * @deprecated - Use getAccount instead
   */
  getAccountByLocalId(e) {
    return this.controller.getAccountByLocalId(e);
  }
  /**
   * Returns the signed in account matching username.
   * (the account object is created at the time of successful login)
   * or null when no matching account is found.
   * This API is provided for convenience but getAccountById should be used for best reliability
   * @param userName
   * @returns The account object stored in MSAL
   * @deprecated - Use getAccount instead
   */
  getAccountByUsername(e) {
    return this.controller.getAccountByUsername(e);
  }
  /**
   * Returns all the accounts in the cache that match the optional filter. If no filter is provided, all accounts are returned.
   * @param accountFilter - (Optional) filter to narrow down the accounts returned
   * @returns Array of AccountInfo objects in cache
   */
  getAllAccounts(e) {
    return this.controller.getAllAccounts(e);
  }
  /**
   * Event handler function which allows users to fire events after the PublicClientApplication object
   * has loaded during redirect flows. This should be invoked on all page loads involved in redirect
   * auth flows.
   * @param hash Hash to process. Defaults to the current value of window.location.hash. Only needs to be provided explicitly if the response to be handled is not contained in the current value.
   * @returns Token response or null. If the return value is null, then no auth redirect was detected.
   */
  handleRedirectPromise(e) {
    return this.controller.handleRedirectPromise(e);
  }
  /**
   * Use when initiating the login process via opening a popup window in the user's browser
   *
   * @param request
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  loginPopup(e) {
    return this.controller.loginPopup(e);
  }
  /**
   * Use when initiating the login process by redirecting the user's browser to the authorization endpoint. This function redirects the page, so
   * any code that follows this function will not execute.
   *
   * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current
   * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.
   *
   * @param request
   */
  loginRedirect(e) {
    return this.controller.loginRedirect(e);
  }
  /**
   * Deprecated logout function. Use logoutRedirect or logoutPopup instead
   * @param logoutRequest
   * @deprecated
   */
  logout(e) {
    return this.controller.logout(e);
  }
  /**
   * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.
   * Default behaviour is to redirect the user to `window.location.href`.
   * @param logoutRequest
   */
  logoutRedirect(e) {
    return this.controller.logoutRedirect(e);
  }
  /**
   * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server
   * @param logoutRequest
   */
  logoutPopup(e) {
    return this.controller.logoutPopup(e);
  }
  /**
   * This function uses a hidden iframe to fetch an authorization code from the eSTS. There are cases where this may not work:
   * - Any browser using a form of Intelligent Tracking Prevention
   * - If there is not an established session with the service
   *
   * In these cases, the request must be done inside a popup or full frame redirect.
   *
   * For the cases where interaction is required, you cannot send a request with prompt=none.
   *
   * If your refresh token has expired, you can use this function to fetch a new set of tokens silently as long as
   * you session on the server still exists.
   * @param request {@link SsoSilentRequest}
   *
   * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
   */
  ssoSilent(e) {
    return this.controller.ssoSilent(e);
  }
  /**
   * Gets the token cache for the application.
   */
  getTokenCache() {
    return this.controller.getTokenCache();
  }
  /**
   * Returns the logger instance
   */
  getLogger() {
    return this.controller.getLogger();
  }
  /**
   * Replaces the default logger set in configurations with new Logger with new configurations
   * @param logger Logger instance
   */
  setLogger(e) {
    this.controller.setLogger(e);
  }
  /**
   * Sets the account to use as the active account. If no account is passed to the acquireToken APIs, then MSAL will use this active account.
   * @param account
   */
  setActiveAccount(e) {
    this.controller.setActiveAccount(e);
  }
  /**
   * Gets the currently active account
   */
  getActiveAccount() {
    return this.controller.getActiveAccount();
  }
  /**
   * Called by wrapper libraries (Angular & React) to set SKU and Version passed down to telemetry, logger, etc.
   * @param sku
   * @param version
   */
  initializeWrapperLibrary(e, t) {
    return this.controller.initializeWrapperLibrary(e, t);
  }
  /**
   * Sets navigation client
   * @param navigationClient
   */
  setNavigationClient(e) {
    this.controller.setNavigationClient(e);
  }
  /**
   * Returns the configuration object
   * @internal
   */
  getConfiguration() {
    return this.controller.getConfiguration();
  }
  /**
   * Hydrates cache with the tokens and account in the AuthenticationResult object
   * @param result
   * @param request - The request object that was used to obtain the AuthenticationResult
   * @returns
   */
  async hydrateCache(e, t) {
    return this.controller.hydrateCache(e, t);
  }
  /**
   * Clears tokens and account from the browser cache.
   * @param logoutRequest
   */
  clearCache(e) {
    return this.controller.clearCache(e);
  }
}
export {
  U as AccountEntity,
  b as ApiId,
  O as AuthError,
  k as AuthenticationScheme,
  tn as AzureCloudInstance,
  Ve as BrowserAuthError,
  q as BrowserCacheLocation,
  kn as BrowserConfigurationAuthError,
  z as CacheLookupPolicy,
  gt as ClientAuthError,
  sn as ClientConfigurationError,
  Gt as DEFAULT_IFRAME_TIMEOUT_MS,
  Zs as EventHandler,
  I as EventType,
  X as InteractionRequiredAuthError,
  y as InteractionType,
  si as JsonWebTokenTypes,
  Bs as LocalStorage,
  N as LogLevel,
  pe as Logger,
  qt as MemoryStorage,
  ut as NavigationClient,
  De as OIDC_DEFAULT_SCOPES,
  l as PerformanceEvents,
  D as PromptValue,
  le as ProtocolMode,
  ni as PublicClientApplication,
  me as ServerError,
  $e as ServerResponseType,
  Fs as SessionStorage,
  j as StringUtils,
  es as StubPerformanceClient,
  w as UrlString,
  Le as version
};
