{"name": "@crm/contact-timeline-control", "version": "1.0.0", "description": "Contact Timeline PCF Control for Dynamics 365", "main": "./dist/index.js", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> out", "rebuild": "npm run clean && npm run build", "type-check": "tsc --noEmit"}, "dependencies": {"react": "16.14.0", "react-dom": "16.14.0"}, "devDependencies": {"@types/powerapps-component-framework": "^1.3.4", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "rimraf": "^5.0.5", "typescript": "^4.9.5"}}