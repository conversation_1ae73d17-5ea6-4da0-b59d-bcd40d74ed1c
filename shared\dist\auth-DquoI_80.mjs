var yt = Object.defineProperty;
var gt = (r, e, t) => e in r ? yt(r, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : r[e] = t;
var b = (r, e, t) => gt(r, typeof e != "symbol" ? e + "" : e, t);
import { l as g } from "./logger-jecF8wz6.mjs";
import { useState as wt, useEffect as Et, useCallback as _ } from "react";
function qe(r, e) {
  return function() {
    return r.apply(e, arguments);
  };
}
const { toString: At } = Object.prototype, { getPrototypeOf: Te } = Object, { iterator: ne, toStringTag: Ve } = Symbol, se = /* @__PURE__ */ ((r) => (e) => {
  const t = At.call(e);
  return r[t] || (r[t] = t.slice(8, -1).toLowerCase());
})(/* @__PURE__ */ Object.create(null)), U = (r) => (r = r.toLowerCase(), (e) => se(e) === r), ie = (r) => (e) => typeof e === r, { isArray: q } = Array, W = ie("undefined");
function J(r) {
  return r !== null && !W(r) && r.constructor !== null && !W(r.constructor) && x(r.constructor.isBuffer) && r.constructor.isBuffer(r);
}
const He = U("ArrayBuffer");
function St(r) {
  let e;
  return typeof ArrayBuffer < "u" && ArrayBuffer.isView ? e = ArrayBuffer.isView(r) : e = r && r.buffer && He(r.buffer), e;
}
const bt = ie("string"), x = ie("function"), We = ie("number"), X = (r) => r !== null && typeof r == "object", Rt = (r) => r === !0 || r === !1, Y = (r) => {
  if (se(r) !== "object")
    return !1;
  const e = Te(r);
  return (e === null || e === Object.prototype || Object.getPrototypeOf(e) === null) && !(Ve in r) && !(ne in r);
}, Tt = (r) => {
  if (!X(r) || J(r))
    return !1;
  try {
    return Object.keys(r).length === 0 && Object.getPrototypeOf(r) === Object.prototype;
  } catch {
    return !1;
  }
}, Ct = U("Date"), Ot = U("File"), xt = U("Blob"), kt = U("FileList"), Lt = (r) => X(r) && x(r.pipe), Ut = (r) => {
  let e;
  return r && (typeof FormData == "function" && r instanceof FormData || x(r.append) && ((e = se(r)) === "formdata" || // detect form-data instance
  e === "object" && x(r.toString) && r.toString() === "[object FormData]"));
}, It = U("URLSearchParams"), [Nt, _t, Pt, Dt] = ["ReadableStream", "Request", "Response", "Headers"].map(U), Ft = (r) => r.trim ? r.trim() : r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function G(r, e, { allOwnKeys: t = !1 } = {}) {
  if (r === null || typeof r > "u")
    return;
  let n, s;
  if (typeof r != "object" && (r = [r]), q(r))
    for (n = 0, s = r.length; n < s; n++)
      e.call(null, r[n], n, r);
  else {
    if (J(r))
      return;
    const i = t ? Object.getOwnPropertyNames(r) : Object.keys(r), o = i.length;
    let c;
    for (n = 0; n < o; n++)
      c = i[n], e.call(null, r[c], c, r);
  }
}
function Je(r, e) {
  if (J(r))
    return null;
  e = e.toLowerCase();
  const t = Object.keys(r);
  let n = t.length, s;
  for (; n-- > 0; )
    if (s = t[n], e === s.toLowerCase())
      return s;
  return null;
}
const M = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : global, Xe = (r) => !W(r) && r !== M;
function me() {
  const { caseless: r } = Xe(this) && this || {}, e = {}, t = (n, s) => {
    const i = r && Je(e, s) || s;
    Y(e[i]) && Y(n) ? e[i] = me(e[i], n) : Y(n) ? e[i] = me({}, n) : q(n) ? e[i] = n.slice() : e[i] = n;
  };
  for (let n = 0, s = arguments.length; n < s; n++)
    arguments[n] && G(arguments[n], t);
  return e;
}
const vt = (r, e, t, { allOwnKeys: n } = {}) => (G(e, (s, i) => {
  t && x(s) ? r[i] = qe(s, t) : r[i] = s;
}, { allOwnKeys: n }), r), Bt = (r) => (r.charCodeAt(0) === 65279 && (r = r.slice(1)), r), Mt = (r, e, t, n) => {
  r.prototype = Object.create(e.prototype, n), r.prototype.constructor = r, Object.defineProperty(r, "super", {
    value: e.prototype
  }), t && Object.assign(r.prototype, t);
}, $t = (r, e, t, n) => {
  let s, i, o;
  const c = {};
  if (e = e || {}, r == null) return e;
  do {
    for (s = Object.getOwnPropertyNames(r), i = s.length; i-- > 0; )
      o = s[i], (!n || n(o, r, e)) && !c[o] && (e[o] = r[o], c[o] = !0);
    r = t !== !1 && Te(r);
  } while (r && (!t || t(r, e)) && r !== Object.prototype);
  return e;
}, zt = (r, e, t) => {
  r = String(r), (t === void 0 || t > r.length) && (t = r.length), t -= e.length;
  const n = r.indexOf(e, t);
  return n !== -1 && n === t;
}, jt = (r) => {
  if (!r) return null;
  if (q(r)) return r;
  let e = r.length;
  if (!We(e)) return null;
  const t = new Array(e);
  for (; e-- > 0; )
    t[e] = r[e];
  return t;
}, qt = /* @__PURE__ */ ((r) => (e) => r && e instanceof r)(typeof Uint8Array < "u" && Te(Uint8Array)), Vt = (r, e) => {
  const n = (r && r[ne]).call(r);
  let s;
  for (; (s = n.next()) && !s.done; ) {
    const i = s.value;
    e.call(r, i[0], i[1]);
  }
}, Ht = (r, e) => {
  let t;
  const n = [];
  for (; (t = r.exec(e)) !== null; )
    n.push(t);
  return n;
}, Wt = U("HTMLFormElement"), Jt = (r) => r.toLowerCase().replace(
  /[-_\s]([a-z\d])(\w*)/g,
  function(t, n, s) {
    return n.toUpperCase() + s;
  }
), Ue = (({ hasOwnProperty: r }) => (e, t) => r.call(e, t))(Object.prototype), Xt = U("RegExp"), Ge = (r, e) => {
  const t = Object.getOwnPropertyDescriptors(r), n = {};
  G(t, (s, i) => {
    let o;
    (o = e(s, i, r)) !== !1 && (n[i] = o || s);
  }), Object.defineProperties(r, n);
}, Gt = (r) => {
  Ge(r, (e, t) => {
    if (x(r) && ["arguments", "caller", "callee"].indexOf(t) !== -1)
      return !1;
    const n = r[t];
    if (x(n)) {
      if (e.enumerable = !1, "writable" in e) {
        e.writable = !1;
        return;
      }
      e.set || (e.set = () => {
        throw Error("Can not rewrite read-only method '" + t + "'");
      });
    }
  });
}, Kt = (r, e) => {
  const t = {}, n = (s) => {
    s.forEach((i) => {
      t[i] = !0;
    });
  };
  return q(r) ? n(r) : n(String(r).split(e)), t;
}, Qt = () => {
}, Yt = (r, e) => r != null && Number.isFinite(r = +r) ? r : e;
function Zt(r) {
  return !!(r && x(r.append) && r[Ve] === "FormData" && r[ne]);
}
const er = (r) => {
  const e = new Array(10), t = (n, s) => {
    if (X(n)) {
      if (e.indexOf(n) >= 0)
        return;
      if (J(n))
        return n;
      if (!("toJSON" in n)) {
        e[s] = n;
        const i = q(n) ? [] : {};
        return G(n, (o, c) => {
          const d = t(o, s + 1);
          !W(d) && (i[c] = d);
        }), e[s] = void 0, i;
      }
    }
    return n;
  };
  return t(r, 0);
}, tr = U("AsyncFunction"), rr = (r) => r && (X(r) || x(r)) && x(r.then) && x(r.catch), Ke = ((r, e) => r ? setImmediate : e ? ((t, n) => (M.addEventListener("message", ({ source: s, data: i }) => {
  s === M && i === t && n.length && n.shift()();
}, !1), (s) => {
  n.push(s), M.postMessage(t, "*");
}))(`axios@${Math.random()}`, []) : (t) => setTimeout(t))(
  typeof setImmediate == "function",
  x(M.postMessage)
), nr = typeof queueMicrotask < "u" ? queueMicrotask.bind(M) : typeof process < "u" && process.nextTick || Ke, sr = (r) => r != null && x(r[ne]), a = {
  isArray: q,
  isArrayBuffer: He,
  isBuffer: J,
  isFormData: Ut,
  isArrayBufferView: St,
  isString: bt,
  isNumber: We,
  isBoolean: Rt,
  isObject: X,
  isPlainObject: Y,
  isEmptyObject: Tt,
  isReadableStream: Nt,
  isRequest: _t,
  isResponse: Pt,
  isHeaders: Dt,
  isUndefined: W,
  isDate: Ct,
  isFile: Ot,
  isBlob: xt,
  isRegExp: Xt,
  isFunction: x,
  isStream: Lt,
  isURLSearchParams: It,
  isTypedArray: qt,
  isFileList: kt,
  forEach: G,
  merge: me,
  extend: vt,
  trim: Ft,
  stripBOM: Bt,
  inherits: Mt,
  toFlatObject: $t,
  kindOf: se,
  kindOfTest: U,
  endsWith: zt,
  toArray: jt,
  forEachEntry: Vt,
  matchAll: Ht,
  isHTMLForm: Wt,
  hasOwnProperty: Ue,
  hasOwnProp: Ue,
  // an alias to avoid ESLint no-prototype-builtins detection
  reduceDescriptors: Ge,
  freezeMethods: Gt,
  toObjectSet: Kt,
  toCamelCase: Jt,
  noop: Qt,
  toFiniteNumber: Yt,
  findKey: Je,
  global: M,
  isContextDefined: Xe,
  isSpecCompliantForm: Zt,
  toJSONObject: er,
  isAsyncFn: tr,
  isThenable: rr,
  setImmediate: Ke,
  asap: nr,
  isIterable: sr
};
function y(r, e, t, n, s) {
  Error.call(this), Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this.stack = new Error().stack, this.message = r, this.name = "AxiosError", e && (this.code = e), t && (this.config = t), n && (this.request = n), s && (this.response = s, this.status = s.status ? s.status : null);
}
a.inherits(y, Error, {
  toJSON: function() {
    return {
      // Standard
      message: this.message,
      name: this.name,
      // Microsoft
      description: this.description,
      number: this.number,
      // Mozilla
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      // Axios
      config: a.toJSONObject(this.config),
      code: this.code,
      status: this.status
    };
  }
});
const Qe = y.prototype, Ye = {};
[
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION",
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
  // eslint-disable-next-line func-names
].forEach((r) => {
  Ye[r] = { value: r };
});
Object.defineProperties(y, Ye);
Object.defineProperty(Qe, "isAxiosError", { value: !0 });
y.from = (r, e, t, n, s, i) => {
  const o = Object.create(Qe);
  return a.toFlatObject(r, o, function(d) {
    return d !== Error.prototype;
  }, (c) => c !== "isAxiosError"), y.call(o, r.message, e, t, n, s), o.cause = r, o.name = r.name, i && Object.assign(o, i), o;
};
const ir = null;
function ye(r) {
  return a.isPlainObject(r) || a.isArray(r);
}
function Ze(r) {
  return a.endsWith(r, "[]") ? r.slice(0, -2) : r;
}
function Ie(r, e, t) {
  return r ? r.concat(e).map(function(s, i) {
    return s = Ze(s), !t && i ? "[" + s + "]" : s;
  }).join(t ? "." : "") : e;
}
function or(r) {
  return a.isArray(r) && !r.some(ye);
}
const ar = a.toFlatObject(a, {}, null, function(e) {
  return /^is[A-Z]/.test(e);
});
function oe(r, e, t) {
  if (!a.isObject(r))
    throw new TypeError("target must be an object");
  e = e || new FormData(), t = a.toFlatObject(t, {
    metaTokens: !0,
    dots: !1,
    indexes: !1
  }, !1, function(m, p) {
    return !a.isUndefined(p[m]);
  });
  const n = t.metaTokens, s = t.visitor || l, i = t.dots, o = t.indexes, d = (t.Blob || typeof Blob < "u" && Blob) && a.isSpecCompliantForm(e);
  if (!a.isFunction(s))
    throw new TypeError("visitor must be a function");
  function u(h) {
    if (h === null) return "";
    if (a.isDate(h))
      return h.toISOString();
    if (a.isBoolean(h))
      return h.toString();
    if (!d && a.isBlob(h))
      throw new y("Blob is not supported. Use a Buffer instead.");
    return a.isArrayBuffer(h) || a.isTypedArray(h) ? d && typeof Blob == "function" ? new Blob([h]) : Buffer.from(h) : h;
  }
  function l(h, m, p) {
    let A = h;
    if (h && !p && typeof h == "object") {
      if (a.endsWith(m, "{}"))
        m = n ? m : m.slice(0, -2), h = JSON.stringify(h);
      else if (a.isArray(h) && or(h) || (a.isFileList(h) || a.endsWith(m, "[]")) && (A = a.toArray(h)))
        return m = Ze(m), A.forEach(function(T, N) {
          !(a.isUndefined(T) || T === null) && e.append(
            // eslint-disable-next-line no-nested-ternary
            o === !0 ? Ie([m], N, i) : o === null ? m : m + "[]",
            u(T)
          );
        }), !1;
    }
    return ye(h) ? !0 : (e.append(Ie(p, m, i), u(h)), !1);
  }
  const f = [], w = Object.assign(ar, {
    defaultVisitor: l,
    convertValue: u,
    isVisitable: ye
  });
  function E(h, m) {
    if (!a.isUndefined(h)) {
      if (f.indexOf(h) !== -1)
        throw Error("Circular reference detected in " + m.join("."));
      f.push(h), a.forEach(h, function(A, R) {
        (!(a.isUndefined(A) || A === null) && s.call(
          e,
          A,
          a.isString(R) ? R.trim() : R,
          m,
          w
        )) === !0 && E(A, m ? m.concat(R) : [R]);
      }), f.pop();
    }
  }
  if (!a.isObject(r))
    throw new TypeError("data must be an object");
  return E(r), e;
}
function Ne(r) {
  const e = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\0"
  };
  return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g, function(n) {
    return e[n];
  });
}
function Ce(r, e) {
  this._pairs = [], r && oe(r, this, e);
}
const et = Ce.prototype;
et.append = function(e, t) {
  this._pairs.push([e, t]);
};
et.toString = function(e) {
  const t = e ? function(n) {
    return e.call(this, n, Ne);
  } : Ne;
  return this._pairs.map(function(s) {
    return t(s[0]) + "=" + t(s[1]);
  }, "").join("&");
};
function cr(r) {
  return encodeURIComponent(r).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function tt(r, e, t) {
  if (!e)
    return r;
  const n = t && t.encode || cr;
  a.isFunction(t) && (t = {
    serialize: t
  });
  const s = t && t.serialize;
  let i;
  if (s ? i = s(e, t) : i = a.isURLSearchParams(e) ? e.toString() : new Ce(e, t).toString(n), i) {
    const o = r.indexOf("#");
    o !== -1 && (r = r.slice(0, o)), r += (r.indexOf("?") === -1 ? "?" : "&") + i;
  }
  return r;
}
class _e {
  constructor() {
    this.handlers = [];
  }
  /**
   * Add a new interceptor to the stack
   *
   * @param {Function} fulfilled The function to handle `then` for a `Promise`
   * @param {Function} rejected The function to handle `reject` for a `Promise`
   *
   * @return {Number} An ID used to remove interceptor later
   */
  use(e, t, n) {
    return this.handlers.push({
      fulfilled: e,
      rejected: t,
      synchronous: n ? n.synchronous : !1,
      runWhen: n ? n.runWhen : null
    }), this.handlers.length - 1;
  }
  /**
   * Remove an interceptor from the stack
   *
   * @param {Number} id The ID that was returned by `use`
   *
   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
   */
  eject(e) {
    this.handlers[e] && (this.handlers[e] = null);
  }
  /**
   * Clear all interceptors from the stack
   *
   * @returns {void}
   */
  clear() {
    this.handlers && (this.handlers = []);
  }
  /**
   * Iterate over all the registered interceptors
   *
   * This method is particularly useful for skipping over any
   * interceptors that may have become `null` calling `eject`.
   *
   * @param {Function} fn The function to call for each interceptor
   *
   * @returns {void}
   */
  forEach(e) {
    a.forEach(this.handlers, function(n) {
      n !== null && e(n);
    });
  }
}
const rt = {
  silentJSONParsing: !0,
  forcedJSONParsing: !0,
  clarifyTimeoutError: !1
}, ur = typeof URLSearchParams < "u" ? URLSearchParams : Ce, lr = typeof FormData < "u" ? FormData : null, hr = typeof Blob < "u" ? Blob : null, dr = {
  isBrowser: !0,
  classes: {
    URLSearchParams: ur,
    FormData: lr,
    Blob: hr
  },
  protocols: ["http", "https", "file", "blob", "url", "data"]
}, Oe = typeof window < "u" && typeof document < "u", ge = typeof navigator == "object" && navigator || void 0, fr = Oe && (!ge || ["ReactNative", "NativeScript", "NS"].indexOf(ge.product) < 0), pr = typeof WorkerGlobalScope < "u" && // eslint-disable-next-line no-undef
self instanceof WorkerGlobalScope && typeof self.importScripts == "function", mr = Oe && window.location.href || "http://localhost", yr = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  hasBrowserEnv: Oe,
  hasStandardBrowserEnv: fr,
  hasStandardBrowserWebWorkerEnv: pr,
  navigator: ge,
  origin: mr
}, Symbol.toStringTag, { value: "Module" })), C = {
  ...yr,
  ...dr
};
function gr(r, e) {
  return oe(r, new C.classes.URLSearchParams(), {
    visitor: function(t, n, s, i) {
      return C.isNode && a.isBuffer(t) ? (this.append(n, t.toString("base64")), !1) : i.defaultVisitor.apply(this, arguments);
    },
    ...e
  });
}
function wr(r) {
  return a.matchAll(/\w+|\[(\w*)]/g, r).map((e) => e[0] === "[]" ? "" : e[1] || e[0]);
}
function Er(r) {
  const e = {}, t = Object.keys(r);
  let n;
  const s = t.length;
  let i;
  for (n = 0; n < s; n++)
    i = t[n], e[i] = r[i];
  return e;
}
function nt(r) {
  function e(t, n, s, i) {
    let o = t[i++];
    if (o === "__proto__") return !0;
    const c = Number.isFinite(+o), d = i >= t.length;
    return o = !o && a.isArray(s) ? s.length : o, d ? (a.hasOwnProp(s, o) ? s[o] = [s[o], n] : s[o] = n, !c) : ((!s[o] || !a.isObject(s[o])) && (s[o] = []), e(t, n, s[o], i) && a.isArray(s[o]) && (s[o] = Er(s[o])), !c);
  }
  if (a.isFormData(r) && a.isFunction(r.entries)) {
    const t = {};
    return a.forEachEntry(r, (n, s) => {
      e(wr(n), s, t, 0);
    }), t;
  }
  return null;
}
function Ar(r, e, t) {
  if (a.isString(r))
    try {
      return (e || JSON.parse)(r), a.trim(r);
    } catch (n) {
      if (n.name !== "SyntaxError")
        throw n;
    }
  return (t || JSON.stringify)(r);
}
const K = {
  transitional: rt,
  adapter: ["xhr", "http", "fetch"],
  transformRequest: [function(e, t) {
    const n = t.getContentType() || "", s = n.indexOf("application/json") > -1, i = a.isObject(e);
    if (i && a.isHTMLForm(e) && (e = new FormData(e)), a.isFormData(e))
      return s ? JSON.stringify(nt(e)) : e;
    if (a.isArrayBuffer(e) || a.isBuffer(e) || a.isStream(e) || a.isFile(e) || a.isBlob(e) || a.isReadableStream(e))
      return e;
    if (a.isArrayBufferView(e))
      return e.buffer;
    if (a.isURLSearchParams(e))
      return t.setContentType("application/x-www-form-urlencoded;charset=utf-8", !1), e.toString();
    let c;
    if (i) {
      if (n.indexOf("application/x-www-form-urlencoded") > -1)
        return gr(e, this.formSerializer).toString();
      if ((c = a.isFileList(e)) || n.indexOf("multipart/form-data") > -1) {
        const d = this.env && this.env.FormData;
        return oe(
          c ? { "files[]": e } : e,
          d && new d(),
          this.formSerializer
        );
      }
    }
    return i || s ? (t.setContentType("application/json", !1), Ar(e)) : e;
  }],
  transformResponse: [function(e) {
    const t = this.transitional || K.transitional, n = t && t.forcedJSONParsing, s = this.responseType === "json";
    if (a.isResponse(e) || a.isReadableStream(e))
      return e;
    if (e && a.isString(e) && (n && !this.responseType || s)) {
      const o = !(t && t.silentJSONParsing) && s;
      try {
        return JSON.parse(e);
      } catch (c) {
        if (o)
          throw c.name === "SyntaxError" ? y.from(c, y.ERR_BAD_RESPONSE, this, null, this.response) : c;
      }
    }
    return e;
  }],
  /**
   * A timeout in milliseconds to abort a request. If set to 0 (default) a
   * timeout is not created.
   */
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: C.classes.FormData,
    Blob: C.classes.Blob
  },
  validateStatus: function(e) {
    return e >= 200 && e < 300;
  },
  headers: {
    common: {
      Accept: "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
a.forEach(["delete", "get", "head", "post", "put", "patch"], (r) => {
  K.headers[r] = {};
});
const Sr = a.toObjectSet([
  "age",
  "authorization",
  "content-length",
  "content-type",
  "etag",
  "expires",
  "from",
  "host",
  "if-modified-since",
  "if-unmodified-since",
  "last-modified",
  "location",
  "max-forwards",
  "proxy-authorization",
  "referer",
  "retry-after",
  "user-agent"
]), br = (r) => {
  const e = {};
  let t, n, s;
  return r && r.split(`
`).forEach(function(o) {
    s = o.indexOf(":"), t = o.substring(0, s).trim().toLowerCase(), n = o.substring(s + 1).trim(), !(!t || e[t] && Sr[t]) && (t === "set-cookie" ? e[t] ? e[t].push(n) : e[t] = [n] : e[t] = e[t] ? e[t] + ", " + n : n);
  }), e;
}, Pe = Symbol("internals");
function H(r) {
  return r && String(r).trim().toLowerCase();
}
function Z(r) {
  return r === !1 || r == null ? r : a.isArray(r) ? r.map(Z) : String(r);
}
function Rr(r) {
  const e = /* @__PURE__ */ Object.create(null), t = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
  let n;
  for (; n = t.exec(r); )
    e[n[1]] = n[2];
  return e;
}
const Tr = (r) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());
function le(r, e, t, n, s) {
  if (a.isFunction(n))
    return n.call(this, e, t);
  if (s && (e = t), !!a.isString(e)) {
    if (a.isString(n))
      return e.indexOf(n) !== -1;
    if (a.isRegExp(n))
      return n.test(e);
  }
}
function Cr(r) {
  return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (e, t, n) => t.toUpperCase() + n);
}
function Or(r, e) {
  const t = a.toCamelCase(" " + e);
  ["get", "set", "has"].forEach((n) => {
    Object.defineProperty(r, n + t, {
      value: function(s, i, o) {
        return this[n].call(this, e, s, i, o);
      },
      configurable: !0
    });
  });
}
let k = class {
  constructor(e) {
    e && this.set(e);
  }
  set(e, t, n) {
    const s = this;
    function i(c, d, u) {
      const l = H(d);
      if (!l)
        throw new Error("header name must be a non-empty string");
      const f = a.findKey(s, l);
      (!f || s[f] === void 0 || u === !0 || u === void 0 && s[f] !== !1) && (s[f || d] = Z(c));
    }
    const o = (c, d) => a.forEach(c, (u, l) => i(u, l, d));
    if (a.isPlainObject(e) || e instanceof this.constructor)
      o(e, t);
    else if (a.isString(e) && (e = e.trim()) && !Tr(e))
      o(br(e), t);
    else if (a.isObject(e) && a.isIterable(e)) {
      let c = {}, d, u;
      for (const l of e) {
        if (!a.isArray(l))
          throw TypeError("Object iterator must return a key-value pair");
        c[u = l[0]] = (d = c[u]) ? a.isArray(d) ? [...d, l[1]] : [d, l[1]] : l[1];
      }
      o(c, t);
    } else
      e != null && i(t, e, n);
    return this;
  }
  get(e, t) {
    if (e = H(e), e) {
      const n = a.findKey(this, e);
      if (n) {
        const s = this[n];
        if (!t)
          return s;
        if (t === !0)
          return Rr(s);
        if (a.isFunction(t))
          return t.call(this, s, n);
        if (a.isRegExp(t))
          return t.exec(s);
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(e, t) {
    if (e = H(e), e) {
      const n = a.findKey(this, e);
      return !!(n && this[n] !== void 0 && (!t || le(this, this[n], n, t)));
    }
    return !1;
  }
  delete(e, t) {
    const n = this;
    let s = !1;
    function i(o) {
      if (o = H(o), o) {
        const c = a.findKey(n, o);
        c && (!t || le(n, n[c], c, t)) && (delete n[c], s = !0);
      }
    }
    return a.isArray(e) ? e.forEach(i) : i(e), s;
  }
  clear(e) {
    const t = Object.keys(this);
    let n = t.length, s = !1;
    for (; n--; ) {
      const i = t[n];
      (!e || le(this, this[i], i, e, !0)) && (delete this[i], s = !0);
    }
    return s;
  }
  normalize(e) {
    const t = this, n = {};
    return a.forEach(this, (s, i) => {
      const o = a.findKey(n, i);
      if (o) {
        t[o] = Z(s), delete t[i];
        return;
      }
      const c = e ? Cr(i) : String(i).trim();
      c !== i && delete t[i], t[c] = Z(s), n[c] = !0;
    }), this;
  }
  concat(...e) {
    return this.constructor.concat(this, ...e);
  }
  toJSON(e) {
    const t = /* @__PURE__ */ Object.create(null);
    return a.forEach(this, (n, s) => {
      n != null && n !== !1 && (t[s] = e && a.isArray(n) ? n.join(", ") : n);
    }), t;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([e, t]) => e + ": " + t).join(`
`);
  }
  getSetCookie() {
    return this.get("set-cookie") || [];
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(e) {
    return e instanceof this ? e : new this(e);
  }
  static concat(e, ...t) {
    const n = new this(e);
    return t.forEach((s) => n.set(s)), n;
  }
  static accessor(e) {
    const n = (this[Pe] = this[Pe] = {
      accessors: {}
    }).accessors, s = this.prototype;
    function i(o) {
      const c = H(o);
      n[c] || (Or(s, o), n[c] = !0);
    }
    return a.isArray(e) ? e.forEach(i) : i(e), this;
  }
};
k.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
a.reduceDescriptors(k.prototype, ({ value: r }, e) => {
  let t = e[0].toUpperCase() + e.slice(1);
  return {
    get: () => r,
    set(n) {
      this[t] = n;
    }
  };
});
a.freezeMethods(k);
function he(r, e) {
  const t = this || K, n = e || t, s = k.from(n.headers);
  let i = n.data;
  return a.forEach(r, function(c) {
    i = c.call(t, i, s.normalize(), e ? e.status : void 0);
  }), s.normalize(), i;
}
function st(r) {
  return !!(r && r.__CANCEL__);
}
function V(r, e, t) {
  y.call(this, r ?? "canceled", y.ERR_CANCELED, e, t), this.name = "CanceledError";
}
a.inherits(V, y, {
  __CANCEL__: !0
});
function it(r, e, t) {
  const n = t.config.validateStatus;
  !t.status || !n || n(t.status) ? r(t) : e(new y(
    "Request failed with status code " + t.status,
    [y.ERR_BAD_REQUEST, y.ERR_BAD_RESPONSE][Math.floor(t.status / 100) - 4],
    t.config,
    t.request,
    t
  ));
}
function xr(r) {
  const e = /^([-+\w]{1,25})(:?\/\/|:)/.exec(r);
  return e && e[1] || "";
}
function kr(r, e) {
  r = r || 10;
  const t = new Array(r), n = new Array(r);
  let s = 0, i = 0, o;
  return e = e !== void 0 ? e : 1e3, function(d) {
    const u = Date.now(), l = n[i];
    o || (o = u), t[s] = d, n[s] = u;
    let f = i, w = 0;
    for (; f !== s; )
      w += t[f++], f = f % r;
    if (s = (s + 1) % r, s === i && (i = (i + 1) % r), u - o < e)
      return;
    const E = l && u - l;
    return E ? Math.round(w * 1e3 / E) : void 0;
  };
}
function Lr(r, e) {
  let t = 0, n = 1e3 / e, s, i;
  const o = (u, l = Date.now()) => {
    t = l, s = null, i && (clearTimeout(i), i = null), r(...u);
  };
  return [(...u) => {
    const l = Date.now(), f = l - t;
    f >= n ? o(u, l) : (s = u, i || (i = setTimeout(() => {
      i = null, o(s);
    }, n - f)));
  }, () => s && o(s)];
}
const te = (r, e, t = 3) => {
  let n = 0;
  const s = kr(50, 250);
  return Lr((i) => {
    const o = i.loaded, c = i.lengthComputable ? i.total : void 0, d = o - n, u = s(d), l = o <= c;
    n = o;
    const f = {
      loaded: o,
      total: c,
      progress: c ? o / c : void 0,
      bytes: d,
      rate: u || void 0,
      estimated: u && c && l ? (c - o) / u : void 0,
      event: i,
      lengthComputable: c != null,
      [e ? "download" : "upload"]: !0
    };
    r(f);
  }, t);
}, De = (r, e) => {
  const t = r != null;
  return [(n) => e[0]({
    lengthComputable: t,
    total: r,
    loaded: n
  }), e[1]];
}, Fe = (r) => (...e) => a.asap(() => r(...e)), Ur = C.hasStandardBrowserEnv ? /* @__PURE__ */ ((r, e) => (t) => (t = new URL(t, C.origin), r.protocol === t.protocol && r.host === t.host && (e || r.port === t.port)))(
  new URL(C.origin),
  C.navigator && /(msie|trident)/i.test(C.navigator.userAgent)
) : () => !0, Ir = C.hasStandardBrowserEnv ? (
  // Standard browser envs support document.cookie
  {
    write(r, e, t, n, s, i) {
      const o = [r + "=" + encodeURIComponent(e)];
      a.isNumber(t) && o.push("expires=" + new Date(t).toGMTString()), a.isString(n) && o.push("path=" + n), a.isString(s) && o.push("domain=" + s), i === !0 && o.push("secure"), document.cookie = o.join("; ");
    },
    read(r) {
      const e = document.cookie.match(new RegExp("(^|;\\s*)(" + r + ")=([^;]*)"));
      return e ? decodeURIComponent(e[3]) : null;
    },
    remove(r) {
      this.write(r, "", Date.now() - 864e5);
    }
  }
) : (
  // Non-standard browser env (web workers, react-native) lack needed support.
  {
    write() {
    },
    read() {
      return null;
    },
    remove() {
    }
  }
);
function Nr(r) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(r);
}
function _r(r, e) {
  return e ? r.replace(/\/?\/$/, "") + "/" + e.replace(/^\/+/, "") : r;
}
function ot(r, e, t) {
  let n = !Nr(e);
  return r && (n || t == !1) ? _r(r, e) : e;
}
const ve = (r) => r instanceof k ? { ...r } : r;
function z(r, e) {
  e = e || {};
  const t = {};
  function n(u, l, f, w) {
    return a.isPlainObject(u) && a.isPlainObject(l) ? a.merge.call({ caseless: w }, u, l) : a.isPlainObject(l) ? a.merge({}, l) : a.isArray(l) ? l.slice() : l;
  }
  function s(u, l, f, w) {
    if (a.isUndefined(l)) {
      if (!a.isUndefined(u))
        return n(void 0, u, f, w);
    } else return n(u, l, f, w);
  }
  function i(u, l) {
    if (!a.isUndefined(l))
      return n(void 0, l);
  }
  function o(u, l) {
    if (a.isUndefined(l)) {
      if (!a.isUndefined(u))
        return n(void 0, u);
    } else return n(void 0, l);
  }
  function c(u, l, f) {
    if (f in e)
      return n(u, l);
    if (f in r)
      return n(void 0, u);
  }
  const d = {
    url: i,
    method: i,
    data: i,
    baseURL: o,
    transformRequest: o,
    transformResponse: o,
    paramsSerializer: o,
    timeout: o,
    timeoutMessage: o,
    withCredentials: o,
    withXSRFToken: o,
    adapter: o,
    responseType: o,
    xsrfCookieName: o,
    xsrfHeaderName: o,
    onUploadProgress: o,
    onDownloadProgress: o,
    decompress: o,
    maxContentLength: o,
    maxBodyLength: o,
    beforeRedirect: o,
    transport: o,
    httpAgent: o,
    httpsAgent: o,
    cancelToken: o,
    socketPath: o,
    responseEncoding: o,
    validateStatus: c,
    headers: (u, l, f) => s(ve(u), ve(l), f, !0)
  };
  return a.forEach(Object.keys({ ...r, ...e }), function(l) {
    const f = d[l] || s, w = f(r[l], e[l], l);
    a.isUndefined(w) && f !== c || (t[l] = w);
  }), t;
}
const at = (r) => {
  const e = z({}, r);
  let { data: t, withXSRFToken: n, xsrfHeaderName: s, xsrfCookieName: i, headers: o, auth: c } = e;
  e.headers = o = k.from(o), e.url = tt(ot(e.baseURL, e.url, e.allowAbsoluteUrls), r.params, r.paramsSerializer), c && o.set(
    "Authorization",
    "Basic " + btoa((c.username || "") + ":" + (c.password ? unescape(encodeURIComponent(c.password)) : ""))
  );
  let d;
  if (a.isFormData(t)) {
    if (C.hasStandardBrowserEnv || C.hasStandardBrowserWebWorkerEnv)
      o.setContentType(void 0);
    else if ((d = o.getContentType()) !== !1) {
      const [u, ...l] = d ? d.split(";").map((f) => f.trim()).filter(Boolean) : [];
      o.setContentType([u || "multipart/form-data", ...l].join("; "));
    }
  }
  if (C.hasStandardBrowserEnv && (n && a.isFunction(n) && (n = n(e)), n || n !== !1 && Ur(e.url))) {
    const u = s && i && Ir.read(i);
    u && o.set(s, u);
  }
  return e;
}, Pr = typeof XMLHttpRequest < "u", Dr = Pr && function(r) {
  return new Promise(function(t, n) {
    const s = at(r);
    let i = s.data;
    const o = k.from(s.headers).normalize();
    let { responseType: c, onUploadProgress: d, onDownloadProgress: u } = s, l, f, w, E, h;
    function m() {
      E && E(), h && h(), s.cancelToken && s.cancelToken.unsubscribe(l), s.signal && s.signal.removeEventListener("abort", l);
    }
    let p = new XMLHttpRequest();
    p.open(s.method.toUpperCase(), s.url, !0), p.timeout = s.timeout;
    function A() {
      if (!p)
        return;
      const T = k.from(
        "getAllResponseHeaders" in p && p.getAllResponseHeaders()
      ), O = {
        data: !c || c === "text" || c === "json" ? p.responseText : p.response,
        status: p.status,
        statusText: p.statusText,
        headers: T,
        config: r,
        request: p
      };
      it(function(v) {
        t(v), m();
      }, function(v) {
        n(v), m();
      }, O), p = null;
    }
    "onloadend" in p ? p.onloadend = A : p.onreadystatechange = function() {
      !p || p.readyState !== 4 || p.status === 0 && !(p.responseURL && p.responseURL.indexOf("file:") === 0) || setTimeout(A);
    }, p.onabort = function() {
      p && (n(new y("Request aborted", y.ECONNABORTED, r, p)), p = null);
    }, p.onerror = function() {
      n(new y("Network Error", y.ERR_NETWORK, r, p)), p = null;
    }, p.ontimeout = function() {
      let N = s.timeout ? "timeout of " + s.timeout + "ms exceeded" : "timeout exceeded";
      const O = s.transitional || rt;
      s.timeoutErrorMessage && (N = s.timeoutErrorMessage), n(new y(
        N,
        O.clarifyTimeoutError ? y.ETIMEDOUT : y.ECONNABORTED,
        r,
        p
      )), p = null;
    }, i === void 0 && o.setContentType(null), "setRequestHeader" in p && a.forEach(o.toJSON(), function(N, O) {
      p.setRequestHeader(O, N);
    }), a.isUndefined(s.withCredentials) || (p.withCredentials = !!s.withCredentials), c && c !== "json" && (p.responseType = s.responseType), u && ([w, h] = te(u, !0), p.addEventListener("progress", w)), d && p.upload && ([f, E] = te(d), p.upload.addEventListener("progress", f), p.upload.addEventListener("loadend", E)), (s.cancelToken || s.signal) && (l = (T) => {
      p && (n(!T || T.type ? new V(null, r, p) : T), p.abort(), p = null);
    }, s.cancelToken && s.cancelToken.subscribe(l), s.signal && (s.signal.aborted ? l() : s.signal.addEventListener("abort", l)));
    const R = xr(s.url);
    if (R && C.protocols.indexOf(R) === -1) {
      n(new y("Unsupported protocol " + R + ":", y.ERR_BAD_REQUEST, r));
      return;
    }
    p.send(i || null);
  });
}, Fr = (r, e) => {
  const { length: t } = r = r ? r.filter(Boolean) : [];
  if (e || t) {
    let n = new AbortController(), s;
    const i = function(u) {
      if (!s) {
        s = !0, c();
        const l = u instanceof Error ? u : this.reason;
        n.abort(l instanceof y ? l : new V(l instanceof Error ? l.message : l));
      }
    };
    let o = e && setTimeout(() => {
      o = null, i(new y(`timeout ${e} of ms exceeded`, y.ETIMEDOUT));
    }, e);
    const c = () => {
      r && (o && clearTimeout(o), o = null, r.forEach((u) => {
        u.unsubscribe ? u.unsubscribe(i) : u.removeEventListener("abort", i);
      }), r = null);
    };
    r.forEach((u) => u.addEventListener("abort", i));
    const { signal: d } = n;
    return d.unsubscribe = () => a.asap(c), d;
  }
}, vr = function* (r, e) {
  let t = r.byteLength;
  if (t < e) {
    yield r;
    return;
  }
  let n = 0, s;
  for (; n < t; )
    s = n + e, yield r.slice(n, s), n = s;
}, Br = async function* (r, e) {
  for await (const t of Mr(r))
    yield* vr(t, e);
}, Mr = async function* (r) {
  if (r[Symbol.asyncIterator]) {
    yield* r;
    return;
  }
  const e = r.getReader();
  try {
    for (; ; ) {
      const { done: t, value: n } = await e.read();
      if (t)
        break;
      yield n;
    }
  } finally {
    await e.cancel();
  }
}, Be = (r, e, t, n) => {
  const s = Br(r, e);
  let i = 0, o, c = (d) => {
    o || (o = !0, n && n(d));
  };
  return new ReadableStream({
    async pull(d) {
      try {
        const { done: u, value: l } = await s.next();
        if (u) {
          c(), d.close();
          return;
        }
        let f = l.byteLength;
        if (t) {
          let w = i += f;
          t(w);
        }
        d.enqueue(new Uint8Array(l));
      } catch (u) {
        throw c(u), u;
      }
    },
    cancel(d) {
      return c(d), s.return();
    }
  }, {
    highWaterMark: 2
  });
}, ae = typeof fetch == "function" && typeof Request == "function" && typeof Response == "function", ct = ae && typeof ReadableStream == "function", $r = ae && (typeof TextEncoder == "function" ? /* @__PURE__ */ ((r) => (e) => r.encode(e))(new TextEncoder()) : async (r) => new Uint8Array(await new Response(r).arrayBuffer())), ut = (r, ...e) => {
  try {
    return !!r(...e);
  } catch {
    return !1;
  }
}, zr = ct && ut(() => {
  let r = !1;
  const e = new Request(C.origin, {
    body: new ReadableStream(),
    method: "POST",
    get duplex() {
      return r = !0, "half";
    }
  }).headers.has("Content-Type");
  return r && !e;
}), Me = 64 * 1024, we = ct && ut(() => a.isReadableStream(new Response("").body)), re = {
  stream: we && ((r) => r.body)
};
ae && ((r) => {
  ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((e) => {
    !re[e] && (re[e] = a.isFunction(r[e]) ? (t) => t[e]() : (t, n) => {
      throw new y(`Response type '${e}' is not supported`, y.ERR_NOT_SUPPORT, n);
    });
  });
})(new Response());
const jr = async (r) => {
  if (r == null)
    return 0;
  if (a.isBlob(r))
    return r.size;
  if (a.isSpecCompliantForm(r))
    return (await new Request(C.origin, {
      method: "POST",
      body: r
    }).arrayBuffer()).byteLength;
  if (a.isArrayBufferView(r) || a.isArrayBuffer(r))
    return r.byteLength;
  if (a.isURLSearchParams(r) && (r = r + ""), a.isString(r))
    return (await $r(r)).byteLength;
}, qr = async (r, e) => {
  const t = a.toFiniteNumber(r.getContentLength());
  return t ?? jr(e);
}, Vr = ae && (async (r) => {
  let {
    url: e,
    method: t,
    data: n,
    signal: s,
    cancelToken: i,
    timeout: o,
    onDownloadProgress: c,
    onUploadProgress: d,
    responseType: u,
    headers: l,
    withCredentials: f = "same-origin",
    fetchOptions: w
  } = at(r);
  u = u ? (u + "").toLowerCase() : "text";
  let E = Fr([s, i && i.toAbortSignal()], o), h;
  const m = E && E.unsubscribe && (() => {
    E.unsubscribe();
  });
  let p;
  try {
    if (d && zr && t !== "get" && t !== "head" && (p = await qr(l, n)) !== 0) {
      let O = new Request(e, {
        method: "POST",
        body: n,
        duplex: "half"
      }), F;
      if (a.isFormData(n) && (F = O.headers.get("content-type")) && l.setContentType(F), O.body) {
        const [v, Q] = De(
          p,
          te(Fe(d))
        );
        n = Be(O.body, Me, v, Q);
      }
    }
    a.isString(f) || (f = f ? "include" : "omit");
    const A = "credentials" in Request.prototype;
    h = new Request(e, {
      ...w,
      signal: E,
      method: t.toUpperCase(),
      headers: l.normalize().toJSON(),
      body: n,
      duplex: "half",
      credentials: A ? f : void 0
    });
    let R = await fetch(h, w);
    const T = we && (u === "stream" || u === "response");
    if (we && (c || T && m)) {
      const O = {};
      ["status", "statusText", "headers"].forEach((Le) => {
        O[Le] = R[Le];
      });
      const F = a.toFiniteNumber(R.headers.get("content-length")), [v, Q] = c && De(
        F,
        te(Fe(c), !0)
      ) || [];
      R = new Response(
        Be(R.body, Me, v, () => {
          Q && Q(), m && m();
        }),
        O
      );
    }
    u = u || "text";
    let N = await re[a.findKey(re, u) || "text"](R, r);
    return !T && m && m(), await new Promise((O, F) => {
      it(O, F, {
        data: N,
        headers: k.from(R.headers),
        status: R.status,
        statusText: R.statusText,
        config: r,
        request: h
      });
    });
  } catch (A) {
    throw m && m(), A && A.name === "TypeError" && /Load failed|fetch/i.test(A.message) ? Object.assign(
      new y("Network Error", y.ERR_NETWORK, r, h),
      {
        cause: A.cause || A
      }
    ) : y.from(A, A && A.code, r, h);
  }
}), Ee = {
  http: ir,
  xhr: Dr,
  fetch: Vr
};
a.forEach(Ee, (r, e) => {
  if (r) {
    try {
      Object.defineProperty(r, "name", { value: e });
    } catch {
    }
    Object.defineProperty(r, "adapterName", { value: e });
  }
});
const $e = (r) => `- ${r}`, Hr = (r) => a.isFunction(r) || r === null || r === !1, lt = {
  getAdapter: (r) => {
    r = a.isArray(r) ? r : [r];
    const { length: e } = r;
    let t, n;
    const s = {};
    for (let i = 0; i < e; i++) {
      t = r[i];
      let o;
      if (n = t, !Hr(t) && (n = Ee[(o = String(t)).toLowerCase()], n === void 0))
        throw new y(`Unknown adapter '${o}'`);
      if (n)
        break;
      s[o || "#" + i] = n;
    }
    if (!n) {
      const i = Object.entries(s).map(
        ([c, d]) => `adapter ${c} ` + (d === !1 ? "is not supported by the environment" : "is not available in the build")
      );
      let o = e ? i.length > 1 ? `since :
` + i.map($e).join(`
`) : " " + $e(i[0]) : "as no adapter specified";
      throw new y(
        "There is no suitable adapter to dispatch the request " + o,
        "ERR_NOT_SUPPORT"
      );
    }
    return n;
  },
  adapters: Ee
};
function de(r) {
  if (r.cancelToken && r.cancelToken.throwIfRequested(), r.signal && r.signal.aborted)
    throw new V(null, r);
}
function ze(r) {
  return de(r), r.headers = k.from(r.headers), r.data = he.call(
    r,
    r.transformRequest
  ), ["post", "put", "patch"].indexOf(r.method) !== -1 && r.headers.setContentType("application/x-www-form-urlencoded", !1), lt.getAdapter(r.adapter || K.adapter)(r).then(function(n) {
    return de(r), n.data = he.call(
      r,
      r.transformResponse,
      n
    ), n.headers = k.from(n.headers), n;
  }, function(n) {
    return st(n) || (de(r), n && n.response && (n.response.data = he.call(
      r,
      r.transformResponse,
      n.response
    ), n.response.headers = k.from(n.response.headers))), Promise.reject(n);
  });
}
const ht = "1.11.0", ce = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach((r, e) => {
  ce[r] = function(n) {
    return typeof n === r || "a" + (e < 1 ? "n " : " ") + r;
  };
});
const je = {};
ce.transitional = function(e, t, n) {
  function s(i, o) {
    return "[Axios v" + ht + "] Transitional option '" + i + "'" + o + (n ? ". " + n : "");
  }
  return (i, o, c) => {
    if (e === !1)
      throw new y(
        s(o, " has been removed" + (t ? " in " + t : "")),
        y.ERR_DEPRECATED
      );
    return t && !je[o] && (je[o] = !0, console.warn(
      s(
        o,
        " has been deprecated since v" + t + " and will be removed in the near future"
      )
    )), e ? e(i, o, c) : !0;
  };
};
ce.spelling = function(e) {
  return (t, n) => (console.warn(`${n} is likely a misspelling of ${e}`), !0);
};
function Wr(r, e, t) {
  if (typeof r != "object")
    throw new y("options must be an object", y.ERR_BAD_OPTION_VALUE);
  const n = Object.keys(r);
  let s = n.length;
  for (; s-- > 0; ) {
    const i = n[s], o = e[i];
    if (o) {
      const c = r[i], d = c === void 0 || o(c, i, r);
      if (d !== !0)
        throw new y("option " + i + " must be " + d, y.ERR_BAD_OPTION_VALUE);
      continue;
    }
    if (t !== !0)
      throw new y("Unknown option " + i, y.ERR_BAD_OPTION);
  }
}
const ee = {
  assertOptions: Wr,
  validators: ce
}, I = ee.validators;
let $ = class {
  constructor(e) {
    this.defaults = e || {}, this.interceptors = {
      request: new _e(),
      response: new _e()
    };
  }
  /**
   * Dispatch a request
   *
   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
   * @param {?Object} config
   *
   * @returns {Promise} The Promise to be fulfilled
   */
  async request(e, t) {
    try {
      return await this._request(e, t);
    } catch (n) {
      if (n instanceof Error) {
        let s = {};
        Error.captureStackTrace ? Error.captureStackTrace(s) : s = new Error();
        const i = s.stack ? s.stack.replace(/^.+\n/, "") : "";
        try {
          n.stack ? i && !String(n.stack).endsWith(i.replace(/^.+\n.+\n/, "")) && (n.stack += `
` + i) : n.stack = i;
        } catch {
        }
      }
      throw n;
    }
  }
  _request(e, t) {
    typeof e == "string" ? (t = t || {}, t.url = e) : t = e || {}, t = z(this.defaults, t);
    const { transitional: n, paramsSerializer: s, headers: i } = t;
    n !== void 0 && ee.assertOptions(n, {
      silentJSONParsing: I.transitional(I.boolean),
      forcedJSONParsing: I.transitional(I.boolean),
      clarifyTimeoutError: I.transitional(I.boolean)
    }, !1), s != null && (a.isFunction(s) ? t.paramsSerializer = {
      serialize: s
    } : ee.assertOptions(s, {
      encode: I.function,
      serialize: I.function
    }, !0)), t.allowAbsoluteUrls !== void 0 || (this.defaults.allowAbsoluteUrls !== void 0 ? t.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls : t.allowAbsoluteUrls = !0), ee.assertOptions(t, {
      baseUrl: I.spelling("baseURL"),
      withXsrfToken: I.spelling("withXSRFToken")
    }, !0), t.method = (t.method || this.defaults.method || "get").toLowerCase();
    let o = i && a.merge(
      i.common,
      i[t.method]
    );
    i && a.forEach(
      ["delete", "get", "head", "post", "put", "patch", "common"],
      (h) => {
        delete i[h];
      }
    ), t.headers = k.concat(o, i);
    const c = [];
    let d = !0;
    this.interceptors.request.forEach(function(m) {
      typeof m.runWhen == "function" && m.runWhen(t) === !1 || (d = d && m.synchronous, c.unshift(m.fulfilled, m.rejected));
    });
    const u = [];
    this.interceptors.response.forEach(function(m) {
      u.push(m.fulfilled, m.rejected);
    });
    let l, f = 0, w;
    if (!d) {
      const h = [ze.bind(this), void 0];
      for (h.unshift(...c), h.push(...u), w = h.length, l = Promise.resolve(t); f < w; )
        l = l.then(h[f++], h[f++]);
      return l;
    }
    w = c.length;
    let E = t;
    for (f = 0; f < w; ) {
      const h = c[f++], m = c[f++];
      try {
        E = h(E);
      } catch (p) {
        m.call(this, p);
        break;
      }
    }
    try {
      l = ze.call(this, E);
    } catch (h) {
      return Promise.reject(h);
    }
    for (f = 0, w = u.length; f < w; )
      l = l.then(u[f++], u[f++]);
    return l;
  }
  getUri(e) {
    e = z(this.defaults, e);
    const t = ot(e.baseURL, e.url, e.allowAbsoluteUrls);
    return tt(t, e.params, e.paramsSerializer);
  }
};
a.forEach(["delete", "get", "head", "options"], function(e) {
  $.prototype[e] = function(t, n) {
    return this.request(z(n || {}, {
      method: e,
      url: t,
      data: (n || {}).data
    }));
  };
});
a.forEach(["post", "put", "patch"], function(e) {
  function t(n) {
    return function(i, o, c) {
      return this.request(z(c || {}, {
        method: e,
        headers: n ? {
          "Content-Type": "multipart/form-data"
        } : {},
        url: i,
        data: o
      }));
    };
  }
  $.prototype[e] = t(), $.prototype[e + "Form"] = t(!0);
});
let Jr = class dt {
  constructor(e) {
    if (typeof e != "function")
      throw new TypeError("executor must be a function.");
    let t;
    this.promise = new Promise(function(i) {
      t = i;
    });
    const n = this;
    this.promise.then((s) => {
      if (!n._listeners) return;
      let i = n._listeners.length;
      for (; i-- > 0; )
        n._listeners[i](s);
      n._listeners = null;
    }), this.promise.then = (s) => {
      let i;
      const o = new Promise((c) => {
        n.subscribe(c), i = c;
      }).then(s);
      return o.cancel = function() {
        n.unsubscribe(i);
      }, o;
    }, e(function(i, o, c) {
      n.reason || (n.reason = new V(i, o, c), t(n.reason));
    });
  }
  /**
   * Throws a `CanceledError` if cancellation has been requested.
   */
  throwIfRequested() {
    if (this.reason)
      throw this.reason;
  }
  /**
   * Subscribe to the cancel signal
   */
  subscribe(e) {
    if (this.reason) {
      e(this.reason);
      return;
    }
    this._listeners ? this._listeners.push(e) : this._listeners = [e];
  }
  /**
   * Unsubscribe from the cancel signal
   */
  unsubscribe(e) {
    if (!this._listeners)
      return;
    const t = this._listeners.indexOf(e);
    t !== -1 && this._listeners.splice(t, 1);
  }
  toAbortSignal() {
    const e = new AbortController(), t = (n) => {
      e.abort(n);
    };
    return this.subscribe(t), e.signal.unsubscribe = () => this.unsubscribe(t), e.signal;
  }
  /**
   * Returns an object that contains a new `CancelToken` and a function that, when called,
   * cancels the `CancelToken`.
   */
  static source() {
    let e;
    return {
      token: new dt(function(s) {
        e = s;
      }),
      cancel: e
    };
  }
};
function Xr(r) {
  return function(t) {
    return r.apply(null, t);
  };
}
function Gr(r) {
  return a.isObject(r) && r.isAxiosError === !0;
}
const Ae = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(Ae).forEach(([r, e]) => {
  Ae[e] = r;
});
function ft(r) {
  const e = new $(r), t = qe($.prototype.request, e);
  return a.extend(t, $.prototype, e, { allOwnKeys: !0 }), a.extend(t, e, null, { allOwnKeys: !0 }), t.create = function(s) {
    return ft(z(r, s));
  }, t;
}
const S = ft(K);
S.Axios = $;
S.CanceledError = V;
S.CancelToken = Jr;
S.isCancel = st;
S.VERSION = ht;
S.toFormData = oe;
S.AxiosError = y;
S.Cancel = S.CanceledError;
S.all = function(e) {
  return Promise.all(e);
};
S.spread = Xr;
S.isAxiosError = Gr;
S.mergeConfig = z;
S.AxiosHeaders = k;
S.formToJSON = (r) => nt(a.isHTMLForm(r) ? new FormData(r) : r);
S.getAdapter = lt.getAdapter;
S.HttpStatusCode = Ae;
S.default = S;
const {
  Axios: cn,
  AxiosError: un,
  CanceledError: ln,
  isCancel: hn,
  CancelToken: dn,
  VERSION: fn,
  all: pn,
  Cancel: mn,
  isAxiosError: yn,
  spread: gn,
  toFormData: wn,
  AxiosHeaders: En,
  HttpStatusCode: An,
  formToJSON: Sn,
  getAdapter: bn,
  mergeConfig: Rn
} = S;
class xe extends Error {
  constructor(t, n, s) {
    super(t);
    b(this, "statusCode");
    b(this, "response");
    this.name = "ApiError", this.statusCode = n, this.response = s;
  }
}
class pt extends xe {
  constructor(e = "Authentication failed") {
    super(e, 401), this.name = "AuthenticationError";
  }
}
class Kr extends xe {
  constructor(e = "Network error") {
    super(e, 0), this.name = "NetworkError";
  }
}
var L = /* @__PURE__ */ ((r) => (r.LOGIN_START = "login_start", r.LOGIN_SUCCESS = "login_success", r.LOGIN_FAILURE = "login_failure", r.LOGOUT = "logout", r.TOKEN_REFRESH = "token_refresh", r.TOKEN_EXPIRED = "token_expired", r.AUTH_ERROR = "auth_error", r))(L || {});
class Qr {
  constructor() {
    b(this, "authState", {
      user: null,
      isAuthenticated: !1,
      isLoading: !1,
      error: null,
      token: null
    });
    b(this, "listeners", []);
    b(this, "d365Context", null);
  }
  async initialize() {
    this.setLoading(!0);
    try {
      await this.waitForDynamicsContext();
      const e = await this.getDynamicsUser();
      if (e)
        this.authState = {
          user: e,
          isAuthenticated: !0,
          isLoading: !1,
          error: null,
          token: null
          // D365 handles tokens internally
        }, this.emitAuthEvent(L.LOGIN_SUCCESS, { user: e }), g.info("Dynamics 365 authentication initialized successfully");
      else
        throw new Error("Failed to get user information from Dynamics 365");
    } catch (e) {
      const t = e instanceof Error ? e.message : "Unknown authentication error";
      this.authState = {
        user: null,
        isAuthenticated: !1,
        isLoading: !1,
        error: t,
        token: null
      }, this.emitAuthEvent(L.AUTH_ERROR, { error: t }), g.error("Dynamics 365 authentication initialization failed:", e);
    }
    this.notifyListeners();
  }
  getAuthState() {
    return { ...this.authState };
  }
  async login(e) {
    try {
      return this.authState.isAuthenticated && this.authState.user ? {
        success: !0,
        user: this.authState.user,
        token: this.authState.token || void 0
      } : (await this.initialize(), {
        success: this.authState.isAuthenticated,
        user: this.authState.user || void 0,
        error: this.authState.error || void 0
      });
    } catch (t) {
      return {
        success: !1,
        error: t instanceof Error ? t.message : "Login failed"
      };
    }
  }
  async logout() {
    this.authState = {
      user: null,
      isAuthenticated: !1,
      isLoading: !1,
      error: null,
      token: null
    }, this.emitAuthEvent(L.LOGOUT), this.notifyListeners(), g.info("Dynamics 365 authentication state cleared");
  }
  async getCurrentUser() {
    if (this.authState.user)
      return this.authState.user;
    try {
      return await this.getDynamicsUser();
    } catch (e) {
      return g.error("Failed to get current user:", e), null;
    }
  }
  async getAccessToken() {
    return null;
  }
  async refreshToken() {
    return this.authState.isAuthenticated;
  }
  isAuthenticated() {
    return this.authState.isAuthenticated;
  }
  onAuthStateChanged(e) {
    return this.listeners.push(e), () => {
      const t = this.listeners.indexOf(e);
      t > -1 && this.listeners.splice(t, 1);
    };
  }
  async waitForDynamicsContext(e = 1e4) {
    return new Promise((t, n) => {
      const s = Date.now(), i = () => {
        if (this.isDynamicsContextAvailable()) {
          t();
          return;
        }
        if (Date.now() - s > e) {
          n(new Error("Timeout waiting for Dynamics 365 context"));
          return;
        }
        setTimeout(i, 100);
      };
      i();
    });
  }
  isDynamicsContextAvailable() {
    try {
      return !!window.Xrm && !!window.Xrm.WebApi;
    } catch {
      return !1;
    }
  }
  async getDynamicsUser() {
    try {
      const e = window.Xrm;
      if (!e || !e.WebApi)
        throw new Error("Dynamics 365 context not available");
      const t = await e.WebApi.retrieveRecord("systemuser", e.Utility.getGlobalContext().getUserId(), "?$select=systemuserid,fullname,internalemailaddress"), n = await e.WebApi.retrieveMultipleRecords("role", "?$filter=_parentroleid_value eq null&$select=name"), s = e.Utility.getGlobalContext().getOrganizationSettings();
      return this.d365Context = {
        userId: t.systemuserid,
        userRoles: n.entities.map((i) => i.name),
        organizationId: s.organizationId,
        organizationName: s.uniqueName,
        serverUrl: e.Utility.getGlobalContext().getClientUrl(),
        version: e.Utility.getGlobalContext().getVersion()
      }, {
        id: t.systemuserid,
        email: t.internalemailaddress,
        name: t.fullname,
        roles: this.d365Context.userRoles,
        organizationId: this.d365Context.organizationId
      };
    } catch (e) {
      throw g.error("Failed to get Dynamics 365 user information:", e), e;
    }
  }
  setLoading(e) {
    this.authState.isLoading = e, this.notifyListeners();
  }
  notifyListeners() {
    this.listeners.forEach((e) => {
      try {
        e(this.getAuthState());
      } catch (t) {
        g.error("Error in auth state listener:", t);
      }
    });
  }
  emitAuthEvent(e, t) {
    const n = {
      event: e,
      timestamp: /* @__PURE__ */ new Date(),
      ...t
    };
    g.debug("Auth event emitted:", n);
  }
  /**
   * Get Dynamics 365 specific context information
   */
  getDynamics365Context() {
    return this.d365Context;
  }
}
class Yr {
  constructor(e) {
    b(this, "authState", {
      user: null,
      isAuthenticated: !1,
      isLoading: !1,
      error: null,
      token: null
    });
    b(this, "listeners", []);
    b(this, "msalInstance", null);
    b(this, "config");
    b(this, "tokenRefreshTimer", null);
    this.config = e;
  }
  async initialize() {
    this.setLoading(!0);
    try {
      await this.initializeMSAL(), await this.checkExistingAuth(), g.info("MSAL authentication initialized successfully");
    } catch (e) {
      const t = e instanceof Error ? e.message : "MSAL initialization failed";
      this.authState = {
        user: null,
        isAuthenticated: !1,
        isLoading: !1,
        error: t,
        token: null
      }, this.emitAuthEvent(L.AUTH_ERROR, { error: t }), g.error("MSAL authentication initialization failed:", e);
    }
    this.notifyListeners();
  }
  getAuthState() {
    return { ...this.authState };
  }
  async login(e) {
    this.setLoading(!0), this.emitAuthEvent(L.LOGIN_START);
    try {
      if (!this.msalInstance)
        throw new Error("MSAL not initialized");
      const t = {
        scopes: this.config.scopes,
        prompt: e != null && e.interactive ? "select_account" : void 0
      };
      let n;
      try {
        n = await this.msalInstance.acquireTokenSilent(t);
      } catch {
        n = await this.msalInstance.loginPopup(t);
      }
      const s = await this.processAuthResult(n);
      if (s)
        return this.authState = {
          user: s,
          isAuthenticated: !0,
          isLoading: !1,
          error: null,
          token: this.createTokenFromAuthResult(n)
        }, this.startTokenRefreshTimer(), this.emitAuthEvent(L.LOGIN_SUCCESS, { user: s }), {
          success: !0,
          user: s,
          token: this.authState.token || void 0
        };
      throw new Error("Failed to process authentication result");
    } catch (t) {
      const n = t instanceof Error ? t.message : "Login failed";
      return this.authState = {
        user: null,
        isAuthenticated: !1,
        isLoading: !1,
        error: n,
        token: null
      }, this.emitAuthEvent(L.LOGIN_FAILURE, { error: n }), {
        success: !1,
        error: n
      };
    } finally {
      this.notifyListeners();
    }
  }
  async logout() {
    try {
      this.tokenRefreshTimer && (clearTimeout(this.tokenRefreshTimer), this.tokenRefreshTimer = null), this.msalInstance && await this.msalInstance.logout({
        postLogoutRedirectUri: this.config.redirectUri
      });
    } catch (e) {
      g.error("Logout error:", e);
    }
    this.authState = {
      user: null,
      isAuthenticated: !1,
      isLoading: !1,
      error: null,
      token: null
    }, this.emitAuthEvent(L.LOGOUT), this.notifyListeners(), g.info("User logged out successfully");
  }
  async getCurrentUser() {
    if (this.authState.user)
      return this.authState.user;
    try {
      if (!this.msalInstance)
        return null;
      const e = this.msalInstance.getActiveAccount();
      return e ? this.createUserFromAccount(e) : null;
    } catch (e) {
      return g.error("Failed to get current user:", e), null;
    }
  }
  async getAccessToken() {
    try {
      if (!this.msalInstance)
        return null;
      if (this.authState.token && this.authState.token.expiresAt > /* @__PURE__ */ new Date())
        return this.authState.token.accessToken;
      const e = {
        scopes: this.config.scopes,
        account: this.msalInstance.getActiveAccount()
      }, t = await this.msalInstance.acquireTokenSilent(e);
      return t ? (this.authState.token = this.createTokenFromAuthResult(t), this.notifyListeners(), t.accessToken) : null;
    } catch (e) {
      return g.error("Failed to get access token:", e), this.emitAuthEvent(L.TOKEN_EXPIRED), null;
    }
  }
  async refreshToken() {
    try {
      return await this.getAccessToken() ? (this.emitAuthEvent(L.TOKEN_REFRESH), !0) : !1;
    } catch (e) {
      return g.error("Token refresh failed:", e), !1;
    }
  }
  isAuthenticated() {
    return this.authState.isAuthenticated;
  }
  onAuthStateChanged(e) {
    return this.listeners.push(e), () => {
      const t = this.listeners.indexOf(e);
      t > -1 && this.listeners.splice(t, 1);
    };
  }
  async initializeMSAL() {
    try {
      const { PublicClientApplication: e } = await import("./index-BBFh9CQU.mjs"), t = {
        auth: {
          clientId: this.config.clientId,
          authority: this.config.authority,
          redirectUri: this.config.redirectUri
        },
        cache: {
          cacheLocation: this.config.cacheLocation || "localStorage",
          storeAuthStateInCookie: !1
        }
      };
      this.msalInstance = new e(t), await this.msalInstance.initialize();
    } catch (e) {
      if (e instanceof Error && e.message.includes("Failed to resolve module"))
        g.warn("MSAL library not available, using mock implementation"), this.msalInstance = this.createMockMSALInstance();
      else
        throw e;
    }
  }
  async checkExistingAuth() {
    if (this.msalInstance)
      try {
        const e = this.msalInstance.getAllAccounts();
        if (e.length > 0) {
          this.msalInstance.setActiveAccount(e[0]);
          const t = {
            scopes: this.config.scopes,
            account: e[0]
          }, n = await this.msalInstance.acquireTokenSilent(t);
          if (n) {
            const s = this.createUserFromAccount(e[0]);
            this.authState = {
              user: s,
              isAuthenticated: !0,
              isLoading: !1,
              error: null,
              token: this.createTokenFromAuthResult(n)
            }, this.startTokenRefreshTimer();
          }
        }
      } catch (e) {
        g.debug("No existing valid authentication found:", e);
      }
  }
  async processAuthResult(e) {
    var t;
    return !e || !e.account ? null : ((t = this.msalInstance) == null || t.setActiveAccount(e.account), this.createUserFromAccount(e.account));
  }
  createUserFromAccount(e) {
    return {
      id: e.homeAccountId || e.localAccountId,
      email: e.username,
      name: e.name || e.username,
      roles: [],
      // Roles would need to be fetched from additional API calls
      tenantId: e.tenantId
    };
  }
  createTokenFromAuthResult(e) {
    return {
      accessToken: e.accessToken,
      expiresAt: new Date(e.expiresOn),
      scopes: e.scopes || this.config.scopes
    };
  }
  startTokenRefreshTimer() {
    if (this.tokenRefreshTimer && clearTimeout(this.tokenRefreshTimer), this.authState.token) {
      const e = this.authState.token.expiresAt.getTime() - Date.now() - 3e5;
      e > 0 && (this.tokenRefreshTimer = setTimeout(() => {
        this.refreshToken();
      }, e));
    }
  }
  setLoading(e) {
    this.authState.isLoading = e, this.notifyListeners();
  }
  notifyListeners() {
    this.listeners.forEach((e) => {
      try {
        e(this.getAuthState());
      } catch (t) {
        g.error("Error in auth state listener:", t);
      }
    });
  }
  emitAuthEvent(e, t) {
    const n = {
      event: e,
      timestamp: /* @__PURE__ */ new Date(),
      ...t
    };
    g.debug("Auth event emitted:", n);
  }
  createMockMSALInstance() {
    return {
      initialize: async () => {
      },
      loginPopup: async () => ({ account: { username: "<EMAIL>", name: "Mock User" }, accessToken: "mock-token", expiresOn: new Date(Date.now() + 36e5) }),
      loginRedirect: async () => {
      },
      logout: async () => {
      },
      acquireTokenSilent: async () => ({ accessToken: "mock-token", expiresOn: new Date(Date.now() + 36e5) }),
      acquireTokenPopup: async () => ({ accessToken: "mock-token", expiresOn: new Date(Date.now() + 36e5) }),
      getAllAccounts: () => [],
      getActiveAccount: () => null,
      setActiveAccount: () => {
      }
    };
  }
}
const fe = { BASE_URL: "/", DEV: !1, MODE: "production", PROD: !0, SSR: !1 };
var j = /* @__PURE__ */ ((r) => (r.WEB_RESOURCE = "web_resource", r.STANDALONE = "standalone", r))(j || {});
const B = class B {
  constructor() {
    b(this, "_detectedMode", null);
    b(this, "_config", null);
  }
  static getInstance() {
    return B._instance || (B._instance = new B()), B._instance;
  }
  /**
   * Detects the deployment mode based on runtime environment
   */
  detectDeploymentMode() {
    if (this._detectedMode)
      return this._detectedMode;
    const e = this.isDynamics365Environment(), t = this.getConfigurationOverride();
    return t ? this._detectedMode = t : this._detectedMode = e ? "web_resource" : "standalone", this._detectedMode;
  }
  /**
   * Checks if running within Dynamics 365 context
   */
  isDynamics365Environment() {
    try {
      if (typeof window < "u") {
        if (window.Xrm && window.Xrm.WebApi)
          return !0;
        if (window.parent && window.parent !== window)
          try {
            if (window.parent.Xrm)
              return !0;
          } catch {
          }
        const e = window.location.href;
        if (e.includes(".dynamics.com") || e.includes(".crm.dynamics.com") || e.includes("orgname.crm"))
          return !0;
        const t = new URLSearchParams(window.location.search);
        if (t.has("orgname") || t.has("appid"))
          return !0;
      }
      return !1;
    } catch (e) {
      return console.warn("Error detecting Dynamics 365 environment:", e), !1;
    }
  }
  /**
   * Gets configuration override from environment variables or URL parameters
   */
  getConfigurationOverride() {
    try {
      const e = this.getEnvironmentVariable("VITE_DEPLOYMENT_MODE");
      if (e)
        return e;
      if (typeof window < "u") {
        const n = new URLSearchParams(window.location.search).get("deploymentMode");
        if (n && Object.values(j).includes(n))
          return n;
      }
      return null;
    } catch (e) {
      return console.warn("Error getting configuration override:", e), null;
    }
  }
  /**
   * Gets environment variable with fallback
   */
  getEnvironmentVariable(e) {
    try {
      return (fe == null ? void 0 : fe[e]) || null;
    } catch {
      return null;
    }
  }
  /**
   * Gets the deployment configuration for the detected mode
   */
  getDeploymentConfig() {
    if (this._config)
      return this._config;
    const e = this.detectDeploymentMode();
    return this._config = this.createConfigForMode(e), this._config;
  }
  /**
   * Creates configuration object for the specified deployment mode
   */
  createConfigForMode(e) {
    const t = {
      mode: e,
      features: {
        enableLogging: this.getEnvironmentVariable("VITE_ENABLE_LOGGING") === "true" || e === "standalone",
        enableOfflineMode: this.getEnvironmentVariable("VITE_ENABLE_OFFLINE") === "true" || !1,
        enableTelemetry: this.getEnvironmentVariable("VITE_ENABLE_TELEMETRY") === "true" || !1
      }
    };
    return e === "web_resource" ? {
      ...t,
      apiBaseUrl: "",
      // Will use relative URLs within D365
      authMethod: "dynamics365",
      dynamicsConfig: {
        serverUrl: this.getEnvironmentVariable("VITE_DYNAMICS_SERVER_URL") || "",
        version: this.getEnvironmentVariable("VITE_DYNAMICS_API_VERSION") || "9.2"
      }
    } : {
      ...t,
      apiBaseUrl: this.getEnvironmentVariable("VITE_API_BASE_URL") || "https://your-org.api.crm.dynamics.com/api/data/v9.2",
      authMethod: "msal",
      msalConfig: {
        clientId: this.getEnvironmentVariable("VITE_MSAL_CLIENT_ID") || "",
        authority: this.getEnvironmentVariable("VITE_MSAL_AUTHORITY") || "https://login.microsoftonline.com/common",
        redirectUri: this.getEnvironmentVariable("VITE_MSAL_REDIRECT_URI") || window.location.origin,
        scopes: (this.getEnvironmentVariable("VITE_MSAL_SCOPES") || "https://your-org.crm.dynamics.com/.default").split(",")
      }
    };
  }
  /**
   * Forces a specific deployment mode (useful for testing)
   */
  forceDeploymentMode(e) {
    this._detectedMode = e, this._config = null;
  }
  /**
   * Resets the detector state
   */
  reset() {
    this._detectedMode = null, this._config = null;
  }
};
b(B, "_instance");
let Se = B;
function ue() {
  return Se.getInstance().getDeploymentConfig();
}
function Tn() {
  return ue().mode === "web_resource";
}
function Cn() {
  return ue().mode === "standalone";
}
const P = class P {
  /**
   * Gets the singleton authentication service instance
   */
  static getInstance() {
    return P._instance || (P._instance = P.createAuthService()), P._instance;
  }
  /**
   * Creates the appropriate authentication service based on deployment context
   */
  static createAuthService() {
    const e = ue();
    switch (g.info(`Creating authentication service for ${e.mode} deployment mode`), e.mode) {
      case j.WEB_RESOURCE:
        return new Qr();
      case j.STANDALONE:
        if (!e.msalConfig)
          throw new Error("MSAL configuration is required for standalone deployment mode");
        return new Yr(e.msalConfig);
      default:
        throw new Error(`Unsupported deployment mode: ${e.mode}`);
    }
  }
  /**
   * Resets the singleton instance (useful for testing)
   */
  static reset() {
    P._instance = null;
  }
  /**
   * Forces creation of a specific authentication service (useful for testing)
   */
  static forceInstance(e) {
    P._instance = e;
  }
};
b(P, "_instance", null);
let be = P;
function ke() {
  return be.getInstance();
}
const pe = {};
class mt {
  constructor(e) {
    b(this, "client");
    b(this, "authService", ke());
    this.client = S.create({
      baseURL: e.baseURL,
      timeout: e.timeout || 3e4,
      headers: {
        "Content-Type": "application/json",
        "OData-MaxVersion": "4.0",
        "OData-Version": "4.0",
        Accept: "application/json",
        Prefer: "return=representation",
        ...e.headers
      }
    }), this.setupInterceptors();
  }
  async initialize() {
    try {
      await this.authService.initialize(), g.info("External API client initialized successfully");
    } catch (e) {
      throw g.error("Failed to initialize external API client:", e), e;
    }
  }
  setupInterceptors() {
    this.client.interceptors.request.use(
      async (e) => {
        var n;
        g.info(`API Request: ${(n = e.method) == null ? void 0 : n.toUpperCase()} ${e.url}`);
        const t = await this.getAuthToken();
        return t && (e.headers = e.headers || {}, e.headers.Authorization = `Bearer ${t}`), e;
      },
      (e) => (g.error("API Request Error:", e), Promise.reject(this.createApiError(e)))
    ), this.client.interceptors.response.use(
      (e) => (g.info(`API Response: ${e.status} ${e.config.url}`), e),
      async (e) => {
        var t;
        if (g.error("API Response Error:", e), ((t = e.response) == null ? void 0 : t.status) === 401 && await this.handleUnauthorized() && e.config) {
          const s = await this.getAuthToken();
          if (s)
            return e.config.headers.Authorization = `Bearer ${s}`, this.client.request(e.config);
        }
        return Promise.reject(this.createApiError(e));
      }
    );
  }
  async getAuthToken() {
    try {
      return await this.authService.getAccessToken();
    } catch (e) {
      return g.error("Failed to get auth token:", e), null;
    }
  }
  async handleUnauthorized() {
    try {
      return await this.authService.refreshToken() ? (g.info("Token refreshed successfully"), !0) : (g.warn("Token refresh failed, triggering re-authentication"), await this.authService.login({ interactive: !0 }), !0);
    } catch (e) {
      return g.error("Failed to handle unauthorized access:", e), !1;
    }
  }
  async get(e, t) {
    try {
      const n = this.convertConfig(t), s = await this.client.get(e, n);
      return {
        data: s.data,
        success: !0,
        statusCode: s.status
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async post(e, t, n) {
    try {
      const s = this.convertConfig(n), i = await this.client.post(e, t, s);
      return {
        data: i.data,
        success: !0,
        statusCode: i.status
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async put(e, t, n) {
    try {
      const s = this.convertConfig(n), i = await this.client.put(e, t, s);
      return {
        data: i.data,
        success: !0,
        statusCode: i.status
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async patch(e, t, n) {
    try {
      const s = this.convertConfig(n), i = await this.client.patch(e, t, s);
      return {
        data: i.data,
        success: !0,
        statusCode: i.status
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async delete(e, t) {
    try {
      const n = this.convertConfig(t), s = await this.client.delete(e, n);
      return {
        data: s.data,
        success: !0,
        statusCode: s.status
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  // Dataverse-specific methods
  async retrieveRecord(e, t, n) {
    const s = this.buildQueryString(n), i = `${e}(${t})${s}`;
    return this.get(i);
  }
  async retrieveMultipleRecords(e, t) {
    try {
      const n = this.buildQueryString(t), s = `${e}${n}`, i = await this.get(s);
      return i.success && i.data ? {
        data: i.data.value,
        success: !0,
        statusCode: i.statusCode,
        pagination: {
          page: Math.floor(((t == null ? void 0 : t.skip) || 0) / ((t == null ? void 0 : t.top) || 50)) + 1,
          pageSize: i.data.value.length,
          totalCount: i.data["@odata.count"] || i.data.value.length,
          hasNext: !!i.data["@odata.nextLink"],
          hasPrevious: ((t == null ? void 0 : t.skip) || 0) > 0
        }
      } : this.handleErrorPaginated(new Error("Invalid response format"));
    } catch (n) {
      return this.handleErrorPaginated(n);
    }
  }
  async createRecord(e, t) {
    return this.post(e, t);
  }
  async updateRecord(e, t, n) {
    const s = `${e}(${t})`;
    return this.patch(s, n);
  }
  async deleteRecord(e, t) {
    const n = `${e}(${t})`;
    return this.delete(n);
  }
  async executeFunction(e, t) {
    let n = e;
    if (t) {
      const s = Object.keys(t).map((i) => `${i}=${encodeURIComponent(t[i])}`).join(",");
      n += `(${s})`;
    }
    return this.get(n);
  }
  async executeBatch(e) {
    try {
      const t = this.generateBatchId(), n = this.generateChangesetId(), s = this.buildBatchBody(e, t, n), i = await this.client.post("$batch", s, {
        headers: {
          "Content-Type": `multipart/mixed; boundary=batch_${t}`
        }
      }), o = this.parseBatchResponse(i.data);
      return {
        responses: o,
        success: o.every((c) => c.success)
      };
    } catch (t) {
      return g.error("Batch execution failed:", t), {
        responses: [],
        success: !1,
        errors: [t instanceof Error ? t.message : "Batch execution failed"]
      };
    }
  }
  convertConfig(e) {
    return e ? {
      headers: e.headers,
      timeout: e.timeout
    } : {};
  }
  buildQueryString(e) {
    if (!e) return "";
    const t = [];
    return e.select && t.push(`$select=${e.select.join(",")}`), e.filter && t.push(`$filter=${encodeURIComponent(e.filter)}`), e.orderBy && t.push(`$orderby=${encodeURIComponent(e.orderBy)}`), e.expand && t.push(`$expand=${e.expand.join(",")}`), e.top && t.push(`$top=${e.top}`), e.skip && t.push(`$skip=${e.skip}`), t.length > 0 ? `?${t.join("&")}` : "";
  }
  generateBatchId() {
    return Math.random().toString(36).substring(2, 15);
  }
  generateChangesetId() {
    return Math.random().toString(36).substring(2, 15);
  }
  buildBatchBody(e, t, n) {
    return e.map((s) => `--batch_${t}
Content-Type: application/http

${s.method} ${s.url} HTTP/1.1

`).join("");
  }
  parseBatchResponse(e) {
    return [];
  }
  createApiError(e) {
    var t, n, s, i, o, c;
    return e.code === "ECONNABORTED" || e.code === "ENOTFOUND" ? new Kr(e.message) : ((t = e.response) == null ? void 0 : t.status) === 401 ? new pt(((n = e.response.data) == null ? void 0 : n.message) || "Authentication failed") : new xe(
      ((i = (s = e.response) == null ? void 0 : s.data) == null ? void 0 : i.message) || e.message || "An error occurred",
      ((o = e.response) == null ? void 0 : o.status) || 500,
      (c = e.response) == null ? void 0 : c.data
    );
  }
  handleError(e) {
    const t = this.createApiError(e);
    return {
      data: null,
      success: !1,
      message: t.message,
      statusCode: t.statusCode,
      errors: [t.message]
    };
  }
  handleErrorPaginated(e) {
    return {
      ...this.handleError(e),
      data: [],
      pagination: {
        page: 1,
        pageSize: 0,
        totalCount: 0,
        hasNext: !1,
        hasPrevious: !1
      }
    };
  }
}
const Zr = () => (pe == null ? void 0 : pe.VITE_API_BASE_URL) || "http://localhost:3001/api";
new mt({
  baseURL: Zr()
});
class en {
  constructor() {
    b(this, "xrmWebApi", null);
  }
  async initialize() {
    try {
      if (await this.waitForXrmContext(), this.xrmWebApi = window.Xrm.WebApi, !this.xrmWebApi)
        throw new Error("Xrm.WebApi is not available");
      g.info("Dynamics 365 API client initialized successfully");
    } catch (e) {
      throw g.error("Failed to initialize Dynamics 365 API client:", e), e;
    }
  }
  async get(e, t) {
    try {
      return this.ensureInitialized(), {
        data: await this.xrmWebApi.retrieveRecord("systemuser", e),
        success: !0
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async post(e, t, n) {
    try {
      this.ensureInitialized();
      const s = this.extractEntityNameFromUrl(e);
      return {
        data: await this.xrmWebApi.createRecord(s, t),
        success: !0
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async put(e, t, n) {
    try {
      this.ensureInitialized();
      const { entityName: s, id: i } = this.parseEntityUrl(e);
      return await this.xrmWebApi.updateRecord(s, i, t), {
        data: null,
        success: !0
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async patch(e, t, n) {
    return this.put(e, t, n);
  }
  async delete(e, t) {
    try {
      this.ensureInitialized();
      const { entityName: n, id: s } = this.parseEntityUrl(e);
      return await this.xrmWebApi.deleteRecord(n, s), {
        data: null,
        success: !0
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async retrieveRecord(e, t, n) {
    try {
      this.ensureInitialized();
      const s = this.buildQueryString(n);
      return {
        data: await this.xrmWebApi.retrieveRecord(e, t, s),
        success: !0
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async retrieveMultipleRecords(e, t) {
    try {
      this.ensureInitialized();
      const n = this.buildQueryString(t), s = await this.xrmWebApi.retrieveMultipleRecords(e, n);
      return {
        data: s.entities,
        success: !0,
        pagination: {
          page: 1,
          // Dynamics 365 doesn't provide page numbers directly
          pageSize: s.entities.length,
          totalCount: s.entities.length,
          // This would need additional logic for accurate count
          hasNext: !!s["@odata.nextLink"],
          hasPrevious: !1
        }
      };
    } catch (n) {
      return this.handleErrorPaginated(n);
    }
  }
  async createRecord(e, t) {
    try {
      return this.ensureInitialized(), {
        data: await this.xrmWebApi.createRecord(e, t),
        success: !0
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async updateRecord(e, t, n) {
    try {
      return this.ensureInitialized(), await this.xrmWebApi.updateRecord(e, t, n), {
        data: null,
        success: !0
      };
    } catch (s) {
      return this.handleError(s);
    }
  }
  async deleteRecord(e, t) {
    try {
      return this.ensureInitialized(), await this.xrmWebApi.deleteRecord(e, t), {
        data: void 0,
        success: !0
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async executeFunction(e, t) {
    try {
      this.ensureInitialized();
      let n = e;
      if (t) {
        const i = Object.keys(t).map((o) => `${o}=${encodeURIComponent(t[o])}`).join(",");
        n += `(${i})`;
      }
      return {
        data: await this.xrmWebApi.online.executeFunction(n),
        success: !0
      };
    } catch (n) {
      return this.handleError(n);
    }
  }
  async executeBatch(e) {
    try {
      this.ensureInitialized();
      const t = [];
      for (const n of e)
        try {
          let s;
          switch (n.method) {
            case "GET":
              s = await this.get(n.url);
              break;
            case "POST":
              s = await this.post(n.url, n.data);
              break;
            case "PUT":
              s = await this.put(n.url, n.data);
              break;
            case "PATCH":
              s = await this.patch(n.url, n.data);
              break;
            case "DELETE":
              s = await this.delete(n.url);
              break;
            default:
              throw new Error(`Unsupported method: ${n.method}`);
          }
          t.push(s);
        } catch (s) {
          t.push(this.handleError(s));
        }
      return {
        responses: t,
        success: t.every((n) => n.success)
      };
    } catch (t) {
      return g.error("Batch execution failed:", t), {
        responses: [],
        success: !1,
        errors: [t instanceof Error ? t.message : "Batch execution failed"]
      };
    }
  }
  async waitForXrmContext(e = 1e4) {
    return new Promise((t, n) => {
      const s = Date.now(), i = () => {
        if (window.Xrm && window.Xrm.WebApi) {
          t();
          return;
        }
        if (Date.now() - s > e) {
          n(new Error("Timeout waiting for Dynamics 365 context"));
          return;
        }
        setTimeout(i, 100);
      };
      i();
    });
  }
  ensureInitialized() {
    if (!this.xrmWebApi)
      throw new pt("Dynamics 365 API client not initialized");
  }
  buildQueryString(e) {
    if (!e) return "";
    const t = [];
    return e.select && t.push(`$select=${e.select.join(",")}`), e.filter && t.push(`$filter=${encodeURIComponent(e.filter)}`), e.orderBy && t.push(`$orderby=${encodeURIComponent(e.orderBy)}`), e.expand && t.push(`$expand=${e.expand.join(",")}`), e.top && t.push(`$top=${e.top}`), e.skip && t.push(`$skip=${e.skip}`), t.length > 0 ? `?${t.join("&")}` : "";
  }
  extractEntityNameFromUrl(e) {
    const t = e.match(/\/([^\/\?]+)/);
    return t ? t[1] : "systemuser";
  }
  parseEntityUrl(e) {
    const t = e.match(/\/([^\/]+)\/([^\/\?]+)/);
    return {
      entityName: t ? t[1] : "systemuser",
      id: t ? t[2] : ""
    };
  }
  handleError(e) {
    g.error("Dynamics 365 API error:", e);
    let t = "An error occurred", n = 500;
    return e && e.message && (t = e.message), e && e.status && (n = e.status), {
      data: null,
      success: !1,
      message: t,
      statusCode: n,
      errors: [t]
    };
  }
  handleErrorPaginated(e) {
    return {
      ...this.handleError(e),
      data: [],
      pagination: {
        page: 1,
        pageSize: 0,
        totalCount: 0,
        hasNext: !1,
        hasPrevious: !1
      }
    };
  }
}
const D = class D {
  /**
   * Gets the singleton API client instance
   */
  static async getInstance() {
    return D._instance || (D._instance = await D.createApiClient()), D._instance;
  }
  /**
   * Creates the appropriate API client based on deployment context
   */
  static async createApiClient() {
    const e = ue();
    g.info(`Creating API client for ${e.mode} deployment mode`);
    let t;
    switch (e.mode) {
      case j.WEB_RESOURCE:
        t = new en();
        break;
      case j.STANDALONE:
        t = new mt({
          baseURL: e.apiBaseUrl,
          timeout: 3e4,
          headers: {
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
            Accept: "application/json",
            Prefer: "return=representation"
          }
        });
        break;
      default:
        throw new Error(`Unsupported deployment mode: ${e.mode}`);
    }
    return await t.initialize(), t;
  }
  /**
   * Resets the singleton instance (useful for testing)
   */
  static reset() {
    D._instance = null;
  }
  /**
   * Forces creation of a specific API client (useful for testing)
   */
  static forceInstance(e) {
    D._instance = e;
  }
};
b(D, "_instance", null);
let Re = D;
async function On() {
  return Re.getInstance();
}
class tn {
  constructor() {
    b(this, "authService", ke());
  }
  async initialize() {
    return this.authService.initialize();
  }
  getAuthState() {
    return this.authService.getAuthState();
  }
  async login(e) {
    return await this.authService.login(e);
  }
  async logout() {
    return this.authService.logout();
  }
  async refreshToken() {
    return this.authService.refreshToken();
  }
  async getCurrentUser() {
    return this.authService.getCurrentUser();
  }
  async getAccessToken() {
    return this.authService.getAccessToken();
  }
  isAuthenticated() {
    return this.authService.isAuthenticated();
  }
  onAuthStateChanged(e) {
    return this.authService.onAuthStateChanged(e);
  }
}
const xn = new tn(), kn = () => {
  const [r, e] = wt({
    user: null,
    isAuthenticated: !1,
    isLoading: !0,
    error: null,
    token: null
  }), t = ke();
  Et(() => {
    const E = (async () => {
      try {
        await t.initialize();
        const h = t.onAuthStateChanged((m) => {
          e(m);
        });
        return e(t.getAuthState()), h;
      } catch (h) {
        g.error("Auth initialization error:", h), e((m) => ({
          ...m,
          error: h instanceof Error ? h.message : "Authentication initialization failed",
          isLoading: !1
        }));
      }
    })();
    return () => {
      E.then((h) => {
        h && h();
      });
    };
  }, [t]);
  const n = _(async (w) => await t.login(w), [t]), s = _(async () => {
    await t.logout();
  }, [t]), i = _(async () => await t.refreshToken(), [t]), o = _(async () => await t.getCurrentUser(), [t]), c = _(async () => await t.getAccessToken(), [t]), d = _(() => t.isAuthenticated(), [t]), u = _((w) => t.onAuthStateChanged(w), [t]), l = _(() => t.getAuthState(), [t]), f = _(async () => t.initialize(), [t]);
  return {
    ...r,
    login: n,
    logout: s,
    refreshToken: i,
    getCurrentUser: o,
    getAccessToken: c,
    checkAuthenticated: d,
    onAuthStateChanged: u,
    getAuthState: l,
    initialize: f
  };
};
export {
  j as D,
  mt as E,
  xn as a,
  ke as b,
  Se as c,
  ue as d,
  Cn as e,
  On as g,
  Tn as i,
  kn as u
};
