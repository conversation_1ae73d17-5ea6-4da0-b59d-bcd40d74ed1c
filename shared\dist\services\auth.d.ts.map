{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../services/auth.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EACL,QAAQ,IAAI,IAAI,EAChB,gBAAgB,EAChB,SAAS,EACT,UAAU,EACV,YAAY,IAAI,WAAW,EAC5B,MAAM,kBAAkB,CAAC;AAI1B,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC;AAG3E,MAAM,WAAW,aAAc,SAAQ,SAAS;IAC9C,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,gBAAgB,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC;IAC/D,MAAM,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,YAAY,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,cAAc,EAAE,MAAM,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAC3C,cAAc,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAC7C,kBAAkB,EAAE,MAAM,OAAO,CAAC;IAClC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC;IACzE,YAAY,EAAE,MAAM,SAAS,CAAC;IAC9B,UAAU,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;CACjC;AAGD,cAAM,wBAAyB,YAAW,WAAW;IACnD,OAAO,CAAC,WAAW,CAAoB;IAEjC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,YAAY,IAAI,SAAS;IAInB,KAAK,CAAC,WAAW,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;IAI1D,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAIvB,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;IAIhC,cAAc,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IAItC,cAAc,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI9C,eAAe,IAAI,OAAO;IAI1B,kBAAkB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,IAAI,GAAG,MAAM,IAAI;CAGrE;AAED,eAAO,MAAM,WAAW,0BAAiC,CAAC;AAE1D;;GAEG;AACH,eAAO,MAAM,OAAO,QAAO,aA+F1B,CAAC"}