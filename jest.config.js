/**
 * Jest Configuration for CRM React Apps Monorepo
 * 
 * Supports testing across multiple workspaces with theme-aware utilities
 */

module.exports = {
  // Use multiple projects for workspace support
  projects: [
    {
      displayName: 'shared',
      testMatch: ['<rootDir>/shared/**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      moduleNameMapping: {
        '^@shared/(.*)$': '<rootDir>/shared/$1',
      },
      transform: {
        '^.+\\.(ts|tsx)$': 'ts-jest',
      },
      moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
      collectCoverageFrom: [
        'shared/**/*.{ts,tsx}',
        '!shared/**/*.d.ts',
        '!shared/dist/**',
        '!shared/node_modules/**',
      ],
    },
    {
      displayName: 'transcript-and-summary',
      testMatch: ['<rootDir>/apps/transcript-and-summary/**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      moduleNameMapping: {
        '^@shared/(.*)$': '<rootDir>/shared/$1',
        '^@shared/components$': '<rootDir>/shared/components',
        '^@shared/services$': '<rootDir>/shared/services',
        '^@shared/utils$': '<rootDir>/shared/utils',
        '^@shared/config$': '<rootDir>/shared/config',
        '^@shared/styles$': '<rootDir>/shared/styles',
      },
      transform: {
        '^.+\\.(ts|tsx)$': 'ts-jest',
      },
      moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
      collectCoverageFrom: [
        'apps/transcript-and-summary/src/**/*.{ts,tsx}',
        '!apps/transcript-and-summary/src/**/*.d.ts',
        '!apps/transcript-and-summary/dist/**',
      ],
    },
    {
      displayName: 'if-party-master',
      testMatch: ['<rootDir>/apps/if-party-master/**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      moduleNameMapping: {
        '^@shared/(.*)$': '<rootDir>/shared/$1',
        '^@shared/components$': '<rootDir>/shared/components',
        '^@shared/services$': '<rootDir>/shared/services',
        '^@shared/utils$': '<rootDir>/shared/utils',
        '^@shared/config$': '<rootDir>/shared/config',
        '^@shared/styles$': '<rootDir>/shared/styles',
      },
      transform: {
        '^.+\\.(ts|tsx)$': 'ts-jest',
      },
      moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
      collectCoverageFrom: [
        'apps/if-party-master/src/**/*.{ts,tsx}',
        '!apps/if-party-master/src/**/*.d.ts',
        '!apps/if-party-master/dist/**',
      ],
    },
  ],

  // Global settings
  testTimeout: 10000,
  verbose: true,
  
  // Coverage settings
  collectCoverage: false, // Enable with --coverage flag
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },

  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
  ],

  // Watch mode settings
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
  ],

  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
};
