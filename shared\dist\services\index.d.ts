export { ExternalApiClient as ApiClient } from './apiClient';
export type { ApiClientConfig } from './apiClient';
export { getApiClient } from './api/apiFactory';
export type { IApiClient, ApiResponse, PaginatedResponse, QueryOptions, BatchRequest, BatchResponse } from './api/apiTypes';
export { authService, useAuth } from './auth';
export type { User, LoginCredentials, AuthState, AuthService } from './auth';
export { getAuthService } from './auth/authFactory';
export type { IAuthService, AuthUser, AuthToken, AuthResult } from './auth/authTypes';
//# sourceMappingURL=index.d.ts.map