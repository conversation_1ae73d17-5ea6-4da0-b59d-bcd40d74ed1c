{"version": 3, "file": "SessionStorage.mjs", "sources": ["../../src/cache/SessionStorage.ts"], "sourcesContent": [null], "names": ["BrowserConfigurationAuthErrorCodes.storageNotSupported"], "mappings": ";;;;;AAAA;;;AAGG;MAQU,cAAc,CAAA;AACvB,IAAA,WAAA,GAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACxB,YAAA,MAAM,mCAAmC,CACrCA,mBAAsD,CACzD,CAAC;AACL,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,GAAW,EAAA;QACf,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC7C;IAED,OAAO,CAAC,GAAW,EAAE,KAAa,EAAA;QAC9B,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7C;AAED,IAAA,UAAU,CAAC,GAAW,EAAA;AAClB,QAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACzC;IAED,OAAO,GAAA;QACH,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;KAC7C;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;QACnB,OAAO,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;KACpD;AACJ;;;;"}